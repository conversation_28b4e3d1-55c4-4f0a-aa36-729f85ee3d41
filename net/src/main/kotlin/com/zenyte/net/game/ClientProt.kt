package com.zenyte.net.game

import com.zenyte.net.NetworkConstants

/**
 * Sorted by <PERSON>.
 *
 * <AUTHOR> | 28 jul. 2018 | 12:45:15 | <AUTHOR> | 23. sept 2018 : 02:09:42
 * <AUTHOR>
 */
enum class ClientProt(
    override val opcode: Int,
    override val size: Int,
    val limit: Int = 10
) : Prot {

    NO_TIMEOUT(0, 0),
    OPOBJ5(1, 7),
    EVENT_MOUSE_MOVE(2, -1),
    EVENT_MOUSE_CLICK(3, 6),
    OPLOC4(4, 7),
    UNUSED_1(5, 7),
    IGNORELIST_ADD(6, -1),
    SET_CHATFILTERSETTINGS(7, 3),
    EVENT_CAMERA_POSITION(8, 4),
    OPNPC4(9, 3),
    WINDOW_STATUS(10, 5),
    OPPLAYER8(11, 3),
    CLICKWORLDMAP(12, 4),
    field3074(13, -1),
    MESSAGE_PRIVATE(14, -2),
    CLAN_JOINCHAT_LEAVECHAT(15, -1),
    OPOBJ<PERSON>(16, 7),
    OPNPCU(17, 11),
    IF_BUTTON3(18, 8),
    FRIENDLIST_DEL(19, -1),
    OPNPC6(20, 2),
    CLAN_KICKUSER(21, -1),
    IF_BUTTON4(22, 8),
    OPLOC1(23, 7),
    field3085(24, -1),
    IF_BUTTON10(25, 8),
    MOVE_MINIMAPCLICK(26, -1),
    field3088(27, 16),
    LOGIN_STATISTICS(28, -1),
    BUTTON_CLICK(29, 4),
    OPPLAYER7(30, 3),
    OPOBJ6(31, 2),
    OPNPC1(32, 3),
    FRIENDLIST_ADD(33, -1),
    field3095(34, -1),
    DETECT_MODIFIED_CLIENT(35, 4),
    IF1_BUTTON3(36, 8),
    OPPLAYER1(37, 3),
    OPOBJ3(38, 7),
    EVENT_APPLET_FOCUS(39, 1),
    field3101(40, 13),
    OPLOC2(41, 7),
    RESUME_P_STRINGDIALOG(42, -1),
    OPHELD4(43, 8),
    IF1_BUTTON5(44, 8),
    UNUSED_2(45, -1),
    MESSAGE_PUBLIC(46, -1),
    OPPLAYERU(47, 11),
    SEND_PING_REPLY(48, 10),
    OPPLAYER6(49, 3),
    OPPLAYER5(50, 3),
    field3080(51, 14),
    CLIENT_CHEAT(52, -1),
    FRIEND_SETRANK(53, -1),
    RESUME_P_NAMEDIALOG(54, -1),
    IF_BUTTON2(55, 8),
    OPOBJU(56, 15),
    IF_BUTTON8(57, 8),
    IF_BUTTOND(58, 16),
    OPNPC2(59, 3),
    OPPLAYERT(60, 11),
    IGNORELIST_DEL(61, -1),
    IF_BUTTON1(62, 8),
    OPNPC5(63, 3),
    IF1_BUTTON4(64, 8),
    OPLOC5(65, 7),
    UNUSED_3(66, -1),
    OPLOC3(67, 7),
    IF_BUTTONT(68, 16),
    BUG_REPORT(69, -2),
    OPNPC3(70, 3),
    REFLECTION_CHECK_REPLY(71, -1),
    TELEPORT(72, 9),
    UNUSED_4(73, 2),
    RESUME_P_COUNTDIALOG(74, 4),
    IF_BUTTON6(75, 8),
    OPHELD5(76, 8),
    UNUSED_5(77, -1),
    IF_BUTTON7(78, 8),
    IDLE_LOGOUT(79, 0),
    OPLOC6(80, 2),
    IF1_BUTTON2(81, 8),
    OPOBJT(82, 15),
    OPHELDD(83, 9),
    MAP_BUILD_COMPLETE(84, 0),
    IF_BUTTON5(85, 8),
    RESUME_P_OBJDIALOG(86, 2),
    OPOBJE(87, 6),
    OPPLAYER4(88, 3),
    OPHELD3(89, 8),
    IF1_BUTTON1(90, 8),
    OPHELD2(91, 8),
    OPPLAYER2(92, 3),
    EVENT_KEYBOARD(93, -2),
    RESUME_PAUSEBUTTON(94, 6),
    OPLOCU(95, 15),
    OPPLAYER3(96, 3),
    MOVE_GAMECLICK(97, -1),
    EXIT_FREECAM(98, 0),
    field3164(99, 22),
    CLOSE_MODAL(100, 0),
    OPOBJ2(101, 7),
    IF_BUTTON9(102, 8),
    OPHELD1(103, 8),
    OPOBJ1(104, 7),
    OPLOCT(105, 15),
    SEND_SNAPSHOT(106, -1),
    OPNPCT(107, 11),
    
    /*CLANCHANNEL_KICKUSER(0, -1),
    EVENT_KEYBOARD(1, -2),
    OPLOC6(2, 2),
    UNUSED1(3, -1),
    EVENT_MOUSE_MOVE(4, -1),
    LOGIN_STATISTICS(5, -1),
    UNKNOWN1(6, 22),
    EVENT_APPLET_FOCUS(7, 1),
    IF_BUTTON4(8, 8),
    MOVE_GAMECLICK(9, -1),
    AFFINEDCLANSETTINGS_ADDBANNED_FROMCHANNEL(10, -1),
    IGNORELIST_ADD(11, -1),
    IF_BUTTON5(12, 8),
    IF1_BUTTON4(13, 8),
    OPPLAYER4(14, 3),
    IF_BUTTONT(15, 16),
    RESUME_P_NAMEDIALOG(16, -1),
    IF_BUTTON2(17, 8),
    TELEPORT(18, 9),
    UNUSED_2(19, 7),
    IF_BUTTON6(20, 8),
    UNUSED_3(21, 2),
    CLAN_KICKUSER(22, -1),
    OPHELD1(23, 8),
    RESUME_P_COUNTDIALOG(24, 4),
    MESSAGE_PUBLIC(25, -1),
    BUG_REPORT(26, -2),
    OPPLAYER2(27, 3),
    OPPLAYER6(28, 3),
    OPNPC4(29, 3),
    OPOBJ5(30, 7),
    OPHELD5(31, 8),
    OPPLAYER7(32, 3),
    OPLOCU(33, 15),
    NO_TIMEOUT(34, 0),
    OPPLAYER8(35, 3),
    OPHELD3(36, 8),
    OPNPCU(37, 11),
    OPLOC1(38, 7),
    OPNPCT(39, 11),
    CLIENT_CHEAT(40, -1),
    IF_BUTTON8(41, 8),
    EXIT_FREECAM(42, 0),
    FRIENDLIST_DEL(43, -1),
    RESUME_PAUSEBUTTON(44, 6),
    AFFINEDCLANSETTINGS_SETMUTED_FROMCHANNEL(45, -1),
    OPLOC5(46, 7),
    OPPLAYER5(47, 3),
    IF1_BUTTON3(48, 8),
    OPOBJ3(49, 7),
    FRIENDLIST_ADD(50, -1),
    OPLOC4(51, 7),
    OPPLAYERU(52, 11),
    CLOSE_MODAL(53, 0),
    IF_BUTTON7(54, 8),
    DETECT_MODIFIED_CLIENT(55, 4),
    OPNPC1(56, 3),
    UNUSED5(57, -1),
    IF_BUTTON1(58, 8),
    IF1_BUTTON1(59, 8),
    IF1_BUTTON2(60, 8),
    OPOBJT(61, 15),
    OPOBJU(62, 15),
    IF_BUTTON3(63, 8),
    UPDATE_APPEARANCE(64, 13),
    OPOBJ1(65, 7),
    BUTTON_CLICK(66, 4),
    IF_BUTTON9(67, 8),
    CLAN_JOINCHAT_LEAVECHAT(68, -1),
    OPNPC6(69, 2),
    OPOBJ4(70, 7),
    OPHELDT(71, 14),
    RESUME_P_OBJDIALOG(72, 2),
    OPOBJ2(73, 7),
    OPPLAYERT(74, 11),
    MOVE_MINIMAPCLICK(75, -1),
    OPLOCT(76, 15),
    IF1_BUTTON5(77, 8),
    FRIEND_SETRANK(78, -1),
    IDLE_LOGOUT(79, 0),
    OPHELD4(80, 8),
    IF_BUTTOND(81, 16),
    OPNPC2(82, 3),
    MAP_BUILD_COMPLETE(83, 0),
    MESSAGE_PRIVATE(84, -2),
    RESUME_P_STRINGDIALOG(85, -1),
    IGNORELIST_DEL(86, -1),
    EVENT_CAMERA_POSITION(87, 4),
    OPNPC3(88, 3),
    UNUSED6(89, -1),
    OPLOC3(90, 7),
    SEND_SNAPSHOT(91, -1),
    WINDOW_STATUS(92, 5),
    CLICKWORLDMAP(93, 4),
    OPHELD_D(94, 9),
    IF_BUTTON10(95, 8),
    OPOBJ6(96, 2),
    OPPLAYER3(97, 3),
    OPNPC5(98, 3),
    REFLECTION_CHECK_REPLY(99, -1),
    OPPLAYER1(100, 3),
    OPLOC2(101, 7),
    OPHELDU(102, 16),
    EVENT_MOUSE_CLICK(103, 6),
    SET_CHATFILTERSETTINGS(104, 3),
    SEND_PING_REPLY(105, 10),
    OPHELD2(106, 8),
    UNUSED4(107, 6)*/
    ;

    init {
        if (opcode != ordinal)
            throw IllegalArgumentException("($name) opcode ($opcode) must match ordinal ($ordinal)")
    }

    override val headerSize = when (size) {
        -1 -> 1
        -2 -> 2
        in 0..NetworkConstants.MAX_CLIENT_BUFFER_SIZE -> 0
        else -> throw IllegalArgumentException("($name, opcode $opcode) invalid size $size")
    }

    companion object {
        val values = values()
    }

}