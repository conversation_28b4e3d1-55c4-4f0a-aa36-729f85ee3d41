/**
 * Copyright (c) <PERSON>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
package com.zenyte.game.util;

/**
 * <AUTHOR>
 * @since Feb 11, 2016
 */
public class BitUtils {

    private static final int[] MASKS = new int[32];

    static {
        for (int i = 0; i < 32; i++) {
            MASKS[i] = (1 << i) - 1;
        }
    }

    public static final int getMask(final int i) {
        return MASKS[i];
    }

    public static final int nextPowerOfTwo(int n) {
        if (n == 0) {
            return 1;
        }

        // If n is already a power of two, return it:
        if ((n & (n - 1)) == 0) {
            return n;
        }

        n |= n >> 1;
        n |= n >> 2;
        n |= n >> 4;
        n |= n >> 8;
        n |= n >> 16;

        return n + 1;
    }

    public static final int getAmountOfBitsFlagged(final long value) {
        int bits = 0;
        for (int i = 0; i < 64; i++) {
            if ((value >> i & 0x1) != 0) {
                bits++;
            }
        }
        return bits;
    }

}
