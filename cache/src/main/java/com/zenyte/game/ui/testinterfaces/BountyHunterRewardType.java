package com.zenyte.game.ui.testinterfaces;

import com.zenyte.game.item.ItemId;
import it.unimi.dsi.fastutil.ints.Int2ObjectMap;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;

import java.util.Optional;

public enum BountyHunterRewardType {
    DRAGON_LONGSWORD(1305, 300000),
	DRAGON_BATTLEAXE(1377, 400000),
	DRAGON_MACE(1434, 150000),
	DRAGON_HALBERD(3204, 900000),
	HELM_OF_NEITIZNOT(10828, 150000),
	BERSERKER_HELM(3751, 234000),
	WARRIOR_HELM(3753, 234000),
	ARCHER_HELM(3749, 234000),
	FARSEER_HELM(3755, 234000),
	GREEN_DARK_BOW_PAINT(12759, 500000),
	YELLOW_DARK_BOW_PAINT(12761, 500000),
	WHITE_DARK_BOW_PAINT(12763, 500000),
	BLUE_DARK_BOW_PAINT(12757, 500000),
	PADDEWWA_TELEPORT(12781, 10000),
	SENNTISTEN_TELEPORT(12782, 10000),
	ANNAKARL_TELEPORT(12775, 10000),
	CARRALLANGAR_TELEPORT(12776, 10000),
	DAREEYAK_TELEPORT(12777, 10000),
	GHORROCK_TELEPORT(12778, 10000),
	KHARYRLL_TELEPORT(12779, 10000),
	LASSAR_TELEPORT(12780, 10000),
	VOLCANIC_WHIP_MIX(12771, 500000),
	FROZEN_WHIP_MIX(12769, 500000),
	STEAM_STAFF_UPGRADE_KIT(12798, 250000),
	LAVA_STAFF_UPGRADE_KIT(21202, 250000),
	DRAGON_PICKAXE_UPGRADE_KIT(12800, 300000),
	WARD_UPGRADE_KIT(12802, 350000),
	RING_OF_WEALTH_SCROLL(12783, 50000),
	MAGIC_SHORTBOW_SCROLL(12786, 100000),
	SARADOMINS_TEAR(12804, 25000000),
	RUNE_POUCH(12791, 1200000),
	LOOTING_BAG(11941, 10000),
	BOLT_RACK(4740, 360),
	RUNE_ARROW(892, 600),
	ADAMANT_ARROW(890, 240),
	GRANITE_CLAMP(12849, 250000),
	HUNTERS_HONOUR(12855, 2500000),
	REVENANT_CAVE_TELEPORT(21802, 75000),
	BURNING_AMULET(21166, 10000),
	ROYAL_SEED_POD(19564, 30000),
	SUPER_ATTACK(2436, 7500),
	SUPER_STRENGTH(2440, 8500),
	SUPER_DEFENCE(2442, 6000),
	RANGING_POTION(2444, 7500),
	MAGIC_POTION(3040, 5000),
	SUPER_COMBAT_POTION(12695, 25000),
	SUPER_RESTORE(3024, 12500),
	SANFEW_SERUM(10925, 15000),
	PRAYER_POTION(2434, 10000),
	SARADOMIN_BREW(6685, 13500),
	STAMINA_POTION(12625, 17500),
	ANTI_VENOM_PLUS(12913, 15000),
	FIGHTER_TORSO(10551, 600000),
	MASTER_WAND(6914, 35000000),
	DRAGON_CROSSBOW(21902, 30000000),
	DRAGON_THROWNAXE(20849, 5500),
	DRAGON_KNIFE(22804, 3000),
	DRAGON_BOLT(21905, 5000),
	ANCIENT_MACE(11061, 550000),
	DECORATIVE_RANGE_TOP(11899, 150000),
	DECORATIVE_RANGE_BOTTOM(11900, 150000),
	DECORATIVE_MAGE_TOP(11896, 150000),
	DECORATIVE_MAGE_BOTTOM(11897, 150000),
	SARADOMIN_HALO(12637, 300000),
	ZAMORAK_HALO(12638, 300000),
	GUTHIX_HALO(12639, 300000),
	TOME_OF_FIRE(ItemId.TOME_OF_FIRE_EMPTY, 15000000),
	CRYSTAL_SEED(4207, 540000);

    private static final Int2ObjectMap<BountyHunterRewardType> map = new Int2ObjectOpenHashMap<>();

    static {
        for (final BountyHunterRewardType value : values()) {
            map.put(value.id, value);
        }
    }

    public static Optional<BountyHunterRewardType> get(final int id) {
        return Optional.ofNullable(map.get(id));
    }

    private final int id;
    private final int cost;

    BountyHunterRewardType(int id, int cost) {
        this.id = id;
        this.cost = cost;
    }

    public int getId() {
        return id;
    }

    public int getCost() {
        return cost;
    }
}
