package com.near_reality.cache.interfaces.teleports.categories

import com.near_reality.cache.interfaces.teleports.builder.TeleportsBuilder

internal fun TeleportsBuilder.misc() = "Misc Teleports"(10009) {
    "Barbarian Outpost"(-10551, 2548, 3569, 0, "")
    "Braindeath Island"(-6714, 2149, 5097, 0, "")
    "Crandor"(-11279, 2834, 3259, 0, "")
    "Gambling"(-30050, 3366, 6939, 2, "")
    "Isafdar"(-23991, 2223, 3211, 0, "")
    "Lunar Isle"(-9075, 2105, 3914, 0, "")
    "Tyras Camp"(-9629, 2186, 3147, 0, "")
    "Waterbirth Island"(-3758, 2528, 3740, 0, "")
    "Watson"(-19835, 1645, 3577, 0, "")
}