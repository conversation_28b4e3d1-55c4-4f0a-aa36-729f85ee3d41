package com.near_reality.cache.interfaces.teleports.categories

import com.near_reality.cache.interfaces.teleports.builder.TeleportsBuilder

internal fun TeleportsBuilder.dungeons() = "Dungeons Teleports"(10006) {
    "Ancient Cavern"(-2359, 1764, 5365, 1, "")
    "Asgarnian Ice Dungeon"(-9467, 3009, 9549, 0, "")
    "Ape Atoll Dungeon"(-4031, 2766, 9103, 0, "")
    "Brimhaven Dungeon"(-2353, 2708, 9564, 0, "")
    "Brine Rat Cavern"(-11037, 2693, 10123, 0, "")
    "Catacombs of Kourend"(-19685, 1639, 3673, 0, "")
    "Chasm Of Fire"(-13501, 1435, 10079, 3, "")
    "Corsair Cove Dungeon"(-21838, 1933, 9009, 1, "")
    "Crabclaw Caves"(-7537, 1647, 9847, 0, "")
    "Demonic Gorillas"(-19529, 2126, 5646, 0, "")
    "<PERSON><PERSON><PERSON>"(-8880, 2715, 5240, 0, "")
    "Edgeville Dungeon"(-532, 3132, 9912, 0, "")
    "Evil Chicken Lair"(-2138, 2461, 4356, 0, "")
    "<PERSON>em<PERSON><PERSON> <PERSON> <PERSON><PERSON>on"(-11902, 2807, 10002, 0, "")
    "<PERSON><PERSON> <PERSON><PERSON>on"(-23522, 1830, 9973, 0, "")
    "<PERSON><PERSON><PERSON>and's <PERSON>"(-24271, 2465, 4010, 0, "")
    "<PERSON>l<PERSON>te <PERSON>"(-13489, 3304, 9497, 0, "")
    "<PERSON>l<PERSON>te <PERSON>ve"(-7981, 3485, 9510, 2, "")
    "Karamja Underground"(-1963, 2861, 9571, 0, "")
    "Karuulm Slayer Dungeon"(-23037, 1311, 10205, 0, "")
    "Kraken Cove"(-11908, 2280, 3609, 0, "")
    "Kruk's Dungeon"(-19525, 2316, 9159, 1, "")
    "Lighthouse"(-3842, 2509, 3634, 0, "")
    "Lithkren Vault"(-21902, 1568, 5063, 0, "")
    "Lizardman Cavern"(-13576, 1305, 9973, 0, "")
    "Lizardman Canyon"(-13576, 1507, 3678, 0, "")
    "Lizardman Temple"(-13576, 1314, 10077, 0, "")
    "Lumbridge Swamp Dungeon"(-594, 3170, 3172, 0, "")
    "Mos Le'Harmless Dungeon"(-8921, 3747, 9374, 0, "")
    "Mourner Tunnels"(-11235, 2032, 4636, 0, "")
    "Meiyerditch Laboratories"(-13495, 3608, 9739, 0, "")
    "Smoke Dungeon"(-12002, 3207, 9378, 0, "")
    "Observatory Dungeon"(-600, 2335, 9350, 0, "")
    "Slayer Tower"(-11864, 3428, 3530, 0, "")
    "Stronghold of Security"(-9005, 1859, 5244, 0, "")
    "Stronghold Slayer Cave"(-13495, 2427, 9824, 0, "")
    "Taverley Dungeon"(-1751, 2884, 9799, 0, "")
    "Varrock Sewers"(-526, 3237, 9862, 0, "")
    "Waterfall Dungeon"(-1333, 2575, 9861, 0, "")
    "Wyvern Cave"(-6812, 3604, 10230, 0, "")
}
