package com.near_reality.cache_tool.packing.custom

import com.zenyte.game.item.ItemId
import mgi.types.config.items.ItemDefinitions

object NearRealityBarrowsItemDefinitions {

    private val barrowsItemIds = listOf(
        ItemId.DHA<PERSON>KS_HELM, ItemId.D<PERSON><PERSON><PERSON>_PLATEBODY, ItemId.<PERSON><PERSON><PERSON><PERSON>_PLATELEGS, ItemId.D<PERSON><PERSON>KS_GREATAXE,
        ItemId.DHAROKS_HELM_100, ItemId.DHAROKS_PLATEBODY_100, ItemId.DHAROKS_PLATELEGS_100, ItemId.DHAROKS_GREATAXE_100,
        ItemId.DHAROKS_HELM_75, ItemId.<PERSON><PERSON><PERSON><PERSON>_PLATEBODY_75, ItemId.DHAROKS_PLATELEGS_75, ItemId.DHAROKS_GREATAXE_75,
        ItemId.DHAROKS_HELM_50, ItemId.<PERSON><PERSON><PERSON>KS_PLATEBODY_50, ItemId.<PERSON><PERSON><PERSON><PERSON>_PLATELEGS_50, ItemId.DHAROKS_GREATAXE_50,
        ItemId.DHA<PERSON><PERSON>_HELM_25, ItemId.<PERSON><PERSON><PERSON><PERSON>_PLA<PERSON>BODY_25, ItemId.DHAROKS_PLATELEGS_25, ItemId.DHAROKS_GREATAXE_25,
        ItemId.AHRIMS_HOOD, ItemId.AHRIMS_ROBETOP, ItemId.AHRIMS_ROBESKIRT, ItemId.AHRIMS_STAFF,
        ItemId.AHRIMS_HOOD_100, ItemId.AHRIMS_ROBETOP_100, ItemId.AHRIMS_ROBESKIRT_100, ItemId.AHRIMS_STAFF_100,
        ItemId.AHRIMS_HOOD_75, ItemId.AHRIMS_ROBETOP_75, ItemId.AHRIMS_ROBESKIRT_75, ItemId.AHRIMS_STAFF_75,
        ItemId.AHRIMS_HOOD_50, ItemId.AHRIMS_ROBETOP_50, ItemId.AHRIMS_ROBESKIRT_50, ItemId.AHRIMS_STAFF_50,
        ItemId.AHRIMS_HOOD_25, ItemId.AHRIMS_ROBETOP_25, ItemId.AHRIMS_ROBESKIRT_25, ItemId.AHRIMS_STAFF_25,
        ItemId.KARILS_COIF, ItemId.KARILS_LEATHERTOP, ItemId.KARILS_LEATHERSKIRT, ItemId.KARILS_CROSSBOW,
        ItemId.KARILS_COIF_100, ItemId.KARILS_LEATHERTOP_100, ItemId.KARILS_LEATHERSKIRT_100, ItemId.KARILS_CROSSBOW_100,
        ItemId.KARILS_COIF_75, ItemId.KARILS_LEATHERTOP_75, ItemId.KARILS_LEATHERSKIRT_75, ItemId.KARILS_CROSSBOW_75,
        ItemId.KARILS_COIF_50, ItemId.KARILS_LEATHERTOP_50, ItemId.KARILS_LEATHERSKIRT_50, ItemId.KARILS_CROSSBOW_50,
        ItemId.KARILS_COIF_25, ItemId.KARILS_LEATHERTOP_25, ItemId.KARILS_LEATHERSKIRT_25, ItemId.KARILS_CROSSBOW_25,
        ItemId.GUTHANS_HELM, ItemId.GUTHANS_PLATEBODY, ItemId.GUTHANS_CHAINSKIRT, ItemId.GUTHANS_WARSPEAR,
        ItemId.GUTHANS_HELM_100, ItemId.GUTHANS_PLATEBODY_100, ItemId.GUTHANS_CHAINSKIRT_100, ItemId.GUTHANS_WARSPEAR_100,
        ItemId.GUTHANS_HELM_75, ItemId.GUTHANS_PLATEBODY_75, ItemId.GUTHANS_CHAINSKIRT_75, ItemId.GUTHANS_WARSPEAR_75,
        ItemId.GUTHANS_HELM_50, ItemId.GUTHANS_PLATEBODY_50, ItemId.GUTHANS_CHAINSKIRT_50, ItemId.GUTHANS_WARSPEAR_50,
        ItemId.GUTHANS_HELM_25, ItemId.GUTHANS_PLATEBODY_25, ItemId.GUTHANS_CHAINSKIRT_25, ItemId.GUTHANS_WARSPEAR_25,
        ItemId.TORAGS_HELM, ItemId.TORAGS_PLATEBODY, ItemId.TORAGS_PLATELEGS, ItemId.TORAGS_HAMMERS,
        ItemId.TORAGS_HELM_100, ItemId.TORAGS_PLATEBODY_100, ItemId.TORAGS_PLATELEGS_100, ItemId.TORAGS_HAMMERS_100,
        ItemId.TORAGS_HELM_75, ItemId.TORAGS_PLATEBODY_75, ItemId.TORAGS_PLATELEGS_75, ItemId.TORAGS_HAMMERS_75,
        ItemId.TORAGS_HELM_50, ItemId.TORAGS_PLATEBODY_50, ItemId.TORAGS_PLATELEGS_50, ItemId.TORAGS_HAMMERS_50,
        ItemId.TORAGS_HELM_25, ItemId.TORAGS_PLATEBODY_25, ItemId.TORAGS_PLATELEGS_25, ItemId.TORAGS_HAMMERS_25,
        ItemId.VERACS_HELM, ItemId.VERACS_BRASSARD, ItemId.VERACS_PLATESKIRT, ItemId.VERACS_FLAIL,
        ItemId.VERACS_HELM_100, ItemId.VERACS_BRASSARD_100, ItemId.VERACS_PLATESKIRT_100, ItemId.VERACS_FLAIL_100,
        ItemId.VERACS_HELM_75, ItemId.VERACS_BRASSARD_75, ItemId.VERACS_PLATESKIRT_75, ItemId.VERACS_FLAIL_75,
        ItemId.VERACS_HELM_50, ItemId.VERACS_BRASSARD_50, ItemId.VERACS_PLATESKIRT_50, ItemId.VERACS_FLAIL_50,
        ItemId.VERACS_HELM_25, ItemId.VERACS_BRASSARD_25, ItemId.VERACS_PLATESKIRT_25, ItemId.VERACS_FLAIL_25
    )

    @JvmStatic
    fun removeCheckOption() {
        barrowsItemIds.forEach {
            val itemDef = ItemDefinitions.get(it)
            val invIndex = itemDef.inventoryOptions.withIndex().find { it.value == "Check" }?.index
            val paramKey = itemDef.parameters.int2ObjectEntrySet().find { it.value == "Check" }?.intKey
            if (invIndex != null) {
                itemDef.setOption(invIndex, "")
            }
            if (paramKey != null)
                itemDef.parameters.remove(paramKey)
            itemDef.pack()
        }
    }
}
