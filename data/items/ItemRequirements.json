[{"id": 35, "description": "Excalibur", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 78, "description": "Ice arrows", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 778, "description": "Steel gauntlets", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 802, "description": "Steel thrownaxe", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 803, "description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 804, "description": "Adamant thrownaxe", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 805, "description": "<PERSON><PERSON> thrownaxe", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 808, "description": "Steel dart", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 809, "description": "<PERSON><PERSON><PERSON> dart", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 810, "description": "Adamant dart", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 811, "description": "Rune dart", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 814, "description": "Steel dart(p)", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 815, "description": "<PERSON><PERSON><PERSON> dart(p)", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 816, "description": "Adamant dart(p)", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 817, "description": "<PERSON>e dart(p)", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 827, "description": "Steel javelin", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 828, "description": "Mithril javelin", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 829, "description": "Adamant javelin", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 830, "description": "Rune javelin", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 833, "description": "Steel javelin(p)", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 834, "description": "Mithril javelin(p)", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 835, "description": "Adamant javelin(p)", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 836, "description": "Rune javelin(p)", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 843, "description": "Oak shortbow", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 845, "description": "Oak longbow", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 847, "description": "Willow longbow", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 849, "description": "Willow shortbow", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 851, "description": "Maple longbow", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 853, "description": "Maple shortbow", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 855, "description": "Yew longbow", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 857, "description": "Yew shortbow", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 859, "description": "Magic longbow", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 861, "description": "Magic shortbow", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 865, "description": "Steel knife", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 866, "description": "Mith<PERSON> knife", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 867, "description": "Adamant knife", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 868, "description": "Rune knife", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 869, "description": "Black knife", "requirements": [{"skill": "Ranged", "level": 10}]}, {"id": 872, "description": "Steel knife(p)", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 873, "description": "<PERSON><PERSON><PERSON> knife(p)", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 874, "description": "Black knife(p)", "requirements": [{"skill": "Ranged", "level": 10}]}, {"id": 875, "description": "Adamant knife(p)", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 876, "description": "Rune knife(p)", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 880, "description": "Pearl bolts", "requirements": [{"skill": "Ranged", "level": 26}]}, {"id": 886, "description": "Steel arrow", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 887, "description": "Steel arrow(p)", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 888, "description": "Mithril arrow", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 889, "description": "<PERSON><PERSON><PERSON> arrow(p)", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 890, "description": "Adamant arrow", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 891, "description": "Adamant arrow(p)", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 892, "description": "Rune arrow", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 893, "description": "Rune arrow(p)", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 1065, "description": "Green d'hide vamb", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 1069, "description": "Steel platelegs", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 1071, "description": "Mith<PERSON> platelegs", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 1073, "description": "Adamant platelegs", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 1077, "description": "Black platelegs", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 1079, "description": "Rune platelegs", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 1083, "description": "Steel plateskirt", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 1085, "description": "<PERSON><PERSON><PERSON>", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 1089, "description": "Black plateskirt", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 1091, "description": "Adamant plateskirt", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 1093, "description": "Rune plateskirt", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 1097, "description": "Studded chaps", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 1099, "description": "Green d'hide chaps", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 1105, "description": "Steel chainbody", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 1107, "description": "Black chainbody", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 1109, "description": "Mithril chainbody", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 1111, "description": "Adamant chainbody", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 1113, "description": "Rune chainbody", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 1119, "description": "Steel platebody", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 1121, "description": "Mithril platebody", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 1123, "description": "Adamant platebody", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 1125, "description": "Black platebody", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 1127, "description": "Rune platebody", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 1131, "description": "Hardleather body", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 1133, "description": "Studded body", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Ranged", "level": 20}]}, {"id": 1135, "description": "Green d'hide body", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 40}]}, {"id": 1141, "description": "Steel med helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 1143, "description": "<PERSON><PERSON><PERSON> med helm", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 1145, "description": "Adamant med helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 1147, "description": "Rune med helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 1149, "description": "Dragon med helm", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 1151, "description": "Black med helm", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 1157, "description": "<PERSON> full helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 1159, "description": "<PERSON><PERSON><PERSON> full helm", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 1161, "description": "Adamant full helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 1163, "description": "<PERSON><PERSON> full helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 1165, "description": "<PERSON> full helm", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 1169, "description": "Coif", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 1177, "description": "Steel sq shield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 1179, "description": "Black sq shield", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 1181, "description": "Mithril sq shield", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 1183, "description": "Adamant sq shield", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 1185, "description": "Rune sq shield", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 1187, "description": "Dragon sq shield", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 1193, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 1195, "description": "Black kiteshield", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 1197, "description": "<PERSON><PERSON><PERSON>", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 1199, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 1201, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 1207, "description": "Steel dagger", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 1209, "description": "Mith<PERSON> dagger", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 1211, "description": "Adamant dagger", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 1213, "description": "Rune dagger", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 1215, "description": "Dragon dagger", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 1217, "description": "Black dagger", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 1223, "description": "Steel dagger(p)", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 1225, "description": "<PERSON><PERSON><PERSON> dagger(p)", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 1227, "description": "Adamant dagger(p)", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 1229, "description": "Rune dagger(p)", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 1231, "description": "Dragon dagger(p)", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 1233, "description": "Black dagger(p)", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 1241, "description": "Steel spear", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 1243, "description": "<PERSON><PERSON><PERSON> spear", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 1245, "description": "Adamant spear", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 1247, "description": "<PERSON>e spear", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 1249, "description": "Dragon spear", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 1255, "description": "Steel spear(p)", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 1257, "description": "<PERSON><PERSON><PERSON> spear(p)", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 1259, "description": "Adamant spear(p)", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 1261, "description": "<PERSON><PERSON> spear(p)", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 1263, "description": "Dragon spear(p)", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 1269, "description": "Steel pickaxe", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 1271, "description": "Adamant pickaxe", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 1273, "description": "Mith<PERSON> pickaxe", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 1275, "description": "Rune pickaxe", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 1281, "description": "Steel sword", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 1283, "description": "Black sword", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 1285, "description": "Mithril sword", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 1287, "description": "Adamant sword", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 1289, "description": "Rune sword", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 1295, "description": "Steel longsword", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 1297, "description": "Black longsword", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 1299, "description": "<PERSON><PERSON><PERSON> longsword", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 1301, "description": "Adamant longsword", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 1303, "description": "Rune longsword", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 1305, "description": "Dragon longsword", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 1311, "description": "Steel 2h sword", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 1313, "description": "Black 2h sword", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 1315, "description": "Mithril 2h sword", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 1317, "description": "Adamant 2h sword", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 1319, "description": "Rune 2h sword", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 1325, "description": "Steel scimitar", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 1327, "description": "Black scimitar", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 1329, "description": "<PERSON><PERSON><PERSON> sci<PERSON>ar", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 1331, "description": "Adamant scimitar", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 1333, "description": "<PERSON><PERSON> scimitar", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 1339, "description": "Steel warhammer", "requirements": [{"skill": "Strength", "level": 5}]}, {"id": 1341, "description": "Black warhammer", "requirements": [{"skill": "Strength", "level": 10}]}, {"id": 1343, "description": "Mithril warhammer", "requirements": [{"skill": "Strength", "level": 20}]}, {"id": 1345, "description": "Adamant warhammer", "requirements": [{"skill": "Strength", "level": 30}]}, {"id": 1347, "description": "Rune warhammer", "requirements": [{"skill": "Strength", "level": 40}]}, {"id": 1353, "description": "Steel axe", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 1355, "description": "<PERSON><PERSON><PERSON> axe", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 1357, "description": "Adamant axe", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 1359, "description": "Rune axe", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 1361, "description": "Black axe", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 1365, "description": "Steel battleaxe", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 1367, "description": "Black battleaxe", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 1369, "description": "<PERSON><PERSON><PERSON> battleaxe", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 1371, "description": "Adamant battleaxe", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 1373, "description": "Rune battleaxe", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 1377, "description": "Dragon battleaxe", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 1391, "description": "Battlestaff", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 1393, "description": "Fire battlestaff", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 1395, "description": "Water battlestaff", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 1397, "description": "Air battlestaff", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 1399, "description": "Earth battlestaff", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 1401, "description": "Mystic fire staff", "requirements": [{"skill": "Attack", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 1403, "description": "Mystic water staff", "requirements": [{"skill": "Attack", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 1405, "description": "Mystic air staff", "requirements": [{"skill": "Attack", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 1407, "description": "Mystic earth staff", "requirements": [{"skill": "Attack", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 1409, "description": "<PERSON><PERSON>'s staff", "requirements": [{"skill": "Attack", "level": 50}, {"skill": "Magic", "level": 50}]}, {"id": 1424, "description": "Steel mace", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 1426, "description": "Black mace", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 1428, "description": "Mithril mace", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 1430, "description": "Adamant mace", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 1432, "description": "Rune mace", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 1434, "description": "Dragon mace", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 2412, "description": "Saradomin cape", "requirements": [{"skill": "Magic", "level": 60}]}, {"id": 2413, "description": "Guthix cape", "requirements": [{"skill": "Magic", "level": 60}]}, {"id": 2414, "description": "Zamorak cape", "requirements": [{"skill": "Magic", "level": 60}]}, {"id": 2415, "description": "Saradomin staff", "requirements": [{"skill": "Magic", "level": 60}]}, {"id": 2416, "description": "Guthix staff", "requirements": [{"skill": "Magic", "level": 60}]}, {"id": 2417, "description": "Zamorak staff", "requirements": [{"skill": "Magic", "level": 60}]}, {"id": 2487, "description": "Blue d'hide vamb", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 2489, "description": "Red d'hide vamb", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 2491, "description": "Black d'hide vamb", "requirements": [{"skill": "Ranged", "level": 70}]}, {"id": 2493, "description": "Blue d'hide chaps", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 2495, "description": "Red d'hide chaps", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 2497, "description": "Black d'hide chaps", "requirements": [{"skill": "Ranged", "level": 70}]}, {"id": 2499, "description": "Blue d'hide body", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 50}]}, {"id": 2501, "description": "Red d'hide body", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 60}]}, {"id": 2503, "description": "Black d'hide body", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 70}]}, {"id": 2513, "description": "Dragon chainbody", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 2534, "description": "Steel fire arrows", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 2536, "description": "Mithril fire arrows", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 2538, "description": "Adamant fire arrows", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 2540, "description": "Rune fire arrows", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 2577, "description": "Ranger boots", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 2579, "description": "Wizard boots", "requirements": [{"skill": "Magic", "level": 20}]}, {"id": 2581, "description": "Robin hood hat", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 2583, "description": "Black platebody (t)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 2585, "description": "Black platelegs (t)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 2587, "description": "<PERSON> full helm (t)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 2589, "description": "<PERSON> kiteshield (t)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 2591, "description": "Black platebody (g)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 2593, "description": "Black platelegs (g)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 2595, "description": "Black full helm (g)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 2597, "description": "Black kiteshield (g)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 2599, "description": "Adamant platebody (t)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 2601, "description": "Adamant platelegs (t)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 2603, "description": "<PERSON><PERSON> (t)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 2605, "description": "Adamant full helm (t)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 2607, "description": "Adamant platebody (g)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 2609, "description": "Adamant platelegs (g)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 2611, "description": "Adam<PERSON> kit<PERSON> (g)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 2613, "description": "Adamant full helm (g)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 2615, "description": "Rune platebody (g)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2617, "description": "Rune platelegs (g)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2619, "description": "Rune full helm (g)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2621, "description": "<PERSON><PERSON> (g)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2623, "description": "Rune platebody (t)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2625, "description": "Rune platelegs (t)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2627, "description": "<PERSON>e full helm (t)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2629, "description": "<PERSON><PERSON> (t)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2653, "description": "Zamorak platebody", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2655, "description": "Zamorak platelegs", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2657, "description": "<PERSON><PERSON><PERSON> full helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2659, "description": "<PERSON><PERSON><PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2661, "description": "Saradomin platebody", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2663, "description": "Saradomin platelegs", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2665, "description": "<PERSON><PERSON><PERSON> full helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2667, "description": "<PERSON><PERSON><PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2669, "description": "Guthix platebody", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2671, "description": "Guthix platelegs", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2673, "description": "Guthix full helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2675, "description": "Guthix kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 2866, "description": "Ogre arrow", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 2883, "description": "Ogre bow", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 3053, "description": "<PERSON><PERSON>ff", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 3054, "description": "Mystic lava staff", "requirements": [{"skill": "Attack", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 3093, "description": "Black dart", "requirements": [{"skill": "Ranged", "level": 10}]}, {"id": 3094, "description": "Black dart(p)", "requirements": [{"skill": "Ranged", "level": 10}]}, {"id": 3097, "description": "Steel claws", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 3098, "description": "Black claws", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 3099, "description": "Mithril claws", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 3100, "description": "Adamant claws", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 3101, "description": "Rune claws", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 3122, "description": "Granite shield", "requirements": [{"skill": "Defence", "level": 50}, {"skill": "Strength", "level": 50}]}, {"id": 3140, "description": "Dragon chainbody", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 3172, "description": "Steel spear(kp)", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 3173, "description": "Mithril spear(kp)", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 3174, "description": "Adamant spear(kp)", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 3175, "description": "Rune spear(kp)", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 3176, "description": "Dragon spear(kp)", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 3194, "description": "Steel halberd", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 3196, "description": "Black halberd", "requirements": [{"skill": "Attack", "level": 5}, {"skill": "Strength", "level": 10}]}, {"id": 3198, "description": "<PERSON><PERSON><PERSON> halberd", "requirements": [{"skill": "Attack", "level": 10}, {"skill": "Strength", "level": 20}]}, {"id": 3200, "description": "Adamant halberd", "requirements": [{"skill": "Attack", "level": 15}, {"skill": "Strength", "level": 30}]}, {"id": 3202, "description": "<PERSON><PERSON> halberd", "requirements": [{"skill": "Attack", "level": 20}, {"skill": "Strength", "level": 40}]}, {"id": 3204, "description": "Dragon halberd", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Strength", "level": 60}]}, {"id": 3385, "description": "<PERSON><PERSON> helm", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 3387, "description": "Splitbark body", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 3389, "description": "Splitbark legs", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 3391, "description": "Splitbark gauntlets", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 3393, "description": "Splitbark boots", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 3472, "description": "Black plateskirt (t)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 3473, "description": "Black plateskirt (g)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 3474, "description": "Adamant plateskirt (t)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 3475, "description": "Adamant plateskirt (g)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 3476, "description": "Rune plateskirt (g)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 3477, "description": "Rune plateskirt (t)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 3478, "description": "Zamorak plateskirt", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 3479, "description": "Saradomin plateskirt", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 3480, "description": "Guthix plateskirt", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 3481, "description": "Gilded platebody", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 3483, "description": "Gilded platelegs", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 3485, "description": "Gilded plateskirt", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 3486, "description": "Gilded full helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 3488, "description": "Gilded kiteshield", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 3748, "description": "Fremennik helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 3749, "description": "<PERSON> helm", "requirements": [{"skill": "Defence", "level": 45}]}, {"id": 3751, "description": "<PERSON><PERSON><PERSON><PERSON> helm", "requirements": [{"skill": "Defence", "level": 45}]}, {"id": 3753, "description": "Warrior helm", "requirements": [{"skill": "Defence", "level": 45}]}, {"id": 3755, "description": "<PERSON><PERSON><PERSON> helm", "requirements": [{"skill": "Defence", "level": 45}]}, {"id": 3757, "description": "Fremennik blade", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 3758, "description": "Fremennik shield", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 4068, "description": "Decorative sword", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 4069, "description": "Decorative armour", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 4070, "description": "Decorative armour", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 4071, "description": "Decorative helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 4072, "description": "Decorative shield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 4087, "description": "Dragon platelegs", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 4089, "description": "Mystic hat", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 4091, "description": "Mystic robe top", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 4093, "description": "Mystic robe bottom", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 4095, "description": "Mystic gloves", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 4097, "description": "Mystic boots", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 4099, "description": "Mystic hat (dark)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 4101, "description": "Mystic robe top (dark)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 4103, "description": "Mystic robe bottom (dark)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 4105, "description": "Mystic gloves (dark)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 4107, "description": "Mystic boots (dark)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 4109, "description": "Mystic hat (light)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 4111, "description": "Mystic robe top (light)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 4113, "description": "Mystic robe bottom (light)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 4115, "description": "Mystic gloves (light)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 4117, "description": "Mystic boots (light)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 4123, "description": "Steel boots", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 4125, "description": "Black boots", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 4127, "description": "Mithril boots", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 4129, "description": "Adamant boots", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 4131, "description": "Rune boots", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 4151, "description": "Abyssal whip", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 4153, "description": "Granite maul", "requirements": [{"skill": "Attack", "level": 50}, {"skill": "Strength", "level": 50}]}, {"id": 4156, "description": "Mirror shield", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Slayer", "level": 25}]}, {"id": 4158, "description": "Leaf-bladed spear", "requirements": [{"skill": "Attack", "level": 55}, {"skill": "Slayer", "level": 50}]}, {"id": 4159, "description": "Leaf-bladed spear", "requirements": [{"skill": "Attack", "level": 55}, {"skill": "Slayer", "level": 50}]}, {"id": 4160, "description": "Broad arrows", "requirements": [{"skill": "Slayer", "level": 55}, {"skill": "Ranged", "level": 50}]}, {"id": 4164, "description": "Facemask", "requirements": [{"skill": "Slayer", "level": 10}]}, {"id": 4166, "description": "Earmuffs", "requirements": [{"skill": "Slayer", "level": 15}]}, {"id": 4168, "description": "Nose peg", "requirements": [{"skill": "Slayer", "level": 60}]}, {"id": 4170, "description": "Slayer's staff", "requirements": [{"skill": "Slayer", "level": 50}, {"skill": "Magic", "level": 55}]}, {"id": 4178, "description": "Abyssal whip", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 4180, "description": "Dragon platelegs", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 4212, "description": "New crystal bow", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Ranged", "level": 70}]}, {"id": 4214, "description": "Crystal bow full", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Ranged", "level": 70}]}, {"id": 4215, "description": "<PERSON> bow 9/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Ranged", "level": 70}]}, {"id": 4216, "description": "Crystal bow 8/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Ranged", "level": 70}]}, {"id": 4217, "description": "<PERSON> bow 7/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Ranged", "level": 70}]}, {"id": 4218, "description": "<PERSON> bow 6/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Ranged", "level": 70}]}, {"id": 4219, "description": "Crystal bow 5/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Ranged", "level": 70}]}, {"id": 4220, "description": "Crystal bow 4/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Ranged", "level": 70}]}, {"id": 4221, "description": "Crystal bow 3/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Ranged", "level": 70}]}, {"id": 4222, "description": "Crystal bow 2/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Ranged", "level": 70}]}, {"id": 4223, "description": "Crystal bow 1/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Ranged", "level": 70}]}, {"id": 4224, "description": "New crystal shield", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Defence", "level": 70}]}, {"id": 4225, "description": "Crystal shield full", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Defence", "level": 70}]}, {"id": 4226, "description": "Crystal shield 9/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Defence", "level": 70}]}, {"id": 4227, "description": "Crystal shield 8/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Defence", "level": 70}]}, {"id": 4228, "description": "Crystal shield 7/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Defence", "level": 70}]}, {"id": 4229, "description": "Crystal shield 6/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Defence", "level": 70}]}, {"id": 4230, "description": "Crystal shield 5/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Defence", "level": 70}]}, {"id": 4231, "description": "Crystal shield 4/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Defence", "level": 70}]}, {"id": 4232, "description": "Crystal shield 3/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Defence", "level": 70}]}, {"id": 4233, "description": "Crystal shield 2/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Defence", "level": 70}]}, {"id": 4234, "description": "Crystal shield 1/10", "requirements": [{"skill": "Agility", "level": 50}, {"skill": "Defence", "level": 70}]}, {"id": 4236, "description": "Signed oak bow", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 4503, "description": "Decorative sword", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 4504, "description": "Decorative armour", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 4505, "description": "Decorative armour", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 4506, "description": "Decorative helm", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 4507, "description": "Decorative shield", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 4508, "description": "Decorative sword", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 4509, "description": "Decorative armour", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 4510, "description": "Decorative armour", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 4511, "description": "Decorative helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 4512, "description": "Decorative shield", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 4551, "description": "Spiny helmet", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 4580, "description": "Black spear", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 4582, "description": "Black spear(p)", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 4584, "description": "Black spear(kp)", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 4585, "description": "Dragon plateskirt", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 4587, "description": "Dragon scimitar", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 4675, "description": "Ancient staff", "requirements": [{"skill": "Attack", "level": 50}, {"skill": "Magic", "level": 50}]}, {"id": 4708, "description": "<PERSON><PERSON>'s hood", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4710, "description": "<PERSON><PERSON>'s staff", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4712, "description": "<PERSON><PERSON>'s robetop", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4714, "description": "<PERSON><PERSON>'s robeskirt", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4716, "description": "<PERSON><PERSON><PERSON>'s helm", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4718, "description": "<PERSON><PERSON><PERSON>'s greataxe", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 70}]}, {"id": 4720, "description": "<PERSON><PERSON><PERSON>'s platebody", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4722, "description": "<PERSON><PERSON><PERSON>'s platelegs", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4724, "description": "<PERSON><PERSON><PERSON>'s helm", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4726, "description": "<PERSON><PERSON><PERSON>'s warspear", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 4728, "description": "<PERSON><PERSON><PERSON>'s platebody", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4730, "description": "<PERSON><PERSON><PERSON>'s chainskirt", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4732, "description": "<PERSON><PERSON>'s coif", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4734, "description": "<PERSON><PERSON>'s crossbow", "requirements": [{"skill": "Ranged", "level": 70}]}, {"id": 4736, "description": "<PERSON><PERSON>'s leathertop", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4738, "description": "<PERSON><PERSON>'s leatherskirt", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4740, "description": "Bolt rack", "requirements": [{"skill": "Ranged", "level": 70}]}, {"id": 4745, "description": "<PERSON><PERSON>'s helm", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4747, "description": "<PERSON><PERSON>'s hammers", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 70}]}, {"id": 4749, "description": "<PERSON><PERSON>'s platebody", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4751, "description": "Torag's platelegs", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4753, "description": "<PERSON><PERSON>'s helm", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4755, "description": "<PERSON><PERSON>'s flail", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 4757, "description": "Vera<PERSON>'s brassard", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4759, "description": "Verac's plateskirt", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4773, "description": "Bronze brutal", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 4778, "description": "Iron brutal", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 4783, "description": "Steel brutal", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 4788, "description": "Black brutal", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 4793, "description": "<PERSON><PERSON><PERSON> brutal", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 4798, "description": "Adamant brutal", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 4803, "description": "Rune brutal", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 4827, "description": "Comp ogre bow", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 4856, "description": "<PERSON><PERSON>'s hood 100", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4857, "description": "<PERSON><PERSON>'s hood 75", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4858, "description": "<PERSON><PERSON>'s hood 50", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4859, "description": "<PERSON><PERSON>'s hood 25", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4860, "description": "<PERSON><PERSON>'s hood 0", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4862, "description": "<PERSON><PERSON>'s staff 100", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4863, "description": "<PERSON><PERSON>'s staff 75", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4864, "description": "<PERSON><PERSON>'s staff 50", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4865, "description": "<PERSON><PERSON>'s staff 25", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4866, "description": "<PERSON><PERSON>'s staff 0", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4868, "description": "<PERSON><PERSON>'s robetop 100", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4869, "description": "<PERSON><PERSON>'s robetop 75", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4870, "description": "<PERSON><PERSON>'s robetop 50", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4871, "description": "<PERSON><PERSON>'s robetop 25", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4872, "description": "<PERSON><PERSON>'s robetop 0", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4874, "description": "<PERSON><PERSON>'s robeskirt 100", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4875, "description": "<PERSON><PERSON>'s robeskirt 75", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4876, "description": "<PERSON><PERSON>'s robeskirt 50", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4877, "description": "<PERSON><PERSON>'s robeskirt 25", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4878, "description": "<PERSON><PERSON>'s robeskirt 0", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 4880, "description": "<PERSON><PERSON><PERSON>'s helm 100", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4881, "description": "<PERSON><PERSON><PERSON>'s helm 75", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4882, "description": "<PERSON><PERSON><PERSON>'s helm 50", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4883, "description": "<PERSON><PERSON><PERSON>'s helm 25", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4884, "description": "<PERSON><PERSON><PERSON>'s helm 0", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4886, "description": "<PERSON><PERSON><PERSON>'s greataxe 100", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 70}]}, {"id": 4887, "description": "<PERSON><PERSON><PERSON>'s great<PERSON>e 75", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 70}]}, {"id": 4888, "description": "<PERSON><PERSON><PERSON>'s great<PERSON>e 50", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 70}]}, {"id": 4889, "description": "<PERSON><PERSON><PERSON>'s greataxe 25", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 70}]}, {"id": 4890, "description": "Dhar<PERSON>'s greataxe 0", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 70}]}, {"id": 4892, "description": "<PERSON><PERSON><PERSON>'s platebody 100", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4893, "description": "<PERSON><PERSON><PERSON>'s platebody 75", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4894, "description": "<PERSON><PERSON><PERSON>'s platebody 50", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4895, "description": "<PERSON><PERSON><PERSON>'s platebody 25", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4896, "description": "<PERSON><PERSON><PERSON>'s platebody 0", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4898, "description": "<PERSON><PERSON><PERSON>'s platelegs 100", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4899, "description": "<PERSON><PERSON><PERSON>'s platelegs 75", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4900, "description": "<PERSON><PERSON><PERSON>'s platelegs 50", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4901, "description": "<PERSON><PERSON><PERSON>'s platelegs 25", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4902, "description": "<PERSON><PERSON><PERSON>'s platelegs 0", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4904, "description": "<PERSON><PERSON><PERSON>'s helm 100", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4905, "description": "<PERSON><PERSON><PERSON>'s helm 75", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4906, "description": "<PERSON><PERSON><PERSON>'s helm 50", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4907, "description": "<PERSON><PERSON><PERSON>'s helm 25", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4908, "description": "<PERSON><PERSON><PERSON>'s helm 0", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4910, "description": "<PERSON><PERSON><PERSON>'s warspear 100", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 4911, "description": "<PERSON><PERSON><PERSON>'s warspear 75", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 4912, "description": "<PERSON><PERSON><PERSON>'s warspear 50", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 4913, "description": "<PERSON><PERSON><PERSON>'s warspear 25", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 4914, "description": "<PERSON><PERSON><PERSON>'s warspear 0", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 4916, "description": "<PERSON><PERSON><PERSON>'s platebody 100", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4917, "description": "<PERSON><PERSON><PERSON>'s platebody 75", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4918, "description": "<PERSON><PERSON><PERSON>'s platebody 50", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4919, "description": "<PERSON><PERSON><PERSON>'s platebody 25", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4920, "description": "<PERSON><PERSON><PERSON>'s platebody 0", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4922, "description": "<PERSON><PERSON><PERSON>'s chainskirt 100", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4923, "description": "<PERSON><PERSON><PERSON>'s chainskirt 75", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4924, "description": "<PERSON><PERSON><PERSON>'s chainskirt 50", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4925, "description": "<PERSON><PERSON><PERSON>'s chainskirt 25", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4926, "description": "<PERSON><PERSON><PERSON>'s chainskirt 0", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4928, "description": "<PERSON><PERSON>'s coif 100", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4929, "description": "<PERSON><PERSON>'s coif 75", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4930, "description": "<PERSON><PERSON>'s coif 50", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4931, "description": "<PERSON><PERSON>'s coif 25", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4932, "description": "<PERSON><PERSON>'s coif 0", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4934, "description": "<PERSON><PERSON>'s crossbow 100", "requirements": [{"skill": "Ranged", "level": 70}]}, {"id": 4935, "description": "<PERSON><PERSON>'s crossbow 75", "requirements": [{"skill": "Ranged", "level": 70}]}, {"id": 4936, "description": "<PERSON><PERSON>'s crossbow 50", "requirements": [{"skill": "Ranged", "level": 70}]}, {"id": 4937, "description": "<PERSON><PERSON>'s crossbow 25", "requirements": [{"skill": "Ranged", "level": 70}]}, {"id": 4938, "description": "<PERSON><PERSON>'s crossbow 0", "requirements": [{"skill": "Ranged", "level": 70}]}, {"id": 4940, "description": "<PERSON><PERSON>'s leathertop 100", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4941, "description": "<PERSON><PERSON>'s leathertop 75", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4942, "description": "<PERSON><PERSON>'s leathertop 50", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4943, "description": "<PERSON><PERSON>'s leathertop 25", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4944, "description": "<PERSON><PERSON>'s leathertop 0", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4946, "description": "<PERSON><PERSON>'s leatherskirt 100", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4947, "description": "<PERSON><PERSON>'s leatherskirt 75", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4948, "description": "<PERSON><PERSON>'s leatherskirt 50", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4949, "description": "<PERSON><PERSON>'s leatherskirt 25", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4950, "description": "<PERSON><PERSON>'s leatherskirt 0", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 4952, "description": "<PERSON><PERSON>'s helm 100", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4953, "description": "<PERSON><PERSON>'s helm 75", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4954, "description": "<PERSON><PERSON>'s helm 50", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4955, "description": "<PERSON><PERSON>'s helm 25", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4956, "description": "<PERSON><PERSON>'s helm 0", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4958, "description": "<PERSON><PERSON>'s hammers 100", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 70}]}, {"id": 4959, "description": "<PERSON><PERSON>'s hammers 75", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 70}]}, {"id": 4960, "description": "<PERSON><PERSON>'s hammers 50", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 70}]}, {"id": 4961, "description": "<PERSON><PERSON>'s hammers 25", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 70}]}, {"id": 4962, "description": "Torag's hammers 0", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 70}]}, {"id": 4964, "description": "Torag's platebody 100", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4965, "description": "Torag's platebody 75", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4966, "description": "Torag's platebody 50", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4967, "description": "Torag's platebody 25", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4968, "description": "Torag's platebody 0", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4970, "description": "Torag's platelegs 100", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4971, "description": "Torag's platelegs 75", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4972, "description": "Torag's platelegs 50", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4973, "description": "Torag's platelegs 25", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4974, "description": "Torag's platelegs 0", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4976, "description": "<PERSON><PERSON>'s helm 100", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4977, "description": "<PERSON><PERSON>'s helm 75", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4978, "description": "<PERSON><PERSON>'s helm 50", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4979, "description": "<PERSON><PERSON>'s helm 25", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4980, "description": "<PERSON><PERSON>'s helm 0", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4982, "description": "<PERSON><PERSON>'s flail 100", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 4983, "description": "<PERSON><PERSON>'s flail 75", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 4984, "description": "<PERSON><PERSON>'s flail 50", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 4985, "description": "<PERSON><PERSON>'s flail 25", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 4986, "description": "Verac's flail 0", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 4988, "description": "Verac's brassard 100", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4989, "description": "Verac's brassard 75", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4990, "description": "Verac's brassard 50", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4991, "description": "Vera<PERSON>'s brassard 25", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4992, "description": "Verac's brassard 0", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4994, "description": "Verac's plateskirt 100", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4995, "description": "Verac's plateskirt 75", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4996, "description": "Verac's plateskirt 50", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4997, "description": "Verac's plateskirt 25", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 4998, "description": "Verac's plateskirt 0", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 5574, "description": "Initiate sallet", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Prayer", "level": 10}]}, {"id": 5575, "description": "Initiate hauberk", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 5576, "description": "Initiate cuisse", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 5618, "description": "Steel arrow(p+)", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 5619, "description": "Mithril arrow(p+)", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 5620, "description": "Adamant arrow(p+)", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 5621, "description": "Rune arrow(p+)", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 5624, "description": "Steel arrow(p++)", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 5625, "description": "Mithril arrow(p++)", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 5626, "description": "Adamant arrow(p++)", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 5627, "description": "Rune arrow(p++)", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 5630, "description": "Steel dart(p+)", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 5631, "description": "Black dart(p+)", "requirements": [{"skill": "Ranged", "level": 10}]}, {"id": 5632, "description": "<PERSON><PERSON><PERSON> dart(p+)", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 5633, "description": "Adamant dart(p+)", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 5634, "description": "Rune dart(p+)", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 5637, "description": "Steel dart(p++)", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 5638, "description": "Black dart(p++)", "requirements": [{"skill": "Ranged", "level": 10}]}, {"id": 5639, "description": "<PERSON><PERSON><PERSON> dart(p++)", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 5640, "description": "Adamant dart(p++)", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 5641, "description": "Rune dart(p++)", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 5644, "description": "Steel javelin(p+)", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 5645, "description": "Mithril javelin(p+)", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 5646, "description": "Adamant javelin(p+)", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 5647, "description": "Rune javelin(p+)", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 5650, "description": "Steel javelin(p++)", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 5651, "description": "Mithril javelin(p++)", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 5652, "description": "Adamant javelin(p++)", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 5653, "description": "Rune javelin(p++)", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 5656, "description": "Steel knife(p+)", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 5657, "description": "Mithril knife(p+)", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 5658, "description": "Black knife(p+)", "requirements": [{"skill": "Ranged", "level": 10}]}, {"id": 5659, "description": "Adamant knife(p+)", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 5660, "description": "Rune knife(p+)", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 5663, "description": "Steel knife(p++)", "requirements": [{"skill": "Ranged", "level": 5}]}, {"id": 5664, "description": "Mithril knife(p++)", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 5665, "description": "Black knife(p++)", "requirements": [{"skill": "Ranged", "level": 10}]}, {"id": 5666, "description": "Adamant knife(p++)", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 5667, "description": "Rune knife(p++)", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 5672, "description": "Steel dagger(p+)", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 5674, "description": "Mithril dagger(p+)", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 5676, "description": "Adamant dagger(p+)", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 5678, "description": "Rune dagger(p+)", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 5680, "description": "Dragon dagger(p+)", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 5682, "description": "Black dagger(p+)", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 5690, "description": "Steel dagger(p++)", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 5692, "description": "Mithril dagger(p++)", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 5694, "description": "Adamant dagger(p++)", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 5696, "description": "Rune dagger(p++)", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 5698, "description": "Dragon dagger(p++)", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 5700, "description": "Black dagger(p++)", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 5708, "description": "Steel spear(p+)", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 5710, "description": "<PERSON><PERSON><PERSON> spear(p+)", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 5712, "description": "Adamant spear(p+)", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 5714, "description": "<PERSON>e spear(p+)", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 5716, "description": "Dragon spear(p+)", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 5722, "description": "Steel spear(p++)", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 5724, "description": "<PERSON><PERSON><PERSON> spear(p++)", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 5726, "description": "Adamant spear(p++)", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 5728, "description": "<PERSON>e spear(p++)", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 5730, "description": "Dragon spear(p++)", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 5734, "description": "Black spear(p+)", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 5736, "description": "Black spear(p++)", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 6082, "description": "Fixed device", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 6128, "description": "Rock-shell helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 6129, "description": "Rock-shell plate", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 6130, "description": "Rock-shell legs", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 6131, "description": "Spined helm", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 40}]}, {"id": 6133, "description": "Spined body", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 40}]}, {"id": 6135, "description": "Spined chaps", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 40}]}, {"id": 6137, "description": "Skeletal helm", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 6139, "description": "Skeletal top", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 6141, "description": "Skeletal bottoms", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 6215, "description": "Broodoo shield (10)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6217, "description": "Broodoo shield (9)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6219, "description": "Broodoo shield (8)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6221, "description": "Broodoo shield (7)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6223, "description": "Broodoo shield (6)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6225, "description": "Broodoo shield (5)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6227, "description": "Broodoo shield (4)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6229, "description": "Broodoo shield (3)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6231, "description": "Broodoo shield (2)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6233, "description": "Broodoo shield (1)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6235, "description": "Broodoo shield", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6237, "description": "Broodoo shield (10)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6239, "description": "Broodoo shield (9)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6241, "description": "Broodoo shield (8)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6243, "description": "Broodoo shield (7)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6245, "description": "Broodoo shield (6)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6247, "description": "Broodoo shield (5)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6249, "description": "Broodoo shield (4)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6251, "description": "Broodoo shield (3)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6253, "description": "Broodoo shield (2)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6255, "description": "Broodoo shield (1)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6257, "description": "Broodoo shield", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6259, "description": "Broodoo shield (10)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6261, "description": "Broodoo shield (9)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6263, "description": "Broodoo shield (8)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6265, "description": "Broodoo shield (7)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6267, "description": "Broodoo shield (6)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6269, "description": "Broodoo shield (5)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6271, "description": "Broodoo shield (4)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6273, "description": "Broodoo shield (3)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6275, "description": "Broodoo shield (2)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6277, "description": "Broodoo shield (1)", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6279, "description": "Broodoo shield", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 6322, "description": "Snakeskin body", "requirements": [{"skill": "Defence", "level": 30}, {"skill": "Ranged", "level": 30}]}, {"id": 6324, "description": "Snakeskin chaps", "requirements": [{"skill": "Defence", "level": 30}, {"skill": "Ranged", "level": 30}]}, {"id": 6326, "description": "Snakeskin bandana", "requirements": [{"skill": "Defence", "level": 30}, {"skill": "Ranged", "level": 30}]}, {"id": 6328, "description": "Snakeskin boots", "requirements": [{"skill": "Defence", "level": 30}, {"skill": "Ranged", "level": 30}]}, {"id": 6330, "description": "Snakeskin vambraces", "requirements": [{"skill": "Defence", "level": 30}, {"skill": "Ranged", "level": 30}]}, {"id": 6408, "description": "Oak blackjack(o)", "requirements": [{"skill": "Attack", "level": 10}, {"skill": "Thieving", "level": 10}]}, {"id": 6410, "description": "Oak blackjack(d)", "requirements": [{"skill": "Thieving", "level": 10}, {"skill": "Defence", "level": 10}]}, {"id": 6412, "description": "Willow blackjack(o)", "requirements": [{"skill": "Attack", "level": 20}, {"skill": "Thieving", "level": 20}]}, {"id": 6414, "description": "Willow blackjack(d)", "requirements": [{"skill": "Thieving", "level": 20}, {"skill": "Defence", "level": 20}]}, {"id": 6416, "description": "Maple blackjack", "requirements": [{"skill": "Thieving", "level": 30}]}, {"id": 6418, "description": "Maple blackjack(o)", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Thieving", "level": 30}]}, {"id": 6420, "description": "Maple blackjack(d)", "requirements": [{"skill": "Thieving", "level": 30}, {"skill": "Defence", "level": 30}]}, {"id": 6522, "description": "Tokt<PERSON>-xil-ul", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 6523, "description": "Toktz-xil-ak", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 6524, "description": "Toktz-ket-xil", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 6525, "description": "Toktz-xil-ek", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 6526, "description": "Toktz-mej-tal", "requirements": [{"skill": "Attack", "level": 60}, {"skill": "Magic", "level": 60}]}, {"id": 6527, "description": "Tzhaar-ket-em", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 6528, "description": "Tzhaar-ket-om", "requirements": [{"skill": "Strength", "level": 60}]}, {"id": 6562, "description": "Mud battlestaff", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 6563, "description": "Mystic mud staff", "requirements": [{"skill": "Attack", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 6587, "description": "White claws", "requirements": [{"skill": "Attack", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6589, "description": "White battleaxe", "requirements": [{"skill": "Attack", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6591, "description": "White dagger", "requirements": [{"skill": "Attack", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6593, "description": "White dagger(p)", "requirements": [{"skill": "Attack", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6595, "description": "White dagger(p+)", "requirements": [{"skill": "Attack", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6597, "description": "White dagger(p++)", "requirements": [{"skill": "Attack", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6599, "description": "White halberd", "requirements": [{"skill": "Attack", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6601, "description": "White mace", "requirements": [{"skill": "Attack", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6603, "description": "White magic staff", "requirements": [{"skill": "Attack", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6605, "description": "White sword", "requirements": [{"skill": "Attack", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6607, "description": "White longsword", "requirements": [{"skill": "Attack", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6609, "description": "White 2h sword", "requirements": [{"skill": "Attack", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6611, "description": "White scimitar", "requirements": [{"skill": "Attack", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6613, "description": "White warhammer", "requirements": [{"skill": "Strength", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6615, "description": "White chainbody", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6617, "description": "White platebody", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6619, "description": "White boots", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6621, "description": "White med helm", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6623, "description": "<PERSON> full helm", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6625, "description": "White platelegs", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6627, "description": "White plateskirt", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6629, "description": "White gloves", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6631, "description": "White sq shield", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6633, "description": "White kiteshield", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Prayer", "level": 10}]}, {"id": 6664, "description": "Fishing explosive", "requirements": [{"skill": "Slayer", "level": 32}]}, {"id": 6696, "description": "Ice cooler", "requirements": [{"skill": "Construction", "level": 18}]}, {"id": 6720, "description": "Slayer gloves", "requirements": [{"skill": "Slayer", "level": 42}]}, {"id": 6724, "description": "<PERSON><PERSON><PERSON>", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 6739, "description": "Dragon axe", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 6809, "description": "Granite legs", "requirements": [{"skill": "Defence", "level": 50}, {"skill": "Strength", "level": 50}]}, {"id": 6889, "description": "<PERSON><PERSON>'s book", "requirements": [{"skill": "Magic", "level": 60}]}, {"id": 6894, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 6895, "description": "Adamant med helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 6897, "description": "Rune longsword", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 6908, "description": "Beginner wand", "requirements": [{"skill": "Magic", "level": 45}]}, {"id": 6910, "description": "Apprentice wand", "requirements": [{"skill": "Magic", "level": 50}]}, {"id": 6912, "description": "Teacher wand", "requirements": [{"skill": "Magic", "level": 55}]}, {"id": 6914, "description": "Master wand", "requirements": [{"skill": "Magic", "level": 60}]}, {"id": 6916, "description": "Infinity top", "requirements": [{"skill": "Defence", "level": 25}, {"skill": "Magic", "level": 50}]}, {"id": 6918, "description": "Infinity hat", "requirements": [{"skill": "Defence", "level": 25}, {"skill": "Magic", "level": 50}]}, {"id": 6920, "description": "Infinity boots", "requirements": [{"skill": "Defence", "level": 25}, {"skill": "Magic", "level": 50}]}, {"id": 6922, "description": "Infinity gloves", "requirements": [{"skill": "Defence", "level": 25}, {"skill": "Magic", "level": 50}]}, {"id": 6924, "description": "Infinity bottoms", "requirements": [{"skill": "Defence", "level": 25}, {"skill": "Magic", "level": 50}]}, {"id": 6967, "description": "Dragon med helm", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 7051, "description": "Unlit bug lantern", "requirements": [{"skill": "Slayer", "level": 33}]}, {"id": 7053, "description": "Lit bug lantern", "requirements": [{"skill": "Slayer", "level": 33}]}, {"id": 7140, "description": "Lucky cutlass", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 7141, "description": "Harry's cutlass", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 7142, "description": "<PERSON><PERSON>", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 7158, "description": "Dragon 2h sword", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 7159, "description": "Insulated boots", "requirements": [{"skill": "Slayer", "level": 37}]}, {"id": 7332, "description": "Black shield (h1)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 7334, "description": "Adamant shield (h1)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 7336, "description": "Rune shield (h1)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 7338, "description": "Black shield (h2)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 7340, "description": "Adamant shield (h2)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 7342, "description": "Rune shield (h2)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 7344, "description": "Black shield (h3)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 7346, "description": "Adamant shield (h3)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 7348, "description": "Rune shield (h3)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 7350, "description": "Black shield (h4)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 7352, "description": "Adamant shield (h4)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 7354, "description": "Rune shield (h4)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 7356, "description": "Black shield (h5)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 7358, "description": "Adamant shield (h5)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 7360, "description": "Rune shield (h5)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 7362, "description": "Studded body (g)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Ranged", "level": 20}]}, {"id": 7364, "description": "Studded body (t)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Ranged", "level": 20}]}, {"id": 7366, "description": "Studded chaps (g)", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 7368, "description": "Studded chaps (t)", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 7370, "description": "Green d'hide body (g)", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 40}]}, {"id": 7372, "description": "Green d'hide body (t)", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 40}]}, {"id": 7374, "description": "Blue d'hide body (g)", "requirements": [{"skill": "Defence", "level": 50}, {"skill": "Ranged", "level": 40}]}, {"id": 7376, "description": "Blue d'hide body (t)", "requirements": [{"skill": "Defence", "level": 50}, {"skill": "Ranged", "level": 40}]}, {"id": 7378, "description": "Green d'hide chaps (g)", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 7380, "description": "Green d'hide chaps (t)", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 7382, "description": "Blue d'hide chaps (g)", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 7384, "description": "Blue d'hide chaps (t)", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 7398, "description": "Enchanted robe", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 7399, "description": "Enchanted top", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 7400, "description": "Enchanted hat", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 7437, "description": "Spork", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 7439, "description": "Spa<PERSON>la", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 7441, "description": "Frying pan", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 7443, "description": "Skewer", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 7445, "description": "Rolling pin", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 7447, "description": "Kitchen knife", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 7449, "description": "Meat tenderiser", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 7451, "description": "Cleaver", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 7459, "description": "Adamant gloves", "requirements": [{"skill": "Defence", "level": 13}]}, {"id": 7460, "description": "Rune gloves", "requirements": [{"skill": "Defence", "level": 34}]}, {"id": 7461, "description": "Dragon gloves", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 7462, "description": "Barrow<PERSON> gloves", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8464, "description": "Rune heraldic helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8466, "description": "Rune heraldic helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8468, "description": "Rune heraldic helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8470, "description": "Rune heraldic helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8472, "description": "Rune heraldic helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8474, "description": "Rune heraldic helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8476, "description": "Rune heraldic helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8478, "description": "Rune heraldic helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8480, "description": "Rune heraldic helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8482, "description": "Rune heraldic helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8484, "description": "Rune heraldic helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8486, "description": "Rune heraldic helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8488, "description": "Rune heraldic helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8490, "description": "Rune heraldic helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8492, "description": "Rune heraldic helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8494, "description": "Rune heraldic helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8682, "description": "Steel heraldic helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8686, "description": "Steel heraldic helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8688, "description": "Steel heraldic helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8690, "description": "Steel heraldic helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8692, "description": "Steel heraldic helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8694, "description": "Steel heraldic helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8696, "description": "Steel heraldic helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8698, "description": "Steel heraldic helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8700, "description": "Steel heraldic helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8702, "description": "Steel heraldic helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8704, "description": "Steel heraldic helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8706, "description": "Steel heraldic helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8708, "description": "Steel heraldic helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8710, "description": "Steel heraldic helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8712, "description": "Steel heraldic helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8714, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8716, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8718, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8720, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8722, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8724, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8726, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8728, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8730, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8732, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8734, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8736, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8738, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8740, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8742, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8744, "description": "<PERSON><PERSON> kit<PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 8746, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8748, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8750, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8752, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8754, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8756, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8758, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8760, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8762, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8764, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8766, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8768, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8770, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8772, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8774, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8776, "description": "Steel kiteshield", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 8839, "description": "Void knight top", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 8840, "description": "Void knight robe", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 8841, "description": "Void knight mace", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 8842, "description": "Void knight gloves", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 8846, "description": "Steel defender", "requirements": [{"skill": "Attack", "level": 5}, {"skill": "Defence", "level": 5}]}, {"id": 8847, "description": "Black defender", "requirements": [{"skill": "Attack", "level": 10}, {"skill": "Defence", "level": 10}]}, {"id": 8848, "description": "Mithril defender", "requirements": [{"skill": "Attack", "level": 20}, {"skill": "Defence", "level": 20}]}, {"id": 8849, "description": "Adamant defender", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Defence", "level": 30}]}, {"id": 8850, "description": "Rune defender", "requirements": [{"skill": "Attack", "level": 40}, {"skill": "Defence", "level": 40}]}, {"id": 8880, "description": "<PERSON><PERSON><PERSON><PERSON> crossbow", "requirements": [{"skill": "Ranged", "level": 28}]}, {"id": 8882, "description": "Bone bolts", "requirements": [{"skill": "Ranged", "level": 28}]}, {"id": 8901, "description": "Black mask (10)", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Strength", "level": 20}]}, {"id": 8903, "description": "Black mask (9)", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Strength", "level": 20}]}, {"id": 8905, "description": "Black mask (8)", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Strength", "level": 20}]}, {"id": 8907, "description": "Black mask (7)", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Strength", "level": 20}]}, {"id": 8909, "description": "Black mask (6)", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Strength", "level": 20}]}, {"id": 8911, "description": "Black mask (5)", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Strength", "level": 20}]}, {"id": 8913, "description": "Black mask (4)", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Strength", "level": 20}]}, {"id": 8915, "description": "Black mask (3)", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Strength", "level": 20}]}, {"id": 8917, "description": "Black mask (2)", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Strength", "level": 20}]}, {"id": 8919, "description": "Black mask (1)", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Strength", "level": 20}]}, {"id": 8921, "description": "Black mask", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Strength", "level": 20}]}, {"id": 8923, "description": "Witchwood icon", "requirements": [{"skill": "Slayer", "level": 35}]}, {"id": 9044, "description": "<PERSON><PERSON><PERSON>'s sceptre (3)", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 9046, "description": "<PERSON><PERSON><PERSON>'s sceptre (2)", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 9048, "description": "<PERSON><PERSON><PERSON>'s sceptre (1)", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 9050, "description": "<PERSON><PERSON><PERSON>'s sceptre", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 9084, "description": "Lunar staff", "requirements": [{"skill": "Magic", "level": 65}]}, {"id": 9096, "description": "Lunar helm", "requirements": [{"skill": "Magic", "level": 65}, {"skill": "Defence", "level": 30}]}, {"id": 9097, "description": "Lunar torso", "requirements": [{"skill": "Magic", "level": 65}, {"skill": "Defence", "level": 30}]}, {"id": 9098, "description": "Lunar legs", "requirements": [{"skill": "Magic", "level": 65}, {"skill": "Defence", "level": 30}]}, {"id": 9099, "description": "Lunar gloves", "requirements": [{"skill": "Magic", "level": 65}, {"skill": "Defence", "level": 30}]}, {"id": 9100, "description": "Lunar boots", "requirements": [{"skill": "Magic", "level": 65}, {"skill": "Defence", "level": 30}]}, {"id": 9101, "description": "Lunar cape", "requirements": [{"skill": "Magic", "level": 65}, {"skill": "Defence", "level": 30}]}, {"id": 9104, "description": "Lunar ring", "requirements": [{"skill": "Magic", "level": 65}, {"skill": "Defence", "level": 30}]}, {"id": 9139, "description": "Blurite bolts", "requirements": [{"skill": "Ranged", "level": 16}]}, {"id": 9140, "description": "Iron bolts", "requirements": [{"skill": "Ranged", "level": 26}]}, {"id": 9141, "description": "Steel bolts", "requirements": [{"skill": "Ranged", "level": 31}]}, {"id": 9142, "description": "Mithril bolts", "requirements": [{"skill": "Ranged", "level": 36}]}, {"id": 9143, "description": "Adamant bolts", "requirements": [{"skill": "Ranged", "level": 46}]}, {"id": 9144, "description": "Runite bolts", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 9145, "description": "Silver bolts", "requirements": [{"skill": "Ranged", "level": 26}]}, {"id": 9176, "description": "Blurite crossbow", "requirements": [{"skill": "Ranged", "level": 16}]}, {"id": 9177, "description": "Iron crossbow", "requirements": [{"skill": "Ranged", "level": 26}]}, {"id": 9179, "description": "Steel crossbow", "requirements": [{"skill": "Ranged", "level": 31}]}, {"id": 9181, "description": "Mith crossbow", "requirements": [{"skill": "Ranged", "level": 36}]}, {"id": 9183, "description": "Adamant crossbow", "requirements": [{"skill": "Ranged", "level": 46}]}, {"id": 9185, "description": "Rune crossbow", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 9237, "description": "Jade bolts (e)", "requirements": [{"skill": "Ranged", "level": 15}]}, {"id": 9238, "description": "Pearl bolts (e)", "requirements": [{"skill": "Ranged", "level": 26}]}, {"id": 9239, "description": "Topaz bolts (e)", "requirements": [{"skill": "Ranged", "level": 31}]}, {"id": 9240, "description": "Sapphire bolts (e)", "requirements": [{"skill": "Ranged", "level": 36}]}, {"id": 9241, "description": "Emerald bolts (e)", "requirements": [{"skill": "Ranged", "level": 36}]}, {"id": 9242, "description": "Ruby bolts (e)", "requirements": [{"skill": "Ranged", "level": 46}]}, {"id": 9243, "description": "Diamond bolts (e)", "requirements": [{"skill": "Ranged", "level": 46}]}, {"id": 9244, "description": "Dragonstone bolts (e)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 9245, "description": "Onyx bolts (e)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 9286, "description": "Blurite bolts (p)", "requirements": [{"skill": "Ranged", "level": 16}]}, {"id": 9287, "description": "Iron bolts (p)", "requirements": [{"skill": "Ranged", "level": 26}]}, {"id": 9288, "description": "Steel bolts (p)", "requirements": [{"skill": "Ranged", "level": 31}]}, {"id": 9289, "description": "<PERSON><PERSON><PERSON> bolts (p)", "requirements": [{"skill": "Ranged", "level": 36}]}, {"id": 9290, "description": "Adamant bolts (p)", "requirements": [{"skill": "Ranged", "level": 46}]}, {"id": 9291, "description": "Runite bolts (p)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 9292, "description": "Silver bolts (p)", "requirements": [{"skill": "Ranged", "level": 26}]}, {"id": 9293, "description": "Blurite bolts (p+)", "requirements": [{"skill": "Ranged", "level": 16}]}, {"id": 9294, "description": "Iron bolts (p+)", "requirements": [{"skill": "Ranged", "level": 26}]}, {"id": 9295, "description": "Steel bolts (p+)", "requirements": [{"skill": "Ranged", "level": 31}]}, {"id": 9296, "description": "Mithril bolts (p+)", "requirements": [{"skill": "Ranged", "level": 36}]}, {"id": 9297, "description": "Adamant bolts (p+)", "requirements": [{"skill": "Ranged", "level": 46}]}, {"id": 9298, "description": "Runite bolts (p+)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 9299, "description": "Silver bolts (p+)", "requirements": [{"skill": "Ranged", "level": 26}]}, {"id": 9300, "description": "Blurite bolts (p++)", "requirements": [{"skill": "Ranged", "level": 16}]}, {"id": 9301, "description": "Iron bolts (p++)", "requirements": [{"skill": "Ranged", "level": 26}]}, {"id": 9302, "description": "Steel bolts (p++)", "requirements": [{"skill": "Ranged", "level": 31}]}, {"id": 9303, "description": "Mithril bolts (p++)", "requirements": [{"skill": "Ranged", "level": 36}]}, {"id": 9304, "description": "Adamant bolts (p++)", "requirements": [{"skill": "Ranged", "level": 46}]}, {"id": 9305, "description": "Runite bolts (p++)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 9306, "description": "Silver bolts (p++)", "requirements": [{"skill": "Ranged", "level": 26}]}, {"id": 9335, "description": "Jade bolts", "requirements": [{"skill": "Ranged", "level": 16}]}, {"id": 9336, "description": "Topaz bolts", "requirements": [{"skill": "Ranged", "level": 31}]}, {"id": 9337, "description": "Sapphire bolts", "requirements": [{"skill": "Ranged", "level": 36}]}, {"id": 9338, "description": "Emerald bolts", "requirements": [{"skill": "Ranged", "level": 36}]}, {"id": 9339, "description": "Ruby bolts", "requirements": [{"skill": "Ranged", "level": 46}]}, {"id": 9340, "description": "Diamond bolts", "requirements": [{"skill": "Ranged", "level": 46}]}, {"id": 9341, "description": "Dragonstone bolts", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 9342, "description": "Onyx bolts", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 9629, "description": "<PERSON><PERSON> helm", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 9672, "description": "Proselyte sallet", "requirements": [{"skill": "Defence", "level": 30}, {"skill": "Prayer", "level": 20}]}, {"id": 9674, "description": "Proselyte hauberk", "requirements": [{"skill": "Defence", "level": 30}, {"skill": "Prayer", "level": 20}]}, {"id": 9676, "description": "Proselyte cuisse", "requirements": [{"skill": "Defence", "level": 30}, {"skill": "Prayer", "level": 20}]}, {"id": 9678, "description": "Proselyte tasset", "requirements": [{"skill": "Defence", "level": 30}, {"skill": "Prayer", "level": 20}]}, {"id": 9747, "description": "Attack cape", "requirements": [{"skill": "Attack", "level": 99}]}, {"id": 9748, "description": "Attack cape(t)", "requirements": [{"skill": "Attack", "level": 99}]}, {"id": 9749, "description": "Attack hood", "requirements": [{"skill": "Attack", "level": 99}]}, {"id": 9750, "description": "Strength cape", "requirements": [{"skill": "Strength", "level": 99}]}, {"id": 9751, "description": "Strength cape(t)", "requirements": [{"skill": "Strength", "level": 99}]}, {"id": 9752, "description": "Strength hood", "requirements": [{"skill": "Strength", "level": 99}]}, {"id": 9753, "description": "Defence cape", "requirements": [{"skill": "Defence", "level": 99}]}, {"id": 9754, "description": "Defence cape(t)", "requirements": [{"skill": "Defence", "level": 99}]}, {"id": 9755, "description": "Defence hood", "requirements": [{"skill": "Defence", "level": 99}]}, {"id": 9756, "description": "Ranging cape", "requirements": [{"skill": "Ranged", "level": 99}]}, {"id": 9757, "description": "Ranging cape(t)", "requirements": [{"skill": "Ranged", "level": 99}]}, {"id": 9758, "description": "Ranging hood", "requirements": [{"skill": "Ranged", "level": 99}]}, {"id": 9759, "description": "Prayer cape", "requirements": [{"skill": "Prayer", "level": 99}]}, {"id": 9760, "description": "Prayer cape(t)", "requirements": [{"skill": "Prayer", "level": 99}]}, {"id": 9761, "description": "Prayer hood", "requirements": [{"skill": "Prayer", "level": 99}]}, {"id": 9762, "description": "Magic cape", "requirements": [{"skill": "Magic", "level": 99}]}, {"id": 9763, "description": "Magic cape(t)", "requirements": [{"skill": "Magic", "level": 99}]}, {"id": 9764, "description": "Magic hood", "requirements": [{"skill": "Magic", "level": 99}]}, {"id": 9765, "description": "Runecraft cape", "requirements": [{"skill": "Runecraft", "level": 99}]}, {"id": 9766, "description": "Runecraft cape(t)", "requirements": [{"skill": "Runecraft", "level": 99}]}, {"id": 9767, "description": "Runecrafting hood", "requirements": [{"skill": "Runecraft", "level": 99}]}, {"id": 9768, "description": "Hitpoints cape", "requirements": [{"skill": "Hitpoints", "level": 99}]}, {"id": 9769, "description": "Hitpoints cape(t)", "requirements": [{"skill": "Hitpoints", "level": 99}]}, {"id": 9770, "description": "Hitpoints hood", "requirements": [{"skill": "Hitpoints", "level": 99}]}, {"id": 9771, "description": "Agility cape", "requirements": [{"skill": "Agility", "level": 99}]}, {"id": 9772, "description": "Agility cape(t)", "requirements": [{"skill": "Agility", "level": 99}]}, {"id": 9773, "description": "Agility hood", "requirements": [{"skill": "Agility", "level": 99}]}, {"id": 9774, "description": "Herblore cape", "requirements": [{"skill": "Herblore", "level": 99}]}, {"id": 9775, "description": "Herblore cape(t)", "requirements": [{"skill": "Herblore", "level": 99}]}, {"id": 9776, "description": "Herblore hood", "requirements": [{"skill": "Herblore", "level": 99}]}, {"id": 9777, "description": "Thieving cape", "requirements": [{"skill": "Thieving", "level": 99}]}, {"id": 9778, "description": "Thieving cape(t)", "requirements": [{"skill": "Thieving", "level": 99}]}, {"id": 9779, "description": "Thieving hood", "requirements": [{"skill": "Thieving", "level": 99}]}, {"id": 9780, "description": "Crafting cape", "requirements": [{"skill": "Crafting", "level": 99}]}, {"id": 9781, "description": "Crafting cape(t)", "requirements": [{"skill": "Crafting", "level": 99}]}, {"id": 9782, "description": "Crafting hood", "requirements": [{"skill": "Crafting", "level": 99}]}, {"id": 9783, "description": "Fletching cape", "requirements": [{"skill": "Fletching", "level": 99}]}, {"id": 9784, "description": "Fletching cape(t)", "requirements": [{"skill": "Fletching", "level": 99}]}, {"id": 9785, "description": "Fletching hood", "requirements": [{"skill": "Fletching", "level": 99}]}, {"id": 9786, "description": "Slayer cape", "requirements": [{"skill": "Slayer", "level": 99}]}, {"id": 9787, "description": "Slayer cape(t)", "requirements": [{"skill": "Slayer", "level": 99}]}, {"id": 9788, "description": "Slayer hood", "requirements": [{"skill": "Slayer", "level": 99}]}, {"id": 9789, "description": "Construct. cape", "requirements": [{"skill": "Construction", "level": 99}]}, {"id": 9790, "description": "Construct. cape(t)", "requirements": [{"skill": "Construction", "level": 99}]}, {"id": 9791, "description": "Construct. hood", "requirements": [{"skill": "Construction", "level": 99}]}, {"id": 9792, "description": "Mining cape", "requirements": [{"skill": "Mining", "level": 99}]}, {"id": 9793, "description": "Mining cape(t)", "requirements": [{"skill": "Mining", "level": 99}]}, {"id": 9794, "description": "Mining hood", "requirements": [{"skill": "Mining", "level": 99}]}, {"id": 9795, "description": "Smithing cape", "requirements": [{"skill": "<PERSON><PERSON>", "level": 99}]}, {"id": 9796, "description": "Smithing cape(t)", "requirements": [{"skill": "<PERSON><PERSON>", "level": 99}]}, {"id": 9797, "description": "Smithing hood", "requirements": [{"skill": "<PERSON><PERSON>", "level": 99}]}, {"id": 9798, "description": "Fishing cape", "requirements": [{"skill": "Fishing", "level": 99}]}, {"id": 9799, "description": "Fishing cape(t)", "requirements": [{"skill": "Fishing", "level": 99}]}, {"id": 9800, "description": "Fishing hood", "requirements": [{"skill": "Fishing", "level": 99}]}, {"id": 9801, "description": "Cooking cape", "requirements": [{"skill": "Cooking", "level": 99}]}, {"id": 9802, "description": "Cooking cape(t)", "requirements": [{"skill": "Cooking", "level": 99}]}, {"id": 9803, "description": "Cooking hood", "requirements": [{"skill": "Cooking", "level": 99}]}, {"id": 9804, "description": "Firemaking cape", "requirements": [{"skill": "Firemaking", "level": 99}]}, {"id": 9805, "description": "Firemaking cape(t)", "requirements": [{"skill": "Firemaking", "level": 99}]}, {"id": 9806, "description": "Firemaking hood", "requirements": [{"skill": "Firemaking", "level": 99}]}, {"id": 9807, "description": "Woodcutting cape", "requirements": [{"skill": "Woodcutting", "level": 99}]}, {"id": 9808, "description": "Woodcut. cape(t)", "requirements": [{"skill": "Woodcutting", "level": 99}]}, {"id": 9809, "description": "Woodcutting hood", "requirements": [{"skill": "Woodcutting", "level": 99}]}, {"id": 9810, "description": "Farming cape", "requirements": [{"skill": "Farming", "level": 99}]}, {"id": 9811, "description": "Farming cape(t)", "requirements": [{"skill": "Farming", "level": 99}]}, {"id": 9812, "description": "Farming hood", "requirements": [{"skill": "Farming", "level": 99}]}, {"id": 9948, "description": "Hunter cape", "requirements": [{"skill": "<PERSON>", "level": 99}]}, {"id": 9949, "description": "Hunter cape(t)", "requirements": [{"skill": "<PERSON>", "level": 99}]}, {"id": 9950, "description": "<PERSON>", "requirements": [{"skill": "<PERSON>", "level": 99}]}, {"id": 10033, "description": "Chinchompa", "requirements": [{"skill": "Ranged", "level": 45}]}, {"id": 10034, "description": "Red chinchompa", "requirements": [{"skill": "Ranged", "level": 55}]}, {"id": 10035, "description": "Kyatt legs", "requirements": [{"skill": "<PERSON>", "level": 52}]}, {"id": 10037, "description": "Kyatt top", "requirements": [{"skill": "<PERSON>", "level": 52}]}, {"id": 10039, "description": "Kyatt hat", "requirements": [{"skill": "<PERSON>", "level": 52}]}, {"id": 10041, "description": "Larupia legs", "requirements": [{"skill": "<PERSON>", "level": 28}]}, {"id": 10043, "description": "Larupia top", "requirements": [{"skill": "<PERSON>", "level": 28}]}, {"id": 10045, "description": "La<PERSON>ia hat", "requirements": [{"skill": "<PERSON>", "level": 28}]}, {"id": 10047, "description": "Graahk legs", "requirements": [{"skill": "<PERSON>", "level": 38}]}, {"id": 10049, "description": "Graahk top", "requirements": [{"skill": "<PERSON>", "level": 38}]}, {"id": 10051, "description": "<PERSON><PERSON><PERSON><PERSON> headdress", "requirements": [{"skill": "<PERSON>", "level": 38}]}, {"id": 10057, "description": "Jungle camo top", "requirements": [{"skill": "<PERSON>", "level": 4}]}, {"id": 10059, "description": "Jungle camo legs", "requirements": [{"skill": "<PERSON>", "level": 4}]}, {"id": 10061, "description": "Desert camo top", "requirements": [{"skill": "<PERSON>", "level": 10}]}, {"id": 10063, "description": "Desert camo legs", "requirements": [{"skill": "<PERSON>", "level": 10}]}, {"id": 10069, "description": "Spotted cape", "requirements": [{"skill": "<PERSON>", "level": 40}]}, {"id": 10071, "description": "Spottier cape", "requirements": [{"skill": "<PERSON>", "level": 66}]}, {"id": 10073, "description": "Spotted cape", "requirements": [{"skill": "<PERSON>", "level": 40}]}, {"id": 10074, "description": "Spottier cape", "requirements": [{"skill": "<PERSON>", "level": 66}]}, {"id": 10075, "description": "Gloves of silence", "requirements": [{"skill": "<PERSON>", "level": 54}]}, {"id": 10079, "description": "Green spiky vambs", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 10081, "description": "Blue spiky vambs", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 10083, "description": "Red spiky vambs", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 10085, "description": "Black spiky vambs", "requirements": [{"skill": "Ranged", "level": 70}]}, {"id": 10129, "description": "Barb-tail harpoon", "requirements": [{"skill": "Fishing", "level": 35}]}, {"id": 10132, "description": "Strung rabbit foot", "requirements": [{"skill": "<PERSON>", "level": 24}]}, {"id": 10146, "description": "Orange salamander", "requirements": [{"skill": "Attack", "level": 50}, {"skill": "Ranged", "level": 50}, {"skill": "Magic", "level": 50}]}, {"id": 10147, "description": "Red salamander", "requirements": [{"skill": "Attack", "level": 60}, {"skill": "Ranged", "level": 60}, {"skill": "Magic", "level": 60}]}, {"id": 10148, "description": "Black salamander", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Ranged", "level": 70}, {"skill": "Magic", "level": 70}]}, {"id": 10149, "description": "Swamp lizard", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Ranged", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 10156, "description": "Hunters' crossbow", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 10158, "description": "Kebbit bolts", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 10159, "description": "Long kebbit bolts", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 10280, "description": "<PERSON> comp bow", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 10282, "description": "Yew comp bow", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 10284, "description": "Magic comp bow", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 10286, "description": "<PERSON><PERSON> helm (h1)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10288, "description": "<PERSON><PERSON> helm (h2)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10290, "description": "<PERSON><PERSON> helm (h3)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10292, "description": "<PERSON><PERSON> helm (h4)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10294, "description": "<PERSON><PERSON> helm (h5)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10296, "description": "Adamant helm (h1)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 10298, "description": "Adamant helm (h2)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 10300, "description": "Adamant helm (h3)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 10302, "description": "Adamant helm (h4)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 10304, "description": "Adamant helm (h5)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 10306, "description": "<PERSON> helm (h1)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 10308, "description": "<PERSON> helm (h2)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 10310, "description": "<PERSON> helm (h3)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 10312, "description": "<PERSON> helm (h4)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 10314, "description": "<PERSON> helm (h5)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 10330, "description": "3rd age range top", "requirements": [{"skill": "Defence", "level": 65}, {"skill": "Ranged", "level": 45}]}, {"id": 10332, "description": "3rd age range legs", "requirements": [{"skill": "Defence", "level": 65}, {"skill": "Ranged", "level": 45}]}, {"id": 10334, "description": "3rd age range coif", "requirements": [{"skill": "Defence", "level": 65}, {"skill": "Ranged", "level": 45}]}, {"id": 10336, "description": "3rd age vambraces", "requirements": [{"skill": "Defence", "level": 65}, {"skill": "Ranged", "level": 45}]}, {"id": 10338, "description": "3rd age robe top", "requirements": [{"skill": "Defence", "level": 65}, {"skill": "Magic", "level": 30}]}, {"id": 10340, "description": "3rd age robe", "requirements": [{"skill": "Defence", "level": 65}, {"skill": "Magic", "level": 30}]}, {"id": 10342, "description": "3rd age mage hat", "requirements": [{"skill": "Defence", "level": 65}, {"skill": "Magic", "level": 30}]}, {"id": 10344, "description": "3rd age amulet", "requirements": [{"skill": "Defence", "level": 65}, {"skill": "Magic", "level": 30}]}, {"id": 10346, "description": "3rd age platelegs", "requirements": [{"skill": "Defence", "level": 65}]}, {"id": 10348, "description": "3rd age platebody", "requirements": [{"skill": "Defence", "level": 65}]}, {"id": 10350, "description": "3rd age full helmet", "requirements": [{"skill": "Defence", "level": 65}]}, {"id": 10352, "description": "3rd age kiteshield", "requirements": [{"skill": "Defence", "level": 65}]}, {"id": 10368, "description": "Zamorak bracers", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 1}]}, {"id": 10370, "description": "Zamorak d'hide", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 40}]}, {"id": 10372, "description": "Zamorak chaps", "requirements": [{"skill": "Defence", "level": 1}, {"skill": "Ranged", "level": 70}]}, {"id": 10374, "description": "Zamorak coif", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 40}]}, {"id": 10376, "description": "Guthix bracers", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 1}]}, {"id": 10378, "description": "<PERSON><PERSON><PERSON> d'hide", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 70}]}, {"id": 10380, "description": "Guthix chaps", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 1}]}, {"id": 10382, "description": "Guthix coif", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 40}]}, {"id": 10384, "description": "Saradomin bracers", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 1}]}, {"id": 10386, "description": "Saradomin d'hide", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 40}]}, {"id": 10388, "description": "<PERSON><PERSON>in chaps", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 1}]}, {"id": 10390, "description": "Saradomin coif", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 40}]}, {"id": 10440, "description": "<PERSON><PERSON><PERSON> crozier", "requirements": [{"skill": "Prayer", "level": 60}]}, {"id": 10442, "description": "Guthix crozier", "requirements": [{"skill": "Prayer", "level": 60}]}, {"id": 10444, "description": "Zamorak crozier", "requirements": [{"skill": "Prayer", "level": 60}]}, {"id": 10446, "description": "Saradomin cloak", "requirements": [{"skill": "Prayer", "level": 40}]}, {"id": 10448, "description": "Guthix cloak", "requirements": [{"skill": "Prayer", "level": 40}]}, {"id": 10450, "description": "Zamorak cloak", "requirements": [{"skill": "Prayer", "level": 40}]}, {"id": 10452, "description": "Saradomin mitre", "requirements": [{"skill": "Prayer", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 10454, "description": "Guthix mitre", "requirements": [{"skill": "Prayer", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 10456, "description": "Zamorak mitre", "requirements": [{"skill": "Prayer", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 10458, "description": "Saradomin robe top", "requirements": [{"skill": "Prayer", "level": 20}]}, {"id": 10460, "description": "Zamorak robe top", "requirements": [{"skill": "Prayer", "level": 20}]}, {"id": 10462, "description": "Guthix robe top", "requirements": [{"skill": "Prayer", "level": 20}]}, {"id": 10464, "description": "Saradomin robe legs", "requirements": [{"skill": "Prayer", "level": 20}]}, {"id": 10466, "description": "Guthix robe legs", "requirements": [{"skill": "Prayer", "level": 20}]}, {"id": 10468, "description": "Zamorak robe legs", "requirements": [{"skill": "Prayer", "level": 20}]}, {"id": 10470, "description": "<PERSON><PERSON><PERSON> stole", "requirements": [{"skill": "Prayer", "level": 60}]}, {"id": 10472, "description": "<PERSON><PERSON><PERSON> stole", "requirements": [{"skill": "Prayer", "level": 60}]}, {"id": 10474, "description": "<PERSON><PERSON><PERSON> stole", "requirements": [{"skill": "Prayer", "level": 60}]}, {"id": 10499, "description": "Ava's accumulator", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 10547, "description": "Healer hat", "requirements": [{"skill": "Defence", "level": 45}]}, {"id": 10548, "description": "Fighter hat", "requirements": [{"skill": "Defence", "level": 45}]}, {"id": 10549, "description": "Runner hat", "requirements": [{"skill": "Defence", "level": 45}]}, {"id": 10550, "description": "Ranger hat", "requirements": [{"skill": "Defence", "level": 45}]}, {"id": 10551, "description": "Fighter torso", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10552, "description": "Runner boots", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10553, "description": "Penance gloves", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10554, "description": "Penance gloves", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10555, "description": "Penance skirt", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10564, "description": "Granite body", "requirements": [{"skill": "Defence", "level": 50}, {"skill": "Strength", "level": 50}]}, {"id": 10581, "description": "<PERSON><PERSON>", "requirements": [{"skill": "Attack", "level": 50}]}, {"id": 10582, "description": "<PERSON><PERSON>(p)", "requirements": [{"skill": "Attack", "level": 50}]}, {"id": 10583, "description": "<PERSON><PERSON>(p+)", "requirements": [{"skill": "Attack", "level": 50}]}, {"id": 10584, "description": "<PERSON><PERSON>(p++)", "requirements": [{"skill": "Attack", "level": 50}]}, {"id": 10589, "description": "Granite helm", "requirements": [{"skill": "Defence", "level": 50}, {"skill": "Strength", "level": 50}]}, {"id": 10665, "description": "Black shield (h1)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 10666, "description": "Adamant shield (h1)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 10667, "description": "Rune shield (h1)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10668, "description": "Black shield (h2)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 10669, "description": "Adamant shield (h2)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 10670, "description": "Rune shield (h2)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10671, "description": "Black shield (h3)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 10672, "description": "Adamant shield (h3)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 10673, "description": "Rune shield (h3)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10674, "description": "Black shield (h4)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 10675, "description": "Adamant shield (h4)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 10676, "description": "Rune shield (h4)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10677, "description": "Black shield (h5)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 10678, "description": "Adamant shield (h5)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 10679, "description": "Rune shield (h5)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10690, "description": "Black platebody (t)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 10691, "description": "Black platebody (g)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 10697, "description": "Adamant platebody (t)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 10698, "description": "Adamant platebody (g)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 10798, "description": "Rune platebody (g)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10800, "description": "Rune platebody (t)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 10822, "description": "Yak-hide armour", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 10824, "description": "Yak-hide armour", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 10826, "description": "Fremennik shield", "requirements": [{"skill": "Defence", "level": 25}]}, {"id": 10828, "description": "<PERSON><PERSON> of ne<PERSON>znot", "requirements": [{"skill": "Defence", "level": 55}]}, {"id": 10887, "description": "Barrelchest anchor", "requirements": [{"skill": "Attack", "level": 60}, {"skill": "Strength", "level": 40}]}, {"id": 10952, "description": "Slayer bell", "requirements": [{"skill": "Slayer", "level": 39}]}, {"id": 10954, "description": "Frog-leather body", "requirements": [{"skill": "Defence", "level": 25}, {"skill": "Ranged", "level": 25}]}, {"id": 10956, "description": "Frog-leather chaps", "requirements": [{"skill": "Defence", "level": 25}, {"skill": "Ranged", "level": 25}]}, {"id": 10958, "description": "Frog-leather boots", "requirements": [{"skill": "Defence", "level": 25}, {"skill": "Ranged", "level": 25}]}, {"id": 10973, "description": "Light orb", "requirements": [{"skill": "Firemaking", "level": 52}]}, {"id": 11037, "description": "Brine sabre", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 11061, "description": "Ancient mace", "requirements": [{"skill": "Attack", "level": 25}, {"skill": "Prayer", "level": 15}]}, {"id": 11200, "description": "Dwarven helmet", "requirements": [{"skill": "Defence", "level": 50}]}, {"id": 11212, "description": "Dragon arrow", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 11217, "description": "Dragon fire arrows", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 11222, "description": "Dragon fire arrows", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 11227, "description": "Dragon arrow(p)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 11228, "description": "Dragon arrow(p+)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 11229, "description": "Dragon arrow(p++)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 11230, "description": "Dragon dart", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 11231, "description": "Dragon dart(p)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 11233, "description": "Dragon dart(p+)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 11234, "description": "Dragon dart(p++)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 11235, "description": "Dark bow", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 11283, "description": "Dragonfire shield", "requirements": [{"skill": "Defence", "level": 75}]}, {"id": 11284, "description": "Dragonfire shield", "requirements": [{"skill": "Defence", "level": 75}]}, {"id": 11335, "description": "<PERSON> full helm", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 11371, "description": "Steel hasta", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 11373, "description": "<PERSON><PERSON><PERSON> hasta", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 11375, "description": "<PERSON><PERSON> hasta", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 11377, "description": "<PERSON><PERSON> hasta", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 11393, "description": "<PERSON> hasta(p)", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 11395, "description": "Steel hasta(kp)", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 11396, "description": "Steel hasta(p+)", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 11398, "description": "Steel hasta(p++)", "requirements": [{"skill": "Attack", "level": 5}]}, {"id": 11400, "description": "<PERSON><PERSON><PERSON> <PERSON><PERSON>(p)", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 11402, "description": "<PERSON><PERSON><PERSON> hasta(kp)", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 11403, "description": "<PERSON><PERSON><PERSON> hasta(p+)", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 11405, "description": "<PERSON><PERSON><PERSON> hasta(p++)", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 11407, "description": "<PERSON><PERSON> hasta(p)", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 11409, "description": "Adamant hasta(kp)", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 11410, "description": "<PERSON><PERSON> hasta(p+)", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 11412, "description": "<PERSON><PERSON> hasta(p++)", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 11414, "description": "<PERSON><PERSON> hasta(p)", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 11416, "description": "<PERSON><PERSON> hasta(kp)", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 11419, "description": "<PERSON><PERSON> hasta(p++)", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 11663, "description": "Void mage helm", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 11664, "description": "Void ranger helm", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 11665, "description": "Void melee helm", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 11666, "description": "Void seal(8)", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 11667, "description": "Void seal(7)", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 11668, "description": "Void seal(6)", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 11669, "description": "Void seal(5)", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 11670, "description": "Void seal(4)", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 11671, "description": "Void seal(3)", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 11672, "description": "Void seal(2)", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 11673, "description": "Void seal(1)", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 11719, "description": "Rune pickaxe (nz)", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 11720, "description": "Mithril pickaxe (nz)", "requirements": [{"skill": "Attack", "level": 20}]}, {"id": 11748, "description": "New crystal bow (i)", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11749, "description": "Crystal bow full (i)", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11750, "description": "Crystal bow 9/10 (i)", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11751, "description": "Crystal bow 8/10 (i)", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11752, "description": "Crystal bow 7/10 (i)", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11753, "description": "Crystal bow 6/10 (i)", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11754, "description": "Crystal bow 5/10 (i)", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11755, "description": "Crystal bow 4/10 (i)", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11756, "description": "Crystal bow 3/10 (i)", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11757, "description": "Crystal bow 2/10 (i)", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11758, "description": "Crystal bow 1/10 (i)", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11760, "description": "Crystal shield full (i)", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11761, "description": "Crystal shield 9/10 (i)", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11762, "description": "Crystal shield 8/10 (i)", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11763, "description": "Crystal shield 7/10 (i)", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11764, "description": "Crystal shield 6/10 (i)", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11765, "description": "Crystal shield 5/10 (i)", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11766, "description": "Crystal shield 4/10 (i)", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11767, "description": "Crystal shield 3/10 (i)", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11768, "description": "Crystal shield 2/10 (i)", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11769, "description": "Crystal shield 1/10 (i)", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Agility", "level": 50}]}, {"id": 11774, "description": "Black mask (10) (i)", "requirements": [{"skill": "Strength", "level": 20}, {"skill": "Defence", "level": 10}]}, {"id": 11775, "description": "Black mask (9) (i)", "requirements": [{"skill": "Strength", "level": 20}, {"skill": "Defence", "level": 10}]}, {"id": 11776, "description": "Black mask (8) (i)", "requirements": [{"skill": "Strength", "level": 20}, {"skill": "Defence", "level": 10}]}, {"id": 11777, "description": "Black mask (7) (i)", "requirements": [{"skill": "Strength", "level": 20}, {"skill": "Defence", "level": 10}]}, {"id": 11778, "description": "Black mask (6) (i)", "requirements": [{"skill": "Strength", "level": 20}, {"skill": "Defence", "level": 10}]}, {"id": 11779, "description": "Black mask (5) (i)", "requirements": [{"skill": "Strength", "level": 20}, {"skill": "Defence", "level": 10}]}, {"id": 11780, "description": "Black mask (4) (i)", "requirements": [{"skill": "Strength", "level": 20}, {"skill": "Defence", "level": 10}]}, {"id": 11781, "description": "Black mask (3) (i)", "requirements": [{"skill": "Strength", "level": 20}, {"skill": "Defence", "level": 10}]}, {"id": 11782, "description": "Black mask (2) (i)", "requirements": [{"skill": "Strength", "level": 20}, {"skill": "Defence", "level": 10}]}, {"id": 11783, "description": "Black mask (1) (i)", "requirements": [{"skill": "Strength", "level": 20}, {"skill": "Defence", "level": 10}]}, {"id": 11784, "description": "Black mask (i)", "requirements": [{"skill": "Strength", "level": 20}, {"skill": "Defence", "level": 10}]}, {"id": 11785, "description": "Armadyl crossbow", "requirements": [{"skill": "Ranged", "level": 70}]}, {"id": 11787, "description": "Steam battlestaff", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 11789, "description": "Mystic steam staff", "requirements": [{"skill": "Attack", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 11791, "description": "Staff of the dead", "requirements": [{"skill": "Attack", "level": 75}, {"skill": "Magic", "level": 75}]}, {"id": 11802, "description": "Armadyl godsword", "requirements": [{"skill": "Attack", "level": 75}]}, {"id": 11804, "description": "Bandos godsword", "requirements": [{"skill": "Attack", "level": 75}]}, {"id": 11806, "description": "Saradomin godsword", "requirements": [{"skill": "Attack", "level": 75}]}, {"id": 11808, "description": "Zamorak godsword", "requirements": [{"skill": "Attack", "level": 75}]}, {"id": 11824, "description": "Zamorakian spear", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 11826, "description": "Armadyl helmet", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 11828, "description": "Armadyl chestplate", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 11830, "description": "Armadyl chainskirt", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Ranged", "level": 70}]}, {"id": 11832, "description": "Bandos chestplate", "requirements": [{"skill": "Defence", "level": 65}]}, {"id": 11834, "description": "Bandos tassets", "requirements": [{"skill": "Defence", "level": 65}]}, {"id": 11836, "description": "Bandos boots", "requirements": [{"skill": "Defence", "level": 65}]}, {"id": 11838, "description": "Saradomin sword", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 11840, "description": "Dragon boots", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 11864, "description": "Slayer helmet", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 11865, "description": "Slayer helmet (i)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 11875, "description": "Broad bolts", "requirements": [{"skill": "Ranged", "level": 61}, {"skill": "Slayer", "level": 55}]}, {"id": 11889, "description": "Zamorak<PERSON> hasta", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 11893, "description": "Decorative armour", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 11894, "description": "Decorative armour", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 11895, "description": "Decorative armour", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 11896, "description": "Decorative armour", "requirements": [{"skill": "Magic", "level": 20}]}, {"id": 11897, "description": "Decorative armour", "requirements": [{"skill": "Magic", "level": 20}]}, {"id": 11898, "description": "Decorative armour", "requirements": [{"skill": "Magic", "level": 20}]}, {"id": 11899, "description": "Decorative armour", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 11900, "description": "Decorative armour", "requirements": [{"skill": "Ranged", "level": 30}]}, {"id": 11901, "description": "Decorative armour", "requirements": [{"skill": "Ranged", "level": 20}]}, {"id": 11902, "description": "Leaf-bladed sword", "requirements": [{"skill": "Attack", "level": 50}, {"skill": "Slayer", "level": 55}]}, {"id": 11905, "description": "Trident of the seas (full)", "requirements": [{"skill": "Magic", "level": 75}]}, {"id": 11907, "description": "Trident of the seas", "requirements": [{"skill": "Magic", "level": 75}]}, {"id": 11908, "description": "Uncharged trident", "requirements": [{"skill": "Magic", "level": 75}]}, {"id": 11920, "description": "Dragon pickaxe", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 11924, "description": "Malediction ward", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 11926, "description": "Odium ward", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 11959, "description": "Black chinchompa", "requirements": [{"skill": "Ranged", "level": 65}]}, {"id": 11998, "description": "Smoke battlestaff", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 12000, "description": "Mystic smoke staff", "requirements": [{"skill": "Attack", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 12001, "description": "Mystic smoke staff", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 12002, "description": "Occult necklace", "requirements": [{"skill": "Magic", "level": 70}]}, {"id": 12006, "description": "Abyssal tentacle", "requirements": [{"skill": "Attack", "level": 75}]}, {"id": 12193, "description": "Ancient robe top", "requirements": [{"skill": "Prayer", "level": 20}]}, {"id": 12195, "description": "Ancient robe legs", "requirements": [{"skill": "Prayer", "level": 20}]}, {"id": 12197, "description": "Ancient cloak", "requirements": [{"skill": "Prayer", "level": 40}]}, {"id": 12199, "description": "Ancient crozier", "requirements": [{"skill": "Prayer", "level": 60}]}, {"id": 12201, "description": "Ancient stole", "requirements": [{"skill": "Prayer", "level": 60}]}, {"id": 12203, "description": "Ancient mitre", "requirements": [{"skill": "Prayer", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 12253, "description": "Armadyl robe top", "requirements": [{"skill": "Prayer", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 12255, "description": "<PERSON><PERSON><PERSON> robe legs", "requirements": [{"skill": "Prayer", "level": 20}]}, {"id": 12257, "description": "<PERSON><PERSON><PERSON> stole", "requirements": [{"skill": "Prayer", "level": 60}]}, {"id": 12259, "description": "Armadyl mitre", "requirements": [{"skill": "Prayer", "level": 40}]}, {"id": 12261, "description": "<PERSON><PERSON>l cloak", "requirements": [{"skill": "Prayer", "level": 40}]}, {"id": 12263, "description": "Armadyl crozier", "requirements": [{"skill": "Prayer", "level": 60}]}, {"id": 12265, "description": "<PERSON>os robe top", "requirements": [{"skill": "Prayer", "level": 40}]}, {"id": 12267, "description": "Bandos robe legs", "requirements": [{"skill": "Prayer", "level": 20}]}, {"id": 12269, "description": "<PERSON><PERSON> stole", "requirements": [{"skill": "Prayer", "level": 60}]}, {"id": 12271, "description": "Bandos mitre", "requirements": [{"skill": "Prayer", "level": 40}]}, {"id": 12273, "description": "<PERSON><PERSON> cloak", "requirements": [{"skill": "Prayer", "level": 40}]}, {"id": 12275, "description": "Bandos crozier", "requirements": [{"skill": "Prayer", "level": 60}]}, {"id": 12277, "description": "Mithril platebody (g)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 12279, "description": "Mithril platelegs (g)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 12281, "description": "<PERSON><PERSON><PERSON> (g)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 12283, "description": "Mithril full helm (g)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 12285, "description": "Mi<PERSON><PERSON><PERSON> (g)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 12287, "description": "Mithril platebody (t)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 12289, "description": "Mithril platelegs (t)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 12291, "description": "<PERSON><PERSON><PERSON> (t)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 12293, "description": "Mi<PERSON><PERSON> full helm (t)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 12295, "description": "<PERSON><PERSON><PERSON><PERSON> (t)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 12297, "description": "Black pickaxe", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 12327, "description": "Red d'hide body (g)", "requirements": [{"skill": "Ranged", "level": 60}, {"skill": "Defence", "level": 40}]}, {"id": 12329, "description": "Red d'hide chaps (g)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 12331, "description": "Red d'hide body (t)", "requirements": [{"skill": "Ranged", "level": 60}, {"skill": "Defence", "level": 40}]}, {"id": 12333, "description": "Red d'hide chaps (t)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 12357, "description": "<PERSON><PERSON>", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 12373, "description": "Dragon cane", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 12375, "description": "Black cane", "requirements": [{"skill": "Attack", "level": 10}]}, {"id": 12377, "description": "Adamant cane", "requirements": [{"skill": "Attack", "level": 30}]}, {"id": 12379, "description": "Rune cane", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 12381, "description": "Black d'hide body (g)", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 40}]}, {"id": 12383, "description": "Black d'hide chaps (g)", "requirements": [{"skill": "Ranged", "level": 70}]}, {"id": 12385, "description": "Black d'hide body (t)", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 40}]}, {"id": 12387, "description": "Black d'hide chaps (t)", "requirements": [{"skill": "Ranged", "level": 70}]}, {"id": 12389, "description": "Gilded scimitar", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 12391, "description": "Gilded boots", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 12414, "description": "Dragon chainbody (g)", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 12415, "description": "Dragon platelegs (g)", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 12416, "description": "Dragon plateskirt (g)", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 12417, "description": "Dragon full helm (g)", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 12418, "description": "Dragon sq shield (g)", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 12419, "description": "Light infinity hat", "requirements": [{"skill": "Magic", "level": 50}, {"skill": "Defence", "level": 25}]}, {"id": 12420, "description": "Light infinity top", "requirements": [{"skill": "Magic", "level": 50}, {"skill": "Defence", "level": 25}]}, {"id": 12421, "description": "Light infinity bottoms", "requirements": [{"skill": "Magic", "level": 50}, {"skill": "Defence", "level": 25}]}, {"id": 12422, "description": "3rd age wand", "requirements": [{"skill": "Magic", "level": 65}]}, {"id": 12424, "description": "3rd age bow", "requirements": [{"skill": "Ranged", "level": 65}]}, {"id": 12426, "description": "3rd age longsword", "requirements": [{"skill": "Attack", "level": 65}]}, {"id": 12437, "description": "3rd age cloak", "requirements": [{"skill": "Prayer", "level": 65}]}, {"id": 12457, "description": "Dark infinity hat", "requirements": [{"skill": "Magic", "level": 50}, {"skill": "Defence", "level": 25}]}, {"id": 12458, "description": "Dark infinity top", "requirements": [{"skill": "Magic", "level": 50}, {"skill": "Defence", "level": 25}]}, {"id": 12459, "description": "Dark infinity bottoms", "requirements": [{"skill": "Magic", "level": 50}, {"skill": "Defence", "level": 25}]}, {"id": 12460, "description": "Ancient platebody", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 12462, "description": "Ancient platelegs", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 12464, "description": "Ancient plateskirt", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 12466, "description": "Ancient full helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 12468, "description": "Ancient kiteshield", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 12470, "description": "Armadyl platebody", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 12472, "description": "Armadyl platelegs", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 12474, "description": "Armadyl plateskirt", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 12476, "description": "Armadyl full helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 12478, "description": "<PERSON><PERSON><PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 12480, "description": "Bandos platebody", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 12482, "description": "Bandos platelegs", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 12484, "description": "Bandos plateskirt", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 12486, "description": "<PERSON><PERSON> full helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 12488, "description": "<PERSON><PERSON>", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 12490, "description": "Ancient bracers", "requirements": [{"skill": "Defence", "level": 1}, {"skill": "Ranged", "level": 70}]}, {"id": 12492, "description": "Ancient d'hide", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 70}]}, {"id": 12494, "description": "Ancient chaps", "requirements": [{"skill": "Defence", "level": 1}, {"skill": "Ranged", "level": 70}]}, {"id": 12496, "description": "Ancient coif", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 70}]}, {"id": 12498, "description": "Bandos bracers", "requirements": [{"skill": "Defence", "level": 1}, {"skill": "Ranged", "level": 70}]}, {"id": 12500, "description": "Bandos d'hide", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 70}]}, {"id": 12502, "description": "Bandos chaps", "requirements": [{"skill": "Defence", "level": 1}, {"skill": "Ranged", "level": 70}]}, {"id": 12504, "description": "Bandos coif", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 70}]}, {"id": 12506, "description": "Armadyl bracers", "requirements": [{"skill": "Defence", "level": 1}, {"skill": "Ranged", "level": 70}]}, {"id": 12508, "description": "<PERSON><PERSON><PERSON> d'hide", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 70}]}, {"id": 12510, "description": "Armadyl chaps", "requirements": [{"skill": "Defence", "level": 1}, {"skill": "Ranged", "level": 70}]}, {"id": 12512, "description": "Armadyl coif", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 70}]}, {"id": 12596, "description": "Rangers' tunic", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 12598, "description": "Holy sandals", "requirements": [{"skill": "Prayer", "level": 31}]}, {"id": 12658, "description": "<PERSON><PERSON>'s staff (u)", "requirements": [{"skill": "Magic", "level": 50}, {"skill": "Attack", "level": 50}]}, {"id": 12765, "description": "Dark bow", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 12766, "description": "Dark bow", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 12767, "description": "Dark bow", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 12768, "description": "Dark bow", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 12773, "description": "Volcanic abyssal whip", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 12774, "description": "Frozen abyssal whip", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 12788, "description": "Magic shortbow (i)", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 12795, "description": "Steam battlestaff", "requirements": [{"skill": "Magic", "level": 30}, {"skill": "Attack", "level": 30}]}, {"id": 12796, "description": "Mystic steam staff", "requirements": [{"skill": "Magic", "level": 40}, {"skill": "Attack", "level": 40}]}, {"id": 12797, "description": "Dragon pickaxe", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 12806, "description": "Malediction ward", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 12807, "description": "Odium ward", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 12808, "description": "<PERSON>'s blessed sword (full)", "requirements": [{"skill": "Attack", "level": 75}]}, {"id": 12809, "description": "<PERSON><PERSON><PERSON>'s blessed sword", "requirements": [{"skill": "Attack", "level": 75}]}, {"id": 12817, "description": "Elysian spirit shield", "requirements": [{"skill": "Defence", "level": 75}, {"skill": "Prayer", "level": 75}]}, {"id": 12821, "description": "Spectral spirit shield", "requirements": [{"skill": "Defence", "level": 75}, {"skill": "Prayer", "level": 75}]}, {"id": 12825, "description": "Arcane spirit shield", "requirements": [{"skill": "Defence", "level": 75}, {"skill": "Prayer", "level": 75}]}, {"id": 12829, "description": "Spirit shield", "requirements": [{"skill": "Defence", "level": 45}, {"skill": "Prayer", "level": 55}]}, {"id": 12831, "description": "Blessed spirit shield", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Prayer", "level": 60}]}, {"id": 12848, "description": "Granite maul", "requirements": [{"skill": "Attack", "level": 50}, {"skill": "Strength", "level": 50}]}, {"id": 24225, "description": "Granite maul", "requirements": [{"skill": "Attack", "level": 50}, {"skill": "Strength", "level": 50}]}, {"id": 24227, "description": "Granite maul", "requirements": [{"skill": "Attack", "level": 50}, {"skill": "Strength", "level": 50}]}, {"id": 12899, "description": "Trident of the swamp", "requirements": [{"skill": "Magic", "level": 75}]}, {"id": 12900, "description": "Uncharged toxic trident", "requirements": [{"skill": "Magic", "level": 75}]}, {"id": 12902, "description": "Toxic staff (uncharged)", "requirements": [{"skill": "Magic", "level": 75}, {"skill": "Attack", "level": 75}]}, {"id": 12904, "description": "Toxic staff of the dead", "requirements": [{"skill": "Magic", "level": 75}, {"skill": "Attack", "level": 75}]}, {"id": 12926, "description": "Toxic blowpipe", "requirements": [{"skill": "Ranged", "level": 75}]}, {"id": 12931, "description": "Serpentine helm", "requirements": [{"skill": "Defence", "level": 75}]}, {"id": 12954, "description": "Dragon defender", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 13072, "description": "Elite void top", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 13073, "description": "Elite void robe", "requirements": [{"skill": "Attack", "level": 42}, {"skill": "Strength", "level": 42}, {"skill": "Defence", "level": 42}, {"skill": "Hitpoints", "level": 42}, {"skill": "Ranged", "level": 42}, {"skill": "Magic", "level": 42}, {"skill": "Prayer", "level": 22}]}, {"id": 13074, "description": "<PERSON><PERSON><PERSON>'s sceptre (8)", "requirements": [{"skill": "Magic", "level": 30}, {"skill": "Attack", "level": 30}]}, {"id": 13075, "description": "<PERSON><PERSON><PERSON>'s sceptre (7)", "requirements": [{"skill": "Magic", "level": 30}, {"skill": "Attack", "level": 30}]}, {"id": 13076, "description": "<PERSON><PERSON><PERSON>'s sceptre (6)", "requirements": [{"skill": "Magic", "level": 30}, {"skill": "Attack", "level": 30}]}, {"id": 13077, "description": "<PERSON><PERSON><PERSON>'s sceptre (5)", "requirements": [{"skill": "Magic", "level": 30}, {"skill": "Attack", "level": 30}]}, {"id": 13078, "description": "<PERSON><PERSON><PERSON>'s sceptre (4)", "requirements": [{"skill": "Magic", "level": 30}, {"skill": "Attack", "level": 30}]}, {"id": 13080, "description": "New crystal halberd full (i)", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13081, "description": "<PERSON> halberd full (i)", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13082, "description": "<PERSON> halberd 9/10 (i)", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13083, "description": "<PERSON> halberd 8/10 (i)", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13084, "description": "<PERSON> halberd 7/10 (i)", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13085, "description": "<PERSON> halberd 6/10 (i)", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13086, "description": "<PERSON> halberd 5/10 (i)", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13087, "description": "<PERSON> halberd 4/10 (i)", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13088, "description": "<PERSON> halberd 3/10 (i)", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13089, "description": "Crystal halberd 2/10 (i)", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13090, "description": "Crystal halberd 1/10 (i)", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13091, "description": "New crystal halberd full", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13092, "description": "<PERSON> halberd full", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13093, "description": "<PERSON> halberd 9/10", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13094, "description": "<PERSON> halberd 8/10", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13095, "description": "<PERSON> halberd 7/10", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13096, "description": "<PERSON> halberd 6/10", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13097, "description": "<PERSON> halberd 5/10", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13098, "description": "<PERSON> halberd 4/10", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13099, "description": "<PERSON> halberd 3/10", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13100, "description": "<PERSON> halberd 2/10", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13101, "description": "<PERSON> halberd 1/10", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 35}, {"skill": "Agility", "level": 50}]}, {"id": 13105, "description": "Varrock armour 2", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 13106, "description": "Varrock armour 3", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 13107, "description": "Varrock armour 4", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 13108, "description": "Wilderness sword 1", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 13109, "description": "Wilderness sword 2", "requirements": [{"skill": "Attack", "level": 50}]}, {"id": 13110, "description": "Wilderness sword 3", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 13111, "description": "Wilderness sword 4", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 13113, "description": "Morytania legs 2", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 13114, "description": "Morytania legs 3", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 13115, "description": "Morytania legs 4", "requirements": [{"skill": "Defence", "level": 70}]}, {"id": 13118, "description": "Falador shield 2", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13119, "description": "Falador shield 3", "requirements": [{"skill": "Defence", "level": 50}]}, {"id": 13120, "description": "Falador shield 4", "requirements": [{"skill": "Defence", "level": 50}]}, {"id": 13130, "description": "Fremennik sea boots 2", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 13131, "description": "Fremennik sea boots 3", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 13132, "description": "Fremennik sea boots 4", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 13197, "description": "Tanzanite helm", "requirements": [{"skill": "Defence", "level": 75}]}, {"id": 13199, "description": "Magma helm", "requirements": [{"skill": "Defence", "level": 75}]}, {"id": 13235, "description": "Eternal boots", "requirements": [{"skill": "Magic", "level": 75}, {"skill": "Defence", "level": 75}]}, {"id": 13237, "description": "Pegasian boots", "requirements": [{"skill": "Ranged", "level": 75}, {"skill": "Defence", "level": 75}]}, {"id": 13239, "description": "Primordial boots", "requirements": [{"skill": "Defence", "level": 75}, {"skill": "Strength", "level": 75}]}, {"id": 13241, "description": "Infernal axe", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 13242, "description": "Infernal axe (uncharged)", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 13243, "description": "Infernal pickaxe", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 13244, "description": "Infernal pickaxe (uncharged)", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 13258, "description": "Angler hat", "requirements": [{"skill": "Fishing", "level": 34}]}, {"id": 13259, "description": "<PERSON><PERSON> top", "requirements": [{"skill": "Fishing", "level": 34}]}, {"id": 13260, "description": "Angler waders", "requirements": [{"skill": "Fishing", "level": 34}]}, {"id": 13261, "description": "Angler boots", "requirements": [{"skill": "Fishing", "level": 34}]}, {"id": 13263, "description": "Abyssal bludgeon", "requirements": [{"skill": "Attack", "level": 70}, {"skill": "Strength", "level": 70}]}, {"id": 13265, "description": "Abyssal dagger", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 13267, "description": "Abyssal dagger (p)", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 13269, "description": "Abyssal dagger (p+)", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 13271, "description": "Abyssal dagger (p++)", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 13280, "description": "Max <PERSON>", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 13281, "description": "<PERSON> hood", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 13282, "description": "Max <PERSON>", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "<PERSON>", "level": 99}, {"skill": "Construction", "level": 99}]}, {"id": 13329, "description": "Fire max cape", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 13330, "description": "Fire max hood", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 13331, "description": "Saradomin max cape", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 13332, "description": "Saradomin max hood", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 13333, "description": "Zamorak max cape", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 13334, "description": "Zamorak max hood", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 13335, "description": "Guthix max cape", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 13336, "description": "Guthix max hood", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "<PERSON>", "level": 99}, {"skill": "Construction", "level": 99}]}, {"id": 13337, "description": "Accumulator max cape", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 13338, "description": "Accumulator max hood", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 13340, "description": "Agility cape", "requirements": [{"skill": "Agility", "level": 99}]}, {"id": 13341, "description": "Agility cape(t)", "requirements": [{"skill": "Agility", "level": 99}]}, {"id": 13342, "description": "Max <PERSON>", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 13357, "description": "Shayzien gloves (1)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13358, "description": "Shayzien boots (1)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13359, "description": "<PERSON><PERSON><PERSON> helm (1)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13360, "description": "<PERSON><PERSON><PERSON> greaves (1)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13361, "description": "Shayzien platebody (1)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13362, "description": "Shayzien gloves (2)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13363, "description": "Shayzien boots (2)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13364, "description": "<PERSON><PERSON><PERSON> helm (2)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13365, "description": "<PERSON><PERSON><PERSON> greaves (2)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13366, "description": "Shayzien platebody (2)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13367, "description": "Shayzien gloves (3)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13368, "description": "Shayzien boots (3)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13369, "description": "<PERSON><PERSON><PERSON> helm (3)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13370, "description": "<PERSON><PERSON><PERSON>reaves (3)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13371, "description": "Shayzien platebody (3)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13372, "description": "Shayzien gloves (4)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13373, "description": "Shayzien boots (4)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13374, "description": "<PERSON><PERSON><PERSON> helm (4)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13375, "description": "<PERSON><PERSON><PERSON>reaves (4)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13376, "description": "Shayzien platebody (4)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13377, "description": "Shayzien gloves (5)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13378, "description": "Shayzien boots (5)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13379, "description": "<PERSON><PERSON><PERSON> helm (5)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13380, "description": "<PERSON><PERSON><PERSON>reaves (5)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13381, "description": "Shayzien platebody (5)", "requirements": [{"skill": "Defence", "level": 20}]}, {"id": 13385, "description": "Xerician hat", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Magic", "level": 20}]}, {"id": 13387, "description": "Xerician top", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Magic", "level": 20}]}, {"id": 13389, "description": "Xerician robe", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Magic", "level": 20}]}, {"id": 13576, "description": "Dragon warhammer", "requirements": [{"skill": "Strength", "level": 60}]}, {"id": 13652, "description": "Dragon claws", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 19478, "description": "Light ballista", "requirements": [{"skill": "Ranged", "level": 65}]}, {"id": 19481, "description": "Heavy ballista", "requirements": [{"skill": "Ranged", "level": 75}, {"skill": "Defence", "level": 33}]}, {"id": 19484, "description": "Dragon javelin", "requirements": [{"skill": "Ranged", "level": 65}]}, {"id": 19486, "description": "Dragon javelin(p)", "requirements": [{"skill": "Ranged", "level": 65}]}, {"id": 19488, "description": "Dragon javelin(p+)", "requirements": [{"skill": "Ranged", "level": 65}]}, {"id": 19490, "description": "Dragon javelin(p++)", "requirements": [{"skill": "Ranged", "level": 65}]}, {"id": 19544, "description": "Tormented bracelet", "requirements": [{"skill": "Hitpoints", "level": 75}]}, {"id": 19547, "description": "Necklace of anguish", "requirements": [{"skill": "Hitpoints", "level": 75}]}, {"id": 19550, "description": "Ring of suffering", "requirements": [{"skill": "Hitpoints", "level": 75}]}, {"id": 19553, "description": "Amulet of torture", "requirements": [{"skill": "Hitpoints", "level": 75}]}, {"id": 19639, "description": "Black slayer helmet", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 19641, "description": "Black slayer helmet (i)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 19643, "description": "Green slayer helmet", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 19645, "description": "Green slayer helmet (i)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 19647, "description": "Red slayer helmet", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 19649, "description": "Red slayer helmet (i)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 19675, "description": "Arclight", "requirements": [{"skill": "Attack", "level": 75}]}, {"id": 19710, "description": "Ring of suffering (i)", "requirements": [{"skill": "Hitpoints", "level": 75}]}, {"id": 19720, "description": "Occult necklace (or)", "requirements": [{"skill": "Magic", "level": 70}]}, {"id": 19722, "description": "Dragon defender (t)", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 19921, "description": "Ancient d'hide boots", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 40}]}, {"id": 19924, "description": "Bandos d'hide boots", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 40}]}, {"id": 19927, "description": "Guthix d'hide boots", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 40}]}, {"id": 19930, "description": "Arm<PERSON><PERSON> d'hide boots", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 40}]}, {"id": 19933, "description": "Saradomin d'hide boots", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 40}]}, {"id": 19936, "description": "Zamorak d'hide boots", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 40}]}, {"id": 19994, "description": "Ranger gloves", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 19997, "description": "Holy wraps", "requirements": [{"skill": "Prayer", "level": 31}]}, {"id": 20000, "description": "Dragon scimitar (or)", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 20011, "description": "3rd age axe", "requirements": [{"skill": "Attack", "level": 65}]}, {"id": 20014, "description": "3rd age pickaxe", "requirements": [{"skill": "Attack", "level": 65}]}, {"id": 20035, "description": "Samurai kasa", "requirements": [{"skill": "Defence", "level": 35}]}, {"id": 20038, "description": "Samurai shirt", "requirements": [{"skill": "Defence", "level": 35}]}, {"id": 20041, "description": "Samurai gloves", "requirements": [{"skill": "Defence", "level": 35}]}, {"id": 20044, "description": "Samurai greaves", "requirements": [{"skill": "Defence", "level": 35}]}, {"id": 20047, "description": "Samurai boots", "requirements": [{"skill": "Defence", "level": 35}]}, {"id": 20128, "description": "Hood of darkness", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 20}]}, {"id": 20131, "description": "Robe top of darkness", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 20}]}, {"id": 20134, "description": "Gloves of darkness", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 20}]}, {"id": 20137, "description": "Robe bottom of darkness", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 20}]}, {"id": 20140, "description": "Boots of darkness", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 20}]}, {"id": 20146, "description": "Gilded med helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 20149, "description": "Gilded chainbody", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 20152, "description": "Gilded sq shield", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 20155, "description": "Gilded 2h sword", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 20158, "description": "Gilded spear", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 20161, "description": "<PERSON><PERSON> hasta", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 20169, "description": "Steel platebody (g)", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 20172, "description": "Steel platelegs (g)", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 20175, "description": "Steel plateskirt (g)", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 20178, "description": "Steel full helm (g)", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 20181, "description": "Steel kiteshield (g)", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 20184, "description": "Steel platebody (t)", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 20187, "description": "Steel platelegs (t)", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 20190, "description": "Steel plateskirt (t)", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 20193, "description": "<PERSON> full helm (t)", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 20196, "description": "Steel kiteshield (t)", "requirements": [{"skill": "Defence", "level": 5}]}, {"id": 20272, "description": "Cabbage round shield", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 20366, "description": "Amulet of torture (or)", "requirements": [{"skill": "Hitpoints", "level": 75}]}, {"id": 20368, "description": "Armadyl godsword (or)", "requirements": [{"skill": "Attack", "level": 75}]}, {"id": 20370, "description": "Bandos godsword (or)", "requirements": [{"skill": "Attack", "level": 75}]}, {"id": 20372, "description": "Saradomin godsword (or)", "requirements": [{"skill": "Attack", "level": 75}]}, {"id": 20374, "description": "Zamorak godsword (or)", "requirements": [{"skill": "Attack", "level": 75}]}, {"id": 20401, "description": "Yew shortbow", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 20402, "description": "<PERSON><PERSON> scimitar", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 20403, "description": "Maple shortbow", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 20405, "description": "Abyssal whip", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 20406, "description": "Dragon scimitar", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 20407, "description": "Dragon dagger", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 20408, "description": "Dark bow", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 20409, "description": "<PERSON><PERSON>", "requirements": [{"skill": "Magic", "level": 50}]}, {"id": 20415, "description": "Adamant platebody", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 20416, "description": "Adamant platelegs", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 20417, "description": "Blue d'hide body", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 50}]}, {"id": 20418, "description": "Blue d'hide chaps", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 20421, "description": "Rune platebody", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 20422, "description": "Rune platelegs", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 20423, "description": "Black d'hide body", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 70}]}, {"id": 20425, "description": "Mystic robe top", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 20}]}, {"id": 20426, "description": "Mystic robe bottom", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 20}]}, {"id": 20428, "description": "Dragon chainbody", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 20429, "description": "Dragon platelegs", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 20517, "description": "Elder chaos top", "requirements": [{"skill": "Magic", "level": 40}]}, {"id": 20520, "description": "Elder chaos robe", "requirements": [{"skill": "Magic", "level": 40}]}, {"id": 20552, "description": "Rune battleaxe", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 20555, "description": "Rune 2h sword", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 20558, "description": "Magic shortbow", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 20559, "description": "Dragon 2h sword", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 20561, "description": "Adamant full helm", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 20562, "description": "Mystic hat", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 20}]}, {"id": 20563, "description": "Proselyte sallet", "requirements": [{"skill": "Defence", "level": 30}, {"skill": "Prayer", "level": 20}]}, {"id": 20564, "description": "Proselyte hauberk", "requirements": [{"skill": "Defence", "level": 30}, {"skill": "Prayer", "level": 20}]}, {"id": 20565, "description": "Proselyte cuisse", "requirements": [{"skill": "Defence", "level": 30}, {"skill": "Prayer", "level": 20}]}, {"id": 20566, "description": "Red d'hide body", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 60}]}, {"id": 20567, "description": "Red d'hide chaps", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 20568, "description": "<PERSON><PERSON> helm", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Magic", "level": 40}]}, {"id": 20571, "description": "Warrior helm", "requirements": [{"skill": "Defence", "level": 45}]}, {"id": 20572, "description": "<PERSON> helm", "requirements": [{"skill": "Defence", "level": 45}]}, {"id": 20573, "description": "<PERSON><PERSON><PERSON> helm", "requirements": [{"skill": "Defence", "level": 45}]}, {"id": 20574, "description": "Infinity top", "requirements": [{"skill": "Magic", "level": 50}, {"skill": "Defence", "level": 25}]}, {"id": 20575, "description": "Infinity bottoms", "requirements": [{"skill": "Magic", "level": 50}, {"skill": "Defence", "level": 25}]}, {"id": 20576, "description": "3rd age robe top", "requirements": [{"skill": "Defence", "level": 65}, {"skill": "Magic", "level": 30}]}, {"id": 20577, "description": "3rd age robe", "requirements": [{"skill": "Defence", "level": 65}, {"skill": "Magic", "level": 30}]}, {"id": 20579, "description": "Mystic boots", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 20580, "description": "Snakeskin boots", "requirements": [{"skill": "Defence", "level": 30}, {"skill": "Ranged", "level": 30}]}, {"id": 20582, "description": "Adamant gloves", "requirements": [{"skill": "Defence", "level": 13}]}, {"id": 20583, "description": "Rune gloves", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 20593, "description": "Armadyl godsword", "requirements": [{"skill": "Attack", "level": 75}]}, {"id": 20595, "description": "Elder chaos hood", "requirements": [{"skill": "Magic", "level": 40}]}, {"id": 20598, "description": "<PERSON><PERSON>'s robetop", "requirements": [{"skill": "Magic", "level": 70}, {"skill": "Defence", "level": 70}]}, {"id": 20599, "description": "<PERSON><PERSON>'s robeskirt", "requirements": [{"skill": "Magic", "level": 70}, {"skill": "Defence", "level": 70}]}, {"id": 20601, "description": "<PERSON><PERSON>", "requirements": [{"skill": "Attack", "level": 75}, {"skill": "Strength", "level": 75}]}, {"id": 20604, "description": "<PERSON><PERSON>", "requirements": [{"skill": "Magic", "level": 75}]}, {"id": 20655, "description": "Ring of suffering (r)", "requirements": [{"skill": "Hitpoints", "level": 75}]}, {"id": 20657, "description": "Ring of suffering (ri)", "requirements": [{"skill": "Hitpoints", "level": 75}]}, {"id": 20714, "description": "Tome of fire", "requirements": [{"skill": "Magic", "level": 50}]}, {"id": 20716, "description": "Tome of fire (empty)", "requirements": [{"skill": "Magic", "level": 50}]}, {"id": 20727, "description": "Leaf-bladed battleaxe", "requirements": [{"skill": "Attack", "level": 65}, {"skill": "Slayer", "level": 55}]}, {"id": 20730, "description": "Mist battlestaff", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 20733, "description": "Mystic mist staff", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 20736, "description": "Dust battlestaff", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 20739, "description": "Mystic dust staff", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 20756, "description": "Hill giant club", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 20760, "description": "<PERSON><PERSON><PERSON><PERSON> max cape", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 20764, "description": "<PERSON><PERSON><PERSON><PERSON> max hood", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 20782, "description": "Bandos godsword", "requirements": [{"skill": "Attack", "level": 75}]}, {"id": 20784, "description": "Dragon claws", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 20785, "description": "Dragon warhammer", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 20849, "description": "Dragon thrownaxe", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 20997, "description": "Twisted bow", "requirements": [{"skill": "Ranged", "level": 75}]}, {"id": 21000, "description": "Twisted buckler", "requirements": [{"skill": "Ranged", "level": 75}, {"skill": "Defence", "level": 75}]}, {"id": 21003, "description": "Elder maul", "requirements": [{"skill": "Attack", "level": 75}, {"skill": "Strength", "level": 75}]}, {"id": 21006, "description": "Kodai wand", "requirements": [{"skill": "Magic", "level": 75}]}, {"id": 21009, "description": "Dragon sword", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 21012, "description": "Dragon hunter crossbow", "requirements": [{"skill": "Ranged", "level": 65}]}, {"id": 21015, "description": "Dinh's bulwark", "requirements": [{"skill": "Attack", "level": 75}, {"skill": "Defence", "level": 75}]}, {"id": 21018, "description": "Ancestral hat", "requirements": [{"skill": "Magic", "level": 75}, {"skill": "Defence", "level": 65}]}, {"id": 21021, "description": "Ancestral robe top", "requirements": [{"skill": "Magic", "level": 75}, {"skill": "Defence", "level": 65}]}, {"id": 21024, "description": "Ancestral robe bottom", "requirements": [{"skill": "Magic", "level": 75}, {"skill": "Defence", "level": 65}]}, {"id": 21028, "description": "Dragon harpoon", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 21031, "description": "Infernal harpoon", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 21033, "description": "Infernal harpoon (uncharged)", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 21060, "description": "Bandos godsword", "requirements": [{"skill": "Attack", "level": 75}]}, {"id": 21186, "description": "Fire max cape", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "<PERSON>", "level": 99}, {"skill": "Construction", "level": 99}]}, {"id": 21198, "description": "<PERSON><PERSON>ff", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 21200, "description": "Mystic lava staff", "requirements": [{"skill": "Attack", "level": 30}, {"skill": "Magic", "level": 30}]}, {"id": 21205, "description": "Elder maul", "requirements": [{"skill": "Attack", "level": 75}, {"skill": "Strength", "level": 75}]}, {"id": 21206, "description": "Dragon sword", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 21207, "description": "Dragon thrownaxe", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 21255, "description": "Slayer's staff (e)", "requirements": [{"skill": "Magic", "level": 50}, {"skill": "Slayer", "level": 55}]}, {"id": 21264, "description": "Purple slayer helmet", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 21266, "description": "Purple slayer helmet (i)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 21282, "description": "Infernal max hood", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 21284, "description": "Infernal max cape", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "<PERSON>", "level": 99}, {"skill": "Construction", "level": 99}]}, {"id": 21285, "description": "Infernal max cape", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 21298, "description": "Obsidian helmet", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 21301, "description": "Obsidian platebody", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 21304, "description": "Obsidian platelegs", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 21316, "description": "Amethyst broad bolts", "requirements": [{"skill": "Ranged", "level": 61}, {"skill": "Slayer", "level": 65}]}, {"id": 21318, "description": "Amethyst javelin", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 21320, "description": "Amethyst javelin(p)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 21322, "description": "Amethyst javelin(p+)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 21324, "description": "Amethyst javelin(p++)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 21326, "description": "Amethyst arrow", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 21328, "description": "Amethyst fire arrows", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 21330, "description": "Amethyst fire arrows", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21332, "description": "Amethyst arrow(p)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 21334, "description": "Amethyst arrow(p+)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 21336, "description": "Amethyst arrow(p++)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 21633, "description": "Ancient wyvern shield", "requirements": [{"skill": "Magic", "level": 70}, {"skill": "Defence", "level": 75}]}, {"id": 21634, "description": "Ancient wyvern shield", "requirements": [{"skill": "Defence", "level": 75}, {"skill": "Magic", "level": 70}]}, {"id": 21643, "description": "Granite boots", "requirements": [{"skill": "Defence", "level": 50}, {"skill": "Strength", "level": 50}]}, {"id": 21646, "description": "Granite longsword", "requirements": [{"skill": "Attack", "level": 50}, {"skill": "Strength", "level": 50}]}, {"id": 21732, "description": "null", "requirements": [{"skill": "Defence", "level": 50}, {"skill": "Strength", "level": 50}]}, {"id": 21733, "description": "Guardian boots", "requirements": [{"skill": "Defence", "level": 75}]}, {"id": 21736, "description": "Granite gloves", "requirements": [{"skill": "Defence", "level": 50}, {"skill": "Strength", "level": 50}]}, {"id": 21739, "description": "Granite ring", "requirements": [{"skill": "Defence", "level": 50}, {"skill": "Strength", "level": 50}]}, {"id": 21742, "description": "Granite hammer", "requirements": [{"skill": "Attack", "level": 50}, {"skill": "Strength", "level": 50}]}, {"id": 21776, "description": "Imbued saradomin max cape", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 21778, "description": "Imbued saradomin max hood", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 21780, "description": "Imbued zamorak max cape", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 21782, "description": "Imbued zamorak max hood", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 21784, "description": "Imbued guthix max cape", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 21786, "description": "Imbued guthix max hood", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 21791, "description": "Imbued saradomin cape", "requirements": [{"skill": "Magic", "level": 60}]}, {"id": 21793, "description": "Imbued guthix cape", "requirements": [{"skill": "Magic", "level": 60}]}, {"id": 21795, "description": "Imbued zamorak cape", "requirements": [{"skill": "Magic", "level": 60}]}, {"id": 21888, "description": "Turquoise slayer helmet", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 21890, "description": "Turquoise slayer helmet (i)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 21892, "description": "Dragon platebody", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 21895, "description": "Dragon kiteshield", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 21898, "description": "Assembler max cape", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 21900, "description": "Assembler max hood", "requirements": [{"skill": "Attack", "level": 99}, {"skill": "Strength", "level": 99}, {"skill": "Defence", "level": 99}, {"skill": "Ranged", "level": 99}, {"skill": "Prayer", "level": 99}, {"skill": "Magic", "level": 99}, {"skill": "Runecraft", "level": 99}, {"skill": "Hitpoints", "level": 99}, {"skill": "Crafting", "level": 99}, {"skill": "Mining", "level": 99}, {"skill": "<PERSON><PERSON>", "level": 99}, {"skill": "Fishing", "level": 99}, {"skill": "Cooking", "level": 99}, {"skill": "Firemaking", "level": 99}, {"skill": "Woodcutting", "level": 99}, {"skill": "Agility", "level": 99}, {"skill": "Herblore", "level": 99}, {"skill": "Thieving", "level": 99}, {"skill": "Fletching", "level": 99}, {"skill": "Slayer", "level": 99}, {"skill": "Farming", "level": 99}, {"skill": "Construction", "level": 99}, {"skill": "<PERSON>", "level": 99}]}, {"id": 21902, "description": "Dragon crossbow", "requirements": [{"skill": "Ranged", "level": 64}]}, {"id": 21905, "description": "Dragon bolts", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21924, "description": "Dragon bolts (p)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21926, "description": "Dragon bolts (p+)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21928, "description": "Dragon bolts (p++)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21932, "description": "Opal dragon bolts (e)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21934, "description": "Jade dragon bolts (e)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21936, "description": "Pearl dragon bolts (e)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21938, "description": "Topaz dragon bolts (e)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21940, "description": "Sapphire dragon bolts (e)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21942, "description": "Emerald dragon bolts (e)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21944, "description": "Ruby dragon bolts (e)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21946, "description": "Diamond dragon bolts (e)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21948, "description": "Dragonstone dragon bolts (e)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21950, "description": "Onyx dragon bolts (e)", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21955, "description": "Opal dragon bolts", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21957, "description": "Jade dragon bolts", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21959, "description": "Pearl dragon bolts", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21961, "description": "Topaz dragon bolts", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21963, "description": "Sapphire dragon bolts", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21965, "description": "Emerald dragon bolts", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21967, "description": "Ruby dragon bolts", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21969, "description": "Diamond dragon bolts", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21971, "description": "Dragonstone dragon bolts", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 21973, "description": "Onyx dragon bolts", "requirements": [{"skill": "Ranged", "level": 61}]}, {"id": 22002, "description": "Dragonfire ward", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 75}]}, {"id": 22003, "description": "Dragonfire ward", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 75}]}, {"id": 22109, "description": "Ava's assembler", "requirements": [{"skill": "Ranged", "level": 70}, {"skill": "Defence", "level": 40}]}, {"id": 22111, "description": "Dragonbone necklace", "requirements": [{"skill": "Prayer", "level": 80}]}, {"id": 22127, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22129, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22131, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22133, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22135, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22137, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22139, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22141, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22143, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22145, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22147, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22149, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22151, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22153, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22155, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22157, "description": "Adamant kitesh<PERSON>", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22159, "description": "Adamant heraldic helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22161, "description": "Adamant heraldic helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22163, "description": "Adamant heraldic helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22165, "description": "Adamant heraldic helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22167, "description": "Adamant heraldic helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22169, "description": "Adamant heraldic helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22171, "description": "Adamant heraldic helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22173, "description": "Adamant heraldic helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22175, "description": "Adamant heraldic helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22177, "description": "Adamant heraldic helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22179, "description": "Adamant heraldic helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22181, "description": "Adamant heraldic helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22183, "description": "Adamant heraldic helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22185, "description": "Adamant heraldic helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22187, "description": "Adamant heraldic helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22189, "description": "Adamant heraldic helm", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22234, "description": "Dragon boots (g)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22242, "description": "Dragon platebody (g)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22244, "description": "Dragon kiteshield (g)", "requirements": [{"skill": "Defence", "level": 60}]}, {"id": 22249, "description": "Necklace of anguish (or)", "requirements": [{"skill": "Hitpoints", "level": 75}]}, {"id": 22251, "description": "Oak shield", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 22254, "description": "Willow shield", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 22257, "description": "Maple shield", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 22260, "description": "Yew shield", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 22263, "description": "Magic shield", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 22266, "description": "Redwood shield", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 22269, "description": "Hard leather shield", "requirements": [{"skill": "Defence", "level": 10}, {"skill": "Ranged", "level": 20}]}, {"id": 22272, "description": "Snakeskin shield", "requirements": [{"skill": "Defence", "level": 30}, {"skill": "Ranged", "level": 30}]}, {"id": 22275, "description": "Green d'hide shield", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 40}]}, {"id": 22278, "description": "Blue d'hide shield", "requirements": [{"skill": "Ranged", "level": 50}, {"skill": "Defence", "level": 40}]}, {"id": 22281, "description": "Red d'hide shield", "requirements": [{"skill": "Ranged", "level": 60}, {"skill": "Defence", "level": 40}]}, {"id": 22284, "description": "Black d'hide shield", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 70}]}, {"id": 22288, "description": "Trident of the seas (e)", "requirements": [{"skill": "Magic", "level": 75}]}, {"id": 22290, "description": "Uncharged trident (e)", "requirements": [{"skill": "Magic", "level": 75}]}, {"id": 22292, "description": "Trident of the swamp (e)", "requirements": [{"skill": "Magic", "level": 75}]}, {"id": 22294, "description": "Uncharged toxic trident (e)", "requirements": [{"skill": "Magic", "level": 75}]}, {"id": 22296, "description": "Staff of light", "requirements": [{"skill": "Magic", "level": 75}, {"skill": "Attack", "level": 75}]}, {"id": 22322, "description": "Avernic defender", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Attack", "level": 70}]}, {"id": 22323, "description": "Sanguinesti staff", "requirements": [{"skill": "Magic", "level": 82}]}, {"id": 22324, "description": "<PERSON><PERSON><PERSON> rapier", "requirements": [{"skill": "Attack", "level": 80}]}, {"id": 22325, "description": "<PERSON><PERSON><PERSON> of vitur", "requirements": [{"skill": "Attack", "level": 80}, {"skill": "Strength", "level": 90}]}, {"id": 22326, "description": "Justiciar faceguard", "requirements": [{"skill": "Defence", "level": 75}]}, {"id": 22327, "description": "Justiciar chestguard", "requirements": [{"skill": "Defence", "level": 75}]}, {"id": 22328, "description": "Justiciar legguards", "requirements": [{"skill": "Defence", "level": 75}]}, {"id": 22481, "description": "Sanguinesti staff (uncharged)", "requirements": [{"skill": "Magic", "level": 82}]}, {"id": 22486, "description": "<PERSON><PERSON><PERSON> of vitur (uncharged)", "requirements": [{"skill": "Attack", "level": 80}, {"skill": "Strength", "level": 90}]}, {"id": 22542, "description": "<PERSON>ig<PERSON><PERSON>'s chainmace (u)", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 22545, "description": "<PERSON>ig<PERSON><PERSON>'s chainmace", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 22547, "description": "<PERSON><PERSON>'s bow (u)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 22550, "description": "<PERSON><PERSON>'s bow", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 22552, "description": "<PERSON><PERSON><PERSON><PERSON>'s sceptre (u)", "requirements": [{"skill": "Magic", "level": 60}]}, {"id": 22555, "description": "<PERSON><PERSON><PERSON><PERSON>'s sceptre", "requirements": [{"skill": "Magic", "level": 60}]}, {"id": 22664, "description": "<PERSON><PERSON><PERSON> of vitur", "requirements": [{"skill": "Attack", "level": 80}, {"skill": "Strength", "level": 90}]}, {"id": 22731, "description": "Dragon hasta", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 22734, "description": "<PERSON> hasta(p)", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 22737, "description": "Dragon hasta(p+)", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 22740, "description": "Dragon hasta(p++)", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 22804, "description": "Dragon knife", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 22806, "description": "Dragon knife(p)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 22808, "description": "Dragon knife(p+)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 22810, "description": "Dragon knife(p++)", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 22812, "description": "Dragon knife", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 22814, "description": "Dragon knife", "requirements": [{"skill": "Ranged", "level": 60}]}, {"id": 22945, "description": "<PERSON><PERSON>'s blessing 3", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 22947, "description": "<PERSON><PERSON>'s blessing 4", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 22951, "description": "Boots of brimstone", "requirements": [{"skill": "Defence", "level": 70}, {"skill": "Magic", "level": 70}, {"skill": "Ranged", "level": 70}, {"skill": "Slayer", "level": 44}]}, {"id": 22954, "description": "Devout boots", "requirements": [{"skill": "Prayer", "level": 60}]}, {"id": 22978, "description": "Dragon hunter lance", "requirements": [{"skill": "Attack", "level": 70}]}, {"id": 22981, "description": "Ferocious gloves", "requirements": [{"skill": "Attack", "level": 80}, {"skill": "Defence", "level": 80}]}, {"id": 22986, "description": "Bonecrusher necklace", "requirements": [{"skill": "Prayer", "level": 80}]}, {"id": 23047, "description": "Mystic hat (dusk)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 23050, "description": "Mystic robe top (dusk)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 23053, "description": "Mystic robe bottom (dusk)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 23056, "description": "Mystic gloves (dusk)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 23059, "description": "Mystic boots (dusk)", "requirements": [{"skill": "Defence", "level": 20}, {"skill": "Magic", "level": 40}]}, {"id": 23073, "description": "Hydra slayer helmet", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 23075, "description": "Hydra slayer helmet (i)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 23188, "description": "Guthix d'hide shield", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 70}]}, {"id": 23191, "description": "Saradomin d'hide shield", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 70}]}, {"id": 23194, "description": "Zamorak d'hide shield", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 70}]}, {"id": 23197, "description": "Ancient d'hide shield", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 70}]}, {"id": 23200, "description": "Armadyl d'hide shield", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 70}]}, {"id": 23203, "description": "Bandos d'hide shield", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 70}]}, {"id": 23209, "description": "Rune platebody (h1)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 23212, "description": "Rune platebody (h2)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 23215, "description": "Rune platebody (h3)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 23218, "description": "Rune platebody (h4)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 23221, "description": "Rune platebody (h5)", "requirements": [{"skill": "Defence", "level": 40}]}, {"id": 23230, "description": "Rune defender (t)", "requirements": [{"skill": "Attack", "level": 40}, {"skill": "Defence", "level": 40}]}, {"id": 23235, "description": "Tz<PERSON>ar-ket-om (t)", "requirements": [{"skill": "Strength", "level": 60}]}, {"id": 23242, "description": "3rd age plateskirt", "requirements": [{"skill": "Defence", "level": 65}]}, {"id": 23249, "description": "Rangers' tights", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 23258, "description": "Gilded coif", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 40}]}, {"id": 23261, "description": "Gilded d'hide vambs", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 23264, "description": "Gilded d'hide body", "requirements": [{"skill": "Defence", "level": 40}, {"skill": "Ranged", "level": 40}]}, {"id": 23267, "description": "Gilded d'hide chaps", "requirements": [{"skill": "Ranged", "level": 40}]}, {"id": 23276, "description": "Gilded pickaxe", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 23279, "description": "Gilded axe", "requirements": [{"skill": "Attack", "level": 40}]}, {"id": 23336, "description": "3rd age druidic robe top", "requirements": [{"skill": "Defence", "level": 65}, {"skill": "Prayer", "level": 65}]}, {"id": 23339, "description": "3rd age druidic robe bottoms", "requirements": [{"skill": "Defence", "level": 65}, {"skill": "Prayer", "level": 65}]}, {"id": 23342, "description": "3rd age druidic staff", "requirements": [{"skill": "Attack", "level": 65}, {"skill": "Prayer", "level": 65}]}, {"id": 23345, "description": "3rd age druidic cloak", "requirements": [{"skill": "Defence", "level": 65}, {"skill": "Prayer", "level": 65}]}, {"id": 23366, "description": "Black platebody (h1)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 23369, "description": "Black platebody (h2)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 23372, "description": "Black platebody (h3)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 23375, "description": "Black platebody (h4)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 23378, "description": "Black platebody (h5)", "requirements": [{"skill": "Defence", "level": 10}]}, {"id": 23392, "description": "Adamant platebody (h1)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 23395, "description": "Adamant platebody (h2)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 23398, "description": "Adamant platebody (h3)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 23401, "description": "Adamant platebody (h4)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 23404, "description": "Adamant platebody (h5)", "requirements": [{"skill": "Defence", "level": 30}]}, {"id": 23444, "description": "Tormented bracelet (or)", "requirements": [{"skill": "Hitpoints", "level": 75}]}, {"id": 25849, "description": "Amethyst dart", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 25851, "description": "Amethyst dart", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 25855, "description": "Amethyst dart", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 25857, "description": "Amethyst dart", "requirements": [{"skill": "Ranged", "level": 50}]}, {"id": 32001, "description": "<PERSON><PERSON><PERSON>'s sword", "requirements": [{"skill": "Attack", "level": 78}, {"skill": "Strength", "level": 78}]}, {"id": 24417, "description": "Inquisitor's mace", "requirements": [{"skill": "Attack", "level": 80}]}, {"id": 24419, "description": "Inquisitor's great helm", "requirements": [{"skill": "Strength", "level": 70}, {"skill": "Defence", "level": 30}]}, {"id": 24420, "description": "Inquisitor's hauberk", "requirements": [{"skill": "Strength", "level": 70}, {"skill": "Defence", "level": 30}]}, {"id": 24421, "description": "Inquisitor's plateskirt", "requirements": [{"skill": "Strength", "level": 70}, {"skill": "Defence", "level": 30}]}, {"id": 26235, "description": "Zaryte vambraces", "requirements": [{"skill": "Ranged", "level": 80}, {"skill": "Defence", "level": 45}]}, {"id": 25736, "description": "Holy scythe of vitur", "requirements": [{"skill": "Attack", "level": 80}, {"skill": "Strength", "level": 90}]}, {"id": 25738, "description": "Holy scythe of vitur (uncharged)", "requirements": [{"skill": "Attack", "level": 80}, {"skill": "Strength", "level": 90}]}, {"id": 25739, "description": "Sanguine scythe of vitur", "requirements": [{"skill": "Attack", "level": 80}, {"skill": "Strength", "level": 90}]}, {"id": 25741, "description": "Sanguine scythe of vitur (uncharged)", "requirements": [{"skill": "Attack", "level": 80}, {"skill": "Strength", "level": 90}]}, {"id": 25734, "description": "Holy ghrazi rapier", "requirements": [{"skill": "Attack", "level": 80}]}, {"id": 25731, "description": "Holy sanguinesti staff", "requirements": [{"skill": "Magic", "level": 82}]}, {"id": 25733, "description": "Holy sanguinesti staff (uncharged)", "requirements": [{"skill": "Magic", "level": 82}]}, {"id": 22610, "description": "<PERSON>esta's spear", "requirements": [{"skill": "Attack", "level": 78}]}, {"id": 22613, "description": "<PERSON><PERSON>'s longsword", "requirements": [{"skill": "Attack", "level": 78}]}, {"id": 22622, "description": "<PERSON><PERSON><PERSON>'s warhammer", "requirements": [{"skill": "Strength", "level": 78}]}, {"id": 22647, "description": "<PERSON><PERSON><PERSON>'s staff", "requirements": [{"skill": "Magic", "level": 78}]}, {"id": 22634, "description": "<PERSON><PERSON>'s throwing axe", "requirements": [{"skill": "Ranged", "level": 78}]}, {"id": 22636, "description": "<PERSON><PERSON>'s javelin", "requirements": [{"skill": "Ranged", "level": 78}]}, {"id": 27900, "description": "<PERSON>esta's spear", "requirements": [{"skill": "Attack", "level": 78}]}, {"id": 27904, "description": "<PERSON><PERSON>'s longsword", "requirements": [{"skill": "Attack", "level": 78}]}, {"id": 27908, "description": "<PERSON><PERSON><PERSON>'s warhammer", "requirements": [{"skill": "Strength", "level": 78}]}, {"id": 27920, "description": "<PERSON><PERSON><PERSON>'s staff", "requirements": [{"skill": "Magic", "level": 78}]}, {"id": 27912, "description": "<PERSON><PERSON>'s throwing axe", "requirements": [{"skill": "Ranged", "level": 78}]}, {"id": 27916, "description": "<PERSON><PERSON>'s javelin", "requirements": [{"skill": "Ranged", "level": 78}]}, {"id": 27836, "description": "<PERSON><PERSON>'s coif", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 27837, "description": "<PERSON><PERSON>'s leather body", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 27838, "description": "<PERSON><PERSON>'s leather chaps", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 27833, "description": "<PERSON><PERSON><PERSON>'s full helm", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 27834, "description": "<PERSON><PERSON><PERSON>'s platebody", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 27835, "description": "<PERSON><PERSON><PERSON>'s platelegs", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 27831, "description": "Vesta's chainbody", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 27832, "description": "Vesta's plateskirt", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 32169, "description": "<PERSON><PERSON>'s helmet", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 22650, "description": "<PERSON><PERSON><PERSON>'s hood", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 22653, "description": "<PERSON><PERSON><PERSON>'s robe top", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 22656, "description": "<PERSON><PERSON><PERSON>'s robe bottom", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 22638, "description": "<PERSON><PERSON>'s coif", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 22641, "description": "<PERSON><PERSON>'s leather body", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 22644, "description": "<PERSON><PERSON>'s leather chaps", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 22625, "description": "<PERSON><PERSON><PERSON>'s full helm", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 22628, "description": "<PERSON><PERSON><PERSON>'s platebody", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 22631, "description": "<PERSON><PERSON><PERSON>'s platelegs", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 22616, "description": "Vesta's chainbody", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 22619, "description": "Vesta's plateskirt", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 32169, "description": "<PERSON><PERSON>'s helmet", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 22650, "description": "<PERSON><PERSON><PERSON>'s hood", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 22653, "description": "<PERSON><PERSON><PERSON>'s robe top", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 22656, "description": "<PERSON><PERSON><PERSON>'s robe bottom", "requirements": [{"skill": "Defence", "level": 78}]}, {"id": 27610, "description": "Venator bow", "requirements": [{"skill": "Ranged", "level": 80}]}, {"id": 27612, "description": "Venator bow (uncharged)", "requirements": [{"skill": "Ranged", "level": 80}]}, {"id": 27624, "description": "Ancient sceptre", "requirements": [{"skill": "Magic", "level": 70}, {"skill": "Strength", "level": 60}, {"skill": "Attack", "level": 50}]}, {"id": 27626, "description": "Ancient sceptre", "requirements": [{"skill": "Magic", "level": 70}, {"skill": "Strength", "level": 60}, {"skill": "Attack", "level": 50}]}, {"id": 24664, "description": "Twisted ancestral hat", "requirements": [{"skill": "Magic", "level": 75}, {"skill": "Defence", "level": 65}]}, {"id": 24666, "description": "Twisted ancestral robe top", "requirements": [{"skill": "Magic", "level": 75}, {"skill": "Defence", "level": 65}]}, {"id": 24668, "description": "Twisted ancestral robe bottom", "requirements": [{"skill": "Magic", "level": 75}, {"skill": "Defence", "level": 65}]}, {"id": 28260, "description": "Ancient sceptre", "requirements": [{"skill": "Magic", "level": 75}, {"skill": "Strength", "level": 60}, {"skill": "Attack", "level": 50}]}, {"id": 28262, "description": "Ancient sceptre", "requirements": [{"skill": "Magic", "level": 75}, {"skill": "Strength", "level": 60}, {"skill": "Attack", "level": 50}]}, {"id": 28264, "description": "Ancient sceptre", "requirements": [{"skill": "Magic", "level": 75}, {"skill": "Strength", "level": 60}, {"skill": "Attack", "level": 50}]}, {"id": 28266, "description": "Ancient sceptre", "requirements": [{"skill": "Magic", "level": 75}, {"skill": "Strength", "level": 60}, {"skill": "Attack", "level": 50}]}, {"id": 28338, "description": "An axe that manipulates the very lifeforce of those who touch it.", "requirements": [{"skill": "Strength", "level": 80}, {"skill": "Attack", "level": 80}]}, {"id": 29577, "description": "Torn from the hands of ancient demons.", "requirements": [{"skill": "Attack", "level": 60}]}, {"id": 29589, "description": "Silver, blood and crystal smelted in tormenting flames.", "requirements": [{"skill": "Attack", "level": 77}]}, {"id": 29591, "description": "The ashen wood is warm to the touch.", "requirements": [{"skill": "Ranged", "level": 77}]}, {"id": 29594, "description": "Fire and lightning sizzle in the brazier.", "requirements": [{"skill": "Magic", "level": 77}, {"skill": "Attack", "level": 50}]}, {"id": 29796, "description": "Noxious halberd", "requirements": [{"skill": "Attack", "level": 80}]}, {"id": 29801, "description": "Amulet of rancour", "requirements": [{"skill": "Hitpoints", "level": 90}]}, {"id": 29804, "description": "Amulet of rancour (s)", "requirements": [{"skill": "Hitpoints", "level": 90}]}]