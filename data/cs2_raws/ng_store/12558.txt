void script_12558(Widget widget0, int arg1, int arg2, int arg3) {
	int int4;
	int int5;
	Sprite sprite6;
	Sprite sprite7;
	int int8;
	int int9;
	int4 = 0;
	CHILD.createChild(widget0, 3, int4);
	int4 = int4 + 1;
	CHILD.setRGB(0x000000);
	CHILD.setFilled(false);
	CHILD.setPosition(0, 0, 0, 0);
	CHILD.setSize(0, 21, 1, 0);
	int5 = script_12572(arg1, arg2);
	sprite6 = enum('i', 'd', 10015, int5);
	sprite7 = enum('i', 'd', 10015, int5 + 1);
	CHILD.createChild(widget0, 5, int4);
	int4 = int4 + 1;
	CHILD.setPosition(1, 1, 0, 0);
	CHILD.setSize(2, 19, 1, 0);
	CHILD.setSprite(sprite7);
	CHILD.createChild(widget0, 5, int4);
	int4 = int4 + 1;
	CHILD.setPosition(1, 1, 0, 0);
	CHILD.setSize(0, 19, 0, 0);
	CHILD.setSprite(sprite6);
	if (arg3 == 1) {
		arg1 = VARP[4205];
		if (arg1 < 1) {
			arg1 = 0;
		}
		if (arg1 > 100) {
			arg1 = 100;
		}
		int8 = widget0.getWidth();
		int9 = arg1 * int8 / 100;
		CHILD.hookFrame(&script_12571(CTX_WIDGET, int9));
	} else {
		CHILD.hookFrame(null);
	}
	return;
}