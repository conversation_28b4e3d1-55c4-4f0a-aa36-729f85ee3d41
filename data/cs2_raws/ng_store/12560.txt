void script_12560(int arg0, Widget widget1, string string2, string string3, string string4) {
	int int5;
	int int6;
	int int7;
	int int8;
	Color color9;
	int int10;
	int5 = 0;
	int6 = 0;
	int7 = 140;
	int8 = 110;
	color9 = 0x726451;
	int10 = int5;
	int5 = script_5288(widget1, int5, 0, 0, int7, widget1.getHeight() - 4, 3712, color9);
	CHILD.hookMouseHover(&script_244(CTX_WIDGET, CTX_WIDGET_CHILD, 12099970, -1));
	CHILD.hookMouseExit(&script_244(CTX_WIDGET, CTX_WIDGET_CHILD, color9, -1));
	if (CHILD.setChild(widget1, int10) == true) {
		CHILD.hookMouseHover(&script_85(CTX_WIDGET, CTX_WIDGET_CHILD, 11965282));
		CHILD.hookMouseExit(&script_85(CTX_WIDGET, CTX_WIDGET_CHILD, 7496785));
	}
	CHILD.createChild(widget1, 5, int5);
	int5 = int5 + 1;
	CHILD.setPosition(0, 0, 0, 0);
	CHILD.setSize(4, 4, 1, 1);
	CHILD.setSprite(897);
	CHILD.setSpriteTiling(true);
	CHILD.setTrans(255);
	CHILD.createChild(widget1, 3, int5);
	int5 = int5 + 1;
	CHILD.setPosition(2, 2, 0, 0);
	CHILD.setSize(4, 4, 1, 1);
	CHILD.setRGB(0x2E2B22);
	CHILD.setFilled(true);
	CHILD.osrs_cs2method1125(2);
	CHILD.osrs_cs2method1124(255);
	CHILD.setTrans(128);
	CHILD.setContextMenuOption(1, "Buy");
	CHILD.setOptionBase(string2);
	CHILD.createChild(widget1, 5, int5);
	int5 = int5 + 1;
	CHILD.setSize(74, 47, 0, 0);
	CHILD.setPosition(20, 10, 0, 0);
	CHILD.setSprite(5256);
	CHILD.createChild(widget1, 4, int5);
	int5 = int5 + 1;
	CHILD.setPosition(0, 20, 0, 2);
	CHILD.setSize(0, 54, 1, 0);
	CHILD.setFont(1447);
	CHILD.setRGB(0xFF981F);
	CHILD.setTextAntiMacro(true);
	CHILD.setText(string2);
	CHILD.setTextAlignment(1, 1, 16);
	CHILD.createChild(widget1, 4, int5);
	int5 = int5 + 1;
	CHILD.setPosition(0, 20, 0, 2);
	CHILD.setSize(0, 24, 1, 0);
	CHILD.setFont(1442);
	CHILD.setRGB(0x8F9A68);
	CHILD.setTextAntiMacro(true);
	CHILD.setText(string3);
	CHILD.setTextAlignment(1, 1, 12);
	CHILD.createChild(widget1, 4, int5);
	int5 = int5 + 1;
	CHILD.setPosition(0, 12, 0, 2);
	CHILD.setSize(0, 14, 1, 0);
	CHILD.setFont(1445);
	CHILD.setRGB(0x9F9F9F);
	CHILD.setTextAntiMacro(true);
	CHILD.setText(string4);
	CHILD.setTextAlignment(1, 1, 14);
	return;
}