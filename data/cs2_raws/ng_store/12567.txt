void script_12567(Widget widget0, int arg1) {
	int int2;
	Sprite sprite3;
	widget0.clearChildren();
	CHILD.createChild(widget0, 5, 0);
	CHILD.setSize(21, 21, 0, 0);
	CHILD.setPosition(0, 0, 0, 0);
	int2 = 2522;
	sprite3 = 2521;
	CHILD.setSprite(sprite3);
	CHILD.hookMouseHover(&script_229(CTX_WIDGET, CTX_WIDGET_CHILD, (Sprite)int2, -1));
	CHILD.hookMouseExit(&script_229(CTX_WIDGET, CTX_WIDGET_CHILD, sprite3, -1));
	widget0.setContextMenuOption(1, "Show Info");
	widget0.hookOptionClick(&script_12568(CTX_WIDGET, widget(arg1)));
	return;
}