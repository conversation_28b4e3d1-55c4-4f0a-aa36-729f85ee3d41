[
{
    //Pest control costs
    "id": 2286,
    "keyType": "int",
    "valType": "int",
    "defaultInt": -1,
    "values": {
      "0": 1,
      "1": 10,
      "2": 100,
      "3": 1,
      "4": 10,
      "5": 100,
      "6": 1,
      "7": 10,
      "8": 100,
      "9": 1,
	  "10": 10,
      "11": 100,
      "12": 1,
      "13": 10,
      "14": 100,
      "15": 1,
      "16": 10,
      "17": 100,
      "18": 1,
	  "19": 10,
      "20": 100,
      "21": 30,
      "22": 15,
      "23": 15,
      "24": 50,
      "25": 50,
      "26": 50,
      "27": 30,
	  "28": 40,
      "29": 40,
      "30": 40,
      "31": 2
    }
  },
  {
    //Slayer rewards cost.
    "id": 842,
    "keyType": "obj",
    "valType": "int",
    "defaultInt": -1,
    "values": {
      "4160": 35,
      "11866": 75,
      "13226": 750,
      "11875": 35,
      "12791": 1250,
      "10551": 400,
      "12863": 500,
      "10887": 200,
      "11738": 10,
      "4081": 40,
	  "13379": 40,
	  "13381": 40,
	  "13380": 40,
	  "13377": 40,
	  "13378": 40,
	  "6714": 200,
	  "10581": 150,
	  "2952": 100,
	  "10587": 40
    }
  },
  {
    //Slayer rewards Info.
    "id": 843,
    "keyType": "obj",
    "valType": "string",
    "defaultString": "You may exchange your points for this item.",
    "values": {
      "4160": "Arrows that can pierce the hides of creatures such as Turoth and Kurasks. Levels 55 Slayer and 50 Ranged required, and a magic bow or better.",
      "11866": "A wieldable ring that can check your task progress. It has 8 charges for teleporting to useful Slayer sites.",
      "13226": "The herb sack has the ability to store up to 30 of each major grimy herb. Requires 58 Herblore to use.",
      "11875": "Crossbow bolts that can pierce the hides of creatures such as Turoth and Kurasks. Levels 55 Slayer and 61 Ranged required, and a runite crossbow or better.",
      "12791": "The rune pouch has the ability to store up to 16,000 runes of 3 types.",
      "10551": "A torso worn by penance fighters. Requires level 40 Defence.",
      "12863": "A powerful ranging device that fires metal balls.",
      "10887": "An anchor used by the Barrelchest. Requires level 60 Attack & 40 Strength.",
      "11738": "A herb box containing an assortment of random herbs, giving ten herbs per box.",
      "4081": "An amulet which increases wthe wearer's strength and accuracy by 15% when fighting the undead.",
	  "13379": "A helmet from the Shayzien guards, used for protection against lizardmen.",
	  "13381": "A platebody from the Shayzien guards, used for protection against lizardmen.",
	  "13380": "Some greaves from the Shayzien guards, used for protection against lizardmen.",
	  "13377": "Some gloves from the Shayzien guards, used for protection against lizardmen.",
	  "13378": "Some boots from the Shayzien guards, used for protection against lizardmen.",
	  "6714": "A shining paragon of wrenchly virtue.",
	  "2952": "A silver dagger that can prevent werewolves from changing form.",
	  "10581": "A sharp mystical dagger that can penetrate through Kalphite chitin.",
	  "10587": "Tarn Razorlor's diary, used for enchanting salve amulets."
    }
  },
  {
    //Quest list.
    "id": 2098,
    "keyType": "int",
    "valType": "struct",
    "defaultInt": -1,
    "values": {
      "0": 299
    }
  },
  {
	//Achievement diary pointers.
    "id": 2500,
    "keyType": "int",
    "valType": "enum",
    "defaultInt": -1,
    "values": {
      "0": 2501,
      "1": 2502,
      "2": 2503,
      "3": 2504,
      "4": 2505,
      "5": 2506,
      "6": 2507,
      "7": 2508,
      "8": 2509,
      "9": 2510,
      "10": 2511,
      "11": 2512
    }
  },
  {
    //tournament equipment slots components
    "id": 2520,
    "keyType": "int",
    "valType": "component",
    "defaultInt": -1,
    "values": {
      "0": 46333965,
      "1": 46333966,
      "2": 46333967,
      "3": 46333968,
      "4": 46333969,
      "5": 46333970,
      "7": 46333971,
      "9": 46333972,
      "10": 46333973,
      "12": 46333974,
      "13": 46333975
  }
  },
  {
    //tournament skills components
    "id": 2521,
    "keyType": "int",
    "valType": "component",
    "defaultInt": -1,
    "values": {
      "0": 46333977,
      "1": 46333978,
      "2": 46333979,
      "3": 46333980,
      "4": 46333981,
      "5": 46333982,
      "6": 46333983
    }
  },
  {
    //tournament supplies
    "id": 2522,
    "keyType": "int",
    "valType": "namedobj",
    "defaultInt": -1,
    "values": {
      "0": 565,
      "1": 560,
      "2": 9075,
      "3": 557,
      "4": 555,
      "5": 562,
      "6": 566,
      "7": 554,
      "8": 556,
      "9": 561,
      "10": 563,
      "11": 564,
      "12": 21880,
      "13": 3144,
      "14": 385,
      "15": 391,
      "16": 397,
      "17": 13441,
      "18": 11936,
      "19": 6685,
      "20": 10925,
      "21": 3024,
      "22": 2434,
      "23": 2440,
      "24": 2442,
      "25": 2436,
      "26": 12695,
      "27": 2444,
      "28": 3040,
      "29": 4417,
      "30": 11090,
      "31": 2550,
      "32": 5698,
      "33": 4153,
      "34": 10887,
      "35": 11802,
      "36": 20784
    }
  },
  {
	//Slayer rewards enum.
    "id": 840,
    "keyType": "int",
    "valType": "namedobj",
    "defaultInt": -1,
    "values": {
		"0": 11866,
		"1": 11875,
		"2": 4160,
		"3": 13226,
		"4": 12791,
		"5": 10551,
		"6": 12863,
		"7": 10887,
		"8": 11738,
		"9": 4081,
		"10": 13379,
		"11": 13381,
		"12": 13380,
		"13": 13377,
		"14": 13378,
		"15": 6714,
		"16": 10581,
		"17": 2952,
		"18": 10587
	}
  },
  {
    "id": 1904,
    "keyType": "int",
    "valType": "boolean",
    "defaultInt": 0,
    "values": {
      "4": 0,
      "5": 1,
      "6": 1,
      "7": 1,
      "8": 1,
      "9": 1,
      "10": 1,
      "11": 1,
      "12": 1,
      "13": 0
    }
  },
  {
	//Slayer rewards quantities.
    "id": 841,
    "keyType": "obj",
    "valType": "int",
    "defaultInt": -1,
    "values": {
		"4160": 250,
		"11875": 250
	}
  },
  {
    //Skill_id : Skill_sprite
    "id": 2513,
    "keyType": "int",
    "valType": "int",
    "defaultInt": -1,
    "values": {
      "0": 197,
      "1": 199,
      "2": 198,
      "3": 203,
      "4": 200,
      "5": 201,
      "6": 202,
      "7": 212,
      "8": 214,
      "9": 208,
      "10": 211,
      "11": 213,
      "12": 207,
      "13": 210,
      "14": 209,
      "15": 205,
      "16": 204,
      "17": 206,
      "18": 216,
      "19": 217,
      "20": 215,
      "21": 220,
      "22": 221
    }
  },
    {
	//Emote names
    "id": 1000,
    "keyType": "int",
    "valType": "string",
    "defaultString": "Emote",
    "values": {
      "0": "Yes",
      "1": "No",
      "2": "Bow",
      "3": "Angry",
      "4": "Think",
      "5": "Wave",
      "6": "Shrug",
      "7": "Cheer",
      "8": "Beckon",
      "9": "Laugh",
      "10": "Jump for Joy",
      "11": "Yawn",
      "12": "Dance",
      "13": "Jig",
      "14": "Spin",
      "15": "Headbang",
      "16": "Cry",
      "17": "Blow Kiss",
      "18": "Panic",
      "19": "Raspberry",
      "20": "Clap",
      "21": "Salute",
      "22": "Goblin Bow",
      "23": "Goblin Salute",
      "24": "Glass Box",
      "25": "Climb Rope",
      "26": "Lean",
      "27": "Glass Wall",
      "28": "Idea",
      "29": "Stamp",
      "30": "Flap",
      "31": "Slap Head",
      "32": "Zombie Walk",
      "33": "Zombie Dance",
      "34": "Scared",
      "35": "Rabbit Hop",
      "36": "Sit up",
      "37": "Push up",
      "38": "Star jump",
      "39": "Jog",
      "40": "Zombie Hand",
      "41": "Hypermobile Drinker",
      "42": "Skill Cape",
      "43": "Air Guitar",
      "44": "Uri transform",
      "45": "Smooth dance",
      "46": "Crazy dance",
      "47": "Premier Shield",
	  "48": "Trick",
	  "49": "Give Thanks",
	  "50": "Snowman Dance",
	  "51": "Freeze",
	  "52": "Dramatic Point"
    }
  },
  {
  //Emote lit sprite ids
    "id": 1001,
    "keyType": "int",
    "valType": "graphic",
    "defaultInt": -1,
    "values": {
      "0": "700",
      "1": "701",
      "2": "703",
      "3": "704",
      "4": "702",
      "5": "708",
      "6": "720",
      "7": "707",
      "8": "709",
      "9": "706",
      "10": "716",
      "11": "718",
      "12": "710",
      "13": "713",
      "14": "714",
      "15": "715",
      "16": "705",
      "17": "721",
      "18": "712",
      "19": "717",
      "20": "711",
      "21": "719",
      "22": "726",
      "23": "727",
      "24": "722",
      "25": "723",
      "26": "724",
      "27": "725",
      "28": "732",
      "29": "730",
      "30": "731",
      "31": "729",
      "32": "733",
      "33": "734",
      "34": "728",
      "35": "735",
      "36": "1202",
      "37": "1204",
      "38": "1203",
      "39": "739",
      "40": "737",
      "41": "1205",
      "42": "736",
      "43": "738",
      "44": "1350",
      "45": "1351",
      "46": "1352",
      "47": "1353",
	  "48": "2507",
	  "49": "2509",
	  "50": "2511",
	  "51": "2513",
	  "52": "2515"
    }
  },
  {
  //Emote unlit sprite ids
    "id": 1002,
    "keyType": "int",
    "valType": "graphic",
    "defaultInt": -1,
    "values": {
      "22": "746",
      "23": "747",
      "24": "742",
      "25": "743",
      "26": "744",
      "27": "745",
      "28": "752",
      "29": "750",
      "30": "751",
      "31": "749",
      "32": "753",
      "33": "754",
      "34": "748",
      "35": "755",
      "36": "1206",
      "37": "1208",
      "38": "1207",
      "39": "759",
      "40": "757",
      "41": "1209",
      "42": "756",
      "43": "758",
      "44": "1354",
      "45": "1355",
      "46": "1356",
      "47": "1357",
	  "48": "2508",
	  "49": "2510",
	  "50": "2512",
	  "51": "2514",
	  "52": "2516"
    }
  },
  {
  //Django item retrieval
    "id": 708,
    "keyType": "int",
    "valType": "namedobj",
    "values": {
      "0": "9921",
      "1": "9922",
      "2": "9923",
      "3": "9924",
      "4": "9925",
      "5": "30102",
      "6": "30103",
      "7": "30104",
      "8": "30105"
    }
  }
]