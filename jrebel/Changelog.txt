********************************************
  JRebel Changelog
********************************************

2024.4.2 (3rd December 2024)

Feature: added support for GlassFish 7.0.19.
Feature: added support for Jetty 12.0.15.
Feature: added support for Tomcat 11.0.1.
Feature: added support for WebSphere Liberty Profile 24.0.0.11.
Feature: added support for Byte Buddy 1.15.10.
Feature: added support for Camel 4.8.1.
Feature: added support for Groovy 4.0.24.
Feature: added support for Helidon 4.1.4.
Feature: added support for Hibernate ORM 6.6.3.Final.
Feature: added support for Jackson 2.18.1.
Feature: added support for Logback 1.5.12.
Feature: added support for RESTEasy 6.2.11.Final.
Feature: added support for Spring Boot 3.4.0.
Feature: added support for Spring Framework 6.2.0.
Feature: added support for Spring Integration 6.3.5.
Feature: added support for Spring Security 6.3.4.
Feature: added support for springdoc-openapi v2.6.0.
Feature: added support for Swagger 2.2.26.
Feature: added support for Vaadin 24.5.4.
Feature: added hotswapper integration to Vaadin 24 series.
Improvement: improved support for WildFly 34.0.0.Final.
Bug fix: fixed an issue with the “Email verification pending” message appearing under the “Team evaluation or commercial license” tab instead of the “Individual trial” tab.

2024.4.1 (5th November 2024)

Feature: added support for GlassFish 7.0.18.
Feature: added support for Liferay 7.4.3.125.
Feature: added support for Payara 6.2024.10.
Feature: added support for Tomcat 11.0.0.
Feature: added support for tc Server 10.1.30.
Feature: added support for WebSphere Liberty Profile 24.0.0.10.
Feature: added support for WildFly 34.0.0.Final.
Feature: added support for Byte Buddy 1.15.5.
Feature: added support for Grails 6.2.1.
Feature: added support for Helidon 4.1.2.
Feature: added support for Hibernate ORM 6.6.1.Final.
Feature: added support for Jackson 2.18.0.
Feature: added support for Jersey 3.1.9.
Feature: added support for Kotlin 2.0.21.
Feature: added support for Logback 1.5.11.
Feature: added support for Log4j 2.24.1.
Feature: added support for Mockito 5.14.2.
Feature: added support for Scala 3.5.2.
Feature: added support for Spring Boot 3.3.4.
Feature: added support for Spring Framework 6.1.14.
Feature: added support for Spring Integration 6.3.4.
Feature: added support for Swagger 2.2.25.
Feature: added support for Struts 6.6.1.
Feature: added support for Vaadin 24.5.0.
Improvement: improved the reload speed for Hibernate ORM integrations.
Bug fix: fixed an issue with running JRebel with a NetBeans Platform application.
Bug fix: fixed a startup issue that occurs when JRebel Remoting is used in conjunction with another Java agent that triggers class transformation.

2024.4.0 (1st October 2024)

Feature: added support for Java 23.
Feature: added support for GlassFish 7.0.17.
Feature: added support for Jetty 12.0.13.
Feature: added support for Payara 6.2024.9.
Feature: added support for Tomcat 10.1.29.
Feature: added support for tc Server 5.0.15.
Feature: added support for WildFly 33.0.1.Final.
Feature: added support for WebSphere Liberty Profile *********.
Feature: added support for Camel 4.8.0.
Feature: added support for Byte Buddy 1.15.1.
Feature: added support for EclipseLink 5.0.0-B03.
Feature: added support for Grails 6.20.
Feature: added support for Groovy 5.0.0-alpha-10.
Feature: added support for Helidon 4.1.1.
Feature: added support for Hibernate ORM 6.2.31.Final.
Feature: added support for Jackson 2.18.0-RC1.
Feature: added support for Jersey 2.45.
Feature: added support for Kotlin 2.0.20.
Feature: added support for Logback 1.5.8.
Feature: added support for Log4j 2.24.0.
Feature: added support for Mockito 5.13.0.
Feature: added support for RESTEasy 7.0.0.Alpha3.
Feature: added support for Scala 2.13.15-M1.
Feature: added support for Spring Boot 3.3.3.
Feature: added support for Spring Framework 6.1.13.
Feature: added support for Spring Integration 6.3.3.
Feature: added support for Spring Security 6.3.3.
Feature: added support for Swagger 2.2.23.
Feature: added support for Weld 5.1.3.Final.
Feature: added support for Vaadin 24.4.12.
Bug fix: fixed an issue with the Kotlin output path directory not being added to the rebel.xml file for the Gradle project. 
Bug fix: fixed a startup issue when using the Spring Boot Actuator without Spring MVC.
Bug fix: fixed a regression issue with JBoss that could cause ClassCastException during startup.
Bug fix: fixed a debugging issue with the JRebel plugin when running on Java 21.
Bug fix: fixed an integration issue where static fields could not be accessed on a class after it was reloaded from a different class loader.

2024.3.2 (3rd September 2024)

Feature: added support for Jetty 12.0.12.
Feature: added support for Payara 6.2024.8.
Feature: added support for WebSphere Liberty Profile *********.
Feature: added support for CXF 4.0.5.
Feature: added support for Camel 4.0.6.
Feature: added support for EclipseLink 4.0.4.
Feature: added support for Helidon 4.1.0.
Feature: added support for Hibernate ORM 6.6.0.Final.
Feature: added support for Jersey 3.1.8.
Feature: added support for Kotlin 2.0.10.
Feature: added support for Metro 4.0.4.
Feature: added support for MyBatis 3.0.4.
Feature: added support for Spring Boot 3.3.2.
Feature: added support for Scala 3.5.0.
Feature: added support for Spring Framework 6.1.12.
Feature: added support for Struts 6.6.0.
Feature: added support for Vaadin 24.4.8.
Bug fix: fixed an integration issue with OpenEJB-s TempClassLoader that would cause NoClassDefFoundError during startup.

2024.3.1 (6th August 2024)

Feature: added support for Jetty 12.0.11.
Feature: added support for Payara Micro 6.2024.7.
Feature: added support for Tomcat 10.1.26.
Feature: added support for WebSphere Liberty Profile *********.
Feature: added support for WildFly 33.0.0.
Feature: added support for Byte Buddy 1.14.18.
Feature: added support for Camel 4.7.0.
Feature: added support for Grails 4.1.4.
Feature: added support for Groovy 4.0.22.
Feature: added support for Jackson 2.17.2.
Feature: added support for Hibernate ORM 6.2.28.Final.
Feature: added support for Scala 3.4.2.
Feature: added support for Spring Boot 3.3.1.
Feature: added support for Spring Framework 6.1.11.
Feature: added support for Spring Integration 6.3.2.
Feature: added support for OpenJ9 0.44.0 (Java 8, 11, 17 and 21).
Feature: added support for Vaadin 24.4.6.
Bug fix: fixed a memory leak in the JRebel HK2 plugin.
Bug fix: fixed an integration issue with WebLogic where JRebel internal class ExtraPaths cannot be found.

2024.3.0 (2nd July 2024)

Feature: added support for Spring Tools 4.23.1.RELEASE.
Feature: added support for VS Code 1.90.2.
Feature: added support for MyEclipse 2024.1.0.
Feature: added support for IntelliJ 2024.1.3.
Feature: added support for Eclipse 2024-06.
Feature: added support for NetBeans 22.
Feature: added support for GlassFish 7.0.15.
Feature: added support for Jetty 12.0.10.
Feature: added support for Wildfly 32.0.1.Final.
Feature: added support for Payara 6.2024.6.
Feature: added support for WLP *********.
Feature: added support for ByteBuddy 1.14.17.
Feature: added support for EclipseLink 4.0.3.
Feature: added support for FreeMarker 2.3.33.
Feature: added support for Hibernate ORM 6.5.2.Final.
Feature: added support for Jersey 3.1.7.
Feature: added support for Kotlin 2.0.0.
Feature: added support for RestEasy 6.2.9.Final.
Feature: added support for PayaraMicro 6.2024.6.
Feature: added support for Spring Framework 6.1.9.
Feature: added support for Spring Integration 6.3.1.
Feature: added support for Spring Security 6.3.1
Feature: added support for Vaadin 24.4.3
Bug fix: Fixed a potential NoClassDefFoundError from the Spring plugin in OSGI environment
Bug fix: Fixed a UTF-8 resource bundle reloading issue with Java 9 and newer.
Bug fix: Fixed a performance issue on reloading on Jetty 10.

2024.2.2 (4th June 2024)

Feature: added support for Jetty 12.0.9.
Feature: added support for Payara Micro 6.2024.5.
Feature: added support for Tomcat 10.1.24.
Feature: added support for WebSphere Liberty Profile *********.
Feature: added support for WildFly 32.0.0.Final.
Feature: added support for AspectJ ********.
Feature: added support for Byte Buddy 1.14.15.
Feature: added support for Camel 4.6.0.
Feature: added support for EclipseLink 2.7.15-RC1.
Feature: added support for Helidon 4.0.8.
Feature: added support for Hibernate ORM 6.5.1.Final.
Feature: added support for Jackson 2.17.1.
Feature: added support for Jersey 2.43.
Feature: added support for Kotlin 2.0.0-RC3.
Feature: added support for Logback 1.5.6
Feature: added support for Mockito 5.12.0.
Feature: added support for MyFaces 4.1.0-RC2.
Feature: added support for Scala 2.13.14.
Feature: added support for Spring Boot 3.2.5.
Feature: added support for Spring Integration 6.2.4.
Feature: added support for Struts 6.4.0.
Feature: added support for Swagger 2.2.22.
Feature: added support for Vaadin 24.3.12.

2024.2.1 (7th May 2024)

Feature: added support for GlassFish 7.0.14.
Feature: added support for Jetty 12.0.8.
Feature: added support for Liferay *********-ga112.
Feature: added support for Payara 6.2024.4.
Feature: added support for Tomcat 10.1.20.
Feature: added support for TomEE 9.1.3.
Feature: added support for tc Server 5.0.11.
Feature: added support for WebSphere Liberty Profile 24.0.0.03.
Feature: added support for WildFly 32.0.0.Final.
Feature: added support for AspectJ 1.9.22.
Feature: added support for Byte Buddy 1.14.13.
Feature: added support for CXF 4.0.4.
Feature: added support for Camel 4.5.0.
Feature: added support for Grails 6.2.0.
Feature: added support for Groovy 4.0.21.
Feature: added support for HK2 4.0.0-M2.
Feature: added support for Helidon 4.0.7.
Feature: added support for Hibernate ORM 6.5.0.CR2.
Feature: added support for Jackson 2.16.2.
Feature: added support for Jersey 4.0.0-M1.
Feature: added support for Logback 1.5.5.
Feature: added support for Kotlin 2.0.0-RC1.
Feature: added support for RESTEasy 6.2.8.Final.
Feature: added support for MyBatis Spring 3.5.16.
Feature: added support for Spring Boot 3.2.4.
Feature: added support for Spring Framework 6.1.6.
Feature: added support for Spring Integration 6.2.3.
Feature: added support for Spring Security 6.2.4.
Feature: added support for Swagger 2.2.21.
Feature: added support for Vaadin 24.3.9.
Feature: added support for DeltaSpike 2.0.0.
Bug fix: fixed an issue with Spring Boot that could cause all the objects configured using the application.properties file to refresh during reload.

2024.2.0 (2nd April 2024)

Feature: added support for Java 22.
Feature: added support for Jetty 12.0.7.
Feature: added support for WebSphere Liberty Profile *********.
Feature: added support for tc Server 5.0.10.
Feature: added support for Byte Buddy 1.14.12.
Feature: added support for Camel 4.4.0.
Feature: added support for Eclipse Krazo 3.0.1.
Feature: added support for Eclipse Vert.x 4.5.7.
Feature: added support for Groovy 4.0.19.
Feature: added support for HK2 3.1.0.
Feature: added support for Jackson 2.17.0.
Feature: added support for Log4j 2.23.1.
Feature: added support for Logback 1.5.3.
Feature: added support for Mockito 5.11.0.
Feature: added support for Scala 2.13.13.
Feature: added support for Spring Boot 3.2.3.
Feature: added support for Spring Framework 5.3.32.
Feature: added support for Spring Integration 6.2.2.
Feature: added support for Spring Security 6.2.2.
Feature: added support for Vaadin 24.3.6.
Feature: added support for ZK 10.0.0.
Improvement: improved the integration with IDE by adding notifications for expiring JRebel license.

2024.1.2 (5th March 2024)

Feature: added support for GlassFish 7.0.12.
Feature: added support for JBoss EAP 8.0.0.
Feature: added support for Jetty 12.0.6.
Feature: added support for Tomcat 10.1.19.
Feature: added support for WebSphere Liberty Profile *********.
Feature: added support for WildFly 31.0.1.Final.
Feature: added support for Camel 4.0.4.
Feature: added support for Grails 6.1.2.
Feature: added support for Groovy 4.0.18.
Feature: added support for HK2 3.0.6.
Feature: added support for Hibernate ORM 6.4.4.Final.
Feature: added support for Mockito 5.10.0.
Feature: added support for MyFaces 4.1.0.
Feature: added support for Payara Micro 6.2024.2.
Feature: added support for Spring Boot 3.2.2.
Feature: added support for Vaadin 24.3.5.
Feature: added support for AspectJ 1.9.21.1.
Feature: added support for Helidon 3.2.6.
Feature: added support for Kotlin 2.0.0.
Feature: added support for OpenWebBeans 4.0.2.
Feature: added support for Spring Framework 6.1.4.
Improvement: improved reloading of Spring Boot Actuator web endpoint base path from application.properties file.
Improvement: improved the debugger integration by automatically including log files in the "Submit a Support Ticket" form in JRebel.
Bug fix: fixed an issue with Gemini where classpath scanning was broken causing Spring to throw NoSuchBeanDefinitionException.

2024.1.1 (6th February 2024)

Feature: added support for Jetty 12.0.4.
Feature: added support for Tomcat 10.1.18.
Feature: added support for WebSphere Liberty Profile ********2.
Feature: added support for WildFly 30.0.1.Final.
Feature: added support for tc Server 5.0.9.
Feature: added support for TomEE 9.1.2.
Feature: added support for Camel 4.3.0.
Feature: added support for Helidon 4.0.3.
Feature: added support for Hibernate ORM 6.4.1.Final.
Feature: added support for Logback 1.4.14.
Feature: added support for Mockito 5.9.0.
Feature: added support for OpenWebBeans 4.0.1.
Feature: added support for Payara Micro 6.2023.12.
Feature: added support for Spring Boot 3.2.1.
Feature: added support for Spring Framework 6.1.3.
Feature: added support for MyBatis Spring 3.5.15.
Feature: added support for Struts *******.
Feature: added support for AspectJ 1.9.21.
Feature: added support for Byte Buddy 1.14.11.
Feature: added support for EclipseLink 2.7.14.
Feature: added support for Grails 6.1.1.
Feature: added support for Groovy 4.0.17.
Feature: added support for Jackson 2.16.1.
Feature: added support for Javassist 3.30.2-GA.
Feature: added support for Jersey 3.1.5.
Feature: added support for Kotlin 1.9.22.
Feature: added support for Log4j 2.22.1.
Feature: added support for Swagger 2.2.20.
Feature: added support for Vaadin 24.3.3.
Feature: added support for RESTEasy 6.2.7.Final.
Improvement: improved the speed and reliability of connecting a debugger to a remote process.
Improvement: improved variable expansion speed in the expressions tree when debugging a remote process.
Bug fix: fixed an issue where a deadlock would occur during startup with AspectJ.
Bug fix: fixed an issue with TomEE that could cause JRebel to throw an exception during startup for Jakarta 9 package names.

2023.4.2 (5th December 2023)

Feature: added support for Jetty 12.0.3.
Feature: added support for GlassFish 7.0.10.
Feature: added support for TomEE 8.0.16.
Feature: added support for Liferay *********-ga102.
Feature: added support for Camel 4.0.2.
Feature: added support for EclipseLink 3.0.4.
Feature: added support for Grails 6.1.0.
Feature: added support for HK2 3.0.5.
Feature: added support for Helidon 3.2.3.
Feature: added support for Hibernate ORM 6.4.0.CR1.
Feature: added support for Jackson 2.16.0.
Feature: added support for Jersey 3.0.12.
Feature: added support for Log4j 2.21.1.
Feature: added support for Metro 4.0.3.
Feature: added support for Mockito 5.7.0.
Feature: added support for MyBatis 3.5.14.
Feature: added support for Payara Micro 6.2023.10.
Feature: added support for RESTEasy 6.2.6.Final.
Feature: added support for Spring Boot 3.2.
Feature: added support for Spring Framework 6.1.
Feature: added support for Swagger 2.2.19.
Feature: added support for Vaadin 24.2.2.
Feature: added support for IBM OpenJ9 20.
Feature: added support for @Aggregate and @Saga annotations for Axon Framework 3.0.
Improvement: improved the reload performance on Spring Beans.

2023.4.1 (7th November 2023)

Feature: added support for GlassFish 7.0.9.
Feature: added support for Liferay 7.4.3.98-ga98.
Feature: added support for Jetty 12.0.2.
Feature: added support for Tomcat 10.1.15.
Feature: added support for WebSphere Liberty Profile ********0.
Feature: added support for tc Server 5.0.6.
Feature: added support for WildFly 30.0.0.
Feature: added support for Glowroot.
Feature: added support for CXF 4.0.3.
Feature: added support for Camel 4.1.0.
Feature: added support for Groovy 4.0.15.
Feature: added support for Helidon 4.0.0.
Feature: added support for Hibernate ORM 6.2.13.Final.
Feature: added support for OpenWebBeans 4.0.0.
Feature: added support for Scala 2.13.12.
Feature: added support for Spring Boot 3.1.5.
Feature: added support for Spring Framework 6.0.13.
Feature: added support for Struts 6.3.0.1.
Feature: added support for Vaadin 24.2.0.
Feature: added support for Byte Buddy 1.14.9.
Feature: added support for Jackson 2.15.3.
Feature: added support for Log4j 2.21.0.
Feature: added support for Mockito 5.6.0.
Feature: added support for Swagger 2.2.17.
Feature: added support for Weld 5.1.2.Final.
Bug fix: fixed a startup issue when using the Jolokia agent.
Bug fix: fixed an integration issue with Spring Boot 3 that removed the beans created by the Spring Security framework from the bean factory during reloading.

2023.4.0 (3rd October 2023)

Feature: added support for Java 21.
Feature: added support for Jetty 12.0.1.
Feature: added support for Liferay 7.4.3.94-ga94.
Feature: added support for GlassFish 7.0.8.
Feature: added support for Payara Micro 6.2023.9.
Feature: added support for Tomcat 10.1.13.
Feature: added support for WebSphere Liberty Profile 23.0.0.9.
Feature: added support for WildFly 29.0.1.Final.
Feature: added support for tc Server 5.0.4.
Feature: added support for Byte Buddy 1.14.8.
Feature: added support for AspectJ 1.9.20.1.
Feature: added support for Groovy 3.0.19.
Feature: added support for Hibernate ORM 6.3.1.Final.
Feature: added support for Kotlin 1.9.10.
Feature: added support for Mockito 5.5.0.
Feature: added support for RESTEasy 6.2.5.Final.
Feature: added support for Spring Boot 3.1.3.
Feature: added support for Struts 6.3.0.1.
Feature: added support for Vaadin 24.1.9.
Bug fix: fixed a bug with JRebel which could cause HibernateException with “Provider is closed” error when reloading classes with Hibernate 6 using the Infinispan cache.

2023.3.2 (5th September 2023)

Feature: added support for Jetty 12.
Feature: added support for Liferay ********-ga89.
Feature: added support for GlassFish 7.0.7.
Feature: added support for WebSphere Liberty Profile *********.
Feature: added support for Tomcat 10.1.12.
Feature: added support for Byte Buddy 1.14.6.
Feature: added support for Grails 6.0.0.
Feature: added support for EclipseLink 2.7.13
Feature: added support for Hibernate ORM 6.2.7.Final.
Feature: added support for Jersey 3.1.3.
Feature: added support for Logback 1.4.11.
Feature: added support for Spring Boot 3.0.9.
Feature: added support for Vaadin 24.1.5.
Feature: added support for Weld 5.1.1.SP2.
Feature: added support for Thymeleaf 3.1.2.RELEASE.
Bug fix: fixed an integration issue with Vaadin that could prevent the application from starting.

2023.3.1 (1st August 2023)

Feature: added support for Liferay ********-ga85.
Feature: added support for GlassFish 7.0.6.
Feature: added support for WebSphere Liberty Profile *********.
Feature: added support for WildFly 29.0.0.Final.
Feature: added support for Payara Micro 6.2023.7.
Feature: added support for Tomcat 10.1.11.
Feature: added support for tc Server 5.0.1.
Feature: added support for Camel 3.21.0.
Feature: added support for Grails 5.3.3.
Feature: added support for Groovy 3.0.18.
Feature: added support for Helidon 3.2.2.
Feature: added support for Hibernate ORM 6.2.6.Final.
Feature: added support for Jersey 3.0.11.
Feature: added support for Hibernate Validator 8.0.1.Final.
Feature: added support for Kotlin 1.9.0.
Feature: added support for Spring Boot 3.1.2.
Feature: added support for Spring Framework 6.0.11.
Feature: added support for Struts 6.2.0.
Feature: added support for Swagger 2.2.15.
Feature: added support for Vaadin 24.1.3.
Feature: added support for Weld 5.1.1.
Improvement: improved the synchronization in Remote Server Support causing the count of uploaded log files to be logged in the JRebel console when the total number of synchronized log files is more than 10. 

2023.3.0 (4th July 2023)

Feature: added support for GlassFish 7.0.5.
Feature: added support for Liferay ********-ga81.
Feature: added support for Payara 6.2023.6.
Feature: added support for TomEE 8.0.15 and 9.1.0.
Feature: added support for Tomcat 8.5.90, 9.0.76, and 10.1.10.
Feature: added support for Virgo 3.7.4.
Feature: added support for WebSphere Liberty Profile *********.
Feature: added support for Byte Buddy 1.14.5.
Feature: added support for CXF 3.6.1 and 4.0.2.
Feature: added support for Camel 3.14.8, 3.18.8, and 3.20.5.
Feature: added support for EclipseLink 4.0.2.
Feature: added support for Helidon 1.4.13 and 2.6.1.
Feature: added support for Hibernate ORM 5.3.30.Final and 6.2.5.Final.
Feature: added support for Jackson 2.15.2.
Feature: added support for Jersey 3.1.2.
Feature: added support for Kotlin 1.8.22.
Feature: added support for Logback 1.3.8 and 1.4.8.
Feature: added support for Mockito 5.3.1.
Feature: added support for MyFaces 4.0.1.
Feature: added support for RESTEasy 5.0.7.Final and 6.2.4.Final.
Feature: added support for Scala 2.13.11.
Feature: added support for Spring Boot 2.5.15, 2.6.15, 2.7.12, 3.0.7, and 3.1.0.
Feature: added support for Spring Framework 5.3.28 and 6.0.10.
Feature: added support for Spring MyBatis 2.1.1 and 3.0.2.
Feature: added support for Struts 2.5.31 and *******.
Feature: added support for Swagger 2.2.12.
Feature: added support for Vaadin 10.0.24, 14.10.2, 23.3.14, 24.0.7, and 24.1.0.
Feature: added support for Weld 5.1.1.Final.
Improvement: fixed a performance issue with AspectJ when defining classes.
Bug fix: fixed an integration issue with external class redefinition that could result in adding or removing methods by JRebel.
Bug fix: fixed an integration issue with HCL Domino 12.0.2 that could cause errors during startup.

2023.2.2 (6th June 2023)

Feature: added support for Liferay 7.4.3.76-ga76.
Feature: added support for GlassFish 7.0.4.
Feature: added support for WebSphere Liberty Profile 23.0.0.04.
Feature: added support for WildFly 28.0.1.Final.
Feature: added support for Tomcat 9.0.75.
Feature: added support for tc Server 5.0.0.
Feature: added support for Camel 3.20.4.
Feature: added support for Helidon 3.2.1.
Feature: added support for Hibernate ORM 6.2.2.Final.
Feature: added support for Kotlin 1.8.21.
Feature: added support for Vaadin 24.0.5.
Feature: added support for CXF 4.0.1.
Feature: added support for Guice 7.0.0.
Feature: added support for Jackson 2.15.1.
Feature: added support for Spring Framework 6.0.9.
Feature: added support for Swagger 2.2.10.
Improvement: added support for customer feedback form for the cloud license server.
Bug fix: fixed an issue with MyFaces that could cause multiple concurrent requests with a client during reload.
Bug fix: fixed an issue with Spring 6 that could cause an error when XmlBeanFactory is removed from the class files.

2023.2.1 (2nd May 2023)

Feature: added support for GlassFish 7.0.3.
Feature: added support for Jetty 11.0.15.
Feature: added support for Liferay ********-ga71.
Feature: added support for Payara 6.2023.4.
Feature: added support for Tomcat 10.1.8.
Feature: added support for WebSphere Liberty Profile ********.
Feature: added support for Byte Buddy 1.14.4.
Feature: added support for Camel 3.20.3.
Feature: added support for Groovy 3.0.17.
Feature: added support for HK2 3.0.4.
Feature: added support for Helidon 3.2.0.
Feature: added support for Hibernate ORM 6.2.1.Final.
Feature: added support for Jackson 2.15.0.
Feature: added support for Jersey 3.0.10.
Feature: added support for Kotlin 1.8.20.
Feature: added support for Logback 1.4.7.
Feature: added support for MyBatis 3.5.13.
Feature: added support for Spring Boot 3.0.6.
Feature: added support for Spring Framework 6.0.8.
Feature: added support for Struts 6.1.2.
Feature: added support for Swagger 2.2.9.
Feature: added support for WebSphere 9.0.5.12.
Improvement: improved performance of reloading EJB classes on WebLogic.
Bug fix: fixed an integration issue with GlassFish and Payara that could cause NullPointerException during startup.
Bug fix: fixed an issue with WebLogic where JRebel classes were not visible from the ChangeAwareClassLoader.
Bug fix: fixed a bug causing ArrayIndexOutOfBoundsException on WebLogic during startup when running on the latest Oracle JDK 8.

2023.2.0 (4th April 2023)

Feature: added support for Java 20.
Feature: added support for Jetty 11.0.14.
Feature: added support for Liferay ********-ga69.
Feature: added support for Payara 6.2023.2.
Feature: added support for Tomcat 10.1.7. 
Feature: added support for tc Server 4.1.21.
Feature: added support for Byte Buddy 1.14.3.
Feature: added support for Camel 3.20.2.
Feature: added support for EclipseLink 4.0.1.
Feature: added support for Grails 5.3.2.
Feature: added support for Groovy 3.0.15.
Feature: added support for Helidon 3.1.2.
Feature: added support for Hibernate ORM 6.1.7.Final.
Feature: added support for Jersey 2.39.
Feature: added support for Log4j 2.20.0.
Feature: added support for Metro 4.0.2.
Feature: added support for MyFaces 4.0.0.
Feature: added support for Payara Micro 6.2023.2.
Feature: added support for RESTEasy 6.2.3.Final.
Feature: added support for Spring Boot 3.0.4.
Feature: added support for Spring Framework 6.0.6.
Feature: added support for Vaadin 23.3.6.

2023.1.2 (7th March 2023)

Feature: added support for GlassFish 7.0.2.
Feature: added support for TomEE 9.0.0.
Feature: added support for Tomcat 8.5.85.
Feature: added support for WebSphere Liberty Profile ********.
Feature: added support for Jackson 2.14.2.
Feature: added support for Apache CXF 4.0.0.
Feature: added support for Kotlin 1.8.10.
Feature: added support for Liferay ********-ga63.
Feature: added support for Spring Framework 5.3.25.
Feature: added support for Camel 3.18.5.
Feature: added support for Helidon 2.5.6.
Feature: added support for RESTEasy 4.7.8.Final.
Feature: added support for HK2 2.6.1.
Feature: added support for Jersey 3.1.1.
Feature: added support for Byte Buddy 1.13.0.
Feature: added support for FreeMarker 2.3.32.
Feature: added support for Groovy 2.5.21.
Feature: added support for Native Agent for Linux on 64-bit ARM.
Bug fix: fixed an integration issue with GlassFish that could cause NullPointerException during startup.

2023.1.1 (6th February 2023)

Feature: added support for GlassFish 7.0.0.
Feature: added support for Payara 6.2023.1. 
Feature: added support for Tomcat 10.1.5. 
Feature: added support for WebSphere Liberty Profile 22.0.0.13.
Feature: added support for tc Server 4.1.20. 
Feature: added support for CXF 3.5.5.
Feature: added support for Hibernate ORM 6.1.6.Final.
Feature: added support for Kotlin 1.8.0.
Feature: added support for Payara Micro 6.2023.1.
Feature: added support for Jetty 11.0.13. 
Feature: added support for Liferay 7.4.3.61-ga61. 
Feature: added support for WildFly 27.0.1.Final. 
Feature: added support for Swagger 2.2.8.
Feature: added support for Spring Framework 6.0.4.
Feature: added support for Camel 3.20.1.
Feature: added support for Vaadin 23.3.5.
Feature: added support for Spring Boot 3.0.2.
Feature: added support for Helidon 3.1.0.
Feature: added support for RESTEasy 6.2.2.Final.
Feature: added support for Spring MyBatis 3.0.1.
Feature: added support for Jersey 3.0.9.
Feature: added support for Byte Buddy 1.12.21.
Feature: added support for Groovy 3.0.14.
Feature: added support for AspectJ 1.9.19.
Feature: added support for Thymeleaf 3.1.1.RELEASE.

2022.4.2 (6th December 2022)

Feature: added support for Payara 6.2022.1.
Feature: added support for SAP Commerce (Hybris) 2205. 
Feature: added support for TomEE 8.0.13.
Feature: added support for Tomcat 10.1.2.
Feature: added support for WebSphere Liberty Profile 22.0.0.11.
Feature: added support for tc Server 4.1.18.
Feature: added support for Hibernate ORM 6.1.5.Final.
Feature: added support for WildFly 27.0.0.Final.
Feature: added support for Spring Framework 6.
Feature: added support for Camel 3.19.0.
Feature: added support for Grails 5.2.5.
Feature: added support for Vaadin 23.2.6.
Feature: added support for Jackson 2.14.0.
Feature: added support for Spring Boot 3.
Feature: added support for Helidon 3.0.2.

2022.4.1 (1st November 2022)

Feature: added support for Grails 5.2.4.
Feature: added support for Tomcat 10.1.0.
Feature: added support for Jetty 11.0.12.
Feature: added support for WebSphere Liberty Profile 22.0.0.10.
Feature: added support for Hibernate ORM 6.1.4.Final.
Feature: added support for Camel 3.19.0.
Feature: added support for Vaadin 23.2.3.
Feature: added support for Jackson 2.14.0.
Feature: added support for Spring Boot 2.7.5.
Feature: added support for Helidon 3.0.1.
Feature: added support for Byte Buddy 1.12.18.

2022.4.0 (5th October 2022)

Feature: added support for Java 19.
Feature: added support for Liferay 7.4.3.41-ga41.
Feature: added support for Payara 5.2022.3.
Feature: added support for Tomcat 8.5.82.
Feature: added support for WebSphere Liberty Profile 22.0.0.9.
Feature: added support for WildFly 26.1.2.Final.
Feature: added support for Camel 3.18.1.
Feature: added support for Grails 4.1.2.
Feature: added support for Vaadin 23.2.0.
Feature: added support for Weld 5.0
Feature: added support for Jackson 2.13.4.
Feature: added support for Hibernate ORM 5.6.11.Final.
Feature: added support for Spring Boot 2.7.3.
Feature: added support for Helidon 3.0.1.
Feature: added support for Byte Buddy 1.12.16.
Feature: added support for Jakarta Faces 4.
Bug fix: fixed an integration issue with Vaadin where methods annotated with @EventData could not reload correctly.
Bug fix: fixed an issue with GlassFish 6.2 on JDK 17 where JRebel could fail to run.

2022.3.2 (6th September 2022)

Feature: added support for Grails 5.1.9 and 5.2.3.
Feature: added support for Liferay 7.4.3.36-ga36.
Feature: added support for Tomcat 9.0.65 and 10.0.23.
Feature: added support for WebSphere Liberty Profile 22.0.0.08.
Feature: added support for tc Server 4.0.29 and 4.1.17.
Feature: added support for Hibernate ORM 6.1.2.Final.
Feature: added support for Spring Boot 2.7.2.
Feature: added support for Vaadin 23.1.4.
Feature: added support for Helidon 2.5.2 and 3.0.0.
Feature: added support for RESTEasy 6.1.0.Final.
Bug fix: fixed a startup issue with IntelliJ IDEA and WebLogic where the IDE gets stuck while opening a project configured using WebLogic deployment and the Java home of WebLogic is corrupt.
Bug fix: fixed an integration issue with OpenJPA caused by the Javax to Jakarta conversion in the new JPA version.
Bug fix: fixed an integration issue with Tomcat and Remote Server Support that could cause the server not being able to shut down.

2022.3.1 (2nd August 2022)

Feature: added support for Camel 3.18.0.
Feature: added support for Metro 4.0.1.
Feature: added support for Kotlin 1.7.10.
Feature: added support for Jetty 9.4.48, 10.0.11, and 11.0.11.
Feature: added support for Liferay 7.4.3.33-ga33.
Feature: added support for TomEE 8.0.12.
Feature: added support for WebSphere Liberty Profile 22.0.0.7.
Bug fix: fixed an integration issue with Language Support for Java(TM) by Red Hat version 1.10 where rebel.xml was not getting generated for Maven projects.

2022.3.0 (5th July 2022)

Feature: added support for Hibernate ORM 6.1.0.Final.
Feature: added support for Struts 6.0.0.
Feature: added support for Kotlin 1.7.
Feature: added support for Spring Boot 2.7.
Feature: added support for Vaadin 23.1.
Feature: added support for Kumuluz 4.0.
Feature: added support for Liferay 7.4.3.29-ga29.
Feature: added support for Tomcat 8.5.81, 9.0.64, and 10.0.22.
Feature: added support for tc Server 4.0.28 and 4.1.16.
Feature: added support for WebSphere Liberty Profile 22.0.0.06.
Feature: added support for WildFly 26.1.1.Final.
Bug fix: fixed an integration issue when shaded Byte Buddy is used by the application causing the classes not to reload.
Bug fix: fixed a bug where classes with very large class initializers could fail verification.

2022.2.2 (7th June 2022)

Feature: added support for Liferay 7.4.3.23.
Feature: added support for Payara 5.2022.2.
Feature: added support for TomEE 8.0.11.
Feature: added support for WebSphere Liberty 22.0.0.4.
Feature: added support for WildFly 26.1.0.
Feature: added support for Swagger 2.2.0.
Feature: added support for Kotlin 1.6.21.
Bug fix: fixed an integration issue with RESTEasy and Spring that could cause NullPointerException during reload.
Bug fix: fixed an integration issue with Tomcat that could cause an error during startup when debug symbols are removed from the class files.
Bug fix: fixed an integration issue with IntelliJ IDEA not being able to generate the rebel-remote.xml file in the correct location.

2022.2.1 (5th May 2022)

Feature: added support for Jetty 9.4.46, 10.0.9, and 11.0.9.
Feature: added support for Liferay 7.4.3-GA18.
Feature: added support for Tomcat 8.5.78, 9.0.62, and 10.0.20.
Feature: added support for tc Server 3.2.33, 4.0.27, and 4.1.15.
Feature: added support for Hibernate ORM 6.0.0.Final.
Feature: added support for Kotlin 1.6.20.
Bug fix: fixed an integration issue with Liferay 7.4 that could cause resource file lookup to fail when running Remote Server setup.
Bug fix: fixed an integration issue with Spring that could cause WicketRuntimeException during reload.
Bug fix: fixed an integration issue with Tomcat that could cause an error during startup when a custom system class loader is used by the application.

2022.2.0 (5th April 2022)

Feature: added support for Java 18.
Feature: discontinued support for Java 6 and Java 7.
Feature: added support for GlassFish 6.2.5.
Feature: added support for Jetty 9.4.45, 10.0.8, and 11.0.8.
Feature: added support for Liferay 7.3.7, 7.4.2, and 7.4.3.15.
Feature: added support for Payara 5.2022.1.
Feature: added support for SAP Commerce (Hybris) 2111.
Feature: added support for TomEE 8.0.10.
Feature: added support for Tomcat 8.5.76, 9.0.60, and 10.0.18.
Feature: added support for WebSphere Liberty 22.0.0.3.
Feature: added support for Camel 3.15.
Feature: added support for Log4j 2.17.2.
Bug fix: fixed an integration issue with CXF and WebSphere Liberty when adding @Action annotations.
Bug fix: fixed an integration issue with GlassFish 6.2.5 where reloading EJB classes could result in ClassCastException.
Bug fix: fixed an integration issue with JBoss that caused CannotCompileException during deployment.
Bug fix: fixed an integration issue with WebSphere Liberty where some JAX-WS annotations were not reloaded correctly.
Bug fix: fixed a bug causing IllegalAccessError during retransformation of classes when other java agents were present.

2022.1.2 (1st March 2022)

Feature: added support for Azul Platform Prime (Zing) 22 for JDK 8, 11, and 17.
Feature: added support for tc Server 3.2.31, 4.0.25, and 4.1.13.
Feature: added support for Tomcat 8.5.75, 9.0.58, and 10.0.16.
Feature: added support for WebSphere Liberty 22.
Feature: added support for WildFly 26.0.1.
Feature: added support for Guice 5.1.
Feature: added support for Hibernate ORM 6.
Feature: added support for Liferay DXP 7.4.
Feature: added support for RESTEasy 6.
Bug fix: fixed an integration issue with Grails 5 that caused GrailsRuntimeException after reload.
Bug fix: fixed an integration issue with Spring Beans where beans could incorrectly be recreated on reload.
Bug fix: fixed an integration issue with Spring where active Transactions could cause NoUniqueBeanDefinitionException on reload.
Bug fix: fixed an integration issue with WebLogic and Spring causing IllegalArgumentException during startup when running on JDK 11.
Bug fix: fixed an integration issue with WebSphere Liberty where JAX-RS annotations were not correctly reloaded in Jakarta EE 9 mode.

2022.1.1 (1st February 2022)

Feature: added support for GlassFish 6.2.4.
Feature: added support for Payara 5.2021.10.
Feature: added support for TomEE 8.0.9.
Feature: added support for tc Server 3.2.30.
Feature: added support for CXF 3.5.
Feature: added support for Kotlin 1.6.10.
Improvement: improved the Spring integration resulting in reduced startup overhead.
Improvement: improved the Tomcat integration adding support for adding new tags to an existing taglib.
Bug fix: fixed an integration issue with Tomcat in embedded mode causing classes not to reload.
Bug fix: fixed an integration issue with WebSphere and Axis 2 causing elements to be omitted from generated WSDL files.

2022.1.0 (6th January 2022)

Feature: added support for GlassFish 6.2.3.
Feature: added support for Payara 5.2021.10.
Feature: added support for Resin 4.0.66.
Feature: added support for tc Server 4.0.24 and 4.1.12.
Feature: added support for Tomcat 8.5.73, 9.0.56 and 10.0.14.
Feature: added support for WebSphere Liberty ********2.
Feature: added support for WildFly 26.
Feature: added support for Grails 5.1.1.
Feature: added support for Camel 3.14.
Feature: added support for Vaadin 22.
Improvement: fixed a performance issue with Thymeleaf.
Bug fix: fixed an integration issue with Spring where an XML declared inner bean would inherit the wrong scope.

2021.4.2 (7th December 2021)

Feature: added support for WildFly 25.
Feature: added support for Payara 5.2021.8.
Feature: added support for Tomcat 8.5.72.
Feature: added support for Jetty 9.3.30, 10.0.7 and 11.0.7.
Feature: added support for WebSphere Liberty *********.
Feature: added support for tc Server 4.0.23 and 4.1.11.
Feature: added support for Spring Boot 2.6.
Feature: added support for Helidon 2.4.
Feature: added support for RESTEasy 5.
Improvement: improved the CXF integration adding support for reloading JAX-RS sub-resources.
Improvement: improved the integration with Spring Data JPA adding support for reloading packagesToScan.
Bug fix: fixed an integration issue with Eclipse causing startup issues when developing Eclipse RCP plugins.
Bug fix: fixed an integration issue with Spring Data JPA when using Java record types.
Bug fix: fixed an integration issue with Tomcat that could cause EOFException during startup when processing annotations.
Bug fix: fixed an integration issue with WildFly that caused IllegalStateException during startup of the logging subsystem.

2021.4.1 (2nd November 2021)

Feature: added support for GlassFish 6.2.2.
Feature: added support for Jetty 9.4.44.
Feature: added support for Tomcat 9.0.54 and 10.0.12.
Feature: added support for WebSphere Liberty *********.
Feature: added support for WildFly 25.
Feature: added support for Hibernate ORM 5.6.
Feature: added support for Spring Boot 2.6.
Feature: added support for Kotlin 1.6.
Bug fix: fixed an integration issue with IntelliJ IDEA not being able to reload plugin classes.

2021.4.0 (5th October 2021)

Feature: Added support for Java 17.
Feature: added support for Grails 5.
Feature: added support for GlassFish 6.2.1.
Feature: added support for Resin 4.0.65.
Feature: added support for Payara 5.2021.7.
Feature: added support for TomEE 8.0.8.
Feature: added support for tc Server 4.0.22 and 4.1.10.
Feature: added support for Tomcat 8.5.71, 9.0.53 and 10.0.11.
Feature: added support for WebSphere Liberty Profile 21.0.0.9.
Feature: added support for Jackson 2.13.
Feature: added support for Axis2 1.8.
Feature: added support for ZK 9.6.
Feature: added support for Spring Data REST 3.5.
Feature: added support for Vaadin 21.
Improvement: added support for reloading @NamedEntityGraph annotations in Hibernate ORM.
Bug fix: fixed an integration issue on Jersey that could cause ClassCastException during start up.
Bug fix: fixed an integration issue with ClassGraph.
Bug fix: fixed an integration issue with Struts 1 that prevented serialization of certain types.
Bug fix: fixed an integration issue with Tomcat and Remote Server Support that could cause NullPointerException during deployment.

2021.3.2 (7th September 2021)

Feature: added support for JBoss EAP 7.4.
Feature: added support for Payara 5.2021.5.
Feature: added support for WildFly 24.0.1.
Feature: added support for tc Server 4.0.21 and 4.1.9.
Feature: added support for Metro 2.4.6 and 3.0.2.
Feature: added support for Weld 4.0.2.
Feature: added support for Hibernate ORM 5.5.5.
Feature: added support for Spring Boot 2.5.3.
Bug fix: fixed a bug where invoking a method from an inner class on super of the enclosing class could cause StackOverflowError.

2021.3.1 (4th August 2021)

Feature: added support for GlassFish 6.2.
Feature: added support for WildFly 24.
Feature: added support for Jetty 9.4.43, 10.0.6 and 11.0.6.
Feature: added support for Tomcat 8.5.69, 9.0.50 and 10.0.8.
Feature: added support for WebSphere Liberty 21.0.0.6.
Feature: added support for WebSphere 9.0.5.8.
Bug fix: fixed an integration issue with MyFaces and WebLogic that caused ClassCastException when reloading JSF configuration.
Bug fix: fixed an integration issue with OpenLiberty where reloading annotations could fail.

2021.3.0 (6th July 2021)

Feature: added support for GlassFish 6.1.
Feature: added support for Jetty 9.4.41, 10.0.3 and 11.0.3.
Feature: added support for Payara 5.2021.4.
Feature: added support for Tomcat 8.5.66, 9.0.46 and 10.0.6.
Feature: added support for WebSphere Liberty 21.0.0.5.
Feature: added support for tc Server 3.2.29, 4.0.20 and 4.1.8.
Feature: added support for Jakarta EE 9 on Open Liberty 21.
Feature: added support for Camel 3.10.
Feature: added support for Helidon 2.3.
Feature: added support for Hibernate ORM 5.5.
Feature: added support for OpenJPA 3.2.
Feature: added support for RestEasy 4.6.
Feature: added support for Spring Boot 2.5.
Feature: added support for Vaadin 20.
Feature: added support for MicroProfile REST Client 2.34.
Bug fix: fixed an integration issue with ClassGraph.

2021.2.2 (1st June 2021)

Feature: added support for Jetty 9.4.40.
Feature: added support for Payara 5.2021.3.
Feature: added support for Tomcat 7.0.109, 8.5.65, 9.0.45 and 10.0.5.
Feature: added support for WebSphere Liberty Profile 21.0.0.04.
Feature: added support for WildFly 23.0.2.
Feature: added support for tc Server 3.2.28, 4.0.19 and 4.1.7.
Feature: added support for Kotlin 1.5.
Improvement: improved integration with EJBs adding support for reloading CDI interceptors.
Bug fix: fixed an integration issue with Liferay where deploying portlet .wars on WildFly could fail.
Bug fix: fixed an integration issue with WildFly that could fail to define classes during deployment.
Bug fix: fixed a bug with nestmate support where calling Class::getNestMembers could cause NoClassDefFoundError to be thrown.

2021.2.1 (4th May 2021)

Feature: added support for Helidon.
Feature: added support for Google App Engine 1.9.88.
Feature: added support for Jetty 9.4.39, 10.0.2 and 11.0.2.
Feature: added support for Payara 5.2021.2.
Feature: added support for JAXB 3.
Feature: added support for Vaadin 19.
Feature: added support for Camel 3.9.
Improvement: improved performance for MyFaces on reload, when detecting annotation changes.
Improvement: improved support for Spring Data JPA when reloading repository method annotations.
Improvement: improved the performance of reloading a large set of changes at once.
Bug fix: fixed an integration issue with FileServlets that could cause StackOverflowError trying to redirect to itself.
Bug fix: fixed an integration issue with Metro 3.
Bug fix: fixed a bug in JRebel that could cause ClassCastException when changing a type from an interface to an enum.

2021.2.0 (6th April 2021)

Feature: added support for Java 16.
Feature: added support for WildFly 23.
Feature: added support for Google App Engine 1.9.87.
Feature: added support for Jetty 9.4.38, 10.0.1 and 11.0.1.
Feature: added support for Payara 5.2021.1.
Feature: added support for Tomcat 7.0.108, 8.5.64, 9.0.44 and 10.0.4.
Feature: added support for tc Server 3.2.27, 4.0.18 and 4.1.6.
Feature: added support for WebSphere 8.5.5-19.
Feature: added support for WebSphere Liberty ********.
Feature: added support for Eclipse MicroProfile on Liberty.
Feature: added support for Eclipse MicroProfile on TomEE.
Feature: added support for Guice 5.
Feature: added support for Apache Camel 3.8.
Feature: added support for RESTEasy 3.15.
Feature: added support for Weld 3.1.7.
Improvement: improved the performance when reloading HK2 services.
Improvement: improved the reload performance on JBoss and WildFly.
Improvement: improved the synchronization speed in Remote Server Support.
Bug fix: fixed an integration issue with Jersey when both Jakarta EE 9 and Java EE 8 dependencies were present.
Bug fix: fixed an integration issue with RESTEasy that caused freezes during startup.
Bug fix: fixed an integration issue with Spring and Remote Server Setup where resource reloading could fail due to internal folder names starting with a period.
Bug fix: fixed an integration issue with Spring where reloading could cause RMI services to be deregistered.
Bug fix: fixed a regression issue with Remote Server Support where JSPs could fail to reload.

2021.1.2 (2nd March 2021)

Feature: added initial support for JVMs running on macOS aarch64 (M1) architecture.
Feature: added support for Jetty 9.4.36.
Feature: added support for TomEE 8.0.6.
Feature: added support for Tomcat 8.5.63, 9.0.43 and 10.0.2.
Feature: added support for WebSphere Liberty ********.
Bug fix: fixed a bug that could cause NullPointerException during reload of a class if the class was being used in parallel.
Bug fix: fixed a bug where resources could be found multiple times if startup and rebel.xml location overlap.

2021.1.1 (4th February 2021)

Feature: added initial support for Jakarta EE 9.
Feature: added support for Glassfish 6.
Feature: added support for Jetty 11.
Feature: added support for Tomcat 10.
Feature: added support for WildFly 22.
Feature: added support for Hibernate Validator 7.
Feature: added support for HK2 3.
Feature: added support for Metro 3.
Feature: added support for MyFaces 3.
Feature: added support for Vaadin 18.
Feature: added support for Weld 4.
Feature: added support for RestEasy 4.6.
Bug fix: fixed an integration issue with ATG that caused stack traces during startup.
Bug fix: fixed an integration issue with Grails where methods annotated with @GrailsCompileStatic could fail validation after method signature changes.
Bug fix: fixed an integration issue with Liferay that caused severe slowdown during startup due to excessive resource monitoring.
Bug fix: fixed an integration issue with Spring Boot and Remote Server Support where executable .war deployment failed to synchronize updates.
Bug fix: fixed an integration issue with WebLogic where application-bundled frameworks could get mixed with server-bundled version.

2021.1.0 (14th January 2021)

Feature: added support for Jetty 10.
Feature: added support for Tomcat 7.0.107, 8.5.61 and 9.0.41.
Feature: added support for Google App Engine 1.9.84.
Feature: added support for Payara 5.2020.7.
Feature: added support for Jetty 9.4.35.
Feature: added support for TomEE 8.0.5.
Feature: added support for tc Server 3.2.26, 4.0.17 and 4.1.5.
Feature: added support for WebSphere Liberty 20.0.0.12.
Feature: added support for WebSphere 9.0.5-6.
Feature: added support for WildFly 21.0.2.Final.
Feature: Added support for Camel 3.7.
Feature: added support for Log4j 2.14.
Feature: added support for Jackson 2.12.
Feature: added support for RestEasy 3.14.
Feature: added support for Hibernate Validator 6.2.
Bug fix: fixed an integration issue with Grails that would sometimes recompile unchanged classes as well.
Bug fix: fixed an integration issue with Hibernate ORM and GORM causing HibernateException with "Provider is closed" when reloading GORM properties.
Bug fix: fixed an integration issue with Spring Framework causing errors during startup.
Bug fix: fixed an integration issue with Swagger where its UI would not correctly show added or reloaded endpoints.
Bug fix: fixed an integration issue with WebLogic when using Remote Server Support causing ConnectionClosedException with missing closing chunk when syncing from IDE.
Bug fix: fixed a bug causing IncompatibleClassChangeError when reloading inner classes.
Bug fix: fixed a bug with Remote Server Support on Tomcat causing AccessControlException during startup.
Bug fix: fixed a bug with classes containing lambdas compiled with the Eclipse Compiler throwing StackOverflowError.
Bug fix: fixed a performance regression when reloading classes for the first time.

2020.3.2 (24th November 2020)

Feature: added support for Kumuluz.
Feature: added support for Payara Micro.
Feature: added support for WildFly MicroProfile.
Feature: added support for Jetty 9.3.29 and 9.4.33.
Feature: added support for Liferay 7.3.5-GA6.
Feature: added support for Payara 5.2020.5.
Feature: added support for tc Server 3.2.25, 4.0.16 and 4.1.4.
Feature: added support for TomEE 7.0.9 and 7.1.4.
Feature: added support for Tomcat 8.5.59 and 9.0.39.
Feature: added support for WebSphere Liberty *********.
Feature: added support for WildFly 21.0.0.Final.
Feature: added support for Spring Framework 5.3.
Feature: added support for Spring Boot 2.4.
Feature: added support for Camel 3.6.0.
Feature: added support for Jackson 2.12.0.
Feature: added support for ZK *******.
Improvement: added support for Spring WebFlux 5.1+ running on Reactor Netty.
Bug fix: fixed an integration issue with Spring WebFlux causing ClassCastException when Spring MVC was not present.
Bug fix: fixed an integration issue with Spring where changes to the superclass of a @Configuration class would not reload correctly.
Bug fix: fixed an integration issue with WebLogic that caused slowdowns after reloads.
Bug fix: fixed a bug where extending a class that was previously inaccesible would fail.
Bug fix: fixed a bug where changing hierarchy of a class could cause ClassCastException when used in an added class.
Bug fix: fixed a bug where invoking static methods using MethodHandles could cause IllegalArgumentException.

2020.3.1 (27th October 2020)

Feature: added support for Tomcat 7.0.106, 8.5.58 and 9.0.38.
Feature: added support for WebSphere 8.5.5-18.
Feature: added support for Vaadin 17.
Feature: added support for Camel 3.5.
Feature: added support for the Sealed Classes Java feature.
Improvement: improved the integration with ATG adding support for reloading @Endpoint changes.
Bug fix: fixed an integration issue with ATG and JBoss that caused NoClassDefFoundError during deployment.
Bug fix: fixed an integration issue with Camel that caused IllegalArgumentException when trying to reload a route.
Bug fix: fixed an integration issue with EclipseLink that caused MalformedURLException when adding entity mappings.
Bug fix: fixed an integration issue with Kotlin that caused VerifyError for suspend functions compiled targeting Java 8 or higher.
Bug fix: fixed an integration issue with OpenJ9 on Alpine Linux causing errors during startup.
Bug fix: fixed an integration issue with Weld and OpenWebBeans where injecting into a super class of a bean could fail.
Bug fix: fixed a bug where MethodHandles adapted with asType could fail with a ClassCastException after reload.
Bug fix: fixed a bug where calling reflectAs on a MethodHandle could return unexpected results.
Bug fix: fixed a bug where classes compiled with ECJ using synchronization could cause VerifyError.

2020.3.0 (22nd September 2020)

Feature: added support for Java 15.
Feature: added support for Google App Engine 1.9.82.
Feature: added support for Jetty 9.4.31.
Feature: added support for Liferay 7.3.4-GA5.
Feature: added support for Payara 5.2020.4.
Feature: added support for Resin 4.0.65.
Feature: added support for TomEE 8.0.4.
Feature: added support for Virgo 3.7.4.
Feature: added support for WebSphere Liberty 20.0.0.09.
Feature: added support for WebSphere 9.0.5-5.
Feature: added support for Kotlin 1.4.10.
Feature: added support for ByteBuddy 1.10.14.
Feature: added support for CXF 3.4.
Bug fix: fixed an integration issue with Hibernate ORM 3.3 when using both XML and annotation based configuration.
Bug fix: fixed an integration issue with Spring that could cause NullPointerException when a singleton bean was removed.
Bug fix: fixed an integration issue with WebSphere Liberty that could cause start up failures if the server was started in debug mode.
Bug fix: fixed an integration issue with Weld 1 that caused archives without beans.xml files to be scanned.
Bug fix: fixed a bug where adding multiple super classes to the class-hierarchy could cause fields on existing instances to not be correctly initialized.
Bug fix: fixed a bug where reloading a class could incorrectly overwrite a static field with an older value.

2020.2.5 (27th August 2020)

Feature: added support for Tomcat 7.0.105, 8.5.57 and 9.0.37.
Feature: added support for Google App Engine 1.9.81.
Feature: added support for Liferay 7.3.3-GA4.
Feature: added support for Payara 5.2020.3.
Feature: added support for TomEE 8.0.3.
Feature: added support for Wildfly 20.0.1.Final.
Feature: added support for Kotlin 1.4.
Feature: added support for SpringFox 3.
Improvement: added support for reloading Spring session beans in JSF environments.
Bug fix: fixed an integration issue with Bean Validation where changing bean validation annotations in a bean's super class where not detected.
Bug fix: fixed an integration issue with JBoss and Seam that caused NoSuchMethodError on bean classes.
Bug fix: fixed an integration issue with Spring Boot and Guice that caused ArrayIndexOutOfBoundsException during context initialization.
Bug fix: fixed an integration issue with Spring where changes to @Bean methods in super classes of @Components where not picked up.
Bug fix: fixed a bug that caused accessibility to be ignored when unreflecting a Field to a MehodHandle.

2020.2.4 (28th July 2020)

Feature: added support for OpenJDK 7u272, 8u262, 11.0.8, 13.0.4 and 14.0.2.
Feature: added support for OpenJ9 0.21.0 (Java 8, 11 and 14).
Feature: added support for Azul Zing ZVM 20.06 (Java 7, 8 and 11).
Feature: added support for WildFly 20.
Feature: added support for Jetty 9.4.30.
Feature: added support for Payara 5.2020.2.
Feature: added support for TomEE 7.0.8, 7.1.3 and 8.0.2.
Feature: added support for Tomcat 8.5.56 and 9.0.36.
Feature: added support for WebSphere Liberty 20.0.0.07.
Feature: added support for WebSphere 9.0.5-4.
Feature: added support for tc Server 3.2.24, 4.0.15 and 4.1.3.
Feature: added support for Vaadin 16.
Feature: added support for Spring Framework 5.1.16 and 5.2.7.
Feature: added support for Spring Boot 2.1.15, 2.2.8 and 2.3.1.
Improvement: improved the performance on WildFly when reloading servlets with injected @EJBs.
Bug fix: fixed an integration issue with IntelliJ IDEA that could cause PluginException during reload of plugin classes.
Bug fix: fixed an integration issue with RESTEasy that caused global configuration to be enabled on reload.
Bug fix: fixed an integration issue with Vaadin causing Vaadin's live reload feature to not work correctly.
Bug fix: fixed an integration issue with Weld 3 that could cause beans to not be found during deployment.
Bug fix: fixed a bug with MethodHandles that could cause ClassCastException when accessing fields.
Bug fix: fixed a bug with reflection on constructors of reloaded classes throwing IllegalAccessException.

2020.2.3 (25th June 2020)

Feature: added support for Tomcat 7.0.104, 8.5.55 and 9.0.35.
Feature: added support for WildFly 19.1.0.Final.
Feature: added support for Jetty 9.4.29.
Feature: added support for Liferay 7.3.2-GA3.
Feature: added support for WebSphere Liberty 20.0.0.05.
Feature: added support for tc Server 3.2.22, 4.0.13 and 4.1.1.
Feature: added support for Spring Data Neumann.
Feature: added support for RestEasy 3.12.
Feature: added support for Jersey 2.31.
Improvement: improved the reloading performance of Hibernate ORM.
Bug fix: fixed an integration issue with Magnolia and Freemarker where templates would not reload if Magnolia's development mode was not enabled.
Bug fix: fixed an integration issue with WildFly and EJB classes that could cause NullPointerException after reload.
Bug fix: fixed a bug with VarHandles that could cause inconsistent behavior after reload.
Bug fix: fixed a bug with JRebel running on Windows for JDK installations where javaw.exe wasn't available.
Bug fix: fixed a bug with JRebel running without its native component that could cause StackOverflowError during startup.
Bug fix: fixed a bug with JRebel which could cause errors when loading classes compiled for Java 1.4 or older with big methods containing try-finally.

2020.2.2 (26th May 2020)

Feature: added support for Azul Zing 20.03.
Feature: added support for Jetty 9.4.28.
Feature: added support for Tomcat 8.5.54 and 9.0.34.
Feature: added support for WebSphere Liberty 20.0.0.4.
Feature: added support for tc Server 3.2.21, 4.0.12 and 4.1.
Feature: added support for Google App Engine 1.9.80.
Feature: added support for Camel 3.2.
Feature: added support for Jackson 2.11.
Feature: added support for Spring Boot 2.3.
Feature: added support for Spring Integration 5.3.
Feature: added support for GWT 2.8 and 2.9.
Feature: added support for OpenWebBeans 2.0.16.
Improvement: improved the reloading time for Weld on WebSphere caused by excessive jar file scanning.
Bug fix: fixed an integration issue with Hibernate ORM that could cause LockObtainFailedException when reloading certain annotations on entity classes.
Bug fix: fixed an integration issue with Spring where reloading classes with @Scheduled could cause the method to be scheduled twice.
Bug fix: fixed an integration issue with Weblogic where classes in .ear files containing META-INF/weblogic-application.xml where not reloadable.
Bug fix: fixed an integration issue with Weld and ByteBuddy causing errors during class loading.
Bug fix: fixed a bug with OpenJ9 and HotSpot where certain atomic actions could work inconsistently after being JIT compiled.

2020.2.1 (29th April 2020)

Feature: added support for WebLogic 14.
Feature: added support for WildFly 19.
Feature: added support for JBoss EAP 7.3.
Feature: added support for Jetty 9.4.27.
Feature: added support for Payara 5.201.
Feature: added support for Liferay 7.3.1-GA2.
Feature: added support for Tomcat 7.0.103, 8.5.53 and 9.0.33.
Feature: added support for WebSphere Liberty Profile 20.0.0.4.
Feature: added support for WebSphere 8.5.5-17 and 9.0.5-3.
Feature: added support for tc Server 3.2.20 and 4.0.11.
Feature: added support for Google App Engine 1.9.79.
Feature: added support for Vaadin 15.
Feature: added support for Camel 3.1.
Feature: added support for Grails 4.0.2.
Feature: added support for RestEasy 3.11.
Feature: added support for Javassist 3.27.
Improvement: improved the integration with Spring AOP adding better support for reloading aspects.
Improvement: improved the integration with Spring adding support for reloading addViewControllers methods in WebMvcConfigurer beans.
Improvement: updated the default JRebel temp dir location to be relative to the rebel base folder.
Bug fix: fixed an integration issue with Camel where reloading WebSocket endpoints in applications without HTTP endpoints could fail.
Bug fix: fixed an integration issue with Hybris throwing NoClassDefFoundError when undeploying an application.
Bug fix: fixed an integration issue with OpenJPA that caused an internal JRebel error during redeployment.
Bug fix: fixed an integration issue with Spring and Kotlin causing exception when adding inner classes.
Bug fix: fixed an integration issue with Spring where lazy Spring bean definitions could be lost during reload.
Bug fix: fixed an integration issue with Vaadin where reloading @Observers methods could result in NullPointerException.
Bug fix: fixed a bug accessing added fields to sub types from a previously non-loaded class.
Bug fix: fixed a bug accessing added static members from a previously non-loaded class.
Bug fix: fixed a bug that caused Class.getSimpleName to return incorrect name after reload.
Bug fix: fixed a bug where changing the order of primitive fields could cause loss of state.
Bug fix: fixed a bug when reloading a class converting an interface to a concrete class.
Bug fix: fixed a bug when changing a static method to be non static.
Bug fix: fixed a bug where changes were not detected in a Remote Server setup if rebel base was sym-linked.
Bug fix: fixed a bug with MethodHandles failing to find members of super types of reloaded classes.

2020.2.0 (24th March 2020)

Feature: added support for Java 14, including records.
Feature: added support for GraalVM used as JVM.
Feature: added support for Tomcat 7.0.100, 8.5.51 and 9.0.31.
Feature: added support for WebSphere Liberty 20.0.0.2.
Feature: added support for Resin 4.0.64.
Feature: added support for RESTEasy 3.10.
Feature: added initial support for Wicket 9.0-M.
Feature: added initial support for Jakarta EE 9 with renamed packages.
Feature: added support for using VarHandles on added and reloaded fields in classes.
Feature: added support for using Unsafe direct access on added and reloaded fields in classes.
Improvement: improved the integration with Spring to support reconfiguration of beans declared as inner classes in @Configuration classes.
Bug fix: fixed an integration issue with CDI where properties were not correctly injected when the super type was reloaded.
Bug fix: fixed an integration issue with EclipseLink causing naming conflicts when multiple EntityManagerFactories share the same persistence unit.
Bug fix: fixed an integration issue with Spring Framework where properties were not correctly Autowired when the super type was reloaded.
Bug fix: fixed an integration issue with TomEE 8 that caused excessive memory allocation.
Bug fix: fixed an integration issue with WebLogic that caused a potential deadlock during JNDI lookup.
Bug fix: fixed an integration issue with WildFly that failed to load the JBoss Log Manager during startup.
Bug fix: fixed a bug where accessing protected members of a reloaded class could throw IllegalAccessError.
Bug fix: fixed a bug where reloading an Enum could cause state on the enum to be lost.

2020.1.1 (13th February 2020)

Feature: added support for WebSphere Liberty 20.0.
Feature: added support for Liferay 7.3.0.
Feature: added support for Tomcat 7.0.99, 8.5.50 and 9.0.30.
Feature: added support for Jetty 9.4.26.
Feature: added support for Payara 5.194.
Feature: added support for TomEE 7.0.7, 7.1.2 and 8.0.1.
Feature: added support for Resin 4.0.63.
Feature: added support for WebSphere 9.0.5-2.
Feature: added support for WebSphere Liberty 1*******2.
Feature: added support for tc Server 3.2.19 and 4.0.10.
Feature: added support for Google App Engine 1.9.78.
Feature: added support for Groovy 3.0.
Feature: added support for ZK 9.0.
Feature: added support for Jersey 2.30.
Improvement: improved the integration with WebLogic and the handling of prefer-application-packages in weblogic.xml to allow adding modules with new dependencies.
Bug fix: fixed an integration issue with OpenWebBeans that could cause ConcurrentModificationException during reload.
Bug fix: fixed an integration issue with Spring and OSGi containers that could cause a NoClassDefFoundError during startup.
Bug fix: fixed an integration issue with WildFly that could cause NoClassDefFoundError when accessing classes in an added package.
Bug fix: fixed an integration issue with WildFly that would fail to start on Azul Zulu 8-232.
Bug fix: fixed an integration issue with Wildfly that could cause the application to fail to deploy.
Bug fix: fixed an integration issue with Java GSS-API that would cause NoClassDefFoundError during startup.
Bug fix: fixed a bug with OpenJ9 where using StackWalker could result in a NullPointerException.

2020.1.0 (9th January 2020)

Feature: added support for Tomcat 8.5.49 and 9.0.29.
Feature: added support for Jetty 9.2.29, 9.3.28 and 9.4.24.
Feature: added support for WildFly 18.0.1.Final.
Feature: added support for WebSphere Liberty 1*******1.
Feature: added support for Liferay 7.2.1-GA2.
Feature: added support for Google App Engine 1.9.77.
Feature: added support for tc Server 3.2.18 and 4.0.9.
Feature: added support for Apache Camel 3.
Feature: added support for Hibernate ORM 5.4.9.Final.
Improvement: improved the JRebel macOS native components signing them with an Apple DeveloperID certificate for better compatibility with macOS Catalina.
Bug fix: fixed an integration issue with ATG that caused a NullPointerException during startup.
Bug fix: fixed an integration issue with JBoss EAP that caused a race condition in the logging subsystem during startup.
Bug fix: fixed an integration issue with Liferay 7.2 GA2.
Bug fix: fixed an integration issue with Spring Boot 2.2 where added Entity beans were not picked up.
Bug fix: fixed an integration issue with Spring where reloading could fail if a null bean was present.
Bug fix: fixed an integration issue with TomEE 8 where reloading EJBs could cause certain Servlet 3.0 annotation to not be reloaded.
Bug fix: fixed an integration issue with TomEE and OpenEJB where reloading could cause the context root setting to be lost.
Bug fix: fixed a bug with the Debugger integration when evaluating expression with caller-sensitive methods.

2019.2.2 (26th November 2019)

Feature: added support for Jetty 9.4.22.
Feature: added support for Tomcat 8.5.47 and 9.0.27.
Feature: added support for WebSphere Liberty 1*******0.
Feature: added support for Grails 4.
Feature: added support for Hibernate Validator 6.1.
Feature: added support for Azul Zing ZVM 19.09.0.0.
Feature: added support for OpenJ9 8u232.
Improvement: improved the integration with Camel adding support for reloading @Consume predicates.
Bug fix: fixed an integration issue with Spring and Thymeleaf that caused Already InitializedException when reloading a TemplateResolver.
Bug fix: fixed an integration issue with Spring Data Neo4j 5.1.
Bug fix: fixed an integration issue with JBoss AOP that caused ClassFileTransformers errors during startup.

2019.2.1 (29th October 2019)

Feature: added support for TomEE 8.
Feature: added support for WildFly 18.
Feature: added support for Tomcat 8.5.46 and 9.0.26.
Feature: added support for Jetty 9.4.21.
Feature: added support for WebLogic 12.2.1.4.
Feature: added support for WebSphere 9.0.5.1.
Feature: added support for WebSphere Liberty 19.0.0.9.
Feature: added support for Spring Boot 2.2.
Feature: added support for Spring Framework 5.2.
Feature: added support for Jackson 2.10.
Feature: added support for Javassist 3.26.
Feature: added support for Spring Data 2.2.
Feature: added support for Spring Data Neo4j 5.2.
Feature: added support for Spring Data Rest WebMVC 3.2.
Feature: added support for Hibernate ORM 5.4.5.
Improvement: improved the integration with Spring Framework adding support for changing @Cacheable and related annotations.
Bug fix: fixed an integration issue with Liferay 6 running on Tomcat 8.5 and newer.
Bug fix: fixed an integration issue with Spring Framework where reload could cause @JmsListener annotated methods to be called multiple times.
Bug fix: fixed an integration issue with WildFly that could cause ConcurrentModificationException during startup.
Bug fix: fixed a bug where certain Java Module System JVM arguments would cause failures during startup.
Bug fix: fixed a bug that caused NoSuchFieldError when reloading classes depending on previously non-loaded classes.

2019.2.0 (24th September 2019)

Feature: added support for Java 13.
Feature: added support for Tomcat 8.5.45 and 9.0.24.
Feature: added support for WebSphere Liberty ********.
Feature: added support for Jetty 9.4.20.
Feature: added support for Payara 5.193.
Feature: added support for tc Server 3.2.17 and 4.0.8.
Feature: added support for Hybris 1905.
Feature: added support for KumuluzEE.
Feature: added support for RestEasy 3.9.
Feature: added support for DeltaSpike 1.9.1.
Feature: added support for MyBatis-Spring 1.3.3.
Bug fix: fixed an integration issue with Jersey 2.4 that caused container initialization error.
Bug fix: fixed an integration issue with MyBatis-Spring used together with an older version of MyBatis.
Bug fix: fixed an integration issue with WebSphere and OpenWebBeans where changing @Named on a bean was not correctly reloaded.
Bug fix: fixed a bug that caused VerifyError when reloading classes compiled for Java 7 but running on Java 8 or newer.

2019.1.6 (2nd September 2019)

Feature: added support for Tomcat 7.0.96, 8.5.43 and 9.0.22.
Feature: added support for WebSphere Liberty ********.
Feature: added support for Vaadin 14.
Feature: added support for HK2 2.6.
Feature: added support for ByteBuddy 1.10.
Feature: added support for Groovy 2.5.8.
Feature: added support for Mojarra 2.2.20.
Feature: added support for MyBatis-Spring 2.0.2.
Improvement: improved the integration with Mojarra adding notification for when facelets are changed.
Bug fix: fixed an integration issue with Guice when multiple Guice versions were found on the classpath.
Bug fix: fixed an integration issue with Hibernate ORM where creating new sessions could fail after reload.
Bug fix: fixed an integration issue with Spring AOP that caused advice methods to not resolve correctly.
Bug fix: fixed an integration issue with WebLogic that caused transaction attributes defined in ejb-jar.xml to be forgotten.
Bug fix: fixed an integration issue with WildFly and JBoss that caused ConcurrentModificationException during deployment.

2019.1.5 (8th August 2019)

Feature: added support for WildFly 17.0.1.
Feature: added support for Jetty 9.4.19.
Feature: added support for TomEE 7.0.6 and 7.1.1.
Feature: added support for WebSphere *******.
Feature: added support for WebSphere Liberty Profile ********.
Feature: added support for Google App Engine 1.9.76.
Feature: added support for Jersey 2.29.
Feature: added support for RestEasy 3.8.
Feature: added support for Camel 2.24.
Feature: added support for MyFaces 2.3.
Feature: added support for Spring MyBatis 2.0.1.
Feature: added support for Velocity 2.1.
Bug fix: fixed an integration issue with EclipseLink and TomEE not correctly identifying data source declarations.
Bug fix: fixed an integration issue with Hibernate that caused registered services not to be destroyed in a timely manner.
Bug fix: fixed an integration issue with Hibernate where entities created during runtime were dropped on reload.
Bug fix: fixed an integration issue with JBoss 5.1 that caused VerifyError due to incompatibilities with Java 7 compiled classes.
Bug fix: fixed an integration issue with JBoss and WildFly where EJB applications could fail to undeploy after a reload.
Bug fix: fixed an integration issue with WebLogic that caused concurrency issues throwing NameNotFoundException while a reload was in progress.
Bug fix: fixed an issue that could cause NullPointerException during reload of classes with static fields.
Bug fix: fixed a bug on Windows where usage of short filenames (8.3) could cause changes to not reload.

2019.1.4 (8th July 2019)

Feature: added support for WildFly 17.
Feature: added support for Liferay 7.2.
Feature: added support for Payara 5.192.
Feature: added support for Tomcat 8.5.42 and 9.0.21.
Feature: added support for Google App Engine 1.9.75.
Feature: added support for WebSphere Liberty Profile 1*******.
Improvement: improved the integration with Spring adding better support for renaming bean producer methods.
Bug fix: fixed an integration issue with Hibernate when using Infinispan as a 2nd level cache.
Bug fix: fixed an integration issue with Hibernate that caused UnknownServiceException when using EJB Extended Persistence.
Bug fix: fixed an integration issue with Magnolia Blossom that caused IllegalStateException due to duplicate URL handlers.
Bug fix: fixed an integration issue with OpenJPA that could cause ClassCastException due to proxying of EntityManagers.
Bug fix: fixed an integration issue with MyFaces that caused ClassNotFoundException on older versions.
Bug fix: fixed an integration issue with WildFly that could cause StackOverflowError.
Bug fix: fixed a bug that caused ClassFormatError for lambda classes when multiple agents were present.
Bug fix: fixed a bug that caused NullPointerException when accessing a static field from a sub-type's static initializer.
Bug fix: fixed a bug that caused NoSuchFieldError when reloading classes depending on previously non-loaded classes.

2019.1.3 (10th June 2019)

Feature: added support for Tomcat 8.5.41 and 9.0.20.
Feature: added support for Jetty 9.4.18.
Feature: added support for tc Server 3.2.16 and 4.0.7.
Feature: added support for Liferay 7.1.3-GA4.
Feature: added support for Google App Engine 1.9.74.
Feature: added support for RESTEasy 4.
Feature: added support for RESTEasy 3.7.
Feature: added support for OpenJPA 3.1.
Bug fix: fixed an integration issue with Liferay and MyBatis that caused PortletException when changing .jsp files.
Bug fix: fixed an integration issue with WebLogic that caused NullPointerException when .class resources were changed during reload.
Bug fix: fixed an integration issue with WebSphere that caused deadlock during concurrent class loading.
Bug fix: fixed an integration issue with WildFly that caused AssertionError after reloading EJB classes.
Bug fix: fixed a bug that caused infinite loop on Windows if an NTFS junction point pointing to a network location was on class path.

2019.1.2 (20th May 2019)

Feature: added support for Tomcat 7.0.94, 8.5.40 and 9.0.19.
Feature: added support for Jetty 9.2.28, 9.3.27 and 9.4.17.
Feature: added support for WebSphere *******1.
Feature: added support for WebSphere Liberty Profile 1*******.
Feature: added support for Google App Engine 1.9.73.
Feature: added support for Wicket 7 and 8.
Improvement: improved integration with JBoss and WildFly reducing memory overhead.
Improvement: improved integration with CXF adding support for reloading @WebServices with interceptors.
Bug fix: fixed an integration issue with WebLogic and JAX-RS that caused NullPointerException during deployment.
Bug fix: fixed an integration issue with Spring and OSGi finding duplicate resources during classpath scanning.
Bug fix: fixed an integration issue with Jetty that caused StackOverflowError when multiple mappings for DefaultServlet are present.
Bug fix: fixed an integration issue with WebSphere when JMX remote management is enabled.
Bug fix: fixed an integration issue with ATG that caused IllegalArgumentException for non-existing ATG-Config-Path entries.
Bug fix: fixed an integration issue with OpenWebBeans on TomEE that caused deployment failures when using exploded deployment.
Bug fix: fixed an integration issue with WebLogic where resource lookup from shared .war library could fail when using a Remote Server setup.
Bug fix: fixed an integration issue with BVal that caused ClassCastException to be thrown.
Bug fix: fixed an integration issue with Weld where resolution of a specialized bean could result in null after reload.
Bug fix: fixed a bug that caused the static initialization of a class to not run if the class was reloaded before having been initialized.
Bug fix: fixed a bug that caused NoSuchMethodError when adding fields to a nested class compiled with Java 11.
Bug fix: fixed a bug that could cause JRebel to throw MethodTooLargeException during class loading.
Bug fix: fixed a bug that could cause NullPointerExceptions after removing a lambda.

2019.1.1 (15th April 2019)

Feature: added support for Tomcat 8.5.39 and 9.0.17.
Feature: added support for Thorntail 2.3 and 2.4.
Improvement: improved integration with Camel adding support for reloading XSLT endpoints when the backing .xsl resource is changed.
Bug fix: fixed a performance issue with Struts that caused slowdowns during validation.
Bug fix: fixed an integration issue with MyBatis 3 that caused issues with .xml config files.
Bug fix: fixed an integration issue with WildFly 16 running on JDK 11 that caused JRebel to throw errors during startup.
Bug fix: fixed an integration issue with Hibernate ORM that caused UnsupportedOperationException during startup due to hashCode methods with side-effects.
Bug fix: fixed an integration issue with Mojarra that caused NoClassDefFoundError during startup.
Bug fix: fixed an integration issue with Spring and Hibernate ORM that caused JRebel to throw errors during startup.

2019.1.0 (26th March 2019)

Feature: added support for Java 12.
Feature: added support for Eclipse OpenJ9 0.13.0.
Feature: added support for WildFly 16.
Feature: added support for WebSphere *******5.
Feature: added support for Payara 5.191.
Feature: added support for Hybris 1811.
Feature: added support for Weld 3.1.0.
Feature: added support for Vaadin 13.0.0.
Feature: added support for MyBatis Spring 2.0.0.
Improvement: improved integration with ATG, added support for loading configuration files from rebel.xml specified paths.
Improvement: improved integration with OpenWebBeans, added support for reloading @ApplicationScoped CDI beans.
Bug fix: fixed an integration issue with Tomcat 8.5.16.
Bug fix: fixed an integration issue with WebLogic ******** that caused NoSuchMethodError exceptions during startup.
Bug fix: fixed an integration issue with MyFaces where development mode was inadvertently enabled by JRebel.
Bug fix: fixed an integration issue with iBatis that caused NoSuchMethodError exceptions when the runtime bytecode enhancement feature was enabled.
Bug fix: fixed an integration issue with Kotlin 1.1 when using reflection.
Bug fix: fixed an integration issue with HK2 and Jersey when reloading resources.
Bug fix: fixed a bug that caused reflective accessibility to not always be honored on members of reloaded classes.

2018.2.7 (7th March 2019)

Feature: added support for WebSphere Liberty Profile 1*******.
Feature: added support for Tomcat 8.5.38 and 9.0.16.
Feature: added support for Resin 4.0.60.
Feature: added support for Google App Engine 1.9.72.
Bug fix: fixed an integration issue with IceFaces on TomEE where reloading a .jsf would fail as MyFaces was also present.
Bug fix: fixed an integration issue with OpenWebBeans on TomEE that caused a @Named bean with the same name as a previously existing named bean to not work.
Bug fix: fixed an integration issue with Spring Framework 5.1.3 that caused instrumentation errors during startup.
Bug fix: fixed a bug where reloading a class previously containing lambdas would cause warnings about lambdas not found.
Bug fix: fixed a bug with reloading classes adding new constructor method references.
Bug fix: fixed a bug where after reload the value of a static final field accessed directly could differ from when accessed reflectively.

2018.2.6 (14th February 2019)

Feature: added support for JBoss EAP 7.2.0.
Feature: added support for GlassFish 5.1.
Feature: added support for Wildfly 15.0.1.Final.
Feature: added support for Liferay 7.1.2-GA3.
Feature: added support for Azul Zing 11.
Feature: added support for HK2 2.5.0.
Bug fix: fixed an integration issue with Spring that caused NoSuchMethodError when changing return type of controller methods.
Bug fix: fixed an integration issue with Liferay.
Bug fix: fixed an integration issue with Spring and Hibernate that caused NullPointerException during initialization.
Bug fix: fixed an integration issue with IntelliJ IDEA 2018.2 that caused plugins classes to not reload correctly.
Bug fix: fixed an integration issue with Spring AOP that caused failure during reloading of proxy classes.
Bug fix: fixed an integration issue with Hibernate that caused DuplicateMappingException after reloading annotations.
Bug fix: fixed an integration issue with Jenkins 2 that caused issues when reloading classes.
Bug fix: fixed a bug with JRebel Agent's native component causing it to crash on Linux with GLIBC 2.4.

2018.2.5 (28th January 2019)

Feature: added support for Wildfly 15.0.0.Final.
Feature: added support for WebSphere *******0.
Feature: added support for WebSphere Liberty Profile 18.0.0.4.
Feature: added support for tcServer 3.2.13 and 4.0.4.
Feature: added support for Tomcat 9.0.14.
Feature: added support for Resin 4.0.59.
Feature: added support for Google App Engine 1.9.71.
Improvement: improved integration with OpenWebBeans, added support for converting an existing class to a managed bean.
Bug fix: fixed a performance regression in the Hybris integration causing slowdowns during reload.
Bug fix: fixed an integration issue with Tomcat that caused proxy settings defined in server.xml to be ignored.
Bug fix: fixed an integration issue with Tomcat that caused deadlock during reload.
Bug fix: fixed an integration issue with Springfox when run on application servers with Servlet API 2.5.
Bug fix: fixed an integration issue with WebSphere that caused NullPointerException during reload of EJBs.
Bug fix: fixed an integration issue with Weld that caused adding a @Specializes bean to not be registered correctly.
Bug fix: fixed an integration issue with Weld that caused a @Named bean with the same name as a previously existing named bean to not work.
Bug fix: fixed an integration issue with Apache CXF that caused NullPointerException during reload.
Bug fix: fixed an integration issue with Apache Log4j 2 that caused the configuration to be sporadically reconfigured.
Bug fix: fixed a bug where using BitSet caused NoSuchMethodError on Java 11.
Bug fix: fixed a bug where instanceof checks on lambdas and arrays could give incorrect results.
Bug fix: fixed a bug with reflection where the wrong method could be returned if an overloaded method with different return type was present.
Bug fix: fixed a bug where reloading a class with a broken static initializer could limit access to static members on the class.
Bug fix: fixed a bug where reloading the class hierarchy of a class could fail invoking methods in the new superclass.

2018.2.4 (19th December 2018)

Feature: added support for Tomcat 7.0.92.
Feature: added support for Jetty 9.4.14.
Feature: added support for Payara 5.184.
Improvement: improved integration with Kotlin, added support for coroutines and improved reflection support.
Improvement: improved integration with Weld, added support for reloading @ApplicationScoped CDI beans.
Improvement: improved integration with Springfox, added support for reloading Swagger @ApiModel properties.
Improvement: improved integration with app servers, added support for remapping /WEB-INF/web.xml location via rebel.xml paths.
Bug fix: fixed an integration issue with WebLogic and Weld that caused NoSuchMethodError when reloading an EJB class multiple times.
Bug fix: fixed an integration issue with GlassFish and Payara that caused CDI interceptors to be lost on reload.
Bug fix: fixed an integration issue with GlassFish and Payara that caused EJBs to not be injected correctly after a reload.
Bug fix: fixed an integration issue with ATG that caused VerifyError for VersionItemDescriptor.
Bug fix: fixed a bug acquiring a license from License Server if the proxyHost system property was present but blank.
Bug fix: fixed a bug that caused NullPointerException when libraries tried to load module-info.class file as a class.
Bug fix: fixed a bug when changing an interface into a class or vice versa.
Bug fix: fixed a bug with the debugger integration on IBM SDK that caused methods with reloaded parameters to not work correctly via evaluate expression.
Bug fix: fixed a bug with the debugger integration when using reflection to introspect classes via evaluate expression.

2018.2.3 (29th November 2018)

Feature: added support for Liferay 7.1.1-GA2.
Bug fix: fixed an integration issue with WebLogic that caused a NullPointerException during request handling.
Bug fix: fixed an integration issue with Hibernate and WildFly that caused NotYetReadyException when an EntityListener was present.
Bug fix: fixed an integration issue with EclipseLink that caused ClassCastException on IBM SDK 7.
Bug fix: fixed an integration issue with WebSphere ******** that caused ClassCastException during start-up.
Bug fix: fixed an integration issue with Spring where reconfiguration of a deserialized session bean could fail.
Bug fix: fixed an integration issue with Liferay.
Bug fix: fixed a bug that caused an illegal reflective access warning.
Bug fix: fixed a bug when changing the signature of the SAM on a functional interface.
Bug fix: fixed a bug reloading lambdas containing MethodHandles.
Bug fix: fixed a bug where deserializing a lambda could also cause it to be executed.
Bug fix: fixed a bug when using the debugger on IBM SDK 7.1 and 8.

2018.2.2 (6th November 2018)

Feature: added support for TomEE 7.1.0.
Feature: added support for Google App Engine 1.9.67.
Feature: added support for tc Server 3.2.12 and 4.0.3.
Feature: added support for Eclipse OpenJ9 0.11.0 (JDK 8 and 11).
Feature: added support for Spring Boot 2.1.
Feature: added support for ByteBuddy 1.9.
Improvement: improved integration with Vaadin, adding support for reloading Vaadin Flow @Route annotations.
Bug fix: fixed an integration issue with Spring Boot that caused InvalidPropertyException when autowiring properties from yaml files.
Bug fix: fixed an integration issue with Hybris that caused UnsupportedOperationException when accessing backoffice extensions.
Bug fix: fixed an integration issue with Payara when a SecurityManager is present.
Bug fix: fixed an integration issue with Spring Boot Actuator 2.0.6 that caused ClassCastException during initialization.
Bug fix: fixed an integration issue with WebLogic with classes added on the boot classpath.
Bug fix: fixed a bug in the debugger integration that caused it to freeze for long periods when reloading classes while suspended in a breakpoint.
Bug fix: fixed a bug where using reflection methods via method-reference lambdas could return incorrect data.

2018.2.1 (18th October 2018)

Feature: added support for WebSphere Liberty Profile 18.0.0.3.
Feature: added support for Jetty 9.3.25.
Feature: added support for Tomcat 7.0.91, 8.5.34 and 9.0.12.
Feature: added support for WebSphere 9.0.0.9.
Feature: added support for Wildfly 14.0.1.Final.
Feature: added support for Google App Engine 1.9.65.
Bug fix: fixed a performance issue with WebSphere Liberty Profile that was caused by excessive overhead for resource lookups during startup.
Bug fix: fixed an integration issue with Hybris that caused reloading problems in backoffice modules.
Bug fix: fixed an integration issue with JAX-RS on WebSphere 9.
Bug fix: fixed an integration issue with Apache Karaf 2.3.5.
Bug fix: fixed an integration issue with Mojarra that caused NullPointerException and StackOverflowError during reload.
Bug fix: fixed an integration issue with OpenWebBeans that caused beans.xml files to not always be found.
Bug fix: fixed an integration issue with Apache Felix that caused it to try to reload classes in stopped bundles.
Bug fix: fixed an integration issue with Liferay 7 that cause application .jsp files to not correctly reload.
Bug fix: fixed an integration issue with Gemini Blueprint that caused beans to be incorrectly resolved during reload.
Bug fix: fixed an integration issue with FileServlet that caused a StackOverflowError when matching certain URLs.
Bug fix: fixed an integration issue with Catalina that caused NullPointerException when running on Java 11.
Bug fix: fixed a bug to allow UNC paths to be used as rebel base.

2018.2.0 (2nd October 2018)

Feature: added support for Java 11.
Feature: added support for OpenJDK 10 and 11 with OpenJ9 (version 0.10.0 and newer).
Feature: added support for SapMachine 11.
Feature: added support for Hybris 6.6, 6.7 and 18.08.
Feature: added support for Wildfly 14.
Feature: added support for JBoss EAP 7.2.0 Beta.
Feature: added support for Tomcat 8.5.33 and 9.0.11.
Feature: added support for Jetty 9.2.26 and 9.4.12.
Feature: added support for Payara 5.183.
Feature: added support for Resin 4.0.58.
Feature: added support for Spring Framework 5.1 GA.
Feature: added support in JAX-RS for adding multiple Application classes at runtime.
Bug fix: fixed an integration issue with Liferay that caused MalformedURLException for resource lookups with relative paths.
Bug fix: fixed an integration issue with ADF.
Bug fix: fixed an integration issue with OpenWebBeans that caused AmbiguousResolutionException when adding a @Specializes bean.
Bug fix: fixed an integration issue with JBoss that caused rewrite rules to not be resolved correctly.
Bug fix: fixed a bug where StackWalker::getCallerClass could throw UnsupportedOperationException.
Bug fix: fixed a bug where invoking Object::getClass using reflection could return unexpected results.
Bug fix: fixed a bug throwing NoSuchMethodError when invoking an instance method after a static method with same name was added to a subtype.
Bug fix: fixed a bug where changing the hierarchy of a class could lead to incorrect resolution of static members.
Bug fix: fixed a bug where accessing an added package-private static member from a new child class could fail.

2018.1.7 (6th September 2018)

Feature: added support for Tomcat 7.0.90 and 8.0.53.
Feature: added support for tc Server 3.1.15, 3.2.11 and 4.0.2.
Feature: added support for TomEE 7.0.5.
Feature: added support for Resin 4.0.57.
Feature: added support for WebSphere *******4.
Feature: added support for Liferay 7.1.0-GA1.
Feature: added support for Vaadin 10.
Improvement: improved performance of the Jersey 2 integration, avoiding unnecessary reconfigurations when JAX-RS annotations were not changed.
Improvement: improved the Spring Framework integration adding support for reloading @ResponseStatus annotations on request methods.
Improvement: Improved support for Spring AOP where reloaded @Aspect classes are now applied to reloaded beans.
Bug fix: fixed an integration issue with Jersey 2 that caused NullPointerException when the Grizzly HTTP connector was used.
Bug fix: fixed an integration issue with Hibernate 5 running on WebSphere where persistence.xml would not be loaded from paths specified in rebel.xml.
Bug fix: fixed an integration issue with Spring Framework 2.0 if compiled without debug information.
Bug fix: fixed an integration issue with HK2 that caused ClassCastException when hierarchical service arrangement was used.
Bug fix: fixed an integration issue with IBM JDK/OpenJ9 and Remote Server sertup that caused SecurityException when using signed jar deployment.
Bug fix: fixed a bug with reflection where Class::getMethods could return incomplete set of methods.
Bug fix: fixed a bug where automatic initialization of an added field could fail or cause VerifyError.
Bug fix: fixed a bug where a reloaded class could be cast to an interface that it no longer implements.
Bug fix: fixed a bug where removing a default method could throw IllegalAccessError on use.
Bug fix: fixed a bug where an added package-private method could incorrectly override a method with same name and signature in a different package in the same hierarchy.

2018.1.6 (16th August 2018)

Feature: added support for Spring Framework 5.1 RC1.
Feature: added support for MyBatis 3.0.4.
Improvement: improved the Spring integration adding support for reloading @RequestMapping and similar annotations on interface methods.
Improvement: improved the Hibernate integration to support reloading mapping files automatically picked up from classpath.
Improvement: improved the performance of the Wicket integration.
Bug fix: fixed an integration issue with Spring that caused unnecessary reloads of @Configuration beans.
Bug fix: fixed an integration issue with WebLogic that caused NoClassDefFoundError exception during startup.
Bug fix: fixed an integration issue with Spring and Camel failing to serialize a PropertyValue with an IllegalStateException.
Bug fix: fixed an integration issue with AspectJ that causes deadlocks during AspectJ's load-time weaving transformation.
Bug fix: fixed an integration issue with Geronimo that caused deadlocks during class loading.

2018.1.5 (26th July 2018)

Feature: added support for Tomcat 8.5.32 and 9.0.10.
Feature: added support for WebSphere 9.0.0.8.
Feature: added support for WebSphere Liberty Profile 18.0.0.2.
Feature: added support for Wildfly 13 running in EE8 preview mode.
Feature: Added support for SAP JVM 8 and SAPMachine 10.
Improvement: improved the performance of the Jersey integration for requests.
Improvement: improved the integration with PrettyFaces adding support for reloading XML configuration from all sources.
Bug fix: fixed an integration issue with Mojarra that caused UnsupportedOperationException when unsubscribing from events.
Bug fix: fixed an integration issue with Weld 2 on GlassFish.
Bug fix: fixed an integration issue with DeltaSpike that caused NotSerializableException during deployment.
Bug fix: fixed an integration issue with Spring Boot Actuator that caused BeanInstantiationException during startup.
Bug fix: fixed an integration issue with Jersey 2 that caused NullPointerException during startup.
Bug fix: fixed an integration issue with Spring Integration that cause excessive new beans to be added.
Bug fix: fixed an integration issue with Eclipse Debugger that failed when inspecting for reloaded classes.
Bug fix: fixed an integration issue with WebSphere 9 that caused a deadlock during startup.
Bug fix: fixed an bug where reloading interfaces with a non-reloadable super interface could cause NoSuchMethodError.

2018.1.4 (5th July 2018)

Feature: added support for Payara 5.182.
Feature: added support for Jetty 9.2.25, 9.3.24 and 9.4.11.
Feature: added support for Google App Engine 1.9.64.
Feature: added support for ModelMapper.
Feature: added support for PowerMock.
Improvement: improved the performance of the Jersey integration on requests.
Improvement: improved the performance of the ADF integration.
Improvement: improved the startup performance of the Spring and Hibernate ORM integrations when classpath scanning with package filters are used.
Bug fix: fixed an integration issue with Spring Boot 2.1.
Bug fix: fixed an integration issue with Spring that could cause excessive log lines of the same bean being added multiple times.
Bug fix: fixed an integration issue with Spring ORM that could cause FileNotFoundException on resource lookups.
Bug fix: fixed an integration issue with Weld 2 and 3 where reloading a @Producer method would not reinject the bean.
Bug fix: fixed an integration issue with WildFly that could cause the server to freeze after a reload.
Bug fix: fixed an integration issue with Undertow.
Bug fix: fixed an integration issue with JBoss 4 that could cause SkipPageException when reloading .jsp files.
Bug fix: fixed an integration issue with Liferay that caused .jsp files to not reload in a Remote Server setup.
Bug fix: fixed an integration issue with WebSphere that caused NullPointerException in a Remote Server setup using HTTPS.
Bug fix: fixed an integration issue with WebSphere that could cause failure when looking up newly added resources.
Bug fix: fixed an integration issue with WebSphere where a new @EJB field would not be injected during a remote EJB invocation.
Bug fix: fixed an integration issue with EclipseLink that attempted to execute an operation on a closed EntityManagerFactory.
Bug fix: fixed a performance issue with EclipseLink when using java.util.logging.
Bug fix: fixed an integration issue with Jersey CDI that could cause multiple service locators to be detected when reloading.
Bug fix: fixed a bug in JRebel that could cause crash during JVM shutdown.
Bug fix: fixed a bug in JRebel that could cause ArrayIndexOutOfBoundsException when reloading.

2018.1.3 (14th June 2018)

Feature: added support for WildFly 13.
Feature: added support for Jetty 9.4.10.
Feature: added support for Liferay 7.0.6.GA7.
Feature: added support for Tomcat 7.0.88.
Feature: added support for Hibernate ORM 5.3.0.
Improvement: improved the startup performance of consecutive startups of the same application.
Improvement: improved the performance of the Spring WS integration, reducing the request time overhead.
Improvement: improved the performance of the Metro integration, reducing the request time overhead.
Bug fix: fixed an integration issue with WebLogic where reloading of a precompiled EJB could fail.
Bug fix: fixed an integration issue with WebLogic where reloading of EJBs accessed via RMI could fail.
Bug fix: fixed a bug on IBM SDK when reflectively looking up methods with reloaded parameter types.
Bug fix: fixed an integration issue with the IntelliJ IDEA debugger when stepping out of methods.
Bug fix: fixed an integration issue with Spring WebFlux running on TomEE 7.
Bug fix: fixed a bug where reloading a type could cause arrays of that type to lose their identity.
Bug fix: fixed a bug where changing the hierarchy of a class would initialize the new super type incorrectly.
Bug fix: fixed an integration issue with Spring MVC 4.3.16.
Bug fix: fixed an integration issue with Karaf not correctly finding resources from paths specified in rebel.xml.
Bug fix: fixed an integration issue with Equinox OSGi framework bundled with Eclipse Photon.
Bug fix: fixed a bug where changing the hierarchy and simultaneously reloading multiple classes in that hierarchy could fail.

2018.1.2 (24th May 2018)

Feature: added support for Tomcat 7.0.86, 8.0.52, 8.5.31, and 9.0.8.
Feature: added support for WebSphere ******** and ********.
Feature: added support for tc Server 3.1.14, 3.2.10, and 4.0.1.
Feature: added support for Resin 4.0.56.
Feature: added support for Liferay 7.0.5.GA6.
Bug fix: fixed an integration issue with Spring and Hibernate that caused IllegalStateException because the Persistence unit was already obtained.
Bug fix: fixed a reloading issue when changing the hierarchy of a class.
Bug fix: fixed an integration issue with Spring that caused a NullPointerException during the reload of beans depending on other beans.
Bug fix: fixed a StackOverflowError caused by resource-names containing certain unicode characters located on case-insensitive file systems.
Bug fix: fixed an integration issue with Kotlin that caused an IllegalAccessError to be thrown.
Bug fix: fixed an integration issue with EclipseLink that caused the resolution of persistence.xml relative jar-file entries to fail.
Bug fix: fixed an integration issue with EclipseLink that caused EntityManagers shared between multiple applications to appear closed.
Bug fix: fixed and integration issue with Tomcat that failed to look up a web resource from a jar file if its parent folder was not explicitly listed in the jar file's index.
Bug fix: fixed a ClassLoader integration issue that caused a NullPointerException when reloading a Proxy.
Bug fix: fixed an issue with reloading types implementing multiple interfaces declaring the same method.
Bug fix: fixed an integration issue with Camel that caused a CamelExecutionException due to a race condition during the reload of a class.
Bug fix: fixed an integration issued with Spring where @Configuration beans were not proxied correctly after reload.
Bug fix: fixed an integration issue with OpenWebBeans when reloading an interceptor changing the target method.
Bug fix: fixed an integration issue with WildFly that caused undeploying applications to get stuck after the EJB beans had been reloaded.
Bug fix: fixed a VerifyError when adding a static method to a child type of a super type containing a method with the same name.

2018.1.1 (7th May 2018)

Feature: added support for WebSphere Liberty Profile 18.
Feature: added support for Payara 5.
Feature: added support for tc Server 4.
Feature: added support for WebSphere 9.0.0-7.
Feature: added support for Jetty 9.3.23 and 9.4.9.
Feature: added support for Tomcat 8.5.29 and 9.0.6.
Feature: added support for Dozer 6.2.0.
Improvement: improved overall performance for Spring Boot when using Java 9 or Java 10.
Bug fix: fixed an integration issue with Spring that caused destroy() method not to be invoked for anonymous inner beans.
Bug fix: fixed an integration issue with Spring that could cause deadlocks when reloading a bean.
Bug fix: fixed a VerifyError when trying to reload classes with dead code on bytecode level.
Bug fix: fixed a bug with IBM JDK that caused start failure when multiple -Xverify JVM options were present.
Bug fix: fixed an integration issue with WildFly that caused exceptions during shutdown or undeploy.
Bug fix: fixed an integration issue with WebLogic that caused SecurityHelper to throw a ConcurrentModificationException during deploy.
Bug fix: fixed an integration issue with JBoss 5 that caused a deadlock during startup.
Bug fix: fixed an integration issue with WebLogic that caused the server to freeze during reload.
Bug fix: fixed an integration issue with Spring when different versions of the spring jars were present.
Bug fix: fixed an integration issue with GlassFish 3 that caused a deadlock when reloading an EJB bean.
Bug fix: fixed an integration issue with Hibernate ORM 5 that caused a NullPointerException to be thrown during startup.
Bug fix: fixed an integration issue with JBoss 7 where recreating an EJB using @RemoteHome failed with an IllegalStateException.
Bug fix: fixed an Integration issue with ByteBuddy 1.2.1.
Bug fix: fixed an integration issue with Weld 1 when reloading an interceptor changing the target method.
Bug fix: fixed various issues when reloading classes with a changed class hierarchy.
Bug fix: fixed an issue which caused NoSuchMethodError for default interface methods.
Bug fix: fixed an issue that caused a VerifyError when reloading a class that has a non-static and static method with the same signature in the hierarchy.

2018.1.0 (2nd April 2018)

Feature: added support for Java 10.
Feature: added support for Spring Boot 2.
Feature: added support for WildFly 12.
Feature: added support for Spring Boot Actuator 2.
Feature: added support for Tomcat 7.0.85, 8.0.50, 8.5.28, and 9.0.5.
Feature: added support for Google App Engine 1.9.63.
Feature: added support for Payara 4.1.2.181.
Feature: added support for tc Server 3.1.13 and 3.2.9.
Feature: added support for Guice 4.2.
Improvement: improved integration with Google App Engine when using the Java 8 Runtime.
Bug fix: fixed some Java Platform Module System incompatibilities with Azul Zulu 9.
Bug fix: fixed an integration issue with Weld on WebLogic that sometimes caused failures in recognizing new EJB beans when beans.xml was not present during deployment.
Bug fix: fixed an integration issue with OpenWebBeans on TomEE that sometimes caused failures in recognizing new EJB beans when beans.xml was not present during deployment.
Bug fix: fixed an integration issue with Log4J2 2.9.0+ that caused failures when reloading configuration changes.
Bug fix: fixed a VerifyError on Oracle JVM 8u161 running with the -Xverify:all option.
Bug fix: fixed an integration issue with Spring that caused a ClassCastException with ScheduledMethodRunnable.
Bug fix: fixed a potential ClassNotFoundException when deploying applications using Weld and GWT on WebLogic.
Bug fix: fixed an integration issue with Spring, where reloading @Transactional annotated bean with cyclic dependencies could fail.
Bug fix: fixed an integration issue with EclipseLink's java agent that caused runtime weaving to fail, disabling lazy initialization of OneToOne and ManyToOne relationships.
Bug fix: fixed an integration issue with Weld 2 where CDI interceptors were not being applied to methods added to managed beans.
Bug fix: fixed an integration issue with using Jersey and Weld together on WebLogic that caused DefinitionException with unable to create an InjectionTarget.
Bug fix: fixed an issue reloading plugin classes when developing IntelliJ IDEA plugins.
Bug fix: fixed a startup issue with Hibernate ORM 5.2.13+.
Bug fix: fixed an integration issue with JBoss EAP 7.1 and WildFly 11, prohibiting correct undeployment of applications.
Bug fix: fixed an integration issue with Tomcat that could cause its classloader to define classes with the wrong name, causing a NoClassDefFoundError.
Bug fix: fixed an integration issue that could cause a LinkageError with a duplicate class definition.
Bug fix: fixed an integration with WebSphere Liberty Profile 17 that caused a VerifyError when started in debug mode.
Bug fix: fixed an integration issue with Hybris, caused by incorrectly ordered Spring beans.
Bug fix: fixed a startup issue with JRebel Agent when multiple java processes were started simultaneously.
Bug Fix: fixed an integration issue with Spring JPA, which could cause deployment issues, unable to resolve ServletContextResource.
Bug fix: fixed an integration issue with Dozer 5.3.2.
Bug fix: fixed a regression in failing to initialize newly added static fields with complex initialization.
Bug fix: fixed an issue reloading classes after their file system resource had been removed, for instance by a clean build.
Bug fix: fixed an startup error when rebel home was declared as a relative path on Windows.

7.1.7 (1st March 2018)

Feature: added support for WebSphere *******3.
Feature: added support for Google App Engine 1.9.62.
Feature: added support for Dozer reloading bean-mappings files.
Feature: added support for Grails 3.3.2.
Improvement: improved integration with the Java Platform Module System, adding support for reloading classes and modules defined via ModuleLayer.
Bug fix: fixed an integration issue with Spring Data causing IllegalStateException when redeploying an application.
Bug fix: fixed an integration issue with Spring Boot 2 and GraphQL causing an UnsatisfiedDependencyException on startup.
Bug fix: fixed an integration issue with JoinFaces 3 RC1, which could cause a bean to lose its scope after reloading.
Bug fix: fixed an issue when reloading @Scheduled methods in Spring, ensuring other configuration changes have also been reloaded before it.
Bug fix: fixed a NoSuchMethodError when using Flight Recorder, which could cause the JVM to fail to start.
Bug fix: fixed an integration issue with IBM SDK 7.0.
Bug fix: fixed a startup issue when running IBM Domino using a newer version of IBM SDK.
Bug fix: fixed an issue causing metadata in the Manifest to be ignored when loading classes using WebSphere Liberty Profile 8.5.5.
Bug fix: fixed an integration issue with Oracle ADF, failing to load JPX from a shared application module.
Bug fix: fixed a performance issue when injecting @EJBs into Servlets or WebServices using WebLogic.
Bug fix: fixed a performance issue on WebLogic during startup and reload when multiple <include> filters were present in rebel.xml.

7.1.6 (6th February 2018)

Feature: added support for JBoss EAP 7.1.0.
Feature: added support for Jetty 9.2.24.
Feature: added support for Tomcat 7.0.84, 8.0.49, 8.5.27 and 9.0.4.
Feature: added support for Oracle JDK 9.0.4 and 8u162.
Feature: added support for IBM SDK *******.
Feature: added support for Azul Zing *********.
Feature: added support for Spring Integration, reloading service activators, gateways and channels.
Improvement: improved the reloading speed on WebLogic 10 series when multiple EJB beans are present.
Improvement: improved integration with Hibernate Validator by adding support for reloading validation messages properties files.
Bug fix: fixed an integration issue with Spring Data REST 3.0.3.
Bug fix: fixed an integration issue with Hybris 6.6.
Bug fix: fixed a bug that caused metadata in the Manifest file to be ignored when loading classes on Tomcat 7.
Bug fix: fixed an integration issue with JSF, where the @PreDestroy method of a ViewScoped bean would not be called after reloading the class.
Bug fix: fixed an integration issue with Spring, where even after correcting a flawed bean definition, the bean would not be autowired as expected.
Bug fix: fixed a regression causing a StackOverflowError when running with FlightRecorder in debug mode.
Bug fix: fixed an issue where reloading a local class compiled with certain versions of EJC caused a StackOverflowError.
Bug fix: fixed an issue with accessing fields from a reloaded class in constructors of non-reloadable classes.
Bug fix: fixed an integration issue with Weld sometimes failing to recognize new EJB beans when beans.xml was not present during deployment.
Bug fix: fixed an integration issue on JBoss and WebSphere when injecting an @EJB into a JAX-WS @WebService.
Bug fix: fixed an integration issue where an added @Inject'ed field on the super class of an @EJB bean would not be injected.

7.1.5 (17th January 2018)

Feature: added support for Tomcat 8.0.48.
Feature: added support for Jetty 9.2.23.
Feature: added support for WebSphere Liberty Profile 17.0.0.4.
Feature: added support for WebSphere 9.0.0.6.
Feature: added support for GAE 1.9.60.
Improvement: improved the Spring AOP integration to support adding the first @Transactional method to a Spring bean.
Improvement: improved EJB integration on WebLogic 12.x and TomEE to support adding new EJBs as JAX-RS endpoints.
Improvement: improved integration with the RESTEasy Spring integration to support adding new root resource classes.
Improvement: improved general GAE performance with JRebel.
Improvement: improved integration with other Java agents that register retransformation capable class file transformers.
Bug fix: fixed Liferay Faces Bridge issue with reloading JSF beans.
Bug fix: fixed an IllegalAccessError when changing a non-static final field value using reflection on JDK 9.
Bug fix: fixed property getters/setters in reloadable classes managed by Spring Data.
Bug fix: fixed an integration error with Spring Security versions older than 3.0.6.
Bug fix: fixed the CodeSource location for reloadable classes that originate from JARs.
Bug fix: fixed a potential ConcurrentModificationException when reloading OpenWebBeans beans.
Bug fix: fixed injecting new EJB into the JAX-WS web service on TomEE.
Bug fix: fixed the inconsistent EclipseLink reloading behaviour when a request is made immediately after entity reload.

7.1.4 (20th December 2017)

Feature: added support for Tomcat 8.5.24 and 9.0.2.
Feature: added support for Jetty 9.4.8.
Feature: added support for Resin 4.0.55.
Feature: added support for Liferay 7.0.4.GA5.
Feature: added support for Spring Boot 2.0.0.M7.
Feature: added support for IBM Java 8 Service Refresh 5 Fix Pack 5.
Feature: added support for Struts 2.5.14.
Feature: added support for Spring Data JPA 2.0.
Improvement: improved Tomcat startup performance when using signed JAR files on Tomcat 8.0.x.
Improvement: improved the Spring MVC integration to support renaming @Controller beans.
Improvement: improved the Spring MVC integration to support adding @Validated to existing controllers.
Improvement: improved EJB integration on GlassFish 3.x, WebSphere Liberty Profile and WebSphere to support adding new EJBs as JAX-RS endpoints.
Bug fix: fixed the CodeSource location for classes loaded from JARs using Tomcat WebappClassLoader.
Bug fix: fixed custom JSF @FacesComponent components getting lost after a reload.
Bug fix: fixed an issue that in some cases prevented stepping over lines during a debug session.
Bug fix: fixed a small change in a Spring configuration file causing the reinitialization of the whole CamelContext.
Bug fix: fixed adding new JAX-RS resource classes when using the ResteasyBootstrap listener.
Bug fix: fixed property placeholders not being expanded after reloading a Spring bean definition on Hybris.
Bug fix: fixed a VerifyError when using a finalize interface method in Scala 2.12.
Bug fix: fixed a rare case where debugger failed to reconnect with the IBM JVM.
Bug fix: fixed a startup error when -Duser.home property was set to an empty directory on IBM Java 1.6, released after July 2011.
Bug fix: fixed an issue where the standalone activation dialog left hanging indefinitely while displaying "Checking license status...".
Bug fix: fixed reloading Spring @Configuration class conditional bean definitions that use the same bean name.
Bug fix: fixed an IllegalAccessError when calling getEntityPersisters on SessionFactoryImpl on Hibernate 5.2+.
Bug fix: fixed an integration issue with Tapestry 5 versions older than 5.3.0.
Bug fix: fixed application startup with some OpenL Ruleservice configurations.
Bug fix: fixed an issue with injecting a Guice non-singleton bean after changing the signature of its constructor.
Bug fix: fixed an IllegalAccessException thrown by Hibernate when accessing private fields of a reloaded entity.
Bug fix: fixed an UnsupportedOperationException when a static or default interface method is evaluated via a debugger.
Bug fix: fixed an IllegalArgumentException: Invalid method error when when a reloaded method is evaluated via a debugger.

7.1.3 (30th November 2017)

Feature: added support for TomEE 1.7.5.
Feature: added support for Payara *********.
Feature: added support for GAE 1.9.59.
Feature: added support for reloading Spring @ControllerAdvice methods.
Feature: added support for changing MyBatis mapper entries.
Bug fix: fixed an issue with cloning an array of a reloaded type when compiled with Eclipse Compiler.
Bug fix: fixed an integration issue with Spring Boot Actuator 0.5.0.M6.
Bug fix: fixed Hibernate second level cache not being refreshed on entity reload.
Bug fix: fixed a regression from JRebel 7.1.2 concerning Spring initialization of complex inner bean hierarchies.
Bug fix: fixed an issue in Spring when bean reload caused the bean constructor changes to be no longer reloadable in some cases.
Bug fix: fixed an ArrayIndexOutOfBoundsException when creating an EnumSet from empty enums.
Bug fix: fixed a EvaluationException when the debugger rendered a Scala collection in IntelliJ IDEA with the Scala plugin installed.
Bug fix: fixed a serialization issue with GigaSpaces data grid client.
Bug fix: fixed reloading JavaEE annotations in WebLogic WAR modules that contain metadata-complete fragments.

7.1.2 (9th November 2017)

Feature: added support for GlassFish 5.
Feature: added support for WildFly 11.0.0.Final.
Feature: added support for WebSphere ******** and *******.
Feature: added support for WebSphere Liberty Profile 17.0.0.3.
Feature: added support for TomEE 7.0.4 plus and 7.0.4 plume.
Feature: added support for Tomcat 7.0.82 and 8.0.47.
Feature: added support for tc Server 3.1.12 and 3.2.8.
Feature: added support for GAE 1.9.58.
Feature: added support for Apache Tiles 3.0.7.
Feature: added support for WebLogic 12.2.1 running on Java 9.
Feature: added support for reloading RouterFunction beans in Spring Boot.
Feature: added support for OpenWebBeans 1.7.
Improvement: improved the startup performance of Spring when MBeanExporter is used.
Improvement: the default value for the command -go-offline is now set to 7 days.
Improvement: improved the Spring integration to avoid recreating inner beans when the XML definition is not changed.
Bug fix: fixed IllegalStateException: EntityManagerFactory is closed after reload when a session was created eagerly.
Bug fix: fixed a NotSerializableException when serialising the Weld 2.x bean identifier.
Bug fix: fixed an issue with injecting new EJB references into Weld 1.x beans on WebLogic.
Bug fix: fixed a rare NoSuchMethodError when starting WebSphere.
Bug fix: fixed a bug where AtomicReference<T>#compareAndSet check could fail after the reload of T.
Bug fix: fixed an integration issue with SpringFox 2.3 and 2.7.0+.
Bug fix: fixed reloading classes generated by CGLIB 3.2.1, 3.2.2 and 3.2.3.
Bug fix: fixed leaking file descriptors with older versions of Jetty.
Bug fix: fixed Guice’s Injector#getExistingBinding returning null after a class reload.

7.1.1 (19th October 2017)

Feature: added support for Tomcat 8.5.23 and 9.0.1.
Feature: added support for Jetty 9.3.21.
Feature: added support for tc Server 3.1.11 and 3.2.7.
Feature: added support for GAE 1.9.57.
Feature: added support for Spring 5.0.0.RELEASE.
Feature: added support for Spring Boot 2.0.0.M4.
Feature: added support for IBM Java 8 Service Refresh 5.
Feature: added support for Oracle JDK 8u144.
Feature: added support for ByteBuddy proxy generation.
Feature: added support for Spring WebFlux on Netty.
Improvement: reduced memory overhead when creating EnumSet instances.
Bug fix: fixed a regression from JRebel 7.1.0 that caused deploy errors on WildFly when an EJB's JAR file META-INF/MANIFEST.MF had Class-Path elements.
Bug fix: fixed various issues that caused memory to be leaked upon the undeploy of an application.
Bug fix: fixed the debugger sometimes disconnecting after a class reload when stepping with HotSwap disabled on IntelliJ IDEA.
Bug fix: fixed classes getting processed twice when running with the NewRelic agent and having REBEL_BASE set to a path containing symlink.
Bug fix: fixed a StackOverflowError when instrumenting a class that contains a very large method with lots of branching.
Bug fix: fixed an issue where reloading an application undeployed the data source defined in web.xml on Payara.
Bug fix: fixed an integration issue with OpenWebBeans 1.7.
Bug fix: fixed a rare StackOverFlowError when reloading Spring beans defined in @Configuration class.
Bug fix: fixed an integration issue with Hibernate Core 5.2.3+.
Bug fix: fixed a ClassNotFoundException caused by a race condition in Tomcat classloader integration.

7.1.0 (28th September 2017)

Feature: added support for Java 9 to JRebel Agent.
Feature: Legacy Agent no longer supports Java 1.4.
Feature: added support for Jetty 9.4.7.
Feature: added support for WebLogic ********.
Feature: added support for Google App Engine 1.9.56.
Feature: added support for Spring 5 RC4.
Feature: added support for CXF 3.2.
Improvement: improved invocation time for reloaded lambda expressions.
Improvement: improved the performance of JAX-RS endpoint reloading on WebLogic.
Improvement: improved MyBatis integration by reloading mapper interfaces registered via the MapperScan annotation.
Improvement: improved MyBatis integration by reloading the <sql> fragments in mapper XMLs.
Bug fix: fixed issue with ClassValue recomputing the value after reloading a class.
Bug fix: fixed TomEE deployer failing with an incompatible serialVersionUID Exception.
Bug fix: fixed changing a non-capturing lambda into a capturing lambda.
Bug fix: fixed an issue with lambda reloading when lambda implementation was reloaded first and the lambda interface later.
Bug fix: fixed reloading of Swagger 2 API operations when used via SpringFox.
Bug fix: fixed embedded Hibernate on WildFly/JBoss sometimes finding wrong Entity classes.
Bug fix: fixed Weld 2 reload causing an IllegalStateException in concurrent HTTP requests.
Bug fix: fixed a VerifyError with Spring Data Commons 2.0.0.RC2.
Bug fix: fixed a possible race condition that could cause @WebListeners to run multiple times on Jetty.
Bug fix: fixed an issue that prevented JRebel users from canceling offline seat mode when using License Server 3.0.4 or older.

7.0.15 (11th September 2017)

Feature: added support for Tomcat 8.0.46 and 7.0.81.
Feature: added support for WildFly 11.0.0.CR1.
Feature: added support for Payara *********.
Feature: added support for Hibernate Validator 6.0.
Feature: added support for Spring 5 RC3.
Feature: added support for Jackson 2.9.
Feature: added support for Mojarra 2.3.
Improvement: improved Spring Boot integration by reloading logging configuration changes in application.properties.
Bug fix: fixed a possible NPE when Grails 2 application is deployed in exploded mode.
Bug fix: fixed a bug in the Mojarra integration where managed beans might be lost after a reload.
Bug fix: fixed issue with Remote Server support for Spring Boot applications using libraries also containing rebel-remote.xml files.
Bug fix: fixed a bug in the Spring integration that caused NPEs when having custom implementations of “org.springframework.core.io.Resource”.
Bug fix: fixed a potential StackOverflowError when Flight Recorder was used together with JRebel.
Bug fix: fixed a rare startup error on WebSphere with IBM JVM.
Bug fix: fixed class changes not being detected in Equinox bundles when using "reference:file:" URLs.
Bug fix: fixed resolving Spring 4.3+ property placeholders when no property resolvers are explicitly configured.
Bug fix: fixed and improved the error reporting when taking offline licenses via the JRebel CLI tool.

7.0.14 (22nd August 2017)

Feature: added support for Tomcat 8.5.20 and 9.0.0.M26.
Feature: added support for Virgo 3.7.2.
Feature: added support for WebSphere ********.
Feature: added support for Struts 2.5.12.
Feature: added support for reloading logging related properties in Spring Boot application properties.
Improvement: improved Spring reload speed when no MVC mapping beans were changed.
Bug fix: fixed a possible NPE when loading a proxy through RMIClassLoader.loadProxyClass.
Bug fix: fixed an issue with changing the signature of a Guice singleton constructor.
Bug fix: fixed a rare issue with deploying/starting apps on Java EE containers when no EJBs were present.
Bug fix: fixed multiple SpringViewDisplayRegistrationBeans for the same UI being created on Vaadin Spring 2.0.
Bug fix: fixed a possible stack overflow when reloading a class containing @Schedule annotated method on GlassFish.
Bug fix: fixed a No session found exception when reloading a domain class on Grails ORM for Hibernate 5.
Bug fix: fixed an issue that caused the standalone activation dialog not to appear for 30 seconds when License Server was unavailable.
Bug fix: fixed an issue where trial activation in the standalone activation dialog did not switch to a proper view after completion.

7.0.13 (31th July 2017)

Feature: added beta support for Java 9.
Feature: added support for Weld 3.
Feature: added support for Vaadin 8.
Feature: added support for EclipseLink 2.6.
Feature: added support for Tomcat 7.0.79, 8.0.45, 8.5.16 and 9.0.0.M22.
Feature: added support for Liferay 7.0.3.GA4.
Feature: added support for Google App Engine 1.9.54.
Feature: added support for Resin 3.1.16 and 4.0.53.
Feature: added support for Virgo 3.7.1.
Improvement: added rebel.xml jar element support for CDI bean archives on Weblogic.
Improvement: improved reload performance in Remote Server setups.
Improvement: improved reload performance on Google App Engine when using security managers.
Improvement: improved support for reloading hierarchy changes to EJB classes on JBoss.
Bug fix: fixed reloading issue after dynamically attaching another agent.
Bug fix: fixed Spring Framework @Scheduled methods being registered multiple times during reload.
Bug fix: fixed JRebel Agent startup errors on Linux, when the user home directory was missing.
Bug fix: fixed Camel and CDI applications failing to reload with AmbiguousResolutionException.
Bug fix: fixed JAX-RS @Context annotated fields being set to null after a reload on WebSphere 8.x.
Bug fix: fixed duplicate class loading on Eclipse Equinox OSGi container.
Bug fix: fixed Hibernate ORM reloading failing with "EntityManagerFactory is closed".
Bug fix: fixed issue with Hibernate ORM integration, causing EntityManagerFactory to be unserializable.
Bug fix: fixed a potential NullPointerException when reloading Weld beans.
Bug fix: fixed debugger in IntelliJ IDEA throwing ClassNotPreparedException when reloading a class with breakpoints.
Bug fix: fixed an IllegalAccessException from the debugger after changing a field's modifier to private.

7.0.12 (11th July 2017)

Bug fix: reverted startup performance improvement from JRebel 7.0.11 that caused deployment failures in Tomcat versions older than 7.0.70.

7.0.11 (6th July 2017)

Feature: added support for WebSphere *******.
Feature: added support for WebSphere Liberty Profile ********.
Feature: added support for Jetty 9.2.22, 9.3.20 and 9.4.6.
Feature: added support for tc Server 3.2.6 and 3.1.10.
Feature: added support for TomEE 7.0.3.
Feature: added support for Jersey 2.26+ (JAX-RS 2.1) milestone builds.
Improvement: improved the handling of large methods in updatable classes to avoid "Method code too large" exceptions.
Improvement: improved Tomcat startup performance when using signed JAR files.
Bug fix: fixed JAX-RS @Context annotated fields being set to null after a reload on WebSphere Liberty Profile.
Bug fix: fixed an issue with Jersey 2 where endpoints were lost after a reload.
Bug fix: fixed a class loader leak with Jersey when multiple applications using Jersey were deployed.
Bug fix: fixed a potential deadlock with Jersey applications during JVM start.
Bug fix: fixed an issue with changing producer methods in Weld.
Bug fix: fixed an issue with SwitchYard causing AmbiguousResolutionException when reloading a bean.
Bug fix: fixed reloading MyFaces with defined templates inside JAR files.
Bug fix: fixed an issue with MyBatis when using custom Spring resources.
Bug fix: fixed an issue with MyBatis where interceptors were lost after a reload.

7.0.10 (12th June 2017)

Feature: added support for Tomcat 7.0.78, 8.0.44, 8.5.15 and 9.0.0.M22.
Feature: added support for Google App Engine (GAE) 1.9.53.
Feature: added support for Resin 4.0.52.
Feature: added support for Payara *********.
Improvement: improved GlassFish and WebLogic integrations to support reloading EJBs from rebel.xml locations with include and exclude directives.
Improvement: improved WebSphere JAX-WS integration to use less memory and to reload faster.
Bug fix: fixed JAX-WS integration where unrelated endpoints would stop working when an endpoint was removed.
Bug fix: fixed reloading scenarios where a non-reloadable interface or parent class referenced reloadable classes.
Bug fix: fixed an NPE when starting WildFly 11.0.0.Alpha1.
Bug fix: fixed re-injecting @Singleton scoped beans in WebSphere Liberty Profile when using the cdi-1.0 feature.
Bug fix: fixed a possible deployment issue with two EJBs with the same name but a different declaring class on GlassFish or Payara.
Bug fix: fixed reloading resources accessed through a path containing multiple consecutive forward slashes.
Bug fix: fixed an integration issue with Magnolia 5.5.x.
Bug fix: fixed a VerifyError caused by erroneous Java 1.3 bytecode.
Bug fix: fixed include and exclude directives not taken into account for the META-INF/ejb-jar.xml file on WebLogic.
Bug fix: fixed a potential deadlock with remote applications during JVM start.
Bug fix: fixed reloading MyBatis when you have multiple applications using MyBatis deployed.
Bug fix: fixed Liferay themes not working after reloading on re-deployed applications.
Bug fix: fixed application not deploying on Payara when it contained Weld JAR files in EAR/lib.
Bug fix: fixed Spring beans with anonymous inner beans being re-injected unnecessarily.
Bug fix: fixed ClassCircularityError that could happen on OpenJDK when another Java agent is present.

7.0.9 (22nd May 2017)

Feature: added support for WebSphere ********.
Feature: added support for Jetty 9.3.19 and 9.4.5.
Feature: added support for Tomcat 9.0.0.M20.
Feature: added support for Google App Engine (GAE) 1.9.52.
Feature: added support for Spring 5 RC1.
Improvement: improved Weld 2 integration to support reloading beans in implicit CDI archives on non Java EE servers.
Improvement: improved CDI reloading speed on WebSphere with the Legacy agent.
Bug fix: fixed reloading classes in Windows folders containing non-ASCII characters in their path.
Bug fix: fixed the unnecessary generation of a new bootstrap JAR at each Hybris startup.
Bug fix: fixed HTML template requests sometimes taking a long time on Tapestry 4.
Bug fix: fixed various instances where JRebel failed to correctly reload lambdas.
Bug fix: fixed a NullPointerException upon reloading a Spring bean that resolves to null.
Bug fix: fixed an integration error in Apache Felix versions older than 1.6.
Bug fix: fixed a race condition that could cause a MappingException after reloading a Hibernate entity class.
Bug fix: fixed an integration issue with early Spring Boot 2.0.0-SNAPSHOT.

7.0.8 (2nd May 2017)

Feature: added support for WebSphere Liberty Profile 17.0.0.1.
Feature: added support for Virgo 3.7.0.
Feature: added support for GlassFish 4.1.2.
Feature: added support for tc Server 3.2.5 and 3.1.9.
Feature: added support for Jetty 9.4.4 and 9.3.18.
Feature: added support for Tomcat 6.0.53, 7.0.77, 8.0.43 and 8.5.14.
Feature: added support for Google App Engine (GAE) 1.9.51.
Feature: added support for Spring Boot  1.5.3 and 1.4.6.
Feature: added support for Weld 2.4.
Improvement: decreased the startup overhead when using Liferay.
Improvement: improved reloading use cases related to accessing static fields from new classes.
Bug fix: fixed an issue with JAX-RS endpoints becoming unavailable after reloading an application on TomEE.
Bug fix: fixed a VerifyError with classes that extend java.util.concurrent.Semaphore.
Bug fix: fixed a potential NPE in the Weld integration.
Bug fix: fixed an issue in Weld 2.x with re-injecting beans from producer methods when changing their qualifiers.
Bug fix: fixed VerifyError when using ObjectDB.
Bug fix: fixed startup errors when processing classes generated by Lucee.
Bug fix: fixed IllegalAccessException on reflective access to EnumSet::clone.
Bug fix: fixed checked exceptions thrown from default methods not being propagated properly on the Legacy Agent.
Bug fix: fixed Spring XML changes not being reloaded inside Gemini OSGi bundles.

7.0.7 (11th April 2017)

Feature: added support for WebSphere 9.0.0.3 and *******1.
Feature: added support for Payara 4.1.1.171.1.
Feature: added support for Tomcat 8.5.12, 8.0.42, 7.0.76 and 6.0.51.
Feature: added support for reloading OSGi bundles when MANIFEST.MF changes on Felix.
Feature: added support for Liferay DXP.
Improvement: improved reloading use cases related to accessing originally private or package private members from a new class.
Improvement: improved the Weld 2.x integration on GlassFish to support adding new beans into implicit CDI archives.
Bug fix: fixed a VerifyError when calling a new constructor, while the constructed object is passed as argument to another method.
Bug fix: fixed BeanDefinitionParsingException while reloading context with the <jms:annotation-driven> element.
Bug fix: fixed a potential ArrayIndexOutOfBoundsException on WebLogic 12.x when JRebel caused some additional JARs to be scanned for EJBs.
Bug fix: fixed recovering from a Spring bean constructor signature change with a missing bean when the bean is added later on.
Bug fix: fixed reloading WEB-INF/lib/*.jar/META-INF/resources on WildFly when the application is deployed to root context.
Bug fix: fixed a potential TimeoutException when connecting to the debugger.
Bug fix: fixed finding Spring Data custom repository implementation.
Bug fix: fixed a potential deadlock on WebSphere startup.
Bug fix: fixed handling ServletContext::getResource for resource names without a leading slash.
Bug fix: fixed a potential ClassCastException from Axis2 integration.
Bug fix: fixed a compatibility issue with Serp.
Bug fix: fixed using a class that was changed to an interface from an unchanged class.
Bug fix: fixed the Hibernate "EntityManagerFactory is closed" exception after reloading Spring configuration.
Bug fix: fixed a VerifyError on WebLogic 12.2.x and Oracle JDK 8u121.
Bug fix: fixed a compatibility issue with Weld 2.2.8.
Bug fix: fixed JRebel not honoring beans.xml exclude entries on Weld 2.x.
Bug fix: fixed Enum.valueOf failing for added enum constants.

7.0.6 (23rd March 2017)

Feature: added support for Jetty 9.4.2.
Feature: added support for Google App Engine (GAE) 1.9.50.
Feature: added support for Resin 4.0.51.
Feature: added support for Tomcat 9.0.0.M17.
Feature: added support for Spring Boot 1.5.2.
Improvement: improved the handling of large methods in updatable classes to avoid "Method code too large" exceptions.
Improvement: improved performance when rebel.xml classpath directory contains a lot of *.xml and *.properties files.
Improvement: improved the Spring Boot integration by adding support for reloading application.properties values that are injected directly into a bean with @Value.
Bug fix: fixed Spring MVC integration not working when Spring Web was loaded in a parent classloader of Spring MVC.
Bug fix: fixed the JBoss classloader integration when modules.jar is in boot classpath.
Bug fix: fixed an integration issue with Jersey 1.0.3.
Bug fix: fixed EclipseLink not recognizing data sources declared in TomEE configuration (./conf/tomee.xml).
Bug fix: fixed AmbiguousResolutionException when rescanning beans in WildFly 10.1.0.
Bug fix: fixed an integration issue with WebLogic 12.1.3 with PSU 23744018.
Bug fix: fixed using a custom scoped bean as a DelegatingFilterProxy target potentially leading to exceptions when reloading any Spring bean.
Bug fix: fixed an IllegalAccessError upon calling the constructor with a package private argument.
Bug fix: fixed ClassCastException when using EclipseLink composite JPA units and runtime weaving.
Bug fix: fixed MyBatis XML mapping files not reloading when a custom implementation of SqlSessionFactory was used.
Bug fix: fixed an UnsupportedOperationException when using spring-web with TomEE.
Bug fix: fixed an issue with removing and adding inner classes.
Bug fix: fixed an issue with WebLogic 12.1.1 native EclipseLink initialization.
Bug fix: fixed an issue with a Weld CDI extension that alters bean metadata.
Bug fix: fixed new static field being null when initialized from a lambda expression.
Bug fix: fixed duplicate SessionBeans being created on rescanning beans in WildFly 10.1.0.
Bug fix: fixed a race condition in Tapestry4 integration that would cause templates not being reloaded.

7.0.5 (2nd March 2017)

Feature: added support for Spring Boot 1.5.
Feature: added support for Google App Engine (GAE) 1.9.49.
Improvement: improved the Spring MVC integration in cases where only the @PathVariable annotation of the endpoint method argument was changed.
Improvement: improved the WebLogic integration by adding support for reloading EJB 3.1 beans that have interfaces without annotations.
Improvement: improved the Liferay integration by adding support for reloading JSPs in the META-INF/resources directories of OSGi bundles.
Bug fix: fixed reloading generated EJB stub classes on WebLogic.
Bug fix: fixed a potential deadlock caused by the Jersey 2 integration.
Bug fix: fixed an issue that could cause failure during deployment on WebSphere when using remote server support.
Bug fix: fixed a startup failure when JAXP XPath classes were missing from the classpath.
Bug fix: fixed an issue with Spring's @ConditionalOnSingleCandidate when the target is a scoped bean.
Bug fix: fixed an integration issue with JBoss EAP 6.4.13.
Bug fix: fixed a potential VerifyError when adding a new anonymous inner class in between two existing ones.
Bug fix: fixed Byteman rules not being applied.
Bug fix: fixed an IllegalStateException with WebLogic that could occur when OmniFaces was on the classpath.
Bug fix: fixed a startup error with Oracle Java 8u112 and WebLogic 12.2.x.
Bug fix: fixed field reflection API throwing undocumented exception types.
Bug fix: fixed class hierarchy changes not being reflected when serializing a bean to JSON via Jackson.

7.0.4 (9th February 2017)

Feature: added support for Tomcat 8.5.11, 8.0.41 and 7.0.75.
Feature: added support for Jetty 9.4.1, 9.3.16 and 9.2.21.
Feature: added support for tc Server 3.2.4 and 3.1.8.
Feature: added support for Resin 4.0.50.
Feature: added support for Payara *********.
Feature: added support for SpringFox.
Feature: added support Oracle JDK 8u121.
Feature: added support for manipulating EnumMap and EnumSet with reloaded enums.
Improvement: improved the performance of Hibernate ORM integration in cases where XML configuration was stored in JARs without rebel.xml.
Improvement: improved the Jetty integration by adding support for Spring Load-time weaving on Jetty 9.2+.
Improvement: improved the Spring integration by adding support for changing proxy mode settings of scoped Spring beans.
Bug fix: fixed adding new @Produces methods in OpenWebBeans.
Bug fix: fixed JBoss Drools not being able to load rules on WildFly.
Bug fix: fixed an issue with MyFaces' DefaultFacesConfigurationProvider over-eagerly clearing the cache.
Bug fix: fixed Atmosphere class scanning when an empty WEB-INF/classes directory was in the workspace.
Bug fix: fixed WAB deployment for WebSphere Liberty Profile ********.
Bug fix: fixed an issue with EJB timer startup on WildFly.
Bug fix: fixed an integration issue with Jython 2.1.
Bug fix: fixed an integration issue with JBoss 7.1.0.CR1b.
Bug fix: fixed Spring constructor injection sometimes failing when adding beans.
Bug fix: fixed an integration issue with Hybris Cockpit core 1.0.2.

7.0.3 (23rd January 2017)

Feature: added support for Tomcat 8.5.9.
Feature: added support for Jetty 9.2.20, 9.3.15 and 9.4.0.
Feature: added support for WLP ********.
Feature: added support for tc Server 3.2.3.
Improvement: improved the EclipseLink MOXy integration to support the reloading of eclipseLink-oxm.xml file without any Java class changes.
Bug fix: fixed an integration issue with TomEE 7.0.2 Plume.
Bug fix: fixed WebSphere OpenJPA load-time weaving not processing some entities from libraries.
Bug fix: re-processing unchanged Spring @Configuration classes is skipped to avoid unexpected side effects.
Bug fix: fixed an integration issue with JoinFaces where a bean could lose its scope after reload.
Bug fix: fixed JDK proxies generated by Spring AOP failing with java.lang.UnsupportedOperationException: JVMTI_ERROR_UNSUPPORTED_REDEFINITION_METHOD_ADDED.
Bug fix: fixed reloading Spring beans defined using @Configuration class when a bean's constructor signature changes.
Bug fix: fixed accidentally excluding some JARs from Weld classpath scanning.
Bug fix: fixed adding new producer methods in Weld 2.x.
Bug fix: fixed startup errors when Java Flight Recorder was enabled.
Bug fix: fixed handling opaque URIs on WebLogic.
Bug fix: fixed Spring beans disappearing on reload when defined with the @Bean's value attribute.
Bug fix: fixed an integration issue with spring-security-config when spring-security-web is not used.
Bug fix: fixed startup errors when the SNMP agent was enabled.
Bug fix: fixed reloading of Apache CXF WSSE endpoints.
Bug fix: fixed EJBs not being injected into request scoped beans after reload on WebSphere 8.x.
Bug fix: fixed breakpoints on nested classes not hitting after a reload in IntelliJ 2016.3.2.
Bug fix: fixed a NPE on MyFaces reload when metadata complete was set to true.
Bug fix: fixed static methods on interfaces getting mixed up with default methods on the Legacy Agent.
Bug fix: fixed a potential ConcurrentModificationException that could happen during reloading Spring beans.
Bug fix: fixed a potential ArrayIndexOutOfBoundsException when j.l.reflect.Array::get was used on an array of reloaded type.
Bug fix: fixed reloading of WebSocket routes in Apache Camel 2.15.x+.

7.0.2 (3rd January 2017)

Feature: added support for Tomcat 6.0.48, 7.0.73 and 8.0.39.
Feature: added support for TomEE 1.7.4 and 7.0.2.
Feature: added support for WebSphere 9.0.0.2.
Feature: added support for tc Server 3.1.7 and 3.2.2.
Feature: added support for Google App Engine (GAE) 1.9.48.
Feature: added support for Jackson 2 Afterburner module.
Improvement: improved reloading use cases related to accessing reloaded class members through an unchanged class, such as calling a static method on a parent via child class reference.
Improvement: improved the HTTP request times on WebSphere and WebLogic.
Improvement: It is now possible to add a new @WebServlet class to a library of a web app.
Improvement: improved the Apache Camel plugin to also enable reloading REST-styled DSL defined endpoints.
Bug fix: fixed a performance issue on JBoss with Spring 3.0.x.
Bug fix: fixed a potential issue when deploying an OSGi bundle to GlassFish.
Bug fix: fixed startup errors on Windows in the case of a long classpath containing absolute paths.
Bug fix: fixed JSF 2.0 beans disappearing on WebLogic 10.3.x after reloading.
Bug fix: fixed a potential ClassNotFoundException on WebLogic 12.x when the deployed WAR does not contain package-info.class but the workspace does.
Bug fix: fixed correct catch block not executed for reloadable exception classes when the call-site had been reloaded but not the callee.
Bug fix: fixed a JRebel Agent startup error when the -XX:InitialCodeCacheSize and -XX:ReservedCodeCacheSize arguments were specified.
Bug fix: fixed some early integration issues with Spring 5.0.0-SNAPSHOT builds.
Bug fix: fixed an integration issue with WebSphere 8.0.0.11.
Bug fix: fixed finding a path to system resources not returning the original URL on IBM JVM.
Bug fix: fixed superclass state potentially not being read from the right place when adding a new superclass to a class.
Bug fix: fixed the finalize method not called after reload.
Bug fix: fixed the overriding package-private methods from an non-reloadable parent class not working.
Bug fix: fixed reading a static field from another package that was declared in a package private class that was changed to public.
Bug fix: fixed the deserialized object of a reloaded class missing field values.

7.0.1 (5th December 2016)

Feature: added support for Tomcat 8.5.8.
Feature: added support for Jetty 9.3.14.
Feature: added support for Google App Engine (GAE) 1.9.46.
Feature: added support for Spring Boot 1.4.
Improvement: improved the CDI reloading speed on WebLogic 12.2.x, WildFly 10.x and WebSphere 8.5.5.x+.
Improvement: improved the startup time when exception breakpoints were enabled without suspending any threads on IntelliJ.
Bug fix: fixed a potential InvalidJarIndexException on WebLogic 12.2.x.
Bug fix: fixed the @PostConstruct method getting called twice on OpenWebBeans versions 1.0.x-1.1.x.
Bug fix: fixed a file handle leak on GlassFish.
Bug fix: fixed a compatibility issue with ObjectDB bytecode processing.
Bug fix: fixed reflective calls to Class::getModifiers not returning the correct values for reloaded classes on the Legacy Agent.
Bug fix: fixed a potential NullPointerException on WebLogic when a Java EE shared library is deployed as EAR.
Bug fix: fixed a NullPointerException on TomEE when openejb.system.apps system property was set to true.
Bug fix: fixed the JRebel Agent startup errors on Alpine Linux.
Bug fix: fixed a NullPointerException when using Weld 2.x in a standalone application.
Bug fix: fixed a potential deadlock during GlassFish startup.
Bug fix: fixed an integration issue with Apache Karaf.
Bug fix: fixed a potential issue with redefining classes while debugging in JDeveloper.
Bug fix: fixed a potential integration issue with Logback.

7.0.0 (15th November 2016)

Upgrading to JRebel 7 from an older version? Refer to https://zeroturnaround.com/software/jrebel/jrebel7-agent-upgrade for the upgrade notes.

Feature: JRebel Agent is now the default and preferred agent.
Feature: standalone .zip file structure changed, JRebel Agent .jar file now resides in the top level folder.
Feature: added support for reconfiguring non-singleton Spring beans.
Feature: added support for Tomcat 6.0.47, 8.0.38 and 8.5.6.
Feature: added support for WebLogic ********.
Feature: added support for Jetty 9.3.13.
Feature: added support for Resin 4.0.49.
Feature: added support for tc Server 3.1.6 and 3.2.1.
Feature: added support for Google App Engine 1.9.44.
Bug fix: fixed JRebel Agent JVM startup errors on Windows caused by incorrect command line escaping.
Bug fix: fixed embedded Jetty with Weld 2.2.5+ finding duplicate classes for CDI beans.
Bug fix: fixed an integration issue with JBoss EAP 6.4.11.
Bug fix: fixed JRebel confusing a package name as a class due to case insensitivity.
Bug fix: fixed JRebel Agent causing a NoClassDefFoundError when casting null to a type that does not exist on the class path.
Bug fix: fixed warning messages about existing jndi bindings appearing in the GlassFish console after reload.
Bug fix: fixed registering @WebServlets twice from WEB-INF/lib artifacts on reload.
Bug fix: fixed reloading two or more method references to the same instance method with the JRebel Agent.
Bug fix: fixed EJB transaction attributes getting reset to default after a reload on WebLogic 12.x.
Bug fix: fixed a potential deadlock in the Legacy Agent.
Bug fix: fixed a potential slowdown in the JRebel debugger integration.
Bug fix: fixed potential JRebel Agent JVM startup errors when using Gradle.
Bug fix: fixed an integration issue with WebSphere Liberty Profile *******.
Bug fix: fixed JVM startup errors with JRebel Agent when using the -Dsun.reflect.noInflation=true option.
Bug fix: fixed potential Spring beans injection issues when the injection depended on the Spring XML resource order.

6.5.2 (19th October 2016)

Feature: added support for Payara.
Feature: added support for Tomcat 7.0.72.
Feature: added support for Jetty 7.6.21, 8.1.21, 9.2.19 and 9.3.12.
Feature: added support for WebSphere *******.
Feature: added support for WebSphere Liberty Profile ********.
Feature: added support for tc Server 3.2.0.
Feature: added support for changing the local interface of an EJB with the JRebel 6 agent on TomEE.
Improvement: improved the startup and request time on WebLogic.
Bug fix: fixed an integration issue with Weld 2.4.
Bug fix: fixed starting application servers with JRebel 6 agent and JProfiler from Eclipse.
Bug fix: fixed a potential startup failure on IBM JVM.
Bug fix: fixed EL expressions not finding CDI beans when WildFly was configured with a MyFaces implementation.
Bug fix: fixed Runtime.class.getPackage().getImplementationVersion() returning null.
Bug fix: fixed Camel reloading all routes on any Spring bean change.
Bug fix: fixed application not working after a redeploy on GlassFish.
Bug fix: fixed injecting newly added EJB in a redeployed application on WebSphere.
Bug fix: fixed an NPE when null was passed as a lambda argument.
Bug fix: fixed reloading JAX-RS endpoints on WebSphere Liberty profile when @ApplicationPath does not begin with "/".
Bug fix: fixed a race condition in defining EJB remote stubs on GlassFish.
Bug fix: fixed calling getClass on a JAXBContext not returning the expected class.
Bug fix: fixed loading classes from a reloaded class loader on IBM JVM.
Bug fix: fixed annotation related methods on java.lang.Class not always returning values corresponding to the latest class definition on the JRebel 6 agent.
Bug fix: fixed an integration issue with the latest Spring bean 4.3.4-SNAPSHOT.

6.5.1 (29th September 2016)

Feature: added support for Tomcat 8.5.5 & 8.0.37.
Feature: added support for Jetty 7.6.20 & 8.1.20.
Feature: added support for WildFly 10.1.0.
Feature: added support for Liferay 7.0.2.GA3.
Feature: added support for Jackson 2.8.
Improvement: improved JAX-RS integration on TomEE, WebSphere and WebSphere Liberty Profile to support changing the path defined in @ApplicationPath or a resource's Interface method declaration.
Bug fix: fixed injecting new EJB into a CDI bean with @EJB on JBoss 7+.
Bug fix: fixed a potential deadlock on WebLogic when the startup classpath contained a JAR with rebel.xml.
Bug fix: fixed a compatibility issue with CGLIB 3.2+ that could cause a CodeGenerationException.
Bug fix: fixed JSF relative path handling.
Bug fix: fixed an issue where EJB reloading did not work on GlassFish when the application was deployed with NetBeans.
Bug fix: fixed an issue where an NPE could be thrown in a method annotated with @Schedule on WebLogic.
Bug fix: fixed the Spring MVC controller not reloading when the MVC JAR was loaded from a different class loader than the Spring Beans JAR.
Bug fix: fixed an issue with JIT compiled code that could cause a crash on 32-bit IBM VMs.
Bug fix: fixed a potential JVM crash in the JRebel Legacy agent when starting up the application with the Eclipse debugger attached.
Bug fix: fixed an issue with JRebel 6 agent where adding anonymous inner classes failed.

6.5.0 (12th September 2016)

Feature: added support for WebSphere 9.
Feature: added support for TomEE 7.
Improvement: the old JAX-RS endpoint will no longer be active after changing the @ApplicationPath on TomEE.
Bug fix: fixed a potential duplicate class definition LinkageError on ProSyst.
Bug fix: fixed a compatibility issue between Dynatrace and JRebel 6 native agents.
Bug fix: fixed JRebel Legacy agent initializing interfaces too early in some cases, causing unexpected initialization ordering.
Bug fix: fixed JRebel causing the Java default LoggerManager to be initialized too early on WebSphere Liberty Profile 16.x with IBM Java 8.
Bug fix: fixed SpringVersion.getVersion() sometimes returning null.
Bug fix: fixed an issue that could cause EJB producer validation to fail on GlassFish 4.
Bug fix: fixed reloading a Spring bean that was defined in an XML file that was imported into another XML file several times.
Bug fix: fixed a potential VerifyError that could occur when multiple arrays were defined in a method with one of them being null on the JRebel 6 Agent.
Bug fix: fixed a LinkageError related to trying to load the same class twice when deploying an EAR with Jersey classes in /lib.
Bug fix: fixed an XRebel and JRebel compatibility issue that could cause a StackOverflowError on startup.
Bug fix: fixed a NotSerializableException that occurred when a CDI bean with an interceptor was injected into a JSF view on WebSphere 8.x.
Bug fix: fixed Java Introspector returning inaccessible methods as accessors when running with the JRebel Legacy agent.

6.4.8 (18th August 2016)

Added support for WebSphere Liberty Profile 16.
Added support for Jetty 9.3.11 & 9.2.18.
Added support for Tomcat 8.5.4.
Added support for Hibernate 5.2.
Added support for changing the local interface of an EJB with the JRebel 6 agent on JBoss, WildFly, GlassFish and WebLogic.
Fixed an issue where Tomcat 7 could throw a RuntimeException when remapping a request.
Fixed an issue with Tiles 3.0.x and Struts 2.5.x sometimes throwing NPEs when reloading.
Fixed a CDI BeanManager leak between applications on WildFly 9.
Fixed an issue where JRebel run via -agentpath failed to start together with other -agentpath libraries.
Fixed an integration issue with Liferay 5.1.2.

6.4.7 (1st August 2016)

Added support for Tomcat 8.5.3 and 7.0.70.
Added support for WebLogic 12.2.1.1.
Added support for Jetty 9.3.10.
Added support for tc Server 3.1.5.
Added support for Azul Zulu JVM.
Added support for adding @WebServlets and @WebFilters on Jetty.
Added support for reloading OSGi bundles when MANIFEST.MF changes on Equinox.
Added support for reloading Jersey endpoints that are registered as OSGi services on Equinox.
Added support for reloading Liferay friendly-url-routes configuration files.
Added support for reloading portlet properties files on Liferay.
Improved ATG reloader, so it can reload request based Nucleus.
Improved ATG reloader to reload components when .properties file is changed.
Fixed potential startup slowdown on JBoss AS 7.x when using Spring Data.
Fixed reloading lists defined in Spring beans xml file.
Fixed a Grails 3 startup when used with Hibernate 5.
Fixed an integration issue with early CXF 2.0.x incubator releases.
Fixed a potential NPE on deploy with Hibernate 5.
Fixed a potential ClassNotFoundException in JRebel Legacy agent when method descriptors of a parent class contained classes that were not accessible.
Fixed an integration issue with Hybris 4.8.7.
Fixed a ConcurrentModificationException in Mojarra when a bean's hashcode method caused recursive bean initialization.
Fixed a ClassCastException that occurred when autowiring a collection with generic type.
Fixed a NoSuchBeanDefinitionException when a Spring bean was overridden by a bean in parent beanfactory using alias.
Fixed Package.getImplementationVersion and similar methods returning null on JBoss 7.x.
Fixed a NPE on GlassFish 4.x that occurred when rebel.xml mapped location contained a jar that was not packed into WEB-INF/lib.
Fixed an AccessControlException when running Wicket on Google App Engine.
Fixed an issue with WebLogic taglib handling where JRebel caused them to be recompiled on every request.
Fixed an issue with JRebel Spring integration where having the same configuration file on multiple rebel.xml paths caused duplicate beans on reload.
Fixed an integration issue with Hibernate 4.1.1.Final.
Fixed JBoss AS 7.x losing EJB injection after JAX-RS resource reload.
Fixed JRebel Legacy agent returning wrong results when accessing method parameters via reflection.

6.4.6 (27th June 2016)

Added support for Tomcat 8.0.36.
Added support for Jetty 7.6.19, 8.1.19 and 9.3.9.
Added support for Liferay 7.0.1.GA2.
Added support for Spring 4.3.
Added support for adding @WebServlets and @WebFilters on Tomcat.
JRebel now logs by default on INFO level.
Fixed reloading MyFaces configuration when CDI enabler jar is used.
Fixed serialization of remote EJB method parameters on GlassFish 3.x.
Fixed WebLogic classpath scanning not taking into account rebel.xml <exclude> elements.
Fixed a NullPointerException in Jetty when its debug logging was enabled.

6.4.5 (2nd June 2016)

Added support for JBoss EAP 7.0.0.
Added support for Tomcat 7.0.69 & 8.0.35.
Added support for Jetty 9.2.16.
Added support for tc Server 3.1.4.
Added support for ProSyst OSGI server.
Improved reload performance for CXF services with EJBs on WildFly and JBoss 7.x.
Improved reload performance for Hibernate entities with the container-managed entity manager on WildFly and JBoss 7.x.
Fixed reloading Jersey 1.x configuration with multiple background requests.
Fixed an issue with WebSphere 8.x not being able to inject EJBs into new CDI beans after a reload.
Fixed JRebel duplicating servlet filters on reload with WebLogic.
Fixed a potential startup error on GlassFish ******* when accessing the administrator console.
Fixed an issue with WebSphere where Axis2 was not able to find web service interfaces from the EAR library folder.
Fixed a potential NoSuchMethodError on WebSphere 8.x startup, related to Xerces XML Parser.
Fixed a ClassCastException with JRebel and Metro.
Fixed potential startup slowdown on JBoss AS 7.x when deploying OSGI bundles.
Fixed a VerifyError that could happen with the Eclipse compiler on JRebel 6 Agent when you create an instance of an object without assigning it anywhere.
Fixed a startup error on GlassFish 4.x when deploying Resource Adapter Archive (RAR) files.

6.4.4 (16th May 2016)

Added support for Liferay 7.0.0 GA1.
Added support for adding new Spring Data @RepositoryRestResources.
Improved JAX-RS integration on WildFly to support changing the path defined in @ApplicationPath or a resource's Interface method declaration.
Improved JSF integration on WebSphere 8.x to handle multiple @PostConstruct methods in the managed bean class hierarchy.
Fixed Hybris not being able to reload changes in XML files because the correct tenant was not active.
Fixed a potential incompatibility with using AspectJ load-time-weaver together with the JRebel Legacy Agent.
Fixed a potential ArrayIndexOutOfBoundsException when enum switch blocks were transformed by AspectJ using the JRebel 6 Agent.
Fixed an issue with the Guice Injector returning old instances after reload, when the Injector itself was obtained via an injection.
Fixed Hibernate 4 integration triggering a configuration reload when nothing had changed.
Fixed an issue with transactions not being active when using DeltaSpike JPA module's @Transactional methods.
Fixed an issue with obtaining a MethodHandle to a restricted class via the Lookup#unreflect* methods using the JRebel 6 Agent.
Fixed JAX-RS @Context injected fields being null in an EJB after a reload on WildFly.
Fixed a potential StringIndexOutOfBoundsException that could occur when the Eclipse compiler generated illegal characters into LocalVariableTypeTable.

6.4.3 (25th April 2016)

Added support for Tomcat 8.0.33.
Added support for WebSphere ******* and WebSphere Liberty Profile *******.
Added support for Jetty 9.3.8.
Improved the GlassFish 3.x and 4.x integration with full coverage for Java EE specifications.
Fixed an issue with GlassFish failing to load some Hibernate resources from paths defined in rebel.xml.
Fixed a NPE during a Spring bean reload when null value was injected into the constructor.
Fixed an issue in Spring where beans defined in XML were reloaded twice in some cases.
Fixed an issue in Spring where the order of PropertyPlaceholderConfigurers was mixed up on reload, causing missing placeholder values in some cases.
Fixed a potential deadlock during WebLogic 12.2.1 startup.
Fixed a potential NoClassDefFoundError that could occur with the JRebel 6 Agent when a class file was deleted during its reload.
Fixed an issue with WebSphere 8.x where JRebel generated wrong EJB descriptor files.
Fixed an incompatibility issue with the Reflections library, caused by JRebel leaving ".jar" in the temporary directory name.
Fixed an issue where the default method implementing another interfaces method was not found when there were multiple types implementing said interface using the JRebel Legacy agent.
Fixed an integration issue with Visualforce.

6.4.2a (6th April 2016)

Fixed a regression in Spring integration related to CGLIB proxies introduced in JRebel 6.4.2 by rolling back the support for adding the first @Transactional method to a Spring bean.

6.4.2 (5th April 2016)

Added support for injecting an @EJB to a new JSF @ManagedBean on JBoss 7.x and WildFly.
Added support for reloading non-Singleton scoped Spring @RestController request mappings.
Added support for adding new CDI beans to an implicit bean archive on WildFly.
Added support for adding the first @Transactional method to a Spring bean.
Added support for adding a root JSP on WildFly.
Improved the WebLogic 10.x and 12.x integration with full coverage for Java EE specifications.
Improved Tapestry 4 integration performance.
Improved error messages in standalone activation.
Fixed changing the repository entity on Spring Data JPA 1.6.x.
Fixed an integration issue with WebSphere Liberty Profile *******.
Fixed renaming an inner class that caused a NPE on the JRebel 6 Agent.
Fixed an issue with the Struts 2 plugin that caused ClassNotFoundException during reloads.
Fixed a NoClassDefFoundError on WebLogic 10.3.x when no JAX-RS classes were on the class path.
Fixed an integration issue with Hibernate when the application server's native class enhancement was used on JBoss.
Fixed an AbstractMethodError that could occur when reloading classes on Virgo.
Fixed an issue wherein activation with a different email was permitted while the previous email was blocked on the License Server.

6.4.1 (17th March 2016)

Added support for Jetty 9.2.15.
Added support for Resin 4.0.48.
Added support for Tomcat 6.0.45, 7.0.68 and 8.0.32.
Added support for reloading Camel routes defined in Spring XML DSL.
Added support for adding new Camel RouteBuilders as Spring Beans.
Added support for reloading Jersey endpoints registered in the constructor or in the @PostConstruct method of a ResourceConfig class.
Added support for reloading changes in JAX-RS @Path annotations declared on an endpoint interface method.
Added support for reloading Stripes ActionBean HTML form binding.
Added support for swapping out Spring Bean classes in XML definitions that have a common parent class used for injection.
Added support for using the root URL for remote Tomcat servers when the default root application was removed.
Fixed an issue concerning the loading of precompiled generated classes on WebLogic.
Fixed a potential startup issue with WebSphere due to incorrect metadata generated by the EJB integration.
Fixed an issue with Hibernate 5 throwing an UnknownServiceException.
Fixed the debugger integration causing breakpoints to be highlighted only on the active debug session window in IntelliJ IDEA 16 EAP.
Fixed MyBatis Dynamic SQL expression reloading.
Fixed JRebel 6 native agent not starting up with IBM J9 ******* due to a VM bug.
Fixed a potential deadlock in starting up WebLogic with the JRebel 6 agent.
Fixed an issue with AspectJ processing generated cglib classes on JBoss 7.x.
Fixed Spring injection sometimes failing due to a ConcurrentModificationException.
Fixed a FileNotFoundException when trying to access custom JSP tags on WebSphere Liberty Profile.
Fixed an issue with generated serialVersionUID changing after class reload.
Fixed @PostConstruct being called multiple times during startup on TomEE 1.7.x.
Fixed an integration issue with Swagger 1.5.3.
Fixed an integration issue with Jersey 1.0.2.
Fixed an integration issue with OpenWebBeans 1.6.3.

6.4.0 (2nd March 2016)

Added support for Jetty 9.3.7.
Added support for WildFly 10.0.0.Final.
Added support for Resin 3.1.15.
Added support for Liferay 6.2.5 GA6.
Added support for tc Server 3.0.6 and 3.1.3.
Improved Spring integration to inject added beans into existing @Autowired bean collection fields.
Improved handling duplicate rebel-remote.xml module IDs by notifying the user about these situations.
JRebel now notifies you when you're on a nightly build and a newer stable release is available.
Fixed an issue with an application failing to start when using MyFaces and when ApplicationFactory was wrapped.
Fixed the MyFaces version detection when MyFaces 2 is provided by the application server, but MyFaces 1 used by the application.
Fixed an issue in the JRebel 6 native agent that failed to start when certain verbose JDK logging options were enabled.
Fixed a potential NoClassDefFoundError from precompiled JSPs when switching the JRebel version.
Fixed an issue with custom AutowireCandidateResolver getting lost while reloading Spring beans, causing re-injection failures.
Fixed an issue where the application failed to deploy when using Vaadin CDI on the WebSphere Liberty profile.
Fixed an AbstractMethodError that could happen when reloading classes on Gemini.
Fixed a NoSuchMethodError that occurred when AspectJ tried to process its own generated classes when running with JRebel.
Fixed a NPE in the debugger integration that could occur when debugging on IntelliJ IDEA.
Fixed an issue in the debugger integration that caused the local variables view to sometimes fail with a VMMismatchException in IntelliJ IDEA.
Fixed an issue where the application failed to start, throwing the exception com.arjuna.ats.arjuna.exceptions.FatalError when using JBossJTS.
Fixed an AbstractMethodError in MyBatis integration that occurred when using it with Spring 4.x.
Fixed a NoSuchMethodError in the JRebel 6 Agent that occurred after changing the signature of a method on a super-type when invoked via a sub-type.
Fixed an issue where the GWT compiler could not find some JDK classes when running with the JRebel 6 Agent.
Fixed an issue with OpenWebBeans not being able to find the beans when the rebel.xml mapped path itself did not contain rebel.xml.
Fixed a potential deadlock in the RESTEasy integration.

6.3.3 (4th February 2016)

Added integration with Swagger 1.3 or newer.
Added support for reloading JAX-RS method signatures using GlassFish 4.x.
Added option to suppress warnings about invalid directories that are mapped in rebel.xml.
Improved the WebSphere 8.x reload speeds.
Fixed re-configuring certain Spring Beans even when those were not changed.
Fixed the NameNotFoundException: Name "comp/websphere/UserWorkArea" not found in context "java:” after reload using WebSphere 8.x.
Fixed an issue with JRebel potentially causing some resources to be not found when URLs were added to a URLClassLoader during runtime.
Fixed a bug with GWT array serialization.
Fixed an issue with Spring Security anonymous beans disappearing after reloading beans.xml.
Fixed applications using the APDPlat framework not being able to start up when run with JRebel.
Fixed a potential NoClassDefFoundError that could occur when the Eclipse Mars compiler generated a faulty LocalVariableTypeTable.
Fixed an AmbiguousResolutionException that could happen with Solder TypedMessageLoggerProducer.
Fixed an integration issue with WebSphere *******.
Fixed Spring finding duplicate resources when using WebLogic.
Fixed an integration issue with EclipseLink 1.x.
Fixed RESTEasy potentially throwing a RuntimeException: Unable to find a public constructor for class when loading a class with JRebel.
Fixed creating duplicate DeltaSpike proxy beans on reload.
Fixed a NPE in the JRebel 6 Agent that could occur when the type of a field was not provided on the classpath.
Fixed JRebel 6 Agent not picking up changes to classes when a custom system class loader was used.
Fixed an issue with accessing public static fields from package private superclasses with the JRebel 6 Agent.
Fixed a VerifyError that occurred when running JRebel 6 Agent together with the ws-javaagent using WebSphere Liberty Profile.
Fixed a VerifyError that occurred when running JMockit unit tests with the JRebel 6 Agent.
Fixed JRebel 6 Agent potentially losing state when reloading a class containing more than 200 fields.
Fixed JRebel 6 Agent potentially picking the wrong case when switching over a reloaded enum in a class compiled with Eclipse.
Fixed JRebel 6 native agent failing to start up when passing main arguments starting with -X (e.g. with GWT).
Fixed an IllegalAccessException with JRebel Legacy Agent regarding changing a static final field via reflection.

6.3.2 (14th January 2016)

Added support for TomEE 1.7.3.
Added support for WildFly 10.0.0.CR5.
Added support for WebSphere Liberty Profile *******.
Added support for Resin 4.0.47.
Added support for Tomcat 7.0.67.
Added support for reloading @XStreamAlias values when using Spring XStreamMarshaller.
Improved the WebSphere 7.x integration with full coverage for Java EE specifications.
Improved WebSphere Liberty Profile servlet reload speed.
Fixed an NPE that occurred when faces-config.xml contained custom entities.
Fixed JRebel creating duplicate Spring request mappings when using DefaultBeanNameGenerator.
Fixed an AmbiguousResolutionException when a Spring Data repository was inserted into an EJB using @Inject.
Fixed the parsing of JSP Tag Libraries that were found from the second <web> root link in rebel.xml on WebSphere.
Fixed an integration issue with Jetty versions 8.1.2 to 8.1.9.
Fixed an NPE on Jetty 9.2.x+ that occurred when all ServletContainerInitializers were disabled.
Fixed an issue with the JRebel 6 Agent where calling isAlive() returned as false on a reloaded class that extended the Thread.
Fixed an NPE that could occur in Wink when JRebel remote server support was used without a rebel-remote.xml present in the artifact.
Fixed an IllegalStateException when JRebel was used with the Apereo CAS Client.
Fixed a NoSuchMethodError caused by the debugger integration with the JRebel 6 Agent when the method with the current suspended breakpoint was removed.
Fixed inner class reloading when multiple inner classes were too similar and JRebel mixed them up following a reload.
Fixed an issue where calling getAnnotatedType returned the wrong type after reload using the JRebel Legacy Agent.
Fixed reloading inner classes with a changed order when compiled with the Eclipse compiler using the JRebel Legacy Agent.

6.3.1 (21st December 2015)

Added support for WebLogic 12.2.1.
Added support for Spring Boot 1.3.0.
Added support for OpenWebBeans 1.6.2.
Added support for Jetty 9.2.14.
Added support for Tomcat 8.0.29.
Added support for Liferay 6.2.4 GA5.
Added support for WebSphere *******.
Improved Hibernate reload performance.
Improved Axis2 integration performance by removing unnecessary reloading.
Fixed an issue with the JRebel 6 Agent where adding a previously deleted enum value was different from the deleted value.
Fixed a NoSuchBeanDefinitionException occurring when both Spring @Configuration class and its superclass exported the same bean with different names.
Fixed an issue with Tomcat 8.x recompiling JSPs multiple times per request.
Fixed an issue with using Hibernate 5.x from Spring ORM 4.x where the SessionFactory was not reconfigured properly on reload, causing an UnsupportedOperationException.
Fixed a potential deadlock in Axis2 1.6.x when reloading classes.
Fixed a potential deadlock in WebLogic 10.3.x.
Fixed Spring @Configuration reloading breaking Spring Integration flows.
Fixed a Tomcat issue with parsing the wrong rebel.xml without replaced properties.
Fixed overly long Tomcat requests caused by unnecessary scanning.
Fixed WebLogic throwing NoClassDefFoundError: JrWlpJPAPUnitInfo on deploy when using prefer-application-packages for package org.eclipse.persistence.* in weblogic.xml.
Fixed the display of the directory listing with JRebel when it should have been disabled on WebLogic 12.x.
Fixed JRebel incorrectly detecting the Spring version used when both Spring 2.x and 3.x classes were on the classpath.
Fixed a potential IllegalStateException: Singleton not set for Classloader, thrown from the Mojarra plugin on reload.
Fixed an ArrayIndexOutOfBoundsException in JRebel Legacy Agent with Java 8 method reflection happening after a reload.

6.3.0 (30th November 2015)

Added support for Tomcat 7.0.65 and 8.0.28.
Added support for WildFly 9.0.2.
Added support for Jetty 9.3.6.
Added support for Resin 4.0.46.
Added support for Mojarra 2.2.12.
Improved the WebSphere 8.x integration with full coverage for Java EE specifications.
Improved the WebSphere Liberty profile integration with full coverage for Java EE specifications.
Improved JRebel 6 Agent support for enum reload.
Improved the speed of rebel.xml path matching.
Fixed a performance regression in our Tomcat integration since 6.2.7.
Fixed an issue with some Liferay templates not being able to import other static resources.
Fixed an issue with reloading EclipseLink MOXy JAXBContext when using an external metadata xml file to define the mappings.
Fixed a potential UnsupportedOperationException with the JRebel 6 Agent that occurred when when class redefinition was invoked.
Fixed an issue with reloading Spring beans with a scoped proxy exported from @Configuration class.
Fixed a potential integration issue with Hibernate 5.0.4.
Fixed an issue with using two or more @WebService instances, annotated with JBoss @WebContext that had identical defined urlPatterns, but different contextRoots.
Fixed an issue where applying integrations failed due to identically named classes in the default package.
Fixed a VerifyError with the JRebel 6 Agent that occurred when using Lucee as a dependency from the cfmlprojects.org repository.
Fixed an issue where the license was not accepted for a few hours due to time zone differences.

6.2.7 (10th November 2015)

Added support for GlassFish 4.1.1.
Added support for OGNL 3.0.12.
Added support for Log4j 2.4.
Added support for changing JAX-RS endpoint method signatures on GlassFish 4.x.
Added support for adding new JAX-RS endpoints on GlassFish 4.x.
Added support for adding new MyBatis mapper XML files, when used together with Spring and classpath scanning to find the mapper files.
Improved the JRebel 6 agent startup time.
Improved the performance of reloading JAX-WS resources on WebSphere.
Fixed an issue with starting JRebel plugins causing a class circularity in rare cases.
Fixed an issue where JRebel called Jersey 2 @PreDestroy methods on reload.
Fixed a regression where JRebel started to throw IllegalStateException and reported that some WebSphere Portal versions are not supported.
Fixed an issue with new resources not being found from <PreResources/> and <PostResources/> locations defined in Tomcat context.xml.
Fixed a Hibernate UnknownServiceException that occurred when reloading multiple deployed web applications.
Fixed an integration issue with JBoss 3.2.8.
Fixed an integration issue with Tomcat 4.
Fixed a ClassCastException that occurred when using Spring 4.1 with Hibernate 3.
Fixed a potential TransactionRequiredException related to reloading LocalContainerEntityManagerFactoryBean.
Fixed an issue related to reloading enums with class bodies on the JRebel Legacy agent.
Fixed an issue where calling the enum ordinal() method did not trigger a reload on the JRebel Legacy agent.
Fixed and issue with using JNDI in a war file with no EJBs in it on WebSphere 8.x.

6.2.6 (21st October 2015)

Added support for Tomcat 8.0.27.
Added support for Resin 4.0.45.
Added support for Pivotal tc Server 3.0.5 and 3.1.2.
Added support for Hibernate 5.0.
Added support for Spring Framework 4.2.
Improved JBoss EAP 6.x and WildFly startup time.
Improved Spring rescanning speed.
Improved Struts 2 rescanning speed.
Made the JRebel 6 native agent use a dynamic DLL base.
Made JRebel Remote start listening on the port right at startup when -Drebel.remoting_port is specified.
Licensing for JRebel instances with License Server licenses running on a virtual machines has been improved.
Fixed regression regarding removing JMX beans when adding regular Spring beans.
Fixed NoSuchMethodErrors that occurred when changing @Bean annotated methods in Spring @Configuration classes.
Fixed an issue with additional resources defined in Tomcat context.xml not being found when using JRebel Remote.
Fixed an issue with JRebel not receiving file system events from exFAT file systems.
Fixed a PersistenceException that occurred after reload when using Hibernate with custom scanners.
Fixed a potential deadlock in WildFly 8.x EJB integration when multiple EJBs referenced each other.
Fixed a potential IllegalStateException from Jersey 1 that could happen after reload.
Fixed a compatibility issue between the JRebel 6 agent and the Spring-Instrument agent.
Fixed a potential ArrayStoreException on the JRebel 6 agent with Arrays of a reloaded type.
Fixed a non-fatal IllegalAccessException when accessing reloaded lambdas with the JRebel 6 agent.
Fixed an issue with the JRebel 6 agent potentially corrupting an array when using System.arraycopy when source and destination overlapped.
Fixed a NoSuchFieldError in the JRebel 6 agent when adding a static field and then using it later from a reloaded class.
Fixed a IllegalAccessException with JRebel 6 agent regarding changing a static final field via reflection.
Fixed the JRebel 6 agent potentially calling the old method of a class with a changed hierarchy when the method called is present in both old and new class hierarchies.
Fixed an issue where the RichFaces <a4j:support> component stopped working after reloading on JSF facelets versions older than 1.1.12.
Fixed an issue in Metro with reloading @WebService classes even though they were not changed.
Fixed a potential deadlock in the EclipseLink integration regarding load-time weaving.

6.2.5 (1st October 2015)

Added support for Tomcat 7.0.64 and 8.0.26.
Added support for WildFly 8.2.1 and 9.0.1.
Added support for Jetty 9.2.13 and 9.3.3.
Added support for Google App Engine 1.9.25.
Added support for Liferay 6.2.3 GA4.
Added support for Virgo 3.6.4.
Added support for Struts JSON Plugin.
Added support for adding new Spring @ManagedResource beans.
Improved Spring re-scanning speed.
Improved JVM startup speed when using multiple paths in rebel.xml.
Improved MyFaces reloading reliability.
Fixed an issue where the JRebel 6 Agent caused a VerifyError when the ternary operator was used to initialize a static field.
Fixed an NPE on JBoss 7.x when using the Infinispan CDI module.
Fixed a RebelSourcesWatcher ConcurrentModificationException on JBoss 7.x.
Fixed an issue with JRebel not preserving a CDI @SessionScoped beans context on JBoss 7.1.
Fixed an issue where having more than one persistence unit with the same name caused deployment issues on JBoss EAP 6.1.
Fixed a potential deadlock on first use of EJBs that depended on each other in WebLogic.
Fixed an issue with adding a new method to an EJB that was retrieved via InitialContext#lookup on WebLogic.
Fixed adding new Spring beans to an @Autowired constructor.
Fixed an NPE on Liferay that occurred following portlet reload.
Fixed an issue where Hibernate EntityPersister could get lost on reload.
Fixed a potential NPE on Log4j initialization.
Fixed an AmbiguousResolutionException caused by JRebel when using CDI 1.1 implicit bean archives.
Fixed an issue where Spring would fail to get the container managed EntityManagerFactory on WebLogic 10.3.5.
Fixed an issue with the Velocity Engine not using correct templates from a custom folder when running with JRebel.
Fixed an NPE on Tomcat when the application had already failed to deploy.

6.2.4 (15th September 2015)

Added support for binding request parameters to new properties on Struts annotated Action classes.
Added support for adding new Spring Data repositories when using Java-based configuration of the Spring container.
Added support for adding new non-singleton Spring MVC @Controllers.
Fixed an issue with Grails 2.x Asset Pipeline Plugin 1.x where resources required from plugin where not loaded.
Fixed a corner case issue with deploying OpenJPA applications that caused entity enhancement to fail with the message: All persistent properties must have getter methods.
Fixed an issue where annotations of modified methods were lost when using the JRebel 6 Agent.
Fixed an issue where the JRebel 6 Agent caused JVM to crash when using static methods of certain functional interfaces (Function.compose etc.).
Fixed an issue with Ehcache CacheManager getting shut down when reloading Hibernate SessionFactory.
Fixed the implementation to avoid throwing an exception when a CoreMedia CMS custom Spring FactoryBean incorrectly declares the type of the bean it is producing.
Fixed an issue with patching the Jersey 1.x ServletContainer class without the servlet API available on the classpath.
Fixed a NPE with reloading EJBs that have bean managed transactions on WildFly and JBoss EAP 6.x.
Fixed an issue where JRebel caused Spring to log warnings about not being able to determine the bean type of abstract factory beans.
Fixed an issue with using non-default JSF implementation on WildFly 8.x.
Fixed an AbstractMethodError caused by using Spring Data MongoDB MappingMongoConverterParser.
Fixed an integration issue with Mustache 0.9.1.

6.2.3 (24th August 2015)

JBoss AS 7.1+ integration has been improved to the full coverage for Java EE specifications.
Support for reloading Spring @Scope("request") @Controllers has been added.
WebSphere 8.5.5.x JSF integration has been improved to allow injecting new EJBs into ManagedBeans.
Adding new Spring components in packages scanned by WebApplicationInitializer is now supported.
Fixed an integration issue with Hibernate 4.3.
Fixed a redirect loop after reload when using MyFaces Extensions CDI project.
Fixed a NullPointerException in Liferay integration when running on a non-root context path.
Fixed an integration issue with Spring Data REST 2.4.0.RC1.
Fixed an issue with Spring @Configuration classes being reloaded just once.
Fixed an integration issue with Spring Boot Actuator 1.3.0.M2.
Fixed an issue with static resources within a JAR META-INF/resources folder that were not reloaded without a <web> link in a parent WAR rebel.xml on WildFly.
Fixed an integration issue with WildFly when started via Arquillian.
Fixed a FileNotFoundException when trying to load a class with URLClassloader from an URL like file://localhost/C:/....
Fixed an issue with TomEE 1.7.x where apps using Spring WebApplicationInitializer failed to deploy.
Fixed a VerifyError with the JRebel 6 Agent reloading Java 8 default methods when class redefinition is disabled.
Fixed an issue when configuring Hibernate with mappingDirectoryLocations caused duplicate class/entity mappings on WebSphere and Virgo.
Fixed an issue with the debugger integration where outer class breakpoints were not hit after reloading an inner class on IntelliJ.

6.2.2 (30th July 2015)

Added support for Wildfly 9.0.0.
Added support for Tomcat 7.0.63 and 8.0.24.
Added support for Google App Engine 1.9.23.
Added support for Pivotal tc Server 3.0.4 and 3.1.1.
Added support for Jetty 9.3.0.
Added support for WebSphere *******.
Added support for Grails 2.5.
Added support for reloading @Named beans in Servlets on TomEE.
Improved EJB support for WebLogic 12.1.x.
Performance improvements on startup for WildFly.
Fixed a few issues related to shutting down WildFly after reload.
Fixed an issue with reloading ADF jspx/jsff files.
Fixed an issue with running Grails 2.x using the run-war command.
Fixed an issue with Grails 2.x not loading static resources from plugins.
Fixed a NotSerializableException when running TomEE via the IntelliJ plugin.
Fixed an issue with ATG where JRebel caused duplicate lines in the log.
Fixed an issue with having an EJB with multiple names on JBoss 7.2.
Fixed an issue with AxonFramework registering double listeners after reloading Spring beans.
Fixed an issue with Hibernate entity listeners not being called.
Fixed an issue with using interfaces for RESTEasy endpoints.
Fixed an issue with the JRebel 6 agent reloading lambdas initialized from the static initializer.
Fixed an issue with the JRebel 6 agent handling reflective calls to retrieve changed type parameters.
Fixed an issue with the JRebel 6 agent concerning invalid lambda de-serialization.
Fixed an issue with the JRebel 6 agent reloading lambdas in default methods.
Fixed an issue with using Guice @Inject in RESTEasy resources.
Fixed an integration error with Guice 4.0.
Fixed an issue with reloading in Spring Boot when eager class loading was configured.
Fixed an integration issue with Spring MVC 4.2 RC1.
Fixed an AmbiguousResolutionException on WebLogic 12.1.x when using an @Decorator injected via producer.
Fixed an integration issue with JRuby 1.7.21.
Fixed an integration issue with Jersey 2.19.
Fixed an issue with Liferay CSS reloading when the filesystem notifications did not work.
Fixed an issue with missing the SASS load path in Liferay themes.

6.2.1 (16th June 2015)

Added support for TomEE 1.7.2.
Added support for Tomcat 6.0.44, 7.0.62 and 8.0.23.
Added support for Jetty 7.6.17, 8.1.17 and 9.2.11.
Added support for JBoss EAP 6.4.
Added support for Resin 4.0.44.
Added support for Google App Engine 1.9.21.
Added support for reloading JPA named queries that are defined in the mapping-file.
Added support for reloading EJB @TransactionAttribute values on Weblogic 12.1.3.
Added support for using newly added @Entity fields from @Stateful EJBs.
Added support for reloading changes to properties files used via @PropertySource in Spring.
Several performance fixes to the JBoss 7 plugin.
Added support for reloading static resources configured in ibm-web-ext.xml on WebSphere.
Added support for reloading JBoss SOAP handler chains.
Improved the reloading of Guice configuration.
Fixed a memory leak in Hibernate integration.
Fixed a possible ClassCircularityError when adding new Resteasy resources.
Fixed an issue with using added methods via OGNL in Tapestry 4.
Fixed a LinkageError when running Jetty via the Maven Jetty plugin.
Fixed an issue with WebSphere 6.1 where JRebel caused a redirect to welcome files and discarded the query parameters.
Fixed an issue with reloading @WindowScoped JSF beans in Apache DeltaSpike.
Fixed an issue with reloading named filter chains in Spring Security.
Fixed an issue where Apache Camel routes did not pick up changes in properties files.
Fixed an issue where using HikariCP with JRebel caused an illegal type at constant pool entry.
Fixed a CannotCompileException that was caused by having Spring MVC JAR without Spring Web JAR on the classpath.
Fixed an issue with GWT where the domain method could not be resolved when the method signature contained generics.
Fixed an issue where JRebel tried to to execute an operation on a closed EntityManagerFactory in EclipseLink after reload.
Fixed an issue with the JRebel 6 agent where changing a class to an interface caused an IncompatibleClassChangeError.
Fixed an issue with the JRebel 6 agent where a bean reload caused clone() to produce null fields.
Fixed an issue with the JRebel 6 agent where reloading interfaces or abstract classes could result in ClassFormatError.
Fixed an issue with the JRebel 6 agent where the presence of other javaagents could cause NoSuchMethodErrors.
Fixed an issue with the JRebel 6 agent where reloading a proxy class multiple times - without using the instance in between - resulted in NoSuchMethodError.
Fixed an issue with the JRebel 6 agent on WebLogic 8 with its Health Center functionality enabled.
Fixed a NullPointerException on WildFly 8.2 when deploying an EAR.
Fixed an issue with the debugger integration where method entry breakpoints did not hit on Eclipse.
Fixed an integration issue with Hibernate Validator 4.3.0.Beta1 that caused IncompatibleClassChangeError on reload.
Fixed an issue with OpenEJB where reloading app containing TomEE resources.xml caused an UnsupportedOperationException.
Fixed a CannotCompileException no such class: Field on GlassFish.
Fixed a performance issue with Equinox 4.4.
Fixed an issue with @PostConstruct methods running multiple times for JSF managed beans on WebLogic 10.3.6.
Fixed an issue with Spring Beans integration where anonymous beans were not reconfigured when descriptor changed.
Fixed an issue where CXF Features were lost after class reload.

6.2.0 (21st May 2015)

Improved the TomEE integration with full coverage for Java EE specifications.
Improved GWT 2.x integration.
Added support for WebSphere ********.
Fixed an issue with retrieving resource variables from JNDI after EJB reloading on WAS 8.5.5.
Fixed an issue on JBoss where after reloading an EJB with a non updateable parent class, the parent class methods were not found anymore.
Fixed an issue with using relative paths for <jar-file> in persistence.xml with spring-orm.
Fixed an integration issue found with iBATIS 3.0.
Fixed an issue where an @Inject added to a JAX-RS endpoint was not injected on WildFly.
Fixed an issue with the remote debugger not detaching when the VM was stopped.
Fixed an issue with the debugger integration, where a break point on a second added inner class did not hit.
Fixed an issue where a Spring bean with multiple annotation types (@Controller and @Configuration) was reconfigured twice on reload.
Fixed an issue with renaming Apache Camel route to a previously existing route name.
Fixed an issue with changing an @WebMethod signature when bundling the JAX-WS RI on WildFly.
Fixed issue where Hibernate SessionFactory configured with mappingDirectoryLocations caused DuplicateMappingException when the provided path ended with a slash symbol.

6.1.3 (5th May 2015)

Added support for Tomcat 8.0.21.
Added support for Resin 4.0.43.
Added support for Pivotal tc Server 3.0.3.
Added support for Pivotal tc Server 3.1.0.
Added support for Jetty 9.2.10.
Improved the Apache Camel integration to reload RouteBuilders when no HTTP requests are detected.
Improved the Grails 2.x cooperation with regular Spring reloading.
Various fixes and improvements to the debugger integration.
Fixed an issue where injecting EJBs failed after a reload on WildFly 8.x.
Fixed an issue where having a <jar-file> entry in persistence.xml of a WAR archive on WebLogic 12 did not work with JRebel.
Fixed an issue where the Grails 2 plugin also activated for Grails 1.
Fixed an issue with calling the new remote EJB2 methods on JBoss 4.
Fixed a VerifyError on the JRebel 6 Agent with auto generated classes that have "()" in their name.
Fixed an issue where the Hibernate annotations on the XML mapped entities were not reloaded.
Fixed an NPE thrown on reload after an application was not deployed on WebLogic 10.
Fixed an issue in Metro where @WebService stopped working after adding parameter to the existing @WebMethod.
Fixed an issue when mixed forward and backward slashes in a path caused JRebel to return null InputStream from ServletContext in some cases.
Fixed an issue where the WebLogic FilteringClassLoader filtering EclipseLink classes caused the JRebel EclipseLink plugin to stop working.
Fixed an issue where reloading caused duplicate Spring Integration endpoints.
Fixed a concurrency issue in the Guice plugin that was causing an NPE.
Fixed an issue where JRebel lost query parameters when redirecting to a welcome file.
Fixed an incompatibility with Swing Explorer agent on Java 7+.
Fixed a ClassCastExceptionwhen in the JRebel 6 Agent when reloading classes used in lambda expressions.
Fixed a failure with Apache Struts when a Struts Action is an inner class.

6.1.2 (6th April 2015)

Improved reloading MyFaces configuration when running with OpenWebBeans
Added support for adding JAX-RS endpoints using classpath scanning on Wildfly 8.x
Added support for adding new JPA entities on Wildfly 8.x
Added support for adding servlets by annotations on Wildfly 8.x
Fixed serialVersionUID warnings when using GWT devmode
Fixed an issue where JRebel added a body to a HTTP 304 response, which could cause Chrome to hang
Fixed an issue where IBM JVM threw java.lang.IncompatibleClassChangeError: incompatible InnerClasses attribute
Fixed preserving CDI bean injected field state on bean reload
Fixed an issue with EJB reload on JBoss when the EJBs where called remotely
Fixed Spring bean constructors not rerunning when @Autowired parameter was added
Fixed an issue when HK2 and Jersey combination crashed on reload
Fixed an issue where reloading class with multiple EJB definitions failed on JBoss EAP 6.x
Fixed an integration issue with CXF 2.7.8
Fixed an integration issue with OpenJPA when using load time enhancement
Fixed an integration issue with Hibernate 3.0.x
Fixed an ConcurrentModificationException from JRebel Hibernate integration
Fixed an issue where Reflection getMethods returned superinterface static methods
Fixed an issue with Grails 2.x integration where a plugins onChange handler was not called
Fixed an issue with JRebel causing classloader leaks, when application is undeployed
Fixed an issue where JRebel caused Log4j to use a wrong configuration file
Fixed an integration issue with jackson-databind 2.6.0-SNAPSHOT

6.1.1 (12th March 2015)

Added support for running JaCoCo agent together with JRebel
Added support for Resin 4.0.42
Added support for Google AppEngine 1.9.18
Added support for Jetty 9.2.9
Added support for Tomcat 7.0.59
Added support for adding/removing JAX-WS Metro SchemaValidators.
Added support for reloading new Spring actuator endpoints.
Added support for reloading URL rewrite patterns in JBoss Seam.
Added support for resetting the InstallationGUID.
Added support for preventing reactivation while having an offline lease from License Server.
Set Tomcat context reloadable attribute to false when running with JRebel to avoid Tomcat starting to redeploy.
Improved the behavior of reloading Hibernate 4.x entity classes. Make sure to always use the "update" strategy for schema regeneration on reload.
Improved JRebel startup time when project dependencies are missing from classpath.
Fixed an integration issue with version 2.1.3 of Grails Asset Pipeline plugin
Fixed an issue with reloading a method with array return type
Fixed an issue with reloading EJBss in JBoss that reference non reloadable EJBs
Fixed an NPE related to @ConversationScoped bean reloading
Fixed an issue where Spring MVC handlers cache wasn't properly cleaned in some case
Fixed an issue where on TomEE 1.7 @WebServlet annotated servlets weren't registered when running with JRebel
Fixed an issue with debugger throwing IllegalStateException on JDK 1.8.0_40
Fixed an issue in debugger integrations where adding a breakpoint to an anonymous inner class could fail after reload.
Fixed an ConcurrentModificationException in Jersey integration.
Fixed a potential class loader leak when servlet API was loaded from the WEB-INF/lib directory.
Fixed an issue where a changed interface method in an unchanged class failed with AbstractMethodError.
Fixed an issue where checking the license status takes over two minutes (network is available, but the License Server is not).

6.1.0 (26th February 2015)

Added support for Grails 2.2, 2.3 and 2.4.
Added support for Tomcat 8.0.17.
Added support for Jetty 9.2.7.
Added support for Liferay 6.2.2 GA3.
Added support for Struts 2.3.20.
Added support for reloading static web resources served by CachingResourceResolver in Spring 4.1.
Added support for reloading Spring Data @RestResource and @RepositoryRestResource annotations.
Improved Wildfly integration with full Java EE specifications coverage.
Fixed an issue where XRebel would not function properly with JRebel.
Fixed an issue where JRebel would fail to reload classes containing a great amount of inner classes.
Fixed an issue where JRebel reloading would fail with Jersey 1.1.4 and 2.16.
Fixed an issue where JRebel reloading would fail with Stripes 1.5.0.
Fixed an issue where FreeMarker templates were not reloaded.
Fixed a Spring issue where adding a Bean dependency got passed through the constructor and was declared in XML, causing NoSuchMethodException.
Fixed an issue with YourKit and JRebel compatibility.
Fixed an issue where RESTEasy threw RuntimeException: Unable to instantiate InjectorFactory implementation.
Fixed an issue which became apparent when adding a new interface and then overriding the default method.
Fixed a StackOverflowError caused by having an annotated method being annotated again.
Fixed an issue where a REBEL_BASE directory was not correctly created on Linux.
Fixed an issue where AVAST antivirus reported JRebel as a virus.
Fixed an issue where ObjectMapperCBP was applied even though Jackson core was not used.
Fixed an issue where GWT running on Google Application Engine threw a NoClassDefFoundError: org/eclipse/jetty/webapp/Descriptor.
Improved handling paths with spaces when configured with -agentpath.
Improved passing configuration arguments when configured with -agentpath on WAS.
Fixed an issue where JRebel configured with -agentpath on Windows XP would fail in some cases.
Fixed an issue where SBT could not compile when JRebel was configured with the -agentpath argument.


6.0.3 (30th January 2015)

Added support for Tomcat 6.0.43.
Added support for Jetty 9.2.6.
Added support for Google AppEngine 1.9.17.
Added support for Pivotal tc Server 3.0.2 SR2.
Added support for 'REBEL_BASE' environment variable and '-Drebel.base' JVM argument to enable customization of JRebel data directory.
Improved Hibernate integration when creating SessionFactory from MetadataSources.
Improved Spring integration to reload Spring Data @RestResource annotation.
Improved Spring integration to use the initial property value if a value injection failed.
Improved Weld/Weld 2 integration to take into account <include> and <exclude> tags when searching for beans.xml.
Fixed an issue where some rebel.xml directories weren't returned on JBoss 7.
Fixed an issue where some Mustache template partials were not reloaded.
Fixed an issue where Mustache reloading reported Wrong ClassLoader: WebappClassLoader.
Fixed an issue where a change in Spring bean definition containing constructor arguments caused a new instance to be created.
Fixed an issue where Spring beans with Hybris tenant scope were null after a reload.
Fixed an issue where no file system notifications were received on an XFS file system.
Fixed an issue where Metro JAX-WS endpoints were reloaded on every request.
Fixed a potential deadlock with counting redeploys.
Fixed an issue with Weld 2 integration where @Context injected field was null after a reload on a @RequestScoped JAX-RS resource.
Fixed a potential NPE in Weld 2 integration.
Fixed a potential NPE in Apache DeltaSpike and Weld 2.
Fixed an issue where a java.lang.VerifyError was thrown when changing a supertype from an interface to a class.


6.0.2 (23rd December 2014)

Added support for Tomcat 7.0.57.
Added support for Tomcat 8.0.15.
Added support for Jetty 9.2.5.
Added support for TomEE 1.7.1.
Added support for reloading languages.properties in Liferay portlets.
Improved JRebel 6 Agent reload speed.
Fixed an issue where the JRebel 6 Agent was unable to start when the JRebel installation path included bracket symbols ( ).
Fixed an issue where a javassist.NotFoundException: getFurthestParentTemp(..) is not found in org.jboss.virtual.plugins.context.AbstractVFSContext was logged with JBoss 5.
Fixed an issue where Spring Beans registered through @Configuration did not reload property values in some cases.
Fixed an issue where an NPE was thrown when inspecting a class with fields of type "short" or "char" while debugging.
Fixed an issue where after an EJB reload with JMSContext jmsContext.createProducer() was null in some cases on WildFly.
Fixed an issue where Spring ResourceHttpRequestHandler could not serve resources outside of webroot.
Fixed an issue where a WELD-001408 Unsatisfied dependencies for type [FacesContext] with qualifiers [@Default] at injection point [[field] @Inject was thrown for Weld applications in some cases.
Fixed an issue where a javassist.CannotCompileException: no such class: EntityManagerFactory was thrown for 'org.springframework.orm.jpa.AbstractEntityManagerFactoryBean' when using Sprint ORM without javax.persistence classes on classpath.
Fixed an issue where classes loaded by Tomcat 8 InstrumentableClassLoader were not reloaded by JRebel.
Fixed an issue where an ERROR ContextLoader: Context initialization failed was thrown with java.util.NoSuchElementException when running Spring 4 on JBoss 5 in some cases.
Fixed an issue where a deadlock would occur with Spring applications in very specific cases.
Fixed an issue where a ClassCastException was thrown for com.google.gwt.dev.javac.CompilationState during startup when running GWT 2.7.
Fixed an issue where a NoClassDefFoundError: javax/servlet/http/HttpServletRequest was thrown during startup of Liferay EE 5.2.6. running on GlassFish *******.
Fixed an issue where Primefaces-Extensions importEnum and importConstants tags were not reloaded properly.
Fixed an issue where a java.io.FileNotFoundException: ServletContext resource [/WEB-INF/classes/spring/] cannot be resolved exception was thrown when deploying a WAR file without directory entries (ant filesonly=true) to WebLogic.
Fixed an issue where a ArrayIndexOutOfBoundsException could be thrown when JRebel happened to reload classes that had not finished compiling.
Fixed an issue where a javassist.NotFoundException: loadSerializationPolicy(..) was thrown for com.google.gwt.user.server.rpc.RemoteServiceServlet when running GWT 1.5.3.
Fixed an issue where a java.net.MalformedURLException: no protocol: /path/to/file was thrown during the startup of Jetty 8 in some cases.
Performance and stability improvements.


6.0.1 (25th November 2014)

Added support for Wildfly 8.2.
Improved the performance of resource lookups in big applications.
Fixed an issue wherein org.zeroturnaround.bundled.javassist.NotFoundException: oracle.adf.JrDefinitionManager.
at org.zeroturnaround.bundled.javassist.ClassPool.get(JRebel:450) was thrown for ADF applications.
Fixed an issue wherein java.lang.NoClassDefFoundError: com/sun/faces/lifecycle/JrLifecycle at org.zeroturnaround.jrebel.adf.faces.cbp.LifecycleImplCBP.process(LifecycleImplCBP.java:18) was thrown for ADF applications.
Fixed an issue wherein added MyBatis XML mappings were not detected in some cases.
Fixed an issue wherein org.zeroturnaround.bundled.javassist.CannotCompileException: [source error] no such class: YamlPropertiesFactoryBean at org.zeroturnaround.bundled.javassist.CtNewMethod.make(SourceFile:79) was thrown for applications using Spring Boot 1.2.
Fixed an issue wherein the following exception was thrown on a TomEE 1.7.1 application using OpenWebBeans: org.apache.catalina.loader.WebappClassLoader loadClass.
INFO: Illegal access: this web application instance has been stopped already. Could not load $CLASS_NAME. The eventual following stack trace is caused by an error thrown for debugging purposes as well as to attempt to terminate the thread which caused the illegal access, and has no functional impact.
java.lang.IllegalStateException.
Fixed an issue wherein Class 'org.hibernate.impl.SessionFactoryImpl' could not be <NAME_EMAIL>@2c2e61f0: org.zeroturnaround.bundled.javassist.CannotCompileException: [source error] no such class: serviceRegistry was thrown on an application with both hibernate 3 and 4 jars present.
Fixed an issue wherein Class 'weblogic/corba/cos/naming/NamingContextAny_WLStub' could not be processed by 'com.zeroturnaround.javarebel.rH@175f175': org.aspectj.apache.bcel.classfile.ClassFormatException: weblogic.corba.cos.naming.NamingContextAny_WLStub is not a Java .class file.
Fixed an issue wherein JRebel Spring integration would reload unrelated beans when using annotation-based configuration.
Fixed an issue wherein Could not redefine class $CLASS_NAME:: com.zeroturnaround.javarebel.support.WrongClassNameException while removing an inner class with Wicket 6.
Fixed an issue wherein JRebel incorrectly set the charset of resources served by the server to platform default instead of what was configured for the server.
Fixed an issue wherein <welcome-file-list> did not work properly on WebSphere 8.
Various other stability fixes


6.0.0 (4th November 2014)

Added support for reloading class hierarchies (adding/swapping superclasses, implemented interfaces).
Added support for initializing new instance fields.
Added support for Tomcat 8.0.14.
Added support for JBoss EAP 6.3.0.
Added support for Glassfish 4.1.
Added support for Spring Boot.
Added support for EclipseLink MOXy 2.3.2, 2.4.2, 2.5.0.
Improved the performance of JBoss 7 / JBoss EAP 6 EJB.
Improved the handling of anonymous inner classes.
Fixed an issue wherein Hibernate, configured with hbm2ddl.auto="create-drop" caused an "Auto-commit cannot be set while enrolled in a transaction" error during reload.
Fixed an issue wherein java.lang.ClassCastException: org.jboss.as.ee.component.BasicComponent cannot be cast to org.jboss.as.webservices.injection.WSComponent were thrown on JBoss 7 in some cases.
Fixed an issue wherein java.lang.NullPointerException was thrown at org.jboss.as.weld.injection.WeldManagedReferenceFactory.getReference() in some cases.
Fixed an issue where processing some lambdas caused a Processing Lambda failed: -1: java.lang.ArrayIndexOutOfBoundsException: -1 to occur.
Fixed an issue wherein javax.ejb.EJBException: java.lang.IllegalArgumentException: Named query not found was thrown if a JBoss EJB module had a bean annotated with @WebService.
Fixed an issue wherein java.lang.NullPointerException at com.ibm.ws.webcontainer.webapp.WebApp.getServletContextName was thrown when running Liferay on WAS.
Fixed an issue wherein javax.servlet.ServletException: PWC1232: Exceeded maximum depth for nested request dispatches: 20 was caused by JRebel Stripes integration in some cases.
Fixed an issue wherein java.lang.NullPointerException was thrown for JAXBContextImpl.equals() in some cases.
Fixed an issue wherein java.lang.NullPointerException at net.sourceforge.stripes.controller.UrlBindingFactory.removeBinding would occur during Stripes action bean reload.
Fixed an issue wherein JSF/Mojarra composite components were not reloaded from the JAR.
Fixed an issue wherein JRebel enabled server VMs did not correctly respect the HTTP Accept-Ranges header.
Fixed an issue wherein the Spring bean @Value annotated properties were not correctly reloaded when the property changed.
Fixed an issue wherein the Spring bean @Autowired annotated properties of List type were not populated with the beans implementing the List type.
Fixed an issue wherein java.lang.NullPointerException was thrown when executing Hibernate Criteria API query in some cases.
Fixed an issue wherein reloading Spring Batch factory beans crashed with a CannotCompileException: [source error] no such class: serviceRegistry in some cases.
Fixed an issue wherein java.lang.NegativeArraySizeException was thrown from org.springframework.core.AttributeAccessorSupport.copyAttributesFrom() during concurrent access.
Fixed an issue wherein JRebel Spring integration threw an exception if ApplicationContext was accessed before initialization.
Fixed an issue with reloading Spring BeanNameAware beans.
Fixed an issue wherein Metro JAX-WS endpoints were not registered and generated 404 errors in some cases.
Fixed an issue wherein @NotNull annotation on entity superclass did not work on WildFly.
Fixed an issue wherein org.apache.camel.component.bean.MethodNotFoundException was thrown for a Camel route when breaking and fixing it.
Fixed an issue wherein java.lang.ClassCastException: org.apache.webbeans.context.CustomContextImpl incompatible with org.zeroturnaround.jrebel.myfaces.util.JrFlowDefinitionContext was thrown using OpenWebBeans with MyFaces in some cases.
Fixed an issue wherein java.lang.IllegalStateException: CDI BeanManager cannot find an instance of requested type org.apache.myfaces.cdi.view.ApplicationContextBean was thrown using OpenWebBeans with MyFaces.
Fixed an issue wherein Simpleframework XML processing was extensively delayed when running with JRebel.
Fixed an issue wherein JRebel Axis2 WAS8 integration would cause problems with previous versions of WAS.
Fixed an issue wherein CannotCompileException was thrown during startup when using Spring Date 1.0.2.
Fixed an issue wherein Mojarra XHTML files were not found from nested modules if JRebel Mojarra integration was disabled.
Fixed an issue wherein java.lang.NullPointerException at org.zeroturnaround.javarebel.webobjects.WebObjectsPlugin.preinit() was thrown when using WebObjects.
Removed support for the deprecated perpetual license type.
Overhauled the JRebel Activation GUI.


5.6.3a (16th September 2014)

Added support Spring 4.1


5.6.3 (8th September 2014)

Added support for Weblogic 12.1.3
Added support for IceFaces 3.3.0
Added support for Spring 4.1 RC2
Added support for Jersey 2.11
Added support for JAXB on Weblogic 12.x
Fixed an issue where VM startup would hang when network interfaces status were unavailable
Fixed an issue where custom Spring BeanFactory.getBean() returning null instead of throwing NoSuchBeanDefinitionException would cause JRebel to behave abnormally
Fixed an issue where Weld beans.xml file paths containing spaces would not work and lead to FileNotFoundExceptions on Wildfly
Fixed an issue where Weld application scoped beans were reloaded if session scoped bean reloads were triggered without a session
Fixed an issue where WebDAV servlets would 404 when there was a <web> element defined in the rebel.xml file
Fixed an issue where Resin classes were patched twice when running from watchdog without a custom java.system.class.loader being set, leading to CannotCompileExceptions
Fixed an issue where Camel bean new methods were not detected during reload
Fixed an issue where changes to @Controller annotations were not picked up correctly for Spring 4.0.6
Enabled most framework plugins by default
Various other stability and performance fixes


5.6.2 (26th August 2014)

Added support for new Reflection API methods in Java 8
Fixed an issue where a 'java.lang.VerifyError: Bad <init> method call from inside of a branch' was thrown with JDK version 7u67 (and higher) and 8u11 (and higher)
Fixed an issue where a 'java.lang.VerifyError: Operand stack overflow' was thrown in some cases when a class contained unreachable code
Fixed an issue where a 'CannotCompileException: cannot find constructor org.jboss.ejb3.vfs.impl.vfs2.VirtualFileWrapper' was thrown on JBoss 5.2 EAP
Fixed an issue where a deadlock would occur during startup with OpenJPA applications in some cases.
Fixed an issue where EJB beans where not injected into WebServices on WAS 8.0.0.5
Fixed an issue where a 'WELD-001318 Cannot resolve an ambiguous dependency' was thrown for applications using picketlink
Fixed an issue where a 'WELD-001318 Cannot resolve an ambiguous dependency' was thrown for type MappingMetaData on Wildfly
Fixed an issue where a 'java.lang.NoSuchFieldException: fieldCache' was thrown for applications using HK2 v2.3.0
Fixed an issue where Magnolia MappedBean property definitions were not properly updated in some cases
Fixed an issue where a 'ClassCastException: org.icefaces.impl.application.ReloadAfterNavigationFix' for applications using portletfaces-bridge 2.0.2
Fixed an issue where a 'NotFoundException: org.codehaus.jackson.type.JavaType' was thrown for Jackson applications with missing jackson-core
Fixed an issue with Weld2 where calling ObserverMethodImpl.getReceiver would return a concrete instance of the bean instead of a proxy, leading to missing aspects (transactions etc.)
Fixed an issue with JRebel Remoting where a 'Could not regenerate proxy class '$Proxy133': java.lang.NegativeArraySizeException' would be thrown in some cases during synchronization
Fixed an issue where adding a service method to a Jersey 2.10 resource endpoint would not work on Grizzly2 server
Fixed an issue where a StackOverflowError was thrown during EJB loading in some cases on Weblogic 12.1.2
Fixed an issue where language.properties hooks were not reloading on Liferay
Fixed an issue where Spring's EhCacheManagerFactoryBean was reloaded incorrectly and this lead to 'Could not open Hibernate Session for transaction: org.hibernate.service.UnknownServiceException' to be thrown


5.6.1 (23rd July 2014)

Added support for JDK 8u11 and JDK 7u65
Added support for Spring 4.0.6
Added support for Picketlink
Added support for jaxws-spring 1.9
Added support for Jenkins plug-ins 1.554.1
Improved Ejb support on JBoss7
Improved WildFly 8 performance
Fixed an issue for Spring properties when properties failed to load correctly.
Fixed an issue for Spring when unknown ListableBeanFactory broke the application
Fixed an issue with IceFaces 4 where headers got lost during reload


5.6.0 (25th June 2014)

Added support for Java 8 (lambdas, default methods, static interface methods etc.)
Added support for EJBs on Wildfly
Added support for Weld 2
Added support for JSF 2.2 Faces Flows
Added support for JSF 2.2 Resource Library Contracts
Added support for Spring Security 3.2
Added support for HK2
Added support for Magnolia 5.1
Added initial support for JBoss EAP 6.3.0-beta1
Updated Hibernate support to version 4.3.5
Updated Jackson support to version 1.8.10
Various stability improvements for JBoss 6 EAP/JBoss 7/Wildfly EJB integration
Fixed an issue where deploying multiple war applications in a single ear would not deploy on JBoss 7
Fixed an issue where Axis2 Webservice endpoints were not reloaded on WAS
Fixed an issue where META-INF/resources in rebel.xml <war> did not work properly
Fixed an issue where using Guice ServletModule for initialization threw a com.google.inject.ConfigurationException
Fixed an issue on Alfresco where changing alfresco-global.properties caused org.springframework.beans.factory.BeanCreationException to occur during reload
Fixed an issue where java.lang.NullPointerException at java.util.prefs.FileSystemPreferences$1.run() could occur in rare cases during starup
Fixed an issue with Grails 2.2.3 where the application would hang after a grails controller reload
Fixed an issue where reloading EJBs on WAS ******* would cause the application to crash in rare cases
Fixed an issue where a com.ibm.ws.asynchbeans.exception.AsynchBeanException:ASYN0064E would be thrown for WAS *******
Fixed an issue where Struts session scoped objects were not reloaded on configuration change
Fixed an issue where Spring property-placeholder properties were not reload correctly
Fixed an issue where a CannotCompileException was thrown for log4j-over-slf4j-1.7.6.jar
Fixed an issue where JSF @PostConstruct was called on already created instance when reloading class
Fixed an issue where JRebel did not clean up its temporary directories properly after shutdown
Fixed an issue where Akka 2.3.2 breaks CDI in GlassFish


5.5.4 (27th May 2014)

Added support for Wildfly 8.1
Added support for reloading Web Services on WAS 8.5.5 (using Axis 2)
Updated OpenWebBeans support to version 1.2
Updated Prettyfaces support to version 3.3.3
Improved licensing to reaquire a License Server 2 more consistently after temporary network connectivity issues
Fixed an issue where Mybatis interceptors were not reloaded properly
Fixed an issue where CXF resource info beans and factory classes were not reloaded properly when both were used
Fixed an issue where Camel 2.13 Websockets were closed after reload
Fixed a NullPointerException that occurred while reloading Jersey configuration classes in DropWizard
Fixed an issue with HotSwap where changed class was not reloaded if JRebel IDE plugin was not installed
Fixed an issue where defining a Spring @Bean in a WebSecurityConfigurerAdapter subclass caused an 'AlreadyBuiltException: This object has already been built' to occur
Fixed an issue where Spring inMemoryAuthentication changes did not reload
Fixed an issue in ADF where adding a ManagedBean in faces-config.xml did not work when both ADF and JSF was used in an application

5.5.3 (21st April 2014)

Added support for SASS reloading on Liferay
Updated Liferay support to version 6.2CE
Updated Jackson support to version 2.3
Updated Jersey support to version 2.7
Updated MyFaces support to version 2.2
Improved performance and decreased memory consumption for JBoss7 integration
Improved reloading servlet context resources bundled in .jar file META-INF/resources on various servers
Fixed an issue that caused jsp files in tag bundles to be recompiled unnecessarily on various servers
Fixed an issue that caused a NullPointerException to occur when using EJBs on JBoss 7.1/EAP 6+
Fixed an issue that caused a StackOverflowError to occur when using Spring in certain conditions
Fixed an issue where using <remember-me> did not work correctly in Spring Security configurations
Fixed an issue where Mojarra facelets would not reload when using concurrent facelet caches
Fixed an issue where Spring inner beans of a factorybean were not reloaded correctly
Fixed an issue that caused an AsynchBeanException ASYN0064E on WAS *******
Fixed an issue in stripes-guice integration when custom nameBasedActionResolver did not override all methods


5.5.2 (18th March 2014)

Added support for JBoss 6.2EAP
Added support for Stripes-Guice 1.5
Added support for EBean javaagent
Improved anonymous inner class handling in JRebel. Should be much more consistent
Improved performance for WebSphere servers, introduced resource URL caching
Fixed an issue that caused a CannotCompileException to occur in Spring MVC 3.1.0.M2
Fixed an issue that caused a CannotCompileException to occur in Liferay 5.2.3
Fixed an issue that caused a CannotCompileException to occur in GWT 2.6
Fixed an issue that caused a CannotCompileException to occur Websphere Liberty Profile 8.5 using MyFaces
Fixed an issue that would cause the Hibernate cache to be closed after reload
Fixed an issue that caused state to be lost for stateful EJBs on JBoss 7
Fixed an issue that would cause @PostConstruct methods on EJBs deployed to WAS to be called multiple times
Updated JRebel Remoting to not require rebel.xml files in modules deployed to remote server


5.5.1 (26th February 2014)

Added support for Tomcat 8.0.1
Added support for Mojarra 2.2
Added support for Jasypt
Improved stability for Wildfly
Improved performance for WAS (smarter resource caching)
Improved performance for PrettyFaces integration
Improved Mojarra plugin to better handle relative paths
Improved Mojarra plugin to not fail when method argument names were not available in library
Fixed an issue that caused excessive reloading of xml beans in Spring 2.0
Fixed an issue where a no such constructor: org.apache.jasper.xmlparser.ParserUtils was thrown on Tomcat 7.0.50
Fixed an issue where unauthorized users could synchronize to remote application
Fixed an issue where CXF service endpoints failed after a reload when configured with spring <jaxrs:server id="restContainer" address="/"><jaxrs:serviceFactories>
Fixed an issue where Apache CODI ViewAccessScoped beans scope was incorrect when run with JRebel
Fixed a rare race condition with License Server licensing that could cause server deadlocks during startup


5.5 (20th December 2013)

Added initial support for Wildfly
Added initial support for TomEE 1.6.0
Added support for EJBs on WebSphere 8.5.5
Added support for EJBs on JBoss 6EAP, 7.1, 7.2
Added support for Spring 4
Added support for PrettyFaces
Added support for reloading Spring ProxyFactoryBean
Extended JRebel Remoting support to cover standalone applications (and applications without a web component)
Improved JRebel Hibernate SessionFactory reloading, for manually configured SessionFactories/builders
Improved CLI utility for enabling JRebel for external servers to also include JBoss 4,5 and 6
Improved Stripes ActionBean handling frequency
Improved performance with Wicket
Improved support for mybatis-spring 1.0.0
Fixed an issue where JRebel would not work when a SecurityManager was enabled
Fixed an issue where calling Charset.availableCharsets() too early would cause application startup to fail
Fixed an issue where JRebel ATG plugin failed to start on Java 4
Fixed an issue where reloading stateless session beans (EJBs) would cause them to lose CDI bean field references on JBoss 6


5.4.2 (25th November 2013)

Improved spring-mybatis integration to cover even more configuration scenarios (for example using classpath:xml/**/*.xml)
Reduced memory footprint for spring applications with very large classfiles leading to an OutOfMemoryError
Fixed an issue that could prevent GlassFish from starting up with JRebel when there was no valid license found
Fixed an issue that caused a NullPointerException to be thrown on application startup
Fixed an issue that prevented JRebel from correctly handling reflection method calls with integer promotion
Fixed an issue with JSF <ui:include> causing FileNotFoundException on windows machines when used with a local resource URI
Fixed an issue that prevented adding Serializable interface to ejb method arguments on JBoss 7
Fixed an issue that caused a performance slowdown with Seam integration on JBoss 7


5.4.1 (16th October 2013)

Added initial support for Tomcat 8
Added support for JRebel log file rolling when -Drebel.log.roll=true is set
Added support for always reloading static initializers during class reload when -Drebel.always.rerun.clinit={true|regex} is set
Improved Spring reloading of beans with constructor injection for specific configurations
Fixed an issue where a singleton Spring bean instance did not get reloaded when it had been cached by OpenEntityManagerInViewFilter
Fixed an issue where a CannotCompileException occurred when using Spring 1.x
Fixed an issue where CDI applications with EJBs did not deploy properly on JBoss EAP 6.1.0
Fixed an issue where Tiles 2 templates were not found if their path contained any spaces
Fixed an issue where custom "named" events disappeared for Mojarra after reload
Fixed an issue where if the VM is attempting to load a class with invalid bytecode, then JRebel does not interfere with it
Fixed an issue where specifying multiple JRebel javaagents to a single VM caused exceptions to occur
Various stability fixes to CLI utility for enabling JRebel for external servers


5.4.0 (17th September 2013)

Added a command-line utility for enabling JRebel for servers started from command-line
Added support for RestEasy 3.0
Added support for Apache BVal 0.5
Added support for WebLogic 12.1.2
Added support for WebSphere 8.5.5
Added support for WebSphere Liberty Profile 8.5.5
Improved Spring reloading of beans with constructor injection
Fixed an issue where a CannotCompileException was thrown for a GWT application with missing gwt-dev.jar
Fixed an issue where a new Hibernate SessionFactory was erroneously created during closing of a previous one
Clarified wording on some license activation messages.


5.3.2 (16th August 2013)

Added support for Jersey 2
Added support for FreeMarker 2.3.20
Added support for reloading ADF View Objects
Added support for reloading ADF Task Flows
Added support for JDeveloper 11.1.1.x
Fixed an issue where JSP files were not reloaded in META-INF/resources folder of a dependency .jar
Fixed an issue where JSP tag and TLD files were not found when they were included in a subfolder of META-INF in an external library
Fixed an issue where reloading @ModelAttribute did not work in Spring
Fixed an issue with org.jboss.weld.exceptions.DeploymentException: WELD-001409 Ambiguous dependencies occurring on latest versions of JBoss
Fixed an issue where a CannotCompileException was thrown for org.eclipse.osgi.baseadaptor.bundlefile.DirBundleFile on Eclipse Kepler
Fixed an issue where a CannotCompileException was thrown when JRebel was used in conjunction with Spring3.2 and Hibernate3.2
Fixed an issue where Seam component.xml loading caused a RuntimeException to be thrown when JRebel was enabled
Fixed an issue where excluding a path pattern in the rebel.xml did not work as expected
Fixed an issue where JRebel would not start up on IBM JDK 1.7 SR4
Fixed an issue where a java.lang.AbstractMethodError was thrown in certain conditions for WebLogic's GenericClassLoader when using JRebel License Server licensing
Fixed an issue where the JRebel License Server was contacted too eagerly when using offline licensing


5.3.1 (1st July 2013)

Added support for Tiles 3
Added support for Axis 1.6.2
Added support for Seam 2.2
Improved performance for JRebel Seam integration (not modified *.component.xml files are not reloaded)
Improved performance for Spring applications on Virgo
Fixed an issue where messages defined in Spring ReloadableResourceBundleMessageSource were not reloaded
Fixed an issue where adding the first SimpleUrlHandlerMapping did not work correctly on Spring 3.0.1
Fixed an issue where Guice3 interceptors did not get applied to added methods
Fixed an issue with remoting where web resource were not uploaded to a non-webapp (e.g. jar file)
Fixed an issue where a CannotCompileException was thrown on WebSphere8 using OpenWebBeans
Fixed an issue where a NullPointerException was thrown in some cases on GlassFish 3.0
Fixed an issue where a programmatically set ViewHandler was lost after reload in Mojarra
Fixed an issue where usernames with non-ascii characters caused issues with MyJRebel communication


5.3.0 (27th May 2013)

Added support for JBoss 7.1.2/6.1.0EAP
Added initial support for Glassfish 4
Added support for Spring 3.2.2
Added support for OpenWebBeans 1.1.4+
Added support for Hibernate Validator 5
Improved support for reloading Spring factory beans
Improved performance for JRebel Seam integration
Improved performance on Tomcat for resources loaded from static jars
Fixed an issue where injecting EJBs into JSF managed beans did not work
Fixed an issue where Jersey reloading did not work if it was configured via Jersey ServletContainer
Fixed an issue where an invalid bytecode in a java core class (that was not used) could cause JRebel startup to fail
Fixed an issue where a StackOverflowError could occur on jetty with an archive with bundled .svn entries
Fixed an issue where an IllegalAccessError was thrown in some versions of JBoss 4
Fixed an issue where an AmbiguousResolutionException could occur in weld when registering beans in Solder ServiceHandlerExtension
Fixed an issue where a NullPointerException was thrown when using ATG on JBoss
Various improvements and stabilization for debugger integration of JRebel IDE plugins
Deprecated support for License Server versions 1.x


5.2.2 (5th April 2013)

Improved Spring support for @ComponentScan annotations
Improved Spring support for interceptors configured with annotations
Improved JRebel handling cases where same jar files were both in server/application lib on JBoss
Fixed an issue where Seam components were constantly rescanned on JBoss 7.1.1
Fixed an issue where an infinite loop occurred in WLS EJB on certain conditions
Fixed an issue where using a <jar> element in the rebel.xml caused inconsistent synchronization to occur
Fixed an issue with Lift integration where it would throw a NullPointerException in some cases


5.2.1 (26th March 2013)

Added support for VRaptor, supports adding @Resource and its associated methods
Added support for Scala 2.10 / Scalate 1.6.1
Improved Spring integration, granular dependency tracking and reloading
Improved Spring integration, added support for automatic rescanning of @ComponentScan inside @Configuration
Improved Weld integration performance on GlassFish
Improved Mojarra reloading, does not require modifying the conf file anymore
Fixed an issue where EJB-s were not reinjected into JSF ManagedBeans on GlassFish 3
Fixed an issue where EJB-s were reloaded excessively on WAS in some cases
Fixed an issue where ByteMan was used along with JRebel


5.2 (28th February 2013)

Major overhaul of the debugger integration to provide a more consistent debugging experience when using JRebel
Added CLI activation by invoking bin/activate.cmd|sh <license_file|activation_token|license_server_url>
Added support for Apache Camel 2.10
Added support for EclipseLink 2.4.1
Added support for TomEE 1.5
Added support for Thymeleaf 2
Added support for Log4j 2
Added support for changing EJB2 method interfaces on WebSphere 6.1
Added support for Guice Servlet 2/3
Improved Spring integration, the reloading is done more granularly now for java-based configuration
Improved Spring Security 3 support
Improved JRebel licensing check, should be done asynchronously and not block JVM startup
Improved Seam3 integration, where it did not activate in some cases
Improved JSF integration in portal environments (Liferay)
Fixed issue with EJB interceptors causing an exception on JBoss EAP 5.1.2
Fixed an issue with Mojarra where and UnsupportOperationException was thrown during reload
Fixed an issue with WebLogic where the EJB stubs were not reloaded in the remote client when the remote interface classes changed
Fixed an issue with Jersey that caused the ClassLoader to leak in some cases


5.1.3 (30th January 2013)

Added support for Spring 3.2
Added support for Apache CXF 2.7.2
Added support for MyBatis 3.2.0
Added support for RESTEasy 2.3.5
Added support for Stripes 1.5.7
Added support for Struts 2.3.8
Added support for Mustache :{
Added initial support for Spring Security 3+
Added support for ignoring files with compilation errors, see -Drebel.ignore_compilation_errors
Improved Mojarra integration performance
Improved facelet path generation for JSF implementations
Improved JSP handling on Tomcat 7.0.29
Fixed issue where modifying a JPA entity caused the entities to not be mapped on JBoss 7
Fixed issue with reflection where an IllegalArgumentException was not thrown when invoking a constructor with incorrect number of arguments
Fixed issue where MyFaces configuration was constantly reloaded in some cases.
Fixed issue where MyBatis(iBatis) mapping locations that were configured with Spring would not be reloaded
Fixed issue with Seam3 where multiple AfterBeanDiscovery events caused duplicate beans to be created
Fixed issue with Seam3 that caused an exception "Ambiguous authorizers found for security binding type" to be thrown in some cases
Fixed issue with WebLogic where a remote session bean call on a newly added EJB3 method caused a deadlock in certain cases
Fixed issue with GlassFish3 where interceptors were registered incorrectly during each reload
Fixed issue where a JSP file would not compile when there was a class and package with the same name and one of these were imported


5.1.2 (17th December 2012)

Added initial support for Apache Camel routes reloading.
Improved support for Spring @Transactional
Imporved support for EJBs on JBoss 7
Improved Equinox integration
Improved WLS integration performance
Fixed issue on WLS when EJB-s were being reloaded in multiple threads
Fixed issue where JRebel was crashing on 64bit IBM vm-s
Fixed issue with JSP hooks on Liferay 6.1.1
Fixed issue with Seam when Log4j was not available
Fixed CannotCompileException on Wicket6 with JRebel 5.1.1
Fixed NullPointerException occuring on startup when using TK-Autopatch


5.1.1 (23rd November 2012)

Added support for Virgo 3.6
Added support for JBoss 6 EAP/JBoss 7.1.2
Added support for Spring OSGi
Added support for JAX-WS on Weblogic 10
Added support for Captain Casa framework
Improved support for Spring nested beans
Improved support for JRebel Remoting and syncing APP-INF/classes
Improved facelets relative path handling
Improved support for older version of Equinox
Fixed NullPointerException after class reload in Spring @Transactional
Fixed issue with JRebel on Tomcat and unpackWARs="true"
Fixed issue with JRebel Remoting sometimes blocking requests to server after sync
Fixed issue with Jax-WS endpoints not being reload on WLS 10.3.5 in some cases
Fixed a random failure with WLS8 using exploded deployment
Fixed java.lang.IllegalStateException: The LogManager was not properly installed occurring on JBoss 6 EAP


5.1.0 (15th October 2012)

Added support for WAS 8
Added support for WAS 8.5
Added support for Oracle ATG
Added support for Spring Data
Added support for Hibernate Validator 4.3
Added support for Jackson 2
Added support for Java Plugin Framework
Added support for encryption to JRebel Remoting
Added proxy support for JRebel License Server clients
Improved support for Oracle ADF
Improved integration for Struts 2, now supports webwork configuration changes
Improved Struts 1 integration on Liferay
Improved support for rebinding Guice modules
Improved integration with Weld and Solder
Improved support for packaged tagfiles in WAS
Fixed controller mappings getting cleared after a reload in Spring MVC 3.1.2
Fixed log4j configurations made through Springs DOMConfigurator/PropertyConfigurator.configure(URL)
Fixed reinjection after reloading on scoped beans and other proxied beans in Spring
Fixed ClassCircularityError occuring on certain conditions in GWT
Fixed integration issue for JSF Mojarra 2.1.13
Fixed issue with OC4J components on JBoss
Fixed Facelets chain includes using relative paths with Maven war overlays
Fixed integration issue for Struts 2.3.4
Fixed issue with duplicate mappers in MyBatis after reload


5.0.1 (18th July 2012)

Added support for WAS 8.5 Liberty Profile
Added support for TomEE 1.0
Added support for Restlet framework
Added support for MyFaces 1.1
Added support for log4j 1.2.17
Added support for ZK 6
Added support for Scalate
Added support for repackaged Jsr250 Metadata in newer WLS servers
Improved enum handling
Improved support for Hibernate 4
Improved support for GWT 2.5.0-RC1
Improved GWT integration performance
Improved WAS integration performance
Improved JBoss7 integration performance
Fixed OpenJPA integration
Fixed Geronimo 3 classloader integration
Fixed adding new method to @Stateless JaxRS resource that caused @PersistenceContext to be null
Fixed premature reload event on WebLogic when EJBs were not yet fully deployed
Fixed NPE in Tiles1 plugin
Fixed Apache CXF 2.6.1 integration
Fixed adding Spring FactoryBean breaking reloading and Spring context
Fixed Spring BeanDefinitionParsingException when using <task:annotation-driven> and including resources with "classpath*:"
Fixed NPE occuring on certain conditions in Spring LocalSessionFactoryBean
Fixed NPE on Liferay


5.0 (7th June 2012)

Fixed EJB regeneration for WAS 7.0.0.21
Fixed NPE after adding new method for SLSB on WAS 7.0.0.21
Fixed ibm-ejb-jar-bnd.xml resource re-injection during EJB re-initialization on WAS
Added @PostConstruct annotation handling for re-initialized EJBs on WAS
Fixed Guice integration: preserve singleton instances
Fixed ConcurrentModificationException in Seam 2.1.2 on JBoss 7.1
Fixed serialization handling for classes that were serialized before class reloading
Fixed resource scanning for resource paths not starting with '/' for Tomcat 7 (and Virgo)
Fixed Tiles 1.3 integration
Fixed GWT performance issues
Fixed class reloading on Eclipse Virgo
Fixed class loading in NetBeans RCP environment
Fixed excessive reconfiguration for beans in Spring 3.1 integration
Fixed ConcurrentModificationException for Spring MVC in case of parallel requests
Fixed Spring class path scanner on Geronimo3
Fixes performance issues in in Seam integration
Fixed NPE in Metro integration
Fixed NPE in Weld plugin
Fixed MyFaces 2.0.6 integration failures
Fixed CXF 2.1 integration
Improved support for Hibernate 4
Added support for MyFaces 1.2
Added applet integration


4.6.2 (2nd May 2012)

Added org.springframework.transaction.annotation.Transactional annotation support
Added handling new included JSPs and changing tag files on WebLogic
Added handling JRealClassType in GWT
Added handling CssResource regeneration in GWT
Added Tiles 2.0.x BasicTilesContainer support
Improved CDI support on JBoss 7
Fixed breaking context in case of static @Beans factory methods in @Configuration annotated classes (Spring 3.1.1)
Fixed wildcard configuration in Spring contexts
Fixed Spring MVC 2.0 integration
Fixed Jackson 1.9.5 integration
Fixed Liferay 6.1CE Spring AOP integration
Fixed portlet redeploy exceptions on Liferay
Fixed EJB integration on WL10
Fixed NPE in Mojarra tag handler
Fiexd SpringWS plugin failures for SpringWS 1.x


4.6.1 (15th March 2012)

Added support for Jetty 4
Fixed GWT performance issues
Fixed integration for GWT 2.0
Spring 3.1: allow re-creation of @Bean annotated singletons in @Configuration class
Spring 3.1: added support for @ImportResource


4.6 (29th February 2012)

Added remote updates support (beta)
Added support for JBoss 7.1
Added Glassfish 3.1.x CDI support
Added support for Glassfish 3.1.2
Added support for Jackson 1.9.5
Added support for @ComponentScan, @Configuration, @Bean in Spring 3.1
Added the ability to change implementation class of existing bean in Spring integration
Added offline token support for license server 1.4.3
Added constants re-initialisation on interfaces
Added i18n support handling to GWT plugin
Added UiBinder support to GWT plugin
Fixed issues with JSNI and serialization in GWT plugin
Fixed JBoss Seam processing failure on JBoss 5.0.1
Fixed JBoss Seam plugin: adding new components should work even if no other class is reloaded
Fixed Apache CXF plugin: reflect changes to generated WSDL even if spring conf is not altered
Fixed Liferay pligin: aui.FormTag handling
Fixed Jersey plugin: shouldn't fail when running multiple Jersey apps in same container
Fixed excessive bean reloading in Mojarra 2.1.1
Fixed WebLogic 10.3.x EJB integration when interfaces reside in server classpath


4.5.4 (5th January 2012)

Added WebLogic 12c support
Added suport for Servlet 3 web fragments
Added initial MyFaces support
Fixed Liferay XML configurations monitoring
Fixed Liferay integration to support pre 5EE SP5 versions
Fixed Liferay portlet re-initialization without portletContext reset
Fixed Spring 2.0.x custom namespace handling
Fixed JBoss Seam component scanning performance issue
Fixed inherited annotations handling


4.5.3 (7th December 2011)

Enabled new methods to EJBs on JBoss 7
Improved JBoss 7 classloader integration
Added initial integration for Hibernate 4
Added Liferay JSP hooks support
Fixed GenericXmlApplicationContext handling for Spring 3.1
Fixed Spring's PropertyPlaceholderConfigurer change handling @Component beans
Fixed Seam deployer integration on JBoss 5.0.0.GA
Fixed mvc interceptor handling in Spring plugin
Fixed Weld integration to respect @EntityManager injection
Reworked Resin classloader integration
Fixed Groovy enum handling


4.5.2 (4th November 2011)

Added initial support for JRuby
Added support for Guice 3
Added support for WebSphere 8
Added support for CXF 2.4 web services
Added support for CXF 2.1.2 restful services
Improved Jersey integration
Fixed Mojarra integration for JBoss 7
Fixed internal resource discovery on WebLogic 10
Fixed NPE when reloading EJB on WebSphere


4.5.1 (19th October 2011)

Added Apache CXF plugin (JAX-RS part)
Added support for EJB 3.1 Lite on Glassfish 3
Added support for beans.xml monitoring in Weld plugin
Added deserialization support for Jackson plugin
Added support page definitions reloading to ADF Core plugin (tested with Oracle Fusion Middleware ********)
Added handling of EclipseLink 2's own javaangent
Added handling of STS Insinght
Added mvc:view-controller tag realoading support in Spring MVC plugin
Added support for Resin 4.0.23
Added support for Virgo 3
Fixed class loader integration for JBoss 5.1.0 EAP
Fixed EJB3 integration on JBoss 4.0.5
Fixed Apache Digester handling in Struts integration
Fixed welcome file handling on Jetty
Fixed EL integration for Resin 4.0.16+
Fixed Mojarra integration on Eclipse Virgo
Felcome file handling on WAS
Improved IntelliJIDEA class loader integration.


4.5 (20th September 2011)

Added support for @EJB in servlets for WebLogic 10.3.x & Glassfish 2/3
Added support for Geronimo 1
Added support for Oracle ADF lifecycle
Added support for @Value annotated properties in Spring plugin
Added support for JAXB on Java 6
Added support for Jersey
Added Weld 1.1 support for adding annotated fields to session, application and conversation scopes
Fixed JBoss Seam performance issue
Fixed Wicket-Spring 1.5 integration
Fixed Mojarra @ResourceDependency annotation handling
Fixed @FacesConverter annotation handling on Glassfish 3.1.1
Fixed Jasper integration for Tomcat 7.0.17+
Spring 3.1 integration improvements


4.0.3 (25th July 2011)

Added beta support for Java 7
Added beta support for JBossAS 7
Added beta support for Apache Wink
Added beta support for Spring-WS
Added beta support for JAXB
Fixed JSP reloading on WAS ********
Fixed issues in Mojarra 2.0 integration
Added support for @ManagedBean in Mojarra integration


4.0.2 (21st June 2011)

Fixed hotswap integration within debugging session
Fixed page.xml change reloading for Seam 2.2.1
More fixes for JBossAOP integration


4.0.1 (17st June 2011)

Added Apacha Geronimo 3 classloader integration
Added integration for Reflections library
Added request logging. Enabled with -Drebel.log.request=true parameter
Fixed method parameter annotations handling
Fixed Spring class path scanning
Fixed JBossAOP integration
Fixed JBoss EAP 5.1 Deployment Unit VFS scanning
Fixed performance issue in Struts 2 integration
Fixed Spring PropertyResourceConfigurers integration bug
Fixed JRockit R28 integration
Fixed issue with Hibernate integration in JBoss 6
Improved JSP integration for WebSphere


4.0 (31st May 2011)

* Preserve method ordering observed via Reflection API
* Enabled hotswap integration for the debugger
* Fixed NPE with Seam 3 + Stateful Beans
* Fixed a deadlock on Tomcat 6.0.26
* Fixed facelet path construction
* Fixed Logback integration for the recent Logback version
* Fixed JBossAOP integrtion bug


4.0-RC1 (23rd May 2011)

* Adding new Stateless/Stateful session beans in Oracle WebLogic 10.3
* Added Metro JAX-WS plugin
* Fixed EJB scanning on JBoss 4.2/5.1
* Fixed issue with @Scope-annotated Spring MVC controllers
* Fixed issue with Seam double-wrapping of JSF apps
* Fixed Spring integration bug: AnnotationConfigApplicationContext IllegalStateException


4.0-M2 (10th May 2011)

* Adding new Stateless/Stateful session beans in WebSphere 6.1 & 7.0
* Added Spring Security plugin
* Improved JBoss Seam integration: can add new components and change page.xml
* Added Hibernate Validator plugin
* Added support for Jetty 8
* Added stripes-guicer support for Stripes plugin
* Added componenets.xml support for JBoss Seam 2.x (enable with -Drebel.seam.componentReloading=true)
* Improved Equinox class loader integration
* Fixed JSP scriplets support on Glassfish and JBoss 3
* Fixed Struts/Tiles multi-module application bug
* Fixed packaged taglib scanning for Glassfish 3
* Fixed a bug related to <exclude> tag in rebel.xml
* Fixed JBoss 4.0.5 EJB deployment bug
* Fixed JBoss 5.1 EJB reloading
* Fixed Mojarra 2.1 classpath scanning


4.0-M1 (22nd March 2011)

* Added integration with HotSwap
* Added anonymous classes support
* Adding new Stateless/Stateful session beans in JBoss 4.2.x & 5.1
* Adding new Stateless/Stateful session beans in Glassfish 2.0, 3.0.1
* Added initial support for Spring 3.1
* Added support for Clojure's gen-class
* Added support for GAE 1.4.2
* Improved URL handler mapping reloading for Spring MVC
* Fixed getResourceAsStream("/") issue on Tomcat
* Fixed Mojarra 2/2.1 tag file resolving issue
* Fixed facelet resolving issue
* Fixes for Tiles integration
* Fixes for Struts integration
* Fixed EAR verification on WebLogic
* Fixed security policy generation on Glassfish 3.0.1/2.0
* Fixed WAS 7.0 tld lookup issue
* Fixed servlet context registration on WAS 5.1
* Fixed verification errors on WAS
* Fixed issue with DuplicateMemberException on WAS
* Fixed Stripes plugin excessive cache cleaning
* Improvements for Lift plugin
* Improved classloader integration JBoss 3


3.6.1  (8th February 2011)

* Added graceful handling of destroyed classloaders for Lift
* Added integration with FilteringClassLoader on WebLogic
* Improved EJB injection on JBoss 6
* More fixes to Tiles integration
* More fixes to Mojarra integration


3.6  (31st January 2011)

* Added Lift integration
* Added Logback integration
* Added JUEL integration
* Added integration with expression language used in early JSF-RI 1.1 releases
* Added support for Sun Application Server 8.1 & 8.2
* Added support for EJB 3.1 on Glassfish 3
* Added support for wiring new @EJB fields on Glassfish
* Added Stripes @Validate annotation support
* Added support for loading tag files from JARs mapped in rebel.xml on Tomact, Jetty, JBoss and Glassfish
* Added Spring web and portlet MVC URL handler mapping
* Fixes for the the current Weld snapshot version
* Fixed issue with WebServiceTestingServlet on Glassfish
* Fixed iBatis reloading when used with Spring template
* Fixed VFS handing on JBoss 6
* Fixed NPE in Tiles integration
* Fixed serialization integration on Oracle 1.6.0_23 JVM
* Fixed serialization integration on JRockit 1.4 and 1.5 JVMs
* Fixed an issue with Hibernate OpenSessionInViewFilter
* Improved Mojarra integration
* Reflection API integration improvements
* Enabled passing jrebel.properties location via system property
* Concurrency fixes
* Eclipse plugin: Added JRebel tab to the following launch configuration types: m2eclipse, Spring tc Server, Eclipse Virgo, RAP, OSGi
* Eclipse plugin: Improved debugger stepping behavior
* Eclipse plugin: Fixed error on opening server options editor for Glassfish 3
* Eclipse plugin: Improved WebSphere 6.1 support with RAD
* Eclipse plugin: ${rebel.workspace.path} property is set to Eclipse workspace location when launching programs from Eclipse
* IntelliJ plugin: Improved debugger stepping behavior


3.5  (15th November 2010)

* Exception stack trace now doesn't include JRebel artifacts
* Support for serializing added fields now enabled by default
* Better handling for modular class loaders (OSGi, NetBeans, Felix)
* Improved reflection performance
* rebel.xml <warFile> and <jar> extracted content is now reused after restart
* Added plugin for Spring Webflow
* Added plugin for Wicket
* Added plugin for Tiles 1.x, 2.x
* Added plugin for Seam Wicket integration
* Added plugin for JBoss AOP
* Added plugin for Jackson
* Added plugin for Lift framework (Scala)
* Added plugin for RESTEasy
* Fixed Spring configuration re-loading within servlet containers
* Maven plug-in renamed from javarebel-maven-plugin to jrebel-maven-plugin
* Maven plug-in improvements for rebel.xml generation
* Maven plug-in synchronized to Maven Central
* Fixed Groovy 1.7 meta class handling
* Fixed NullPointerException in Log4j plugin
* Fixed Log4j configuration re-loading if used via Commons Logging / SLF4J
* Fixed ClassCastException in Mojarra plugin when multiple Mojarra implementations in class path
* Fixed integration with Stripes 1.5.4
* Fixed NullPointerException in GWT
* Fixed NullPointerException in Struts2 DefaultStaticContentLoader while reloading Struts configuration
* Fixed IllegalStateException while reloading Mojarra configuration
* Fixed exceptions in Spring plugin after application redeploy
* Fixed issue with running JBoss 5 with security manager enabled
* Fixed iBATIS sql map reloading when iBATIS is configured by Spring
* Fixed issue with Spring configuration loaded from byte array
* Fixed method arguments being not available in debugger on method entry
* Fixed DuplicateMemberException on WebSphere 7
* Fixed IllegalAccessException when reflectively invoking methods of protected nested class
* Fixed reloading interceptors changes for Seam components
* Fixed response already commited excpetion from JRebel static resource servlet
* Fixed JRebel static resource servlet not redirecting on Jetty when directory is accessed without ending slash
* Fixed BeanInfo flushing on IBM 1.6 JVMs
* Fixed issue with CgLib proxy class regeneration
* Fixed too eager caching of web resource paths
* Eclipse JRebel plug-in: occasional pop-up notifications about saved redeploy time
* IntelliJ JRebel plug-in: occasional pop-up notifications about saved redeploy time
* IntelliJ JRebel plug-in: fixed NPE for rebel.xml generation action
* Fixed license server proxy settings issue

3.1.2 (6th August 2010)

* Added support for JBoss 6
* Added support for Freemarker
* Separated EclipseLink plugin from TopLink plugin
* Improved OSGI integration
* Fix hibernate initialization failing with Liferay 4.2.2
* Fix reflection incorrectly throwing IllegalAccessException
* Fix log4j plugin failing with log4j over sl4j
* Fixed an issue with packaged tag files on JBoss
* Fixed an issue with serialization
* Fixed an issue with JDK proxy classes
* Fixed an issue with Spring resource resolution
* Fixed several license server issues

3.1.1 (14th July 2010)

* Added support for serializing added fields (-Drebel.serialization=true)
* Added support for Tomcat 7
* Added support for Geronimo 2.1.5
* Added support for reloading ListResourceBundles on IBM 6.x JVMs
* Added support for JBoss serialization
* Added supprot for TomcatInstrumentableClassLoader
* Added support for Mojarra 2.0.3
* Added support for reloading Tag files in JARs with Jasper
* Added support for Jetty 7.1
* Added support for EclipseLink 2.0.2
* Added support for Spring 3.0.3
* Added statistics SDK
* Improved support for Groovy closures
* Improved AspectJ support
* Improved support for Google App Engine
* Improved support for Google Web Toolkit
* Fixed issue with missing persistence unit on Websphere
* Fixed issue with serializing hibernate session factory
* Fixed issue with reloading JPA entities on Glassfish 2
* Fixed issue with reloading JPA entities on Glassfish 3
* Fixed integration with old CgLib 2.x versions
* Fixed issue with reflection and package private classes
* Fixed handling of Seam lifecycle methods
* Fixed issue with Guice and constructor changes
* Fixed a NoClassDefFoundError with reflection
* Fixed a NoSuchMethodException with TopLink plugin
* Fixed an issue with log4j plugin

3.1 (1st June 2010)

* Added GlassFish EJB integration
* Added scriptlet support for GlassFish 3
* Added TopLink and EclipseLink JPA plugin that reloads EntityManagerFactory when configuration changes
* Added TopLink Spring (Non-JPA) plugin that reloads SessionFactory when configuration changes
* Added iBatis plugin for reloading sql maps
* Added support for reloading ListResourceBundles
* Added support for Hibernate JPA configuration
* Added support for changing @RequestParam annotations without explictit value in Spring 3
* Added initial support for Geronimo
* Improved Google App Engine rebel.xml support
* Config Wizard: Fixed an issue with saving settings
* Fixed an issue with EJB integration on WebLogic 10.3.3
* Fixed a NoClassDefFoundError on GlassFish
* Fixed a ClassCastException with CgLib proxies
* Fixed a FileNotFoundException with "jrebel.info.tmp"
* Fixed a StackOverflowError with Spring plugin
* Fixed a NullPointerExceptions with rebel.xml class loading
* Fixed an issue with trace log without default log level
* Fixed an issue with web links and JSPs on WebLogic 8
* Fixed an issue with defualt servlet on Tomcat
* Fixed an issue with static resources not being cached by browser
* Fixed integration with AspectJ 1.6.9
* Fixed integration with Javassis 3.12.0.GA
* Fixed integration with Wicket 1.4.9
* Fixed issues with CgLib
* Fixed issues with spring plugin causing failures on application startup
* Fixed livelock in spring plugin
* Fixed issues with struts 1 plugin causing failures on application startup
* Fixed issues with scriptlets referencing classes from non-webapp classloader on WebLogic 10
* Fixed synchronization issues within JRebel

3.0.1 (1st May 2010)

* Added advanced handling of JDK Proxies
* Added scriptlet support for Websphere
* Added scriptlet support for Resin.
* Added support for Resin 4.0.6
* Added support for AspectJ 1.6.8
* Added EJB 3 support for JBoss versions before 4.2.3
* Added support for Spring DM server 2.
* Fix integration with AspectJ load time weaving agent
* Fix classloader integration for NetBeans platform applications
* Fixed an issue with class initialization
* Fixed an issue with EJBs on WebLogic 11g
* Fixed integration with Mojarra 1.2.14
* Fixed IllegalAccessException not thrown for public methods in non-public classes.
* Fixed static fields not reinitializing of no other changes were made.

3.0 (15th April 2010)

* Fixed an issue with GuiceComponentReloader and closed class loaders
* Fixed an issue with rebel.xml being ignored by AppClassLoader

3.0 RC2 (13th April 2010)

* Improved perfomance of resource finding on Windows
* Changing super class now causes a SuperClassChangedException to be thrown.
* JRebel will now call the static method __rebelReload() after each reload, if it exists.
* Enabled plugins no longer display any console output, disabled plugins are listed with the JRebel banner.
* Fixed an issue causing IllegalAccessException with setAccessible(false) calls.
* Fixed synchronization of class reload listeners.
* Fixed an issue with LinkageError instead of NoClassDefFoundError on Windows
* Fixed an issue with closed class loaders on OC4J 10.x and Glassfish 3.x
* Fixed an issue with SpringBeanReloader or CachedIntrospectionResultsCBP and closed class loaders
* Fixed an issue with LinkageError on WebLogic 9 JspClassLoader
* Fixed an issue with IllegalArgumentException on Spring
* Fixed an issue with NullPointerException on class loading
* Fixed an issue with logging to an illegal file

3.0 RC1 (29 March 2010)

* Added OpenJPA plugin; reloads the EntityManagerFactory on configuration changes (disabled by default)
* Added support for load time weaving with spring-agent
* Fixed potential Classloader deadlock on Tomcat 6.0.26
* Fixed NoSuchMethodError for early Spring 2 versions

3.0 M3 (17 March 2010)

* Added rebel.xml editor for simplifying configuration
* Added support for adding new EJB interface methods on Websphere 6.1 with EJB 3.0 Feature Pack and Websphere 7.0
* Added support for adding new methods to CgLib and Javassist proxy classes
* Added Hibernate plugin that reloads the SessionFactory when configuration changes.
* Added plugin for Mojarra that reloads JSF annotations and xml configuration
* Added plugin for Weld that reloads Weld annotations
* Added support for reloading property resource bundles on IBM jvms
* Improved expression language support
* Improved startup behavior to prevent deadlocks and early classloading problems.
* Fixed Jar manifest being null if rebel.xml is used.
* Fixed private inner classes incorrectly treated as accessible.
* Fixed an issue with Javassist incorrectly handling return type overloading.
* Fixed an issue with JAR locking on Windows
* Fixed an issue with class initialization
* Fixed IllegalStateException if static resources were included from JSP
* Fixed WebLogic integration to support JPA
* Fixed Websphere integration for Websphere 7.0.0.7

3.0 M2 (15 February 2010)

* Added resource caching to improve startup time
* Classpath plugins are no longer supported for performance reasons
* Tuned the JBoss 4.x and 5.x classloader integration to improve startup time
* Added support for adding new EJB interface methods on WebLogic 8.x, 9.x, 10.x.
* Added support for adding new EJB interface methods and wiring new @EJB fields on JBoss 4.x, 5.x.
* Added support for Google Guice 2.x.
* Added plugin for reloading modified facelet tag libraries.
* Added plugin for JBoss Seam that reloads Seam annotations.
* Added plugin for Groovy that makes Groovy MetaClass work.
* Added Wicket 1.4 integration
* Added rebel.xml support for Google App Engine
* Better reflection support for JRockit 1.4.
* Jetty 6.x early versions now supported
* JRebel spring plugin will now autowire dependencies to beans having JDK proxies.
* AspectJ plugin will now reweave aspects to reloaded classes.
* Added support for adding new handler methods to spring MultiActionControllers.
* Added support for reloading messages used in struts <bean:message> tag.
* Fixed an issue with protected instance field shadowing
* Fixed an issue with class reloading and reflection
* Fixed an issue with changing method return types on interfaces
* Fixed netbeans classloader integration to find resources from rebel.xml classpath.
* Fixed Websphere 7 integration.
* Fixed Glassfish integration to support JPA load time weaving.
* Fixed default serialVersionUID for RMI-IIOP calls.
* Fixed Method#getGenericParameterTypes missing first parameter after class reload.
* Fixed Jrebel Struts 1 plugin failing in projects using tiles.
* Fixed an issue with custom plugins button in Agent Settings/Configuration Wizard
* Several server configuration fixes in Reference Manual/Configuration Wizard

3.0 M1 (15th October)

* Support for adding static fields and changing enums.
* Full JSP support (including accessing new methods, classes and fields from scriptlets)
* 25%-30% improvement on instrumented class memory use.
* JRebel no longer keeps references to classloaders after application redeployment.

2.2.1 (21st December 2009)

* Added "Packages" tab to the Agent Settings
* Fixed an issue that could cause a deadlock in the server.
* Fixed Class.getMethods skipping some bridge methods
* Fixed ClassLoader integration with NetBeans 6.5
* Fixed performance logging enabled by default after running Configuration Wizard/Agent Settings
* Fixed an issue with file paths containing "+" symbols
* Fixed a Setup issue with shortcuts on 64-bit Windows
* Fixed an issue with the installer that could cause it to fail on Mac OS X
* Fixed bytecode proxy bypass turned on by default by the Configuration Wizard
* Fixed autoupdates disabled by default by the Configuration Wizard
* Improved performance of JSPs on WebLogic

2.2 (15th December 2009)
* Added JRebel Setup
* Added JRebel Configuration Wizard
* Added JRebel Reference Manual
* Now can configure JRebel using conf/jrebel.properties
* Updated Eclipse IDE plugin:
  - WebLogic 9.2+ support
  - Glassfish v2, v3 support
  - JBoss 4+, 5+ support
  - Tomcat 5+, 5.5+, 6+ support
  - Websphere 6.0, 6.1, 7.0 (RAD 7.5) support
  - Automatic bootstrap.jar generation for 1.4 JVMs
* Updated IntelliJ 8+ plugin
  - Community edition support
  - Fixed handling of spaces in paths
  - Fixed the path separator for classpath building
  - Fixed multiple minor issues
* IntelliJ 6+ plugin
  - Community edition support
  - Better integration with IDEA
  - Fixed multiple minor issues
* NetBeans plugin
  - Supports debugging for 6.5 and 6.7
* Maven plugin now available from Maven Central repository
* Fixed an issue causing reflection invocations on static methods with changed return type to fail.
* Fixed an issue that could cause a NoClassDefFoundError on WebSphere.
* Fixed an issue that could cause a ClassNotFoundException on Glassfish (Felix)
* Fixed an issue that could cause a CannotCompileException on Websphere 6.0
* Fixed an issue that could cause a NullPointerException on Websphere

2.1.1 (13th November 2009)
* Support for Jetty 7
* Preliminary support for GlassFish v3 Preview
* Log4J plugin now reloads changes to log configuration on-the-fly.
* Fixed an issue causing the annotations on constructors to disappear after class reload.
* Support for Jetty 7.
* Fixed an issue with Google Web Toolkit client side classes in hosted mode
* Fixed an issue with FileNotFoundException thrown by JavaRebelResourceServlet
* Fixed an issue with ClassCastException when defining web services in web.xml
* Fixed several issues in the Wicket plugin.

2.1a (7th October 2009)
* Fixed the issue with usage reporting including all classes processed by AspectJ in the report
* Fixed Spring plugin backward compatibility with Spring 2.5.1, 2.5.2 and 2.5.3 (see http://www.zeroturnaround.com/forum/topic.php?id=435)

2.1 (6th October 2009)
* Reworked Struts 2.x plugin
* Brand-new Struts 1.x plugin
* JavaRebel renamed to JRebel
* GlassFish v3 Prelude now supported
* Felix OSGi container now supported
* Apache Tomcat will no longer automatically reload applications when JRebel agent is enabled.
* Updated statistics reporting with the latest redeploy & restart survey report (http://www.zeroturnaround.com/blog/java-ee-container-redeploy-restart-turnaround-report/)
* JRebel will now report anonymous usage stats, including jvm version, container name and other stats that can help us improve the product. You can see the text file with reported data next to jrebel.jar as jrebel.info. To disable reporting add -Drebel.usage_reporting=false to the JVM command line.
* Improved logging options (-Drebel.log=echo and -Drebel.log=trace).
* Fixed an issue with private static native methods sometimes not called correctly.
* Fixed ArrayIndexOutOfBoundsException when using -Drebel.packages on WebLogic
* Fixed an issue with Spring MVC plugin being disabled
* Fixed a problem that could cause a NoSuchMethodError when updating a Spring bean.
* Fixed an issue with Spring property placeholder configurers not applying
* Fixed an issue with deadlock of Spring plugin
* Fixed an issue with Spring MVC plugin requiring Spring AOP
* Fixed an issue with Resin up to 3.1.5 not starting
* Fixed issues with FileNotFoundException and NullPointerException
* Fixed an issue with ServletContext paths starting with './'.
* Fixed an issue with JRockit reflection

2.0.3 (25th August 2009)
* Significantly improved Spring classpath* resolution which could previously lead to duplicate beans and conf files to be found.
* Support for IBM WebSphere 6.0
* Statistics now displays the number of elapsed days if less than 30.
* Fixed an issue causing IllegalMonitorStateException to be thrown from changed synchronized methods.
* Fixed an issue that caused AccessControlException on Sun App Server 8.2.
* Fixed an issue that caused a wrong SerialVersionUID to be generated.
* Fixed and issue with Class.getMethods() returning in random order (caused Flex failure).
* Fixed an issue that caused CurrentModificationException in Spring plugin.

2.0.2b (22nd July 2009)
* Fixed a problem with try-catch not working properly
* Fixed Spring plugin compiled with Java 5 in 2.0.2a
* Fixed an issue with autoupdater and Java 1.4.
* Fixed Eclipse not starting with JavaRebel enabled (when developing Eclipse plugins).
* Spring MVC plugin wasn't enabled by default.
* Velocity plugin is now included.
* Reflection performance improvements.

2.0.2a (15th July 2009)
* (Critical) Disabled Spring singleton autoinstantiation after reload (caused too eager instantiation in some complex configurations)

2.0.2 (14th July 2009)
* Property resource bundles now reflect the changes to the property files. This should work without any special configuration from your part, just change the property file and see the changes reflected in your application
* Startup performance improvements.
* JavaRebel will now show usage statistics on startup.
* Resin 3.1, 3.2 and 4.0 are now fully supported
* SAP NetWeaver 7.0 and 7.1 are now fully supported
* Preliminary support for Google App Engine. It should work, but let us know if it doesn't, we're not quite ready to vouch for it yet.
* Directories defined in rebel.xml are now also case sensitive on Windows.
* Spring plugin is now considered stable and enabled by default.
* Spring plugin will now refresh XML files even when no classes were changed and no Spring MVC is used.
* Spring plugin will now instantiate and initialize new singletons eagerly (even if they are not referenced elsewhere).
* Spring plugin now supports springmodules-validator annotation changes.
* Fixed a deadlock in resource lookup (could cause infinite startup on JBoss).
* Fixed a problem with enums being "X is not an enum type"
* Fixed a file handle leak when deploying a web application with rebel.xml.
* Fixed an issue that caused "Class (x) was removed" messages.
* Fixed an issue with backslashes in include/exclude elements of rebel.xml.
* Fixed an issue that caused NullPointerException on JBoss 5.
* Fixed an issue with @Deprecated annotation and AspectJ.
* Fixed an issue that caused NoSuchMethodError on some (otherwise valid) reloads.
* Fixed an issue with wrong class redefinition causing e.g. problems with JBoss 5 (http://www.zeroturnaround.com/forum/topic.php?id=303)
* Fixed an issue that caused DuplicateMemberException on Jetty.
* Fixed an issue in Spring plugin that could cause an infinite loop when reloading a bean.
* You can now use -Xbootclasspath install option with Java 5+ (it works better on some older JVM versions and JRockit JVM).

2.0.1 (25th May)
* Fixed a deadlock that caused infinite startup.
* Fixed new method parameter annotations not parsed correctly.
* Fixed "this" not resolving in debugger.
* Fixed Spring plugin trying to reconfigure beans created by a FactoryBean.
* Fixed AspectJ plugin with Spring reparsing aop.xml.
* Fixed a NoClassDefFound in Struts 2.x and potentially others.
* Fixed EJB modules not-reloading in JBoss and WL when configured with rebel.xml
* Fixed NullPointerException on WL startup
* Fixed Velocity engine startup
* Improved Servlet Context performance when configured with rebel.xml
* Spring plugin now autowires changed beans that don't have BeanDefinition.
* Added Wicket @SpringBean plugin.

2.0 (25th March)
* Improved ClassLoader().getResources() method behaviour by introducing a new fallback mode (default) for rebel.xml.
* Logging no longer starts without -Drebel.log=true.
* Couple of minor issues.

2.0-RC3 (19th March)
* It's no longer necessary to make changes to web.xml in your project for rebel.xml/<web> to work.
* rebel.xml/<web> now supports nested <include>/<exclude>
* Updated Stripes plugin to 1.0.10
* Fixed a bunch of issues.

2.0-RC2 (11th March)
* Stripes plugin now included
* AspectJ plugin with support for load-time weaving is now included
* Improved Spring plugin performance
* Numerous fixes to container compatibility
* Numerous Spring plugin fixes
* Fixed issue with signed JARs throwing exceptions.
* Fixed issue with null assignment to added fields.
* Fixed issue with "synchronized" not appearing in Method.getModifier() (could cause NPE).
* Fixed issue with protected inner classes.
* Fixed issue causing ClassNoDefFoundError.
* Fixed issue causing mvn jetty:run to fail.
* Numerous minor and compatibility issues fixed.

2.0-RC1 (20th February)
* rebel.xml now supports patterns, includes, excludes and system property substitution
* Tapestry4 and Struts2 plugins are now also included in the distribution
* Deleting the application WAR will no longer cause exceptions
* Fixed an issue with Eclipse compiler producing invalid bytecode causing a NoSuchMethodError
* Fixed several minor issues causing a NullPointerException on changes
* Fixed an issue with some classes not processed correctly when loaded after a reflection call.
* Known issue: IllegalArgumentException: com.mypackage.Enum is not an enum type
* Known issue: NullPointerException from switch statement over enums
* Known issue: Resin 3.1, see http://bugs.caucho.com/view.php?id=3339

2.0-M2 (13th February 2009)
* New support for unexploded/packaged deployment including classpath and web resources (rebel.xml)
* Fast boot optimization loads and executes classes much faster before the first change.
* Optimized reflection on unchanged classes.
* Improved debugging on unchanged classes.
* Stack trace in exceptions will be two-three times shorter in many cases.
* Groovy supported out-of-the-box
* EL in JSP 2.1 now sees class changes
* New step-by-step installation manual.
* -Drebel.dirs and -Drebel.packages_include settings are deprecated in favor of the new rebel.xml configuration
* Included plugins (Spring and Guice) are now disabled by default.
* Fixed a problem with security providers now found in the classpath
* Fixed some problems with IBM WebSphere (changed installation instructions!)
* Fixed several issues in Spring and Guice plugins
* There is a known issue with Resin 3.1, see http://bugs.caucho.com/view.php?id=3339

2.0-M1a (19th December 2008)
* Fixed getField() and getFields() behaving wrong
* Fixed virtual classpath not working with Jetty6
* Fixed an initialization problem with parent-child circular dependencies
* Fixed JBoss, some versions of OC4J and Eclipse/Equinox failing on startup (AbstractMethodError or NoClassDefFoundError)
* Fixed abstract methods appearing as non-abstract via Method.getModifier() (caused CMP on WebLogic to fail among others)
* Fixed a problem with the JBoss proxies
* Fixed a problem with accessing a public method of a deafult visibility class via a public subclass (e.g. StringBuilder.substring() on Java 5+)
* Fixed plugins not working properly on classes already instrumented by JavaRebel

2.0-M1 (3rd December 2008)
This version has too many individual changes to bring them out. Therefore we only outline the major goals.
 * Startup time and performance overhead.
   Performance was one of the main goals for this release and we have optimized or otherwise eliminated
   most of the bottlenecks that made the previous versions so slow for some of the users.
   There is some work to be done before the final release, but you should see a noticeable difference already.
 * Compatibility.
   Compatibility was also a large concern for this release. We have devoted a lot of time to tweak
   reflection and annotations support as well as integration with specific frameworks.
   We also compiled an extensive test suite that should make JavaRebel work out of the box for most users.
 * Embedded plugins: Spring and Guice.
   We now support distributing the plugins along with JavaRebel instead of downloading and installing them separately.
   With this release we included Spring and Guice, so you should be able to load new components and dependencies
   without redeploying. More plugins will be included as they are stabilized or contributed.
 * Virtual classpath.
   Another concern for many of our users is configuring the existing build/deploy environment to make use
   of JavaRebel class reloading. Not everyone can use exploded development and -Drebel.dirs have limitations
   in support of added classes and resource propagation. That's why we implemented something we call a
   _virtual classpath_. The -Drebel.path property behaves similar to the -Drebel.dirs, except that instead of directories
   you can add WARs directly, with EARs and more advanced options coming soon.
   Virtual classpath will also propagate new classes and update your resources, like HTML or JSP files.
   It does require some extra configuration so take a look at the configuration manual (Configuration.html).
   NB! Virtual classpath is only supported on Tomcat, Jetty and WebLogic containers in this release.
 * Improved API.
   Besides the embeddable plugins we also now support third-party instrumentation. This will allow us to support
   e.g. AspectJ load-time weaving. Unfortunately the plugin itself didn't make it into this release, but since
   the required infrastructure is now in place we can release it retroactively as a plugin.

1.2.2 (2nd December 2008)
* Fixed a NPE in Method.getParameterTypes()
* Fixed Class.getMethods() not showing some of the added methods
* Fixed a classloader deadlock
* Added support for JBoss 5

1.2.1 (24th September 2008)
* Missing dependencies are no longer logged to console to avoid confusion
* Fixed an issue with IBM J9 JVM that resulted in a StackOverflowException on some setups (mainly with IBM WebSphere)
* Fixed a regression on Java 1.4 that resulted in an ExceptionInInitializerError
* Fixed an issue with JAR relative paths
* Fixed an issue with OC4J system classloader
* Fixed an issue with -Drebel.packages loading JARs as (corrupted) classes, could cause ArrayIndexOutOfBoundsException
* SpringSource Application Platform is now supported (see instructions in the installation manual)

1.2 GA (25th August 2008)
* General availability release

1.2-RC1 (15th August 2008)

* Fixed regression in accessing super methods
* Fixed broken setPrimitive() method behavior in java.lang.reflect.Field
* Fixed "native" methods causing a ClassFormatError
* Fixed a error occurring when super classes have a static and non-static field with the same name.
* Added support for time-limited licenses

1.2-M2 (30th July 2008)

* Spring dependency reloading is now available via an external plugin.
* Fixed Reloader.isReloadEnabled() returning true if agent wasn't enabled, but JavaRebel was in classpath
* Fixed a Java 1.4 incompatibility in javarebel-bootstrap.jar generation logic
* Fixed an infinite recursion on some super calls (http://www.zeroturnaround.com/forum/topic.php?id=87).
* Fixed several possible MethodNotFoundErrors
* Fixed Method.getGenericParameterTypes() returning wrong signature (needed for Spring integration)
* Fixed wrong modifiers returned by Method.getModifier() causing problems with AspectJ (http://www.zeroturnaround.com/forum/topic.php?id=91)
* Fixed a race condition in core logics that could manifest as a NPE.
* Fixed CgLib proxies throwing exceptions on initialization. To enable CgLib proxy bypass "-Drebel.allow_bytecode_proxy=true" should be now added to the JVM command line.
* Fixed some JavaRebel messages (like monitored dirs) not showing up properly.
* Improved reflection correctness and performance.

1.2-M1 (9th June 2008)

* JavaRebel SDK. The SDK has been refactored with added functionality for processing class bytecode and managing JavaRebel configuration.
* JavaRebel Plugins. It is now possible to register JavaRebel plugins that can make use of the SDK APIs to integrate with custom containers or frameworks.
* JavaRebel Integration Project. To test the new SDK APIs and plugins we created an open-source integration project and moved almost all of the custom container/framework integration processors there.
* Full support for Eclipse plugins (and OSGi bundles generally)
* Initial support for IBM WebSphere, please check the installation manual included in the distribution.
* Integration with Commons-EL, to update metadata when classes change.

1.1.4 (15th August 2008)

* Fixed regression in accessing super methods
* Fixed broken setPrimitive() method behavior in java.lang.reflect.Field
* Fixed "native" methods causing a ClassFormatError
* Fixed a error occurring when super classes have a static and non-static field with the same name.
* Added support for time-limited licenses

1.1.3 (12th August 2008)

* Fixed wrong modifiers returned by Method.getModifier() causing problems with AspectJ (http://www.zeroturnaround.com/forum/topic.php?id=91)
* Fixed update notifier choking when update.zeroturnaround.com is unavailable, forbidden or replaced.

1.1.2 (22nd July 2008)

* Fixed a race condition in core logics that could manifest as a NPE.
* Fixed method modifiers not being updated in reflection.
* Fixed an NPE when getting updated method data via reflection.
* Fixed CgLib proxies throwing exceptions on initialization. To enable CgLib proxy bypass "-Drebel.allow_bytecode_proxy=true" should be now added to the JVM command line.
* Improved reflection correctness and performance.
* Backported full support for Equinox OSGi/Eclipse plugins.

1.1.1 (26th May 2008)

* Fixed package visibility constructors sometimes causing a NullPointerException (http://www.zeroturnaround.com/forum/topic.php?id=63).
* Fixed a NullPointerException sometimes occuring on class reload (http://www.zeroturnaround.com/forum/topic.php?id=78).
* Fixed an IllegalAccessException occuring when calling a super protected method via reflection (http://www.zeroturnaround.com/forum/topic.php?id=65).
* Fixed Class.getMethods() not returning methods for indirectly implemented interfaces when JavaRebel was enabled.
* Fixed changes to method return type or field type not propagating to reflection (http://www.zeroturnaround.com/forum/topic.php?id=68).
* Fixed site licenses being displayed as personal licenses.
* Improved compatibility with proxies when "-Drebel.forbid_bypass=true" is enabled.
* Added an experimental hook for receiving class loading notification to the SDK. To enable it add "-Drebel.class_load_notify=true" to the command line.
* Added support for licenses that work only on Scala classes.

1.1 (30th April 2008)

* Changed -Drebel.dirs semantics to always override classes in classpath.
* Fixed Eclipse integration
* Added some helpful messages connected to -Drebel.dirs and Java 1.4 bootstrap generation

1.1-RC2 (28th April 2008)

* Fixed a problem with synthetic attributes messing up with some frameworks. Mainly manifested as NPE when using Spring AspectJ support.

1.1-RC1 (16th April 2008)

* Full support for Java 1.4. Now all of the features available in Java 5 are also available in Java 1.4. The installation is also now simpler and supports more containers. See installation (http://www.zeroturnaround.com/javarebel/installation/) for details.
* Interface changes are now visible through reflection including changes to annotations.
* Fixed: If you added a primitive non-static uninitialized field to a class JavaRebel would throw a NullPointerException.

1.1-M3 (9th April 2008)

* JavaRebel now supports unexploded deployment (e.g. WAR, JAR or EAR) by specifying the paths that .class files are compiled to by the IDE (or command line) using the property "-Drebel.dirs=/path/to/classes1,/path/to/classes2,..."
* JavaRebel now supports reloading changes made to classes in JAR files, for that you need to specify "JavaRebel-Reloadable: true" in MANIFEST.MF or name the JAR file ending with a "-reloadable.jar" suffix.
* Improved logging, now all information on where the class is loading from goes to plaintext log ("-Drebel.log=true").
* Added performance statistics logging that goes to plaintext log ("-Drebel.log.perf=true").
* Fixed a problem with Hibernate JPA implementation caused by Synthetic attribute
* Fixed a problem with "AbstractMethodError __rebel_bypass__()"
* Code base refactored to prepare for SDK expansion in 1.1-M4

1.1-M2 (10th March 2008)

 * Enhanced support for dynamic proxies (JDK, CGLib, Javassist)
 * Improved support for missing dependencies
 * Improved classloading behaviour
 * Support for stepping through generated methods in debugger (all generated methods marked "synthetic")
 * JavaRebel log is no longer encrypted (use -Drebel.log=true to see it)
 * JavaRebel will now flush all JavaBean Introspector caches when a class changes
 * Improved error reporting
 * Fixed static methods showing up with suffix "__RS__" in reflection
 * The development of Spring Framework with JavaRebel tested

1.1-M1b (20th February 2008)

 * Fixed a JavaRebel failure on a virtual and static method with same names and similar parameters.

1.1 M1a (4th February 2008)

 * SDK implementation classes are no longer obfuscated.

1.1 M1 (29th January 2007)

 * Improved reflection support. Now added/removed methods and fields in the reloaded classes will always be reflected correctly in the Java Reflection API. At the moment this does not include constructors.
 * Annotation reloading. Annotations on classes, methods and fields will be updated when the class is reloaded. At the moment this does not include annotations on constructors and method parameters.
 * JavaRebel SDK. For integrating custom frameworks with JavaRebel and getting the next step in turnaround time check out the SDK at our Google Code project.

1.0.3 (20th February 2008)

 * JBoss support has been greatly improved, side-by-side SAR deployments are now supported
 * GlassFish now no longer crashes because of missing LogManager
 * AbstractMethodError could occur in some cases
 * Final fields were wrongly reported as non-final via Reflection API
 * Minor fixes

1.0.2  (24th January 2008)

 * Fixed an IllegalAccessError with package visibility classes in method return type (e.g. Wicket AbstractChoice).

1.0.1  (17th December 2007)

  * Support for GlassFish v2+.
  * Fixes to reflection support, caused NPE on JBoss and issues with Annotation inheritance.

1.0  (5th December 2007)

 * Reflection support. Methods added to classes will be properly visible via the Reflection API under Java 5+.
 * Eclipse PDE support. Plugin code can now be reloaded using JavaRebel. See this post and screencast for details. Instructions are in the installation manual.
 * IntelliJ IDEA plugin development support. Plugin code can now be reloaded using JavaRebel. Instructions are in the installation manual.
 * Java EE container support. We now support Orion and Caucho Resin under Java 5. Instructions are in the installation manual.
 * Custom classloader support. If JavaRebel doesn't officially support your container or you use a custom classloader there is a good chance it will work now.
 * All known issues fixed.

1.0 M3 (5th November 2007)

 * No more need for "-Xbootclasspath/a:javarebel.jar" if the name of the JAR is not changed. JavaRebel will detect if the name of JAR is changed and suggest to either rename it or add to boot classpath.
 * Major improvements in performance. Startup time should in many cases improve two or more times (if slower than vanilla JVM). JavaRebel should also no longer use CPU when the application is idle.
 * Improved support for anonymous classes. Since compiler names anonymous classes consequently, adding a new one may rename all the rest and change their super classes and interfaces. Now JavaRebel handles such renaming without problems. This feature is disabled by default since some problems have been tracked to it. To enable add "-Drebel.enable_anon=true" to the command line.
 * Update notification. JavaRebel will now notify you when a newer version is available. No personal information (not even the currently installed version) is going ot ZeroTurnaround. To disable the feature add "-Drebel.disable_update=true" to the command line.
 * Support on Java 1.4 for Tomcat 4.x, Oracle 9.x, Oracle 10.x. Installation instructions have also been updated.
 * Fixed problems with reloading partially compiled classes present when doing a full rebuild.
 * Fixed an incompatibility with Scala Array construction.
 * Fixed an incompatibility with Tapestry 4.
 * Fixed obfuscated class name conflict.
 * Fixed a problem with Log4J Level constructor throwing an NPE.
 * Fixed problems with shared classloader on Tomcat.
 * Fixed problems with classloading on JBoss.
 * Fixed Class.getPackage() returning null.
 * Fixed problems with serialization when serialVersionUID was missing.
 * Fixed problems with circular class dependency
 * Minor fixes

