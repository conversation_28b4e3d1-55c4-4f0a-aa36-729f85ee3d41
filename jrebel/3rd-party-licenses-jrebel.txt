The JRebel Software contains some open-source 3rd party software which are copyrighted to third
parties and are licensed under different terms and conditions than the JRebel End-User License
Agreement. The licensing terms are as provided below. Reference to the websites of the
corresponding vendors for additional details.


* Copyright for Apache HttpComponents 5.3.1, Apache Lucene 3.6.3, Commons Beanutils 1.9.4,
  Commons Collections 4.4, Commons Configuration 2.11,
  Commons Digester 1.8.1, Commons IO 2.16.1, Commons Lang 3.14.0, Commons Logging 1.1.3,
  Commons Validator 1.9.0, Commons Text 1.12 and Apache Harmony is held by Apache Software Foundation.
  Apache Harmony was included via and with the modifications by Android libcore 4.4.2_r2.
  Copyright for AntPathMatcher (derivative work based on Spring Framework) is held by SpringSource.
  Copyright for WeakIdentityHashMap.java (derivative work based on MapDB) is held by MapDB.
  Copyright for JavaRebel Stripes Plugin 1.0.12 is held by <PERSON>. JRebel embeds a derivative work based on it.
  Copyright for JSON Simple 1.1, 1.1.1 is held by <PERSON><PERSON> and <PERSON>.
  Copyright for Jetty 9.4.53.v20231009 is held by Mort Bay Consulting Pty Ltd.
  Copyright for Java Image Filters 2.0.235 is held by JH Labs.
  Copyright for Jackson Annotations 2.15.2, Jackson Core 2.15.2 and Jackson Databind 2.15.2 is held by FasterXML, LLC.
  Copyright for FSNotifier library is held by JetBrains.
  Copyright for StringUtils.java (extracted from Spring Framework) is held by SpringSource.
  They are all licensed under the Apache Software License, version 2.0:


                                     Apache License
                               Version 2.0, January 2004
                            http://www.apache.org/licenses/

               TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

               1. Definitions.
                  "License" shall mean the terms and conditions for use, reproduction,
                  and distribution as defined by Sections 1 through 9 of this document.

                  "Licensor" shall mean the copyright owner or entity authorized by
                  the copyright owner that is granting the License.

                  "Legal Entity" shall mean the union of the acting entity and all
                  other entities that control, are controlled by, or are under common
                  control with that entity. For the purposes of this definition,
                  "control" means (i) the power, direct or indirect, to cause the
                  direction or management of such entity, whether by contract or
                  otherwise, or (ii) ownership of fifty percent (50%) or more of the
                  outstanding shares, or (iii) beneficial ownership of such entity.

                  "You" (or "Your") shall mean an individual or Legal Entity
                  exercising permissions granted by this License.

                  "Source" form shall mean the preferred form for making modifications,
                  including but not limited to software source code, documentation
                  source, and configuration files.

                  "Object" form shall mean any form resulting from mechanical
                  transformation or translation of a Source form, including but
                  not limited to compiled object code, generated documentation,
                  and conversions to other media types.

                  "Work" shall mean the work of authorship, whether in Source or
                  Object form, made available under the License, as indicated by a
                  copyright notice that is included in or attached to the work
                  (an example is provided in the Appendix below).

                  "Derivative Works" shall mean any work, whether in Source or Object
                  form, that is based on (or derived from) the Work and for which the
                  editorial revisions, annotations, elaborations, or other modifications
                  represent, as a whole, an original work of authorship. For the purposes
                  of this License, Derivative Works shall not include works that remain
                  separable from, or merely link (or bind by name) to the interfaces of,
                  the Work and Derivative Works thereof.

                  "Contribution" shall mean any work of authorship, including
                  the original version of the Work and any modifications or additions
                  to that Work or Derivative Works thereof, that is intentionally
                  submitted to Licensor for inclusion in the Work by the copyright owner
                  or by an individual or Legal Entity authorized to submit on behalf of
                  the copyright owner. For the purposes of this definition, "submitted"
                  means any form of electronic, verbal, or written communication sent
                  to the Licensor or its representatives, including but not limited to
                  communication on electronic mailing lists, source code control systems,
                  and issue tracking systems that are managed by, or on behalf of, the
                  Licensor for the purpose of discussing and improving the Work, but
                  excluding communication that is conspicuously marked or otherwise
                  designated in writing by the copyright owner as "Not a Contribution."

                  "Contributor" shall mean Licensor and any individual or Legal Entity
                  on behalf of whom a Contribution has been received by Licensor and
                  subsequently incorporated within the Work.

               2. Grant of Copyright License. Subject to the terms and conditions of
                  this License, each Contributor hereby grants to You a perpetual,
                  worldwide, non-exclusive, no-charge, royalty-free, irrevocable
                  copyright license to reproduce, prepare Derivative Works of,
                  publicly display, publicly perform, sublicense, and distribute the
                  Work and such Derivative Works in Source or Object form.

               3. Grant of Patent License. Subject to the terms and conditions of
                  this License, each Contributor hereby grants to You a perpetual,
                  worldwide, non-exclusive, no-charge, royalty-free, irrevocable
                  (except as stated in this section) patent license to make, have made,
                  use, offer to sell, sell, import, and otherwise transfer the Work,
                  where such license applies only to those patent claims licensable
                  by such Contributor that are necessarily infringed by their
                  Contribution(s) alone or by combination of their Contribution(s)
                  with the Work to which such Contribution(s) was submitted. If You
                  institute patent litigation against any entity (including a
                  cross-claim or counterclaim in a lawsuit) alleging that the Work
                  or a Contribution incorporated within the Work constitutes direct
                  or contributory patent infringement, then any patent licenses
                  granted to You under this License for that Work shall terminate
                  as of the date such litigation is filed.

               4. Redistribution. You may reproduce and distribute copies of the
                  Work or Derivative Works thereof in any medium, with or without
                  modifications, and in Source or Object form, provided that You
                  meet the following conditions:

                  (a) You must give any other recipients of the Work or
            	  Derivative Works a copy of this License; and

                  (b) You must cause any modified files to carry prominent notices
            	  stating that You changed the files; and

                  (c) You must retain, in the Source form of any Derivative Works
            	  that You distribute, all copyright, patent, trademark, and
            	  attribution notices from the Source form of the Work,
            	  excluding those notices that do not pertain to any part of
            	  the Derivative Works; and

                  (d) If the Work includes a "NOTICE" text file as part of its
            	  distribution, then any Derivative Works that You distribute must
            	  include a readable copy of the attribution notices contained
            	  within such NOTICE file, excluding those notices that do not
            	  pertain to any part of the Derivative Works, in at least one
            	  of the following places: within a NOTICE text file distributed
            	  as part of the Derivative Works; within the Source form or
            	  documentation, if provided along with the Derivative Works; or,
            	  within a display generated by the Derivative Works, if and
            	  wherever such third-party notices normally appear. The contents
            	  of the NOTICE file are for informational purposes only and
            	  do not modify the License. You may add Your own attribution
            	  notices within Derivative Works that You distribute, alongside
            	  or as an addendum to the NOTICE text from the Work, provided
            	  that such additional attribution notices cannot be construed
            	  as modifying the License.

                  You may add Your own copyright statement to Your modifications and
                  may provide additional or different license terms and conditions
                  for use, reproduction, or distribution of Your modifications, or
                  for any such Derivative Works as a whole, provided Your use,
                  reproduction, and distribution of the Work otherwise complies with
                  the conditions stated in this License.

               5. Submission of Contributions. Unless You explicitly state otherwise,
                  any Contribution intentionally submitted for inclusion in the Work
                  by You to the Licensor shall be under the terms and conditions of
                  this License, without any additional terms or conditions.
                  Notwithstanding the above, nothing herein shall supersede or modify
                  the terms of any separate license agreement you may have executed
                  with Licensor regarding such Contributions.

               6. Trademarks. This License does not grant permission to use the trade
                  names, trademarks, service marks, or product names of the Licensor,
                  except as required for reasonable and customary use in describing the
                  origin of the Work and reproducing the content of the NOTICE file.

               7. Disclaimer of Warranty. Unless required by applicable law or
                  agreed to in writing, Licensor provides the Work (and each
                  Contributor provides its Contributions) on an "AS IS" BASIS,
                  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
                  implied, including, without limitation, any warranties or conditions
                  of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
                  PARTICULAR PURPOSE. You are solely responsible for determining the
                  appropriateness of using or redistributing the Work and assume any
                  risks associated with Your exercise of permissions under this License.

               8. Limitation of Liability. In no event and under no legal theory,
                  whether in tort (including negligence), contract, or otherwise,
                  unless required by applicable law (such as deliberate and grossly
                  negligent acts) or agreed to in writing, shall any Contributor be
                  liable to You for damages, including any direct, indirect, special,
                  incidental, or consequential damages of any character arising as a
                  result of this License or out of the use or inability to use the
                  Work (including but not limited to damages for loss of goodwill,
                  work stoppage, computer failure or malfunction, or any and all
                  other commercial damages or losses), even if such Contributor
                  has been advised of the possibility of such damages.

               9. Accepting Warranty or Additional Liability. While redistributing
                  the Work or Derivative Works thereof, You may choose to offer,
                  and charge a fee for, acceptance of support, warranty, indemnity,
                  or other liability obligations and/or rights consistent with this
                  License. However, in accepting such obligations, You may act only
                  on Your own behalf and on Your sole responsibility, not on behalf
                  of any other Contributor, and only if You agree to indemnify,
                  defend, and hold each Contributor harmless for any liability
                  incurred by, or claims asserted against, such Contributor by reason
                  of your accepting any such warranty or additional liability.

               END OF TERMS AND CONDITIONS




* Copyright for SwingLabs (SwingWorker 1.1 and SwingX 1.6.1) is held by Oracle.
  They are all licensed under the GNU Lesser General Public License, version 2.1.
  Copyright for Logback 1.3.14 is held by QOS.ch. The software is dual-licensed under the GNU Lesser General
  Public License, version 2.1 and Eclipse Public License, version 1.0.


            		  GNU LESSER GENERAL PUBLIC LICENSE
            		       Version 2.1, February 1999

             Copyright (C) 1991, 1999 Free Software Foundation, Inc.
                 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
             Everyone is permitted to copy and distribute verbatim copies
             of this license document, but changing it is not allowed.

            [This is the first released version of the Lesser GPL.  It also counts
             as the successor of the GNU Library Public License, version 2, hence
             the version number 2.1.]

            			    Preamble

              The licenses for most software are designed to take away your
            freedom to share and change it.  By contrast, the GNU General Public
            Licenses are intended to guarantee your freedom to share and change
            free software--to make sure the software is free for all its users.

              This license, the Lesser General Public License, applies to some
            specially designated software packages--typically libraries--of the
            Free Software Foundation and other authors who decide to use it.  You
            can use it too, but we suggest you first think carefully about whether
            this license or the ordinary General Public License is the better
            strategy to use in any particular case, based on the explanations below.

              When we speak of free software, we are referring to freedom of use,
            not price.  Our General Public Licenses are designed to make sure that
            you have the freedom to distribute copies of free software (and charge
            for this service if you wish); that you receive source code or can get
            it if you want it; that you can change the software and use pieces of
            it in new free programs; and that you are informed that you can do
            these things.

              To protect your rights, we need to make restrictions that forbid
            distributors to deny you these rights or to ask you to surrender these
            rights.  These restrictions translate to certain responsibilities for
            you if you distribute copies of the library or if you modify it.

              For example, if you distribute copies of the library, whether gratis
            or for a fee, you must give the recipients all the rights that we gave
            you.  You must make sure that they, too, receive or can get the source
            code.  If you link other code with the library, you must provide
            complete object files to the recipients, so that they can relink them
            with the library after making changes to the library and recompiling
            it.  And you must show them these terms so they know their rights.

              We protect your rights with a two-step method: (1) we copyright the
            library, and (2) we offer you this license, which gives you legal
            permission to copy, distribute and/or modify the library.

              To protect each distributor, we want to make it very clear that
            there is no warranty for the free library.  Also, if the library is
            modified by someone else and passed on, the recipients should know
            that what they have is not the original version, so that the original
            author's reputation will not be affected by problems that might be
            introduced by others.

              Finally, software patents pose a constant threat to the existence of
            any free program.  We wish to make sure that a company cannot
            effectively restrict the users of a free program by obtaining a
            restrictive license from a patent holder.  Therefore, we insist that
            any patent license obtained for a version of the library must be
            consistent with the full freedom of use specified in this license.

              Most GNU software, including some libraries, is covered by the
            ordinary GNU General Public License.  This license, the GNU Lesser
            General Public License, applies to certain designated libraries, and
            is quite different from the ordinary General Public License.  We use
            this license for certain libraries in order to permit linking those
            libraries into non-free programs.

              When a program is linked with a library, whether statically or using
            a shared library, the combination of the two is legally speaking a
            combined work, a derivative of the original library.  The ordinary
            General Public License therefore permits such linking only if the
            entire combination fits its criteria of freedom.  The Lesser General
            Public License permits more lax criteria for linking other code with
            the library.

              We call this license the "Lesser" General Public License because it
            does Less to protect the user's freedom than the ordinary General
            Public License.  It also provides other free software developers Less
            of an advantage over competing non-free programs.  These disadvantages
            are the reason we use the ordinary General Public License for many
            libraries.  However, the Lesser license provides advantages in certain
            special circumstances.

              For example, on rare occasions, there may be a special need to
            encourage the widest possible use of a certain library, so that it becomes
            a de-facto standard.  To achieve this, non-free programs must be
            allowed to use the library.  A more frequent case is that a free
            library does the same job as widely used non-free libraries.  In this
            case, there is little to gain by limiting the free library to free
            software only, so we use the Lesser General Public License.

              In other cases, permission to use a particular library in non-free
            programs enables a greater number of people to use a large body of
            free software.  For example, permission to use the GNU C Library in
            non-free programs enables many more people to use the whole GNU
            operating system, as well as its variant, the GNU/Linux operating
            system.

              Although the Lesser General Public License is Less protective of the
            users' freedom, it does ensure that the user of a program that is
            linked with the Library has the freedom and the wherewithal to run
            that program using a modified version of the Library.

              The precise terms and conditions for copying, distribution and
            modification follow.  Pay close attention to the difference between a
            "work based on the library" and a "work that uses the library".  The
            former contains code derived from the library, whereas the latter must
            be combined with the library in order to run.

            		  GNU LESSER GENERAL PUBLIC LICENSE
               TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION

              0. This License Agreement applies to any software library or other
            program which contains a notice placed by the copyright holder or
            other authorized party saying it may be distributed under the terms of
            this Lesser General Public License (also called "this License").
            Each licensee is addressed as "you".

              A "library" means a collection of software functions and/or data
            prepared so as to be conveniently linked with application programs
            (which use some of those functions and data) to form executables.

              The "Library", below, refers to any such software library or work
            which has been distributed under these terms.  A "work based on the
            Library" means either the Library or any derivative work under
            copyright law: that is to say, a work containing the Library or a
            portion of it, either verbatim or with modifications and/or translated
            straightforwardly into another language.  (Hereinafter, translation is
            included without limitation in the term "modification".)

              "Source code" for a work means the preferred form of the work for
            making modifications to it.  For a library, complete source code means
            all the source code for all modules it contains, plus any associated
            interface definition files, plus the scripts used to control compilation
            and installation of the library.

              Activities other than copying, distribution and modification are not
            covered by this License; they are outside its scope.  The act of
            running a program using the Library is not restricted, and output from
            such a program is covered only if its contents constitute a work based
            on the Library (independent of the use of the Library in a tool for
            writing it).  Whether that is true depends on what the Library does
            and what the program that uses the Library does.

              1. You may copy and distribute verbatim copies of the Library's
            complete source code as you receive it, in any medium, provided that
            you conspicuously and appropriately publish on each copy an
            appropriate copyright notice and disclaimer of warranty; keep intact
            all the notices that refer to this License and to the absence of any
            warranty; and distribute a copy of this License along with the
            Library.

              You may charge a fee for the physical act of transferring a copy,
            and you may at your option offer warranty protection in exchange for a
            fee.

              2. You may modify your copy or copies of the Library or any portion
            of it, thus forming a work based on the Library, and copy and
            distribute such modifications or work under the terms of Section 1
            above, provided that you also meet all of these conditions:

                a) The modified work must itself be a software library.

                b) You must cause the files modified to carry prominent notices
                stating that you changed the files and the date of any change.

                c) You must cause the whole of the work to be licensed at no
                charge to all third parties under the terms of this License.

                d) If a facility in the modified Library refers to a function or a
                table of data to be supplied by an application program that uses
                the facility, other than as an argument passed when the facility
                is invoked, then you must make a good faith effort to ensure that,
                in the event an application does not supply such function or
                table, the facility still operates, and performs whatever part of
                its purpose remains meaningful.

                (For example, a function in a library to compute square roots has
                a purpose that is entirely well-defined independent of the
                application.  Therefore, Subsection 2d requires that any
                application-supplied function or table used by this function must
                be optional: if the application does not supply it, the square
                root function must still compute square roots.)

            These requirements apply to the modified work as a whole.  If
            identifiable sections of that work are not derived from the Library,
            and can be reasonably considered independent and separate works in
            themselves, then this License, and its terms, do not apply to those
            sections when you distribute them as separate works.  But when you
            distribute the same sections as part of a whole which is a work based
            on the Library, the distribution of the whole must be on the terms of
            this License, whose permissions for other licensees extend to the
            entire whole, and thus to each and every part regardless of who wrote
            it.

            Thus, it is not the intent of this section to claim rights or contest
            your rights to work written entirely by you; rather, the intent is to
            exercise the right to control the distribution of derivative or
            collective works based on the Library.

            In addition, mere aggregation of another work not based on the Library
            with the Library (or with a work based on the Library) on a volume of
            a storage or distribution medium does not bring the other work under
            the scope of this License.

              3. You may opt to apply the terms of the ordinary GNU General Public
            License instead of this License to a given copy of the Library.  To do
            this, you must alter all the notices that refer to this License, so
            that they refer to the ordinary GNU General Public License, version 2,
            instead of to this License.  (If a newer version than version 2 of the
            ordinary GNU General Public License has appeared, then you can specify
            that version instead if you wish.)  Do not make any other change in
            these notices.

              Once this change is made in a given copy, it is irreversible for
            that copy, so the ordinary GNU General Public License applies to all
            subsequent copies and derivative works made from that copy.

              This option is useful when you wish to copy part of the code of
            the Library into a program that is not a library.

              4. You may copy and distribute the Library (or a portion or
            derivative of it, under Section 2) in object code or executable form
            under the terms of Sections 1 and 2 above provided that you accompany
            it with the complete corresponding machine-readable source code, which
            must be distributed under the terms of Sections 1 and 2 above on a
            medium customarily used for software interchange.

              If distribution of object code is made by offering access to copy
            from a designated place, then offering equivalent access to copy the
            source code from the same place satisfies the requirement to
            distribute the source code, even though third parties are not
            compelled to copy the source along with the object code.

              5. A program that contains no derivative of any portion of the
            Library, but is designed to work with the Library by being compiled or
            linked with it, is called a "work that uses the Library".  Such a
            work, in isolation, is not a derivative work of the Library, and
            therefore falls outside the scope of this License.

              However, linking a "work that uses the Library" with the Library
            creates an executable that is a derivative of the Library (because it
            contains portions of the Library), rather than a "work that uses the
            library".  The executable is therefore covered by this License.
            Section 6 states terms for distribution of such executables.

              When a "work that uses the Library" uses material from a header file
            that is part of the Library, the object code for the work may be a
            derivative work of the Library even though the source code is not.
            Whether this is true is especially significant if the work can be
            linked without the Library, or if the work is itself a library.  The
            threshold for this to be true is not precisely defined by law.

              If such an object file uses only numerical parameters, data
            structure layouts and accessors, and small macros and small inline
            functions (ten lines or less in length), then the use of the object
            file is unrestricted, regardless of whether it is legally a derivative
            work.  (Executables containing this object code plus portions of the
            Library will still fall under Section 6.)

              Otherwise, if the work is a derivative of the Library, you may
            distribute the object code for the work under the terms of Section 6.
            Any executables containing that work also fall under Section 6,
            whether or not they are linked directly with the Library itself.

              6. As an exception to the Sections above, you may also combine or
            link a "work that uses the Library" with the Library to produce a
            work containing portions of the Library, and distribute that work
            under terms of your choice, provided that the terms permit
            modification of the work for the customer's own use and reverse
            engineering for debugging such modifications.

              You must give prominent notice with each copy of the work that the
            Library is used in it and that the Library and its use are covered by
            this License.  You must supply a copy of this License.  If the work
            during execution displays copyright notices, you must include the
            copyright notice for the Library among them, as well as a reference
            directing the user to the copy of this License.  Also, you must do one
            of these things:

                a) Accompany the work with the complete corresponding
                machine-readable source code for the Library including whatever
                changes were used in the work (which must be distributed under
                Sections 1 and 2 above); and, if the work is an executable linked
                with the Library, with the complete machine-readable "work that
                uses the Library", as object code and/or source code, so that the
                user can modify the Library and then relink to produce a modified
                executable containing the modified Library.  (It is understood
                that the user who changes the contents of definitions files in the
                Library will not necessarily be able to recompile the application
                to use the modified definitions.)

                b) Use a suitable shared library mechanism for linking with the
                Library.  A suitable mechanism is one that (1) uses at run time a
                copy of the library already present on the user's computer system,
                rather than copying library functions into the executable, and (2)
                will operate properly with a modified version of the library, if
                the user installs one, as long as the modified version is
                interface-compatible with the version that the work was made with.

                c) Accompany the work with a written offer, valid for at
                least three years, to give the same user the materials
                specified in Subsection 6a, above, for a charge no more
                than the cost of performing this distribution.

                d) If distribution of the work is made by offering access to copy
                from a designated place, offer equivalent access to copy the above
                specified materials from the same place.

                e) Verify that the user has already received a copy of these
                materials or that you have already sent this user a copy.

              For an executable, the required form of the "work that uses the
            Library" must include any data and utility programs needed for
            reproducing the executable from it.  However, as a special exception,
            the materials to be distributed need not include anything that is
            normally distributed (in either source or binary form) with the major
            components (compiler, kernel, and so on) of the operating system on
            which the executable runs, unless that component itself accompanies
            the executable.

              It may happen that this requirement contradicts the license
            restrictions of other proprietary libraries that do not normally
            accompany the operating system.  Such a contradiction means you cannot
            use both them and the Library together in an executable that you
            distribute.

              7. You may place library facilities that are a work based on the
            Library side-by-side in a single library together with other library
            facilities not covered by this License, and distribute such a combined
            library, provided that the separate distribution of the work based on
            the Library and of the other library facilities is otherwise
            permitted, and provided that you do these two things:

                a) Accompany the combined library with a copy of the same work
                based on the Library, uncombined with any other library
                facilities.  This must be distributed under the terms of the
                Sections above.

                b) Give prominent notice with the combined library of the fact
                that part of it is a work based on the Library, and explaining
                where to find the accompanying uncombined form of the same work.

              8. You may not copy, modify, sublicense, link with, or distribute
            the Library except as expressly provided under this License.  Any
            attempt otherwise to copy, modify, sublicense, link with, or
            distribute the Library is void, and will automatically terminate your
            rights under this License.  However, parties who have received copies,
            or rights, from you under this License will not have their licenses
            terminated so long as such parties remain in full compliance.

              9. You are not required to accept this License, since you have not
            signed it.  However, nothing else grants you permission to modify or
            distribute the Library or its derivative works.  These actions are
            prohibited by law if you do not accept this License.  Therefore, by
            modifying or distributing the Library (or any work based on the
            Library), you indicate your acceptance of this License to do so, and
            all its terms and conditions for copying, distributing or modifying
            the Library or works based on it.

              10. Each time you redistribute the Library (or any work based on the
            Library), the recipient automatically receives a license from the
            original licensor to copy, distribute, link with or modify the Library
            subject to these terms and conditions.  You may not impose any further
            restrictions on the recipients' exercise of the rights granted herein.
            You are not responsible for enforcing compliance by third parties with
            this License.

              11. If, as a consequence of a court judgment or allegation of patent
            infringement or for any other reason (not limited to patent issues),
            conditions are imposed on you (whether by court order, agreement or
            otherwise) that contradict the conditions of this License, they do not
            excuse you from the conditions of this License.  If you cannot
            distribute so as to satisfy simultaneously your obligations under this
            License and any other pertinent obligations, then as a consequence you
            may not distribute the Library at all.  For example, if a patent
            license would not permit royalty-free redistribution of the Library by
            all those who receive copies directly or indirectly through you, then
            the only way you could satisfy both it and this License would be to
            refrain entirely from distribution of the Library.

            If any portion of this section is held invalid or unenforceable under any
            particular circumstance, the balance of the section is intended to apply,
            and the section as a whole is intended to apply in other circumstances.

            It is not the purpose of this section to induce you to infringe any
            patents or other property right claims or to contest validity of any
            such claims; this section has the sole purpose of protecting the
            integrity of the free software distribution system which is
            implemented by public license practices.  Many people have made
            generous contributions to the wide range of software distributed
            through that system in reliance on consistent application of that
            system; it is up to the author/donor to decide if he or she is willing
            to distribute software through any other system and a licensee cannot
            impose that choice.

            This section is intended to make thoroughly clear what is believed to
            be a consequence of the rest of this License.

              12. If the distribution and/or use of the Library is restricted in
            certain countries either by patents or by copyrighted interfaces, the
            original copyright holder who places the Library under this License may add
            an explicit geographical distribution limitation excluding those countries,
            so that distribution is permitted only in or among countries not thus
            excluded.  In such case, this License incorporates the limitation as if
            written in the body of this License.

              13. The Free Software Foundation may publish revised and/or new
            versions of the Lesser General Public License from time to time.
            Such new versions will be similar in spirit to the present version,
            but may differ in detail to address new problems or concerns.

            Each version is given a distinguishing version number.  If the Library
            specifies a version number of this License which applies to it and
            "any later version", you have the option of following the terms and
            conditions either of that version or of any later version published by
            the Free Software Foundation.  If the Library does not specify a
            license version number, you may choose any version ever published by
            the Free Software Foundation.

              14. If you wish to incorporate parts of the Library into other free
            programs whose distribution conditions are incompatible with these,
            write to the author to ask for permission.  For software which is
            copyrighted by the Free Software Foundation, write to the Free
            Software Foundation; we sometimes make exceptions for this.  Our
            decision will be guided by the two goals of preserving the free status
            of all derivatives of our free software and of promoting the sharing
            and reuse of software generally.

            			    NO WARRANTY

              15. BECAUSE THE LIBRARY IS LICENSED FREE OF CHARGE, THERE IS NO
            WARRANTY FOR THE LIBRARY, TO THE EXTENT PERMITTED BY APPLICABLE LAW.
            EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT HOLDERS AND/OR
            OTHER PARTIES PROVIDE THE LIBRARY "AS IS" WITHOUT WARRANTY OF ANY
            KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, THE
            IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
            PURPOSE.  THE ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF THE
            LIBRARY IS WITH YOU.  SHOULD THE LIBRARY PROVE DEFECTIVE, YOU ASSUME
            THE COST OF ALL NECESSARY SERVICING, REPAIR OR CORRECTION.

              16. IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN
            WRITING WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIFY
            AND/OR REDISTRIBUTE THE LIBRARY AS PERMITTED ABOVE, BE LIABLE TO YOU
            FOR DAMAGES, INCLUDING ANY GENERAL, SPECIAL, INCIDENTAL OR
            CONSEQUENTIAL DAMAGES ARISING OUT OF THE USE OR INABILITY TO USE THE
            LIBRARY (INCLUDING BUT NOT LIMITED TO LOSS OF DATA OR DATA BEING
            RENDERED INACCURATE OR LOSSES SUSTAINED BY YOU OR THIRD PARTIES OR A
            FAILURE OF THE LIBRARY TO OPERATE WITH ANY OTHER SOFTWARE), EVEN IF
            SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH
            DAMAGES.

            		     END OF TERMS AND CONDITIONS

            	   How to Apply These Terms to Your New Libraries

              If you develop a new library, and you want it to be of the greatest
            possible use to the public, we recommend making it free software that
            everyone can redistribute and change.  You can do so by permitting
            redistribution under these terms (or, alternatively, under the terms of the
            ordinary General Public License).

              To apply these terms, attach the following notices to the library.  It is
            safest to attach them to the start of each source file to most effectively
            convey the exclusion of warranty; and each file should have at least the
            "copyright" line and a pointer to where the full notice is found.


            Also add information on how to contact you by electronic and paper mail.

            You should also get your employer (if you work as a programmer) or your
            school, if any, to sign a "copyright disclaimer" for the library, if
            necessary.  Here is a sample; alter the names:




* Logback 1.3.14 and Jetty 9.4.49.v20220914 are also licensed under the Eclipse Public License, version 1.0.
  Copyright for SWT Designer is held by Google, Inc. JRebel embeds a derivative works based on it. Sources for it are made available via Eclipse update-site.zip distribution.
  They are all licensed under Eclipse Public License 1.0.


        Eclipse Public License -v 1.0

        THE ACCOMPANYING PROGRAM IS PROVIDED UNDER THE TERMS OF THIS ECLIPSE PUBLIC
        LICENSE ("AGREEMENT"). ANY USE, REPRODUCTION OR DISTRIBUTION OF THE PROGRAM
        CONSTITUTES RECIPIENT'S ACCEPTANCE OF THIS AGREEMENT.

        1. DEFINITIONS

        "Contribution" means:

        a) in the case of the initial Contributor, the initial code and documentation
        distributed under this Agreement, and

        b) in the case of each subsequent Contributor:

        i) changes to the Program, and

        ii) additions to the Program;

        where such changes and/or additions to the Program originate from and are
        distributed by that particular Contributor. A Contribution 'originates' from a
        Contributor if it was added to the Program by such Contributor itself or anyone
        acting on such Contributor's behalf. Contributions do not include additions to
        the Program which: (i) are separate modules of software distributed in
        conjunction with the Program under their own license agreement, and (ii) are
        not derivative works of the Program.

        "Contributor" means any person or entity that distributes the Program.

        "Licensed Patents " mean patent claims licensable by a Contributor which are
        necessarily infringed by the use or sale of its Contribution alone or when
        combined with the Program.

        "Program" means the Contributions distributed in accordance with this
        Agreement.

        "Recipient" means anyone who receives the Program under this Agreement,
        including all Contributors.

        2. GRANT OF RIGHTS

        a) Subject to the terms of this Agreement, each Contributor hereby grants
        Recipient a non-exclusive, worldwide, royalty-free copyright license to
        reproduce, prepare derivative works of, publicly display, publicly perform,
        distribute and sublicense the Contribution of such Contributor, if any, and
        such derivative works, in source code and object code form.

        b) Subject to the terms of this Agreement, each Contributor hereby grants
        Recipient a non-exclusive, worldwide, royalty-free patent license under
        Licensed Patents to make, use, sell, offer to sell, import and otherwise
        transfer the Contribution of such Contributor, if any, in source code and
        object code form. This patent license shall apply to the combination of the
        Contribution and the Program if, at the time the Contribution is added by the
        Contributor, such addition of the Contribution causes such combination to be
        covered by the Licensed Patents. The patent license shall not apply to any
        other combinations which include the Contribution. No hardware per se is
        licensed hereunder.

        c) Recipient understands that although each Contributor grants the licenses to
        its Contributions set forth herein, no assurances are provided by any
        Contributor that the Program does not infringe the patent or other intellectual
        property rights of any other entity. Each Contributor disclaims any liability
        to Recipient for claims brought by any other entity based on infringement of
        intellectual property rights or otherwise. As a condition to exercising the
        rights and licenses granted hereunder, each Recipient hereby assumes sole
        responsibility to secure any other intellectual property rights needed, if any.
        For example, if a third party patent license is required to allow Recipient to
        distribute the Program, it is Recipient's responsibility to acquire that
        license before distributing the Program.

        d) Each Contributor represents that to its knowledge it has sufficient
        copyright rights in its Contribution, if any, to grant the copyright license
        set forth in this Agreement.

        3. REQUIREMENTS

        A Contributor may choose to distribute the Program in object code form under
        its own license agreement, provided that:

        a) it complies with the terms and conditions of this Agreement; and

        b) its license agreement:

        i) effectively disclaims on behalf of all Contributors all warranties and
        conditions, express and implied, including warranties or conditions of title
        and non-infringement, and implied warranties or conditions of merchantability
        and fitness for a particular purpose;

        ii) effectively excludes on behalf of all Contributors all liability for
        damages, including direct, indirect, special, incidental and consequential
        damages, such as lost profits;

        iii) states that any provisions which differ from this Agreement are offered by
        that Contributor alone and not by any other party; and

        iv) states that source code for the Program is available from such Contributor,
        and informs licensees how to obtain it in a reasonable manner on or through a
        medium customarily used for software exchange.

        When the Program is made available in source code form:

        a) it must be made available under this Agreement; and

        b) a copy of this Agreement must be included with each copy of the Program.

        Contributors may not remove or alter any copyright notices contained within the
        Program.

        Each Contributor must identify itself as the originator of its Contribution, if
        any, in a manner that reasonably allows subsequent Recipients to identify the
        originator of the Contribution.

        4. COMMERCIAL DISTRIBUTION

        Commercial distributors of software may accept certain responsibilities with
        respect to end users, business partners and the like. While this license is
        intended to facilitate the commercial use of the Program, the Contributor who
        includes the Program in a commercial product offering should do so in a manner
        which does not create potential liability for other Contributors. Therefore, if
        a Contributor includes the Program in a commercial product offering, such
        Contributor ("Commercial Contributor") hereby agrees to defend and indemnify
        every other Contributor ("Indemnified Contributor") against any losses, damages
        and costs (collectively "Losses") arising from claims, lawsuits and other legal
        actions brought by a third party against the Indemnified Contributor to the
        extent caused by the acts or omissions of such Commercial Contributor in
        connection with its distribution of the Program in a commercial product
        offering. The obligations in this section do not apply to any claims or Losses
        relating to any actual or alleged intellectual property infringement. In order
        to qualify, an Indemnified Contributor must: a) promptly notify the Commercial
        Contributor in writing of such claim, and b) allow the Commercial Contributor
        to control, and cooperate with the Commercial Contributor in, the defense and
        any related settlement negotiations. The Indemnified Contributor may
        participate in any such claim at its own expense.

        For example, a Contributor might include the Program in a commercial product
        offering, Product X. That Contributor is then a Commercial Contributor. If that
        Commercial Contributor then makes performance claims, or offers warranties
        related to Product X, those performance claims and warranties are such
        Commercial Contributor's responsibility alone. Under this section, the
        Commercial Contributor would have to defend claims against the other
        Contributors related to those performance claims and warranties, and if a court
        requires any other Contributor to pay any damages as a result, the Commercial
        Contributor must pay those damages.

        5. NO WARRANTY

        EXCEPT AS EXPRESSLY SET FORTH IN THIS AGREEMENT, THE PROGRAM IS PROVIDED ON AN
        "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, EITHER EXPRESS OR
        IMPLIED INCLUDING, WITHOUT LIMITATION, ANY WARRANTIES OR CONDITIONS OF TITLE,
        NON-INFRINGEMENT, MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE. Each
        Recipient is solely responsible for determining the appropriateness of using
        and distributing the Program and assumes all risks associated with its exercise
        of rights under this Agreement , including but not limited to the risks and
        costs of program errors, compliance with applicable laws, damage to or loss of
        data, programs or equipment, and unavailability or interruption of operations.

        6. DISCLAIMER OF LIABILITY

        EXCEPT AS EXPRESSLY SET FORTH IN THIS AGREEMENT, NEITHER RECIPIENT NOR ANY
        CONTRIBUTORS SHALL HAVE ANY LIABILITY FOR ANY DIRECT, INDIRECT, INCIDENTAL,
        SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING WITHOUT LIMITATION LOST
        PROFITS), HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
        STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY
        WAY OUT OF THE USE OR DISTRIBUTION OF THE PROGRAM OR THE EXERCISE OF ANY RIGHTS
        GRANTED HEREUNDER, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.

        7. GENERAL

        If any provision of this Agreement is invalid or unenforceable under applicable
        law, it shall not affect the validity or enforceability of the remainder of the
        terms of this Agreement, and without further action by the parties hereto, such
        provision shall be reformed to the minimum extent necessary to make such
        provision valid and enforceable.

        If Recipient institutes patent litigation against any entity (including a
        cross-claim or counterclaim in a lawsuit) alleging that the Program itself
        (excluding combinations of the Program with other software or hardware)
        infringes such Recipient's patent(s), then such Recipient's rights granted
        under Section 2(b) shall terminate as of the date such litigation is filed.

        All Recipient's rights under this Agreement shall terminate if it fails to
        comply with any of the material terms or conditions of this Agreement and does
        not cure such failure in a reasonable period of time after becoming aware of
        such noncompliance. If all Recipient's rights under this Agreement terminate,
        Recipient agrees to cease use and distribution of the Program as soon as
        reasonably practicable. However, Recipient's obligations under this Agreement
        and any licenses granted by Recipient relating to the Program shall continue
        and survive.

        Everyone is permitted to copy and distribute copies of this Agreement, but in
        order to avoid inconsistency the Agreement is copyrighted and may only be
        modified in the following manner. The Agreement Steward reserves the right to
        publish new versions (including revisions) of this Agreement from time to time.
        No one other than the Agreement Steward has the right to modify this Agreement.
        The Eclipse Foundation is the initial Agreement Steward. The Eclipse Foundation
        may assign the responsibility to serve as the Agreement Steward to a suitable
        separate entity. Each new version of the Agreement will be given a
        distinguishing version number. The Program (including Contributions) may always
        be distributed subject to the version of the Agreement under which it was
        received. In addition, after a new version of the Agreement is published,
        Contributor may elect to distribute the Program (including its Contributions)
        under the new version. Except as expressly stated in Sections 2(a) and 2(b)
        above, Recipient receives no rights or licenses to the intellectual property of
        any Contributor under this Agreement, whether expressly, by implication,
        estoppel or otherwise. All rights in the Program not expressly granted under
        this Agreement are reserved.

        This Agreement is governed by the laws of the State of New York and the
        intellectual property laws of the United States of America. No party to this
        Agreement will bring a legal action under this Agreement more than one year
        after the cause of action arose. Each party waives its rights to a jury trial
        in any resulting litigation.




* Copyright for ASM (9.7.1) is held by INRIA, France Telecom. Software are licensed under the 3-clause BSD license:


         Redistribution and use in source and binary forms, with or without
         modification, are permitted provided that the following conditions are
         met:

          * Redistributions of source code must retain the above copyright
            notice, this list of conditions and the following disclaimer.

          * Redistributions in binary form must reproduce the above copyright
            notice, this list of conditions and the following disclaimer in the
            documentation and/or other materials provided with the distribution.

          * Neither the name of the <ORGANIZATION> nor the names of its
            contributors may be used to endorse or promote products derived
            from this software without specific prior written permission.

        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
        IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
        TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
        PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER
        OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
        EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
        PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
        PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
        LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
        NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
        SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.




* MurmurHash3 java port was originally created Yonik Seeley.
  The software is released as public domain software, giving up any copyrights whatsoever. Software is distributed
  with the following notes:


            The MurmurHash3 algorithm was created by Austin Appleby and placed in the public domain.
            his java port was authored by Yonik Seeley and also placed into the public domain.
            he author hereby disclaims copyright to this source code.




* Copyright for NanoXml Lite 2.2.3 is held by Marc De Scheemaecke.
  The software is licensed under the Zlib/Libpng License:


            Open Source Initiative OSI - The zlib/libpng License (Zlib):Licensing
            [OSI Approved License]
            The zlib/libpng License

            Copyright (c) <year> <copyright holders>

            This software is provided 'as-is', without any express or implied warranty. In no event will the authors be held liable for any damages arising from the use of this software.

            Permission is granted to anyone to use this software for any purpose, including commercial applications, and to alter it and redistribute it freely, subject to the following restrictions:

                1. The origin of this software must not be misrepresented; you must not claim that you wrote the original software. If you use this software in a product, an acknowledgment in the product documentation would be appreciated but is not required.

                2. Altered source versions must be plainly marked as such, and must not be misrepresented as being the original software.

                3. This notice may not be removed or altered from any source distribution.




* Copyright for Bouncy Castle 1.76 is held by The Legion Of The Bouncy Castle Inc.
  Copyright for Analytics Java 0.2.0 is held by Segment, Inc.
  Copyright for SLF4J API Module 2.0.7, SLF4J NOP Binding 2.0.7, SLF4J Simple Binding 2.0.7,
  JCL 1.2 Implemented Over SLF4J 2.0.7 is held by QOS.ch.
  Copyright for Bugsnag Java notifier 3.7.0 is held by Bugsnag Inc.
  Copyright for clipboard.js 2.0.8 is held by Zeno Rocha.
  Copyright for jQuery JavaScript Library v3.6.0 is held by OpenJS Foundation and other contributors, https://openjsf.org/
  Copyright for compare-versions 4.1.3 is held by Ole Michelsen.
  Copyright for lodash 4.17.21 is held by JS Foundation and other contributors <https://js.foundation/>.
  Copyright for rsp-client 0.25.0 is held by Red Hat, Inc.
  Copyright for uuid 8.3.2 is held by Robert Kieffer and other contributors.
  Copyright for vscode-server-connector-api 0.1.7 is held by Red Hat, Inc.
  Copyright for whatwg-url 8.2.1 is held by Sebastian Mayr.
  Copyright for jsoup 1.16.1 is held by Jonathan Hedley (https://jsoup.org/).
  Copyright for electron-log 4.4.8 is held by Alexey Prokhorov.
  They are all licensed under the MIT X11 License:


            Permission is hereby granted, free of charge, to any person obtaining a copy of this
            software and associated documentation files (the "Software"), to deal in the Software
            without restriction, including without limitation the rights to use, copy, modify,
            merge, publish, distribute, sublicense, and/or sell copies of the Software, and to
            permit persons to whom the Software is furnished to do so, subject to the following
            conditions:

            The above copyright notice and this permission notice shall be included in all copies
            or substantial portions of the Software.

            THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,
            INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
            PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
            HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
            CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE
            OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.







* Copyright for Javassist 3.29.2.GA is held by Shigeru Chiba (Tokyo Institute of Technology). The software is triple-licensed under
   Mozilla Public License, version 1.1; Apache License 2.0; and the LGPL 2.1 license.


            MOZILLA PUBLIC LICENSE
            Version 1.1

            1. Definitions.

                  1.0.1. "Commercial Use" means distribution or otherwise making the Covered Code available to a third party.

                  1.1. ''Contributor'' means each entity that creates or contributes to the creation of Modifications.

                  1.2. ''Contributor Version'' means the combination of the Original Code, prior Modifications used by a Contributor, and the Modifications made by that particular Contributor.

                  1.3. ''Covered Code'' means the Original Code or Modifications or the combination of the Original Code and Modifications, in each case including portions thereof.

                  1.4. ''Electronic Distribution Mechanism'' means a mechanism generally accepted in the software development community for the electronic transfer of data.

                  1.5. ''Executable'' means Covered Code in any form other than Source Code.

                  1.6. ''Initial Developer'' means the individual or entity identified as the Initial Developer in the Source Code notice required by Exhibit A.

                  1.7. ''Larger Work'' means a work which combines Covered Code or portions thereof with code not governed by the terms of this License.

                  1.8. ''License'' means this document.

                  1.8.1. "Licensable" means having the right to grant, to the maximum extent possible, whether at the time of the initial grant or subsequently acquired, any and all of the rights conveyed herein.

                  1.9. ''Modifications'' means any addition to or deletion from the substance or structure of either the Original Code or any previous Modifications. When Covered Code is released as a series of files, a Modification is:
            	    A. Any addition to or deletion from the contents of a file containing Original Code or previous Modifications.

            	    B. Any new file that contains any part of the Original Code or previous Modifications.

                  1.10. ''Original Code'' means Source Code of computer software code which is described in the Source Code notice required by Exhibit A as Original Code, and which, at the time of its release under this License is not already Covered Code governed by this License.

                  1.10.1. "Patent Claims" means any patent claim(s), now owned or hereafter acquired, including without limitation,  method, process, and apparatus claims, in any patent Licensable by grantor.

                  1.11. ''Source Code'' means the preferred form of the Covered Code for making modifications to it, including all modules it contains, plus any associated interface definition files, scripts used to control compilation and installation of an Executable, or source code differential comparisons against either the Original Code or another well known, available Covered Code of the Contributor's choice. The Source Code can be in a compressed or archival form, provided the appropriate decompression or de-archiving software is widely available for no charge.

                  1.12. "You'' (or "Your")  means an individual or a legal entity exercising rights under, and complying with all of the terms of, this License or a future version of this License issued under Section 6.1. For legal entities, "You'' includes any entity which controls, is controlled by, or is under common control with You. For purposes of this definition, "control'' means (a) the power, direct or indirect, to cause the direction or management of such entity, whether by contract or otherwise, or (b) ownership of more than fifty percent (50%) of the outstanding shares or beneficial ownership of such entity.

            2. Source Code License.

                  2.1. The Initial Developer Grant.
                  The Initial Developer hereby grants You a world-wide, royalty-free, non-exclusive license, subject to third party intellectual property claims:
            	    (a)  under intellectual property rights (other than patent or trademark) Licensable by Initial Developer to use, reproduce, modify, display, perform, sublicense and distribute the Original Code (or portions thereof) with or without Modifications, and/or as part of a Larger Work; and

            	    (b) under Patents Claims infringed by the making, using or selling of Original Code, to make, have made, use, practice, sell, and offer for sale, and/or otherwise dispose of the Original Code (or portions thereof).
            	    (c) the licenses granted in this Section 2.1(a) and (b) are effective on the date Initial Developer first distributes Original Code under the terms of this License.

            	    (d) Notwithstanding Section 2.1(b) above, no patent license is granted: 1) for code that You delete from the Original Code; 2) separate from the Original Code;  or 3) for infringements caused by: i) the modification of the Original Code or ii) the combination of the Original Code with other software or devices.

                  2.2. Contributor Grant.
                  Subject to third party intellectual property claims, each Contributor hereby grants You a world-wide, royalty-free, non-exclusive license

            	    (a)  under intellectual property rights (other than patent or trademark) Licensable by Contributor, to use, reproduce, modify, display, perform, sublicense and distribute the Modifications created by such Contributor (or portions thereof) either on an unmodified basis, with other Modifications, as Covered Code and/or as part of a Larger Work; and

            	    (b) under Patent Claims infringed by the making, using, or selling of  Modifications made by that Contributor either alone and/or in combination with its Contributor Version (or portions of such combination), to make, use, sell, offer for sale, have made, and/or otherwise dispose of: 1) Modifications made by that Contributor (or portions thereof); and 2) the combination of  Modifications made by that Contributor with its Contributor Version (or portions of such combination).

            	    (c) the licenses granted in Sections 2.2(a) and 2.2(b) are effective on the date Contributor first makes Commercial Use of the Covered Code.

            	    (d)    Notwithstanding Section 2.2(b) above, no patent license is granted: 1) for any code that Contributor has deleted from the Contributor Version; 2)  separate from the Contributor Version;  3)  for infringements caused by: i) third party modifications of Contributor Version or ii)  the combination of Modifications made by that Contributor with other software  (except as part of the Contributor Version) or other devices; or 4) under Patent Claims infringed by Covered Code in the absence of Modifications made by that Contributor.


            3. Distribution Obligations.

                  3.1. Application of License.
                  The Modifications which You create or to which You contribute are governed by the terms of this License, including without limitation Section 2.2. The Source Code version of Covered Code may be distributed only under the terms of this License or a future version of this License released under Section 6.1, and You must include a copy of this License with every copy of the Source Code You distribute. You may not offer or impose any terms on any Source Code version that alters or restricts the applicable version of this License or the recipients' rights hereunder. However, You may include an additional document offering the additional rights described in Section 3.5.

                  3.2. Availability of Source Code.
                  Any Modification which You create or to which You contribute must be made available in Source Code form under the terms of this License either on the same media as an Executable version or via an accepted Electronic Distribution Mechanism to anyone to whom you made an Executable version available; and if made available via Electronic Distribution Mechanism, must remain available for at least twelve (12) months after the date it initially became available, or at least six (6) months after a subsequent version of that particular Modification has been made available to such recipients. You are responsible for ensuring that the Source Code version remains available even if the Electronic Distribution Mechanism is maintained by a third party.

                  3.3. Description of Modifications.
                  You must cause all Covered Code to which You contribute to contain a file documenting the changes You made to create that Covered Code and the date of any change. You must include a prominent statement that the Modification is derived, directly or indirectly, from Original Code provided by the Initial Developer and including the name of the Initial Developer in (a) the Source Code, and (b) in any notice in an Executable version or related documentation in which You describe the origin or ownership of the Covered Code.

                  3.4. Intellectual Property Matters
            	    (a) Third Party Claims.
            	    If Contributor has knowledge that a license under a third party's intellectual property rights is required to exercise the rights granted by such Contributor under Sections 2.1 or 2.2, Contributor must include a text file with the Source Code distribution titled "LEGAL'' which describes the claim and the party making the claim in sufficient detail that a recipient will know whom to contact. If Contributor obtains such knowledge after the Modification is made available as described in Section 3.2, Contributor shall promptly modify the LEGAL file in all copies Contributor makes available thereafter and shall take other steps (such as notifying appropriate mailing lists or newsgroups) reasonably calculated to inform those who received the Covered Code that new knowledge has been obtained.

            	    (b) Contributor APIs.
            	    If Contributor's Modifications include an application programming interface and Contributor has knowledge of patent licenses which are reasonably necessary to implement that API, Contributor must also include this information in the LEGAL file.

            		(c)    Representations.
            	    Contributor represents that, except as disclosed pursuant to Section 3.4(a) above, Contributor believes that Contributor's Modifications are Contributor's original creation(s) and/or Contributor has sufficient rights to grant the rights conveyed by this License.


                  3.5. Required Notices.
                  You must duplicate the notice in Exhibit A in each file of the Source Code.  If it is not possible to put such notice in a particular Source Code file due to its structure, then You must include such notice in a location (such as a relevant directory) where a user would be likely to look for such a notice.  If You created one or more Modification(s) You may add your name as a Contributor to the notice described in Exhibit A.  You must also duplicate this License in any documentation for the Source Code where You describe recipients' rights or ownership rights relating to Covered Code.  You may choose to offer, and to charge a fee for, warranty, support, indemnity or liability obligations to one or more recipients of Covered Code. However, You may do so only on Your own behalf, and not on behalf of the Initial Developer or any Contributor. You must make it absolutely clear than any such warranty, support, indemnity or liability obligation is offered by You alone, and You hereby agree to indemnify the Initial Developer and every Contributor for any liability incurred by the Initial Developer or such Contributor as a result of warranty, support, indemnity or liability terms You offer.

                  3.6. Distribution of Executable Versions.
                  You may distribute Covered Code in Executable form only if the requirements of Section 3.1-3.5 have been met for that Covered Code, and if You include a notice stating that the Source Code version of the Covered Code is available under the terms of this License, including a description of how and where You have fulfilled the obligations of Section 3.2. The notice must be conspicuously included in any notice in an Executable version, related documentation or collateral in which You describe recipients' rights relating to the Covered Code. You may distribute the Executable version of Covered Code or ownership rights under a license of Your choice, which may contain terms different from this License, provided that You are in compliance with the terms of this License and that the license for the Executable version does not attempt to limit or alter the recipient's rights in the Source Code version from the rights set forth in this License. If You distribute the Executable version under a different license You must make it absolutely clear that any terms which differ from this License are offered by You alone, not by the Initial Developer or any Contributor. You hereby agree to indemnify the Initial Developer and every Contributor for any liability incurred by the Initial Developer or such Contributor as a result of any such terms You offer.

                  3.7. Larger Works.
                  You may create a Larger Work by combining Covered Code with other code not governed by the terms of this License and distribute the Larger Work as a single product. In such a case, You must make sure the requirements of this License are fulfilled for the Covered Code.

            4. Inability to Comply Due to Statute or Regulation.

                  If it is impossible for You to comply with any of the terms of this License with respect to some or all of the Covered Code due to statute, judicial order, or regulation then You must: (a) comply with the terms of this License to the maximum extent possible; and (b) describe the limitations and the code they affect. Such description must be included in the LEGAL file described in Section 3.4 and must be included with all distributions of the Source Code. Except to the extent prohibited by statute or regulation, such description must be sufficiently detailed for a recipient of ordinary skill to be able to understand it.

            5. Application of this License.

                  This License applies to code to which the Initial Developer has attached the notice in Exhibit A and to related Covered Code.

            6. Versions of the License.

                  6.1. New Versions.
                  Netscape Communications Corporation (''Netscape'') may publish revised and/or new versions of the License from time to time. Each version will be given a distinguishing version number.

                  6.2. Effect of New Versions.
                  Once Covered Code has been published under a particular version of the License, You may always continue to use it under the terms of that version. You may also choose to use such Covered Code under the terms of any subsequent version of the License published by Netscape. No one other than Netscape has the right to modify the terms applicable to Covered Code created under this License.

                  6.3. Derivative Works.
                  If You create or use a modified version of this License (which you may only do in order to apply it to code which is not already Covered Code governed by this License), You must (a) rename Your license so that the phrases ''Mozilla'', ''MOZILLAPL'', ''MOZPL'', ''Netscape'', "MPL", ''NPL'' or any confusingly similar phrase do not appear in your license (except to note that your license differs from this License) and (b) otherwise make it clear that Your version of the license contains terms which differ from the Mozilla Public License and Netscape Public License. (Filling in the name of the Initial Developer, Original Code or Contributor in the notice described in Exhibit A shall not of themselves be deemed to be modifications of this License.)

            7. DISCLAIMER OF WARRANTY.

                  COVERED CODE IS PROVIDED UNDER THIS LICENSE ON AN "AS IS'' BASIS, WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, WITHOUT LIMITATION, WARRANTIES THAT THE COVERED CODE IS FREE OF DEFECTS, MERCHANTABLE, FIT FOR A PARTICULAR PURPOSE OR NON-INFRINGING. THE ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF THE COVERED CODE IS WITH YOU. SHOULD ANY COVERED CODE PROVE DEFECTIVE IN ANY RESPECT, YOU (NOT THE INITIAL DEVELOPER OR ANY OTHER CONTRIBUTOR) ASSUME THE COST OF ANY NECESSARY SERVICING, REPAIR OR CORRECTION. THIS DISCLAIMER OF WARRANTY CONSTITUTES AN ESSENTIAL PART OF THIS LICENSE. NO USE OF ANY COVERED CODE IS AUTHORIZED HEREUNDER EXCEPT UNDER THIS DISCLAIMER.

            8. TERMINATION.

                  8.1.  This License and the rights granted hereunder will terminate automatically if You fail to comply with terms herein and fail to cure such breach within 30 days of becoming aware of the breach. All sublicenses to the Covered Code which are properly granted shall survive any termination of this License. Provisions which, by their nature, must remain in effect beyond the termination of this License shall survive.

                  8.2.  If You initiate litigation by asserting a patent infringement claim (excluding declatory judgment actions) against Initial Developer or a Contributor (the Initial Developer or Contributor against whom You file such action is referred to as "Participant")  alleging that:

                  (a)  such Participant's Contributor Version directly or indirectly infringes any patent, then any and all rights granted by such Participant to You under Sections 2.1 and/or 2.2 of this License shall, upon 60 days notice from Participant terminate prospectively, unless if within 60 days after receipt of notice You either: (i)  agree in writing to pay Participant a mutually agreeable reasonable royalty for Your past and future use of Modifications made by such Participant, or (ii) withdraw Your litigation claim with respect to the Contributor Version against such Participant.  If within 60 days of notice, a reasonable royalty and payment arrangement are not mutually agreed upon in writing by the parties or the litigation claim is not withdrawn, the rights granted by Participant to You under Sections 2.1 and/or 2.2 automatically terminate at the expiration of the 60 day notice period specified above.

                  (b)  any software, hardware, or device, other than such Participant's Contributor Version, directly or indirectly infringes any patent, then any rights granted to You by such Participant under Sections 2.1(b) and 2.2(b) are revoked effective as of the date You first made, used, sold, distributed, or had made, Modifications made by that Participant.

                  8.3.  If You assert a patent infringement claim against Participant alleging that such Participant's Contributor Version directly or indirectly infringes any patent where such claim is resolved (such as by license or settlement) prior to the initiation of patent infringement litigation, then the reasonable value of the licenses granted by such Participant under Sections 2.1 or 2.2 shall be taken into account in determining the amount or value of any payment or license.

                  8.4.  In the event of termination under Sections 8.1 or 8.2 above,  all end user license agreements (excluding distributors and resellers) which have been validly granted by You or any distributor hereunder prior to termination shall survive termination.

            9. LIMITATION OF LIABILITY.

                  UNDER NO CIRCUMSTANCES AND UNDER NO LEGAL THEORY, WHETHER TORT (INCLUDING NEGLIGENCE), CONTRACT, OR OTHERWISE, SHALL YOU, THE INITIAL DEVELOPER, ANY OTHER CONTRIBUTOR, OR ANY DISTRIBUTOR OF COVERED CODE, OR ANY SUPPLIER OF ANY OF SUCH PARTIES, BE LIABLE TO ANY PERSON FOR ANY INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES OF ANY CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF GOODWILL, WORK STOPPAGE, COMPUTER FAILURE OR MALFUNCTION, OR ANY AND ALL OTHER COMMERCIAL DAMAGES OR LOSSES, EVEN IF SUCH PARTY SHALL HAVE BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES. THIS LIMITATION OF LIABILITY SHALL NOT APPLY TO LIABILITY FOR DEATH OR PERSONAL INJURY RESULTING FROM SUCH PARTY'S NEGLIGENCE TO THE EXTENT APPLICABLE LAW PROHIBITS SUCH LIMITATION. SOME JURISDICTIONS DO NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR CONSEQUENTIAL DAMAGES, SO THIS EXCLUSION AND LIMITATION MAY NOT APPLY TO YOU.

            10. U.S. GOVERNMENT END USERS.

                  The Covered Code is a ''commercial item,'' as that term is defined in 48 C.F.R. 2.101 (Oct. 1995), consisting of ''commercial computer software'' and ''commercial computer software documentation,'' as such terms are used in 48 C.F.R. 12.212 (Sept. 1995). Consistent with 48 C.F.R. 12.212 and 48 C.F.R. 227.7202-1 through 227.7202-4 (June 1995), all U.S. Government End Users acquire Covered Code with only those rights set forth herein.

            11. MISCELLANEOUS.

                  This License represents the complete agreement concerning subject matter hereof. If any provision of this License is held to be unenforceable, such provision shall be reformed only to the extent necessary to make it enforceable. This License shall be governed by California law provisions (except to the extent applicable law, if any, provides otherwise), excluding its conflict-of-law provisions. With respect to disputes in which at least one party is a citizen of, or an entity chartered or registered to do business in the United States of America, any litigation relating to this License shall be subject to the jurisdiction of the Federal Courts of the Northern District of California, with venue lying in Santa Clara County, California, with the losing party responsible for costs, including without limitation, court costs and reasonable attorneys' fees and expenses. The application of the United Nations Convention on Contracts for the International Sale of Goods is expressly excluded. Any law or regulation which provides that the language of a contract shall be construed against the drafter shall not apply to this License.

            12. RESPONSIBILITY FOR CLAIMS.

                  As between Initial Developer and the Contributors, each party is responsible for claims and damages arising, directly or indirectly, out of its utilization of rights under this License and You agree to work with Initial Developer and Contributors to distribute such responsibility on an equitable basis. Nothing herein is intended or shall be deemed to constitute any admission of liability.

            13. MULTIPLE-LICENSED CODE.

                  Initial Developer may designate portions of the Covered Code as ï¿½Multiple-Licensed?.  ï¿½Multiple-Licensed? means that the Initial Developer permits you to utilize portions of the Covered Code under Your choice of the MPL or the alternative licenses, if any, specified by the Initial Developer in the file described in Exhibit A.


* Author of JSON-java is Sean Leary.
  This software is in public domain

* Author of MD5 (OpenSSL-compatible implementation of the RSA Data Security, Inc. MD5 Message-Digest Algorithm) is Alexander Peslyak.
  This software is in public domain and released with following notes:


        This software was written by Alexander Peslyak in 2001.  No copyright is
        claimed, and the software is hereby placed in the public domain.
        In case this attempt to disclaim copyright and place the software in the
        public domain is deemed null and void, then the software is
        Copyright (c) 2001 Alexander Peslyak and it is hereby released to the
        general public under the following terms:

        Redistribution and use in source and binary forms, with or without
        modification, are permitted.

        There's ABSOLUTELY NO WARRANTY, express or implied.

        This is a heavily cut-down "BSD license".)

        This differs from Colin Plumb's older public domain implementation in that
        no exactly 32-bit integer data type is required (any 32-bit or wider
        unsigned integer data type will do), there's no compile-time endianness
        configuration, and the function prototypes match OpenSSL's.  No code from
        Colin Plumb's implementation has been reused; this comment merely compares
        the properties of the two independent implementations.

        The primary goals of this implementation are portability and ease of use.
        It is meant to be fast, but not as fast as possible.  Some known
        optimizations are not included to reduce source code size and avoid
        compile-time configuration.




* Author of miniz.c 1.15 is Rich Geldreich. JRebel embeds a derivative work based on it.
  This software is in public domain. Copyright for it is disclaimed under the terms of unlincese:


        This is free and unencumbered software released into the public domain.

        Anyone is free to copy, modify, publish, use, compile, sell, or
        distribute this software, either in source code form or as a compiled
        binary, for any purpose, commercial or non-commercial, and by any
        means.

        In jurisdictions that recognize copyright laws, the author or authors
        of this software dedicate any and all copyright interest in the
        software to the public domain. We make this dedication for the benefit
        of the public at large and to the detriment of our heirs and
        successors. We intend this dedication to be an overt act of
        relinquishment in perpetuity of all present and future rights to this
        software under copyright law.

        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
        IN NO EVENT SHALL THE AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR
        OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
        ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
        OTHER DEALINGS IN THE SOFTWARE.

        For more information, please refer to <http://unlicense.org/>




* Copyright for @auroratide/toggle-switch 0.2.0 is held by Timothy Foster.
  The software is licensed under the ISC License:


        Permission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.

        THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.




* Copyright for Remote Server Protocol UI 0.23.12 is held by Red Hat, Inc
  The software is licensed under the Eclipse Public License - v 2.0

        Eclipse Public License - v 2.0

            THE ACCOMPANYING PROGRAM IS PROVIDED UNDER THE TERMS OF THIS ECLIPSE
            PUBLIC LICENSE ("AGREEMENT"). ANY USE, REPRODUCTION OR DISTRIBUTION
            OF THE PROGRAM CONSTITUTES RECIPIENT'S ACCEPTANCE OF THIS AGREEMENT.

        1. DEFINITIONS

        "Contribution" means:

          a) in the case of the initial Contributor, the initial content
             Distributed under this Agreement, and

          b) in the case of each subsequent Contributor:
             i) changes to the Program, and
             ii) additions to the Program;
          where such changes and/or additions to the Program originate from
          and are Distributed by that particular Contributor. A Contribution
          "originates" from a Contributor if it was added to the Program by
          such Contributor itself or anyone acting on such Contributor's behalf.
          Contributions do not include changes or additions to the Program that
          are not Modified Works.

        "Contributor" means any person or entity that Distributes the Program.

        "Licensed Patents" mean patent claims licensable by a Contributor which
        are necessarily infringed by the use or sale of its Contribution alone
        or when combined with the Program.

        "Program" means the Contributions Distributed in accordance with this
        Agreement.

        "Recipient" means anyone who receives the Program under this Agreement
        or any Secondary License (as applicable), including Contributors.

        "Derivative Works" shall mean any work, whether in Source Code or other
        form, that is based on (or derived from) the Program and for which the
        editorial revisions, annotations, elaborations, or other modifications
        represent, as a whole, an original work of authorship.

        "Modified Works" shall mean any work in Source Code or other form that
        results from an addition to, deletion from, or modification of the
        contents of the Program, including, for purposes of clarity any new file
        in Source Code form that contains any contents of the Program. Modified
        Works shall not include works that contain only declarations,
        interfaces, types, classes, structures, or files of the Program solely
        in each case in order to link to, bind by name, or subclass the Program
        or Modified Works thereof.

        "Distribute" means the acts of a) distributing or b) making available
        in any manner that enables the transfer of a copy.

        "Source Code" means the form of a Program preferred for making
        modifications, including but not limited to software source code,
        documentation source, and configuration files.

        "Secondary License" means either the GNU General Public License,
        Version 2.0, or any later versions of that license, including any
        exceptions or additional permissions as identified by the initial
        Contributor.

        2. GRANT OF RIGHTS

          a) Subject to the terms of this Agreement, each Contributor hereby
          grants Recipient a non-exclusive, worldwide, royalty-free copyright
          license to reproduce, prepare Derivative Works of, publicly display,
          publicly perform, Distribute and sublicense the Contribution of such
          Contributor, if any, and such Derivative Works.

          b) Subject to the terms of this Agreement, each Contributor hereby
          grants Recipient a non-exclusive, worldwide, royalty-free patent
          license under Licensed Patents to make, use, sell, offer to sell,
          import and otherwise transfer the Contribution of such Contributor,
          if any, in Source Code or other form. This patent license shall
          apply to the combination of the Contribution and the Program if, at
          the time the Contribution is added by the Contributor, such addition
          of the Contribution causes such combination to be covered by the
          Licensed Patents. The patent license shall not apply to any other
          combinations which include the Contribution. No hardware per se is
          licensed hereunder.

          c) Recipient understands that although each Contributor grants the
          licenses to its Contributions set forth herein, no assurances are
          provided by any Contributor that the Program does not infringe the
          patent or other intellectual property rights of any other entity.
          Each Contributor disclaims any liability to Recipient for claims
          brought by any other entity based on infringement of intellectual
          property rights or otherwise. As a condition to exercising the
          rights and licenses granted hereunder, each Recipient hereby
          assumes sole responsibility to secure any other intellectual
          property rights needed, if any. For example, if a third party
          patent license is required to allow Recipient to Distribute the
          Program, it is Recipient's responsibility to acquire that license
          before distributing the Program.

          d) Each Contributor represents that to its knowledge it has
          sufficient copyright rights in its Contribution, if any, to grant
          the copyright license set forth in this Agreement.

          e) Notwithstanding the terms of any Secondary License, no
          Contributor makes additional grants to any Recipient (other than
          those set forth in this Agreement) as a result of such Recipient's
          receipt of the Program under the terms of a Secondary License
          (if permitted under the terms of Section 3).

        3. REQUIREMENTS

        3.1 If a Contributor Distributes the Program in any form, then:

          a) the Program must also be made available as Source Code, in
          accordance with section 3.2, and the Contributor must accompany
          the Program with a statement that the Source Code for the Program
          is available under this Agreement, and informs Recipients how to
          obtain it in a reasonable manner on or through a medium customarily
          used for software exchange; and

          b) the Contributor may Distribute the Program under a license
          different than this Agreement, provided that such license:
             i) effectively disclaims on behalf of all other Contributors all
             warranties and conditions, express and implied, including
             warranties or conditions of title and non-infringement, and
             implied warranties or conditions of merchantability and fitness
             for a particular purpose;

             ii) effectively excludes on behalf of all other Contributors all
             liability for damages, including direct, indirect, special,
             incidental and consequential damages, such as lost profits;

             iii) does not attempt to limit or alter the recipients' rights
             in the Source Code under section 3.2; and

             iv) requires any subsequent distribution of the Program by any
             party to be under a license that satisfies the requirements
             of this section 3.

        3.2 When the Program is Distributed as Source Code:

          a) it must be made available under this Agreement, or if the
          Program (i) is combined with other material in a separate file or
          files made available under a Secondary License, and (ii) the initial
          Contributor attached to the Source Code the notice described in
          Exhibit A of this Agreement, then the Program may be made available
          under the terms of such Secondary Licenses, and

          b) a copy of this Agreement must be included with each copy of
          the Program.

        3.3 Contributors may not remove or alter any copyright, patent,
        trademark, attribution notices, disclaimers of warranty, or limitations
        of liability ("notices") contained within the Program from any copy of
        the Program which they Distribute, provided that Contributors may add
        their own appropriate notices.

        4. COMMERCIAL DISTRIBUTION

        Commercial distributors of software may accept certain responsibilities
        with respect to end users, business partners and the like. While this
        license is intended to facilitate the commercial use of the Program,
        the Contributor who includes the Program in a commercial product
        offering should do so in a manner which does not create potential
        liability for other Contributors. Therefore, if a Contributor includes
        the Program in a commercial product offering, such Contributor
        ("Commercial Contributor") hereby agrees to defend and indemnify every
        other Contributor ("Indemnified Contributor") against any losses,
        damages and costs (collectively "Losses") arising from claims, lawsuits
        and other legal actions brought by a third party against the Indemnified
        Contributor to the extent caused by the acts or omissions of such
        Commercial Contributor in connection with its distribution of the Program
        in a commercial product offering. The obligations in this section do not
        apply to any claims or Losses relating to any actual or alleged
        intellectual property infringement. In order to qualify, an Indemnified
        Contributor must: a) promptly notify the Commercial Contributor in
        writing of such claim, and b) allow the Commercial Contributor to control,
        and cooperate with the Commercial Contributor in, the defense and any
        related settlement negotiations. The Indemnified Contributor may
        participate in any such claim at its own expense.

        For example, a Contributor might include the Program in a commercial
        product offering, Product X. That Contributor is then a Commercial
        Contributor. If that Commercial Contributor then makes performance
        claims, or offers warranties related to Product X, those performance
        claims and warranties are such Commercial Contributor's responsibility
        alone. Under this section, the Commercial Contributor would have to
        defend claims against the other Contributors related to those performance
        claims and warranties, and if a court requires any other Contributor to
        pay any damages as a result, the Commercial Contributor must pay
        those damages.

        5. NO WARRANTY

        EXCEPT AS EXPRESSLY SET FORTH IN THIS AGREEMENT, AND TO THE EXTENT
        PERMITTED BY APPLICABLE LAW, THE PROGRAM IS PROVIDED ON AN "AS IS"
        BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, EITHER EXPRESS OR
        IMPLIED INCLUDING, WITHOUT LIMITATION, ANY WARRANTIES OR CONDITIONS OF
        TITLE, NON-INFRINGEMENT, MERCHANTABILITY OR FITNESS FOR A PARTICULAR
        PURPOSE. Each Recipient is solely responsible for determining the
        appropriateness of using and distributing the Program and assumes all
        risks associated with its exercise of rights under this Agreement,
        including but not limited to the risks and costs of program errors,
        compliance with applicable laws, damage to or loss of data, programs
        or equipment, and unavailability or interruption of operations.

        6. DISCLAIMER OF LIABILITY

        EXCEPT AS EXPRESSLY SET FORTH IN THIS AGREEMENT, AND TO THE EXTENT
        PERMITTED BY APPLICABLE LAW, NEITHER RECIPIENT NOR ANY CONTRIBUTORS
        SHALL HAVE ANY LIABILITY FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
        EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING WITHOUT LIMITATION LOST
        PROFITS), HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
        CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
        ARISING IN ANY WAY OUT OF THE USE OR DISTRIBUTION OF THE PROGRAM OR THE
        EXERCISE OF ANY RIGHTS GRANTED HEREUNDER, EVEN IF ADVISED OF THE
        POSSIBILITY OF SUCH DAMAGES.

        7. GENERAL

        If any provision of this Agreement is invalid or unenforceable under
        applicable law, it shall not affect the validity or enforceability of
        the remainder of the terms of this Agreement, and without further
        action by the parties hereto, such provision shall be reformed to the
        minimum extent necessary to make such provision valid and enforceable.

        If Recipient institutes patent litigation against any entity
        (including a cross-claim or counterclaim in a lawsuit) alleging that the
        Program itself (excluding combinations of the Program with other software
        or hardware) infringes such Recipient's patent(s), then such Recipient's
        rights granted under Section 2(b) shall terminate as of the date such
        litigation is filed.

        All Recipient's rights under this Agreement shall terminate if it
        fails to comply with any of the material terms or conditions of this
        Agreement and does not cure such failure in a reasonable period of
        time after becoming aware of such noncompliance. If all Recipient's
        rights under this Agreement terminate, Recipient agrees to cease use
        and distribution of the Program as soon as reasonably practicable.
        However, Recipient's obligations under this Agreement and any licenses
        granted by Recipient relating to the Program shall continue and survive.

        Everyone is permitted to copy and distribute copies of this Agreement,
        but in order to avoid inconsistency the Agreement is copyrighted and
        may only be modified in the following manner. The Agreement Steward
        reserves the right to publish new versions (including revisions) of
        this Agreement from time to time. No one other than the Agreement
        Steward has the right to modify this Agreement. The Eclipse Foundation
        is the initial Agreement Steward. The Eclipse Foundation may assign the
        responsibility to serve as the Agreement Steward to a suitable separate
        entity. Each new version of the Agreement will be given a distinguishing
        version number. The Program (including Contributions) may always be
        Distributed subject to the version of the Agreement under which it was
        received. In addition, after a new version of the Agreement is published,
        Contributor may elect to Distribute the Program (including its
        Contributions) under the new version.

        Except as expressly stated in Sections 2(a) and 2(b) above, Recipient
        receives no rights or licenses to the intellectual property of any
        Contributor under this Agreement, whether expressly, by implication,
        estoppel or otherwise. All rights in the Program not expressly granted
        under this Agreement are reserved. Nothing in this Agreement is intended
        to be enforceable by any entity that is not a Contributor or Recipient.
        No third-party beneficiary rights are created under this Agreement.

        Exhibit A - Form of Secondary Licenses Notice

        "This Source Code may also be made available under the following
        Secondary Licenses when the conditions for such availability set forth
        in the Eclipse Public License, v. 2.0 are satisfied: {name license(s),
        version(s), and exceptions or additional permissions here}."

          Simply including a copy of this Agreement, including this Exhibit A
          is not sufficient to license the Source Code under Secondary Licenses.

          If it is not possible or desirable to put the notice in a particular
          file, then You may include the notice in a location (such as a LICENSE
          file in a relevant directory) where a recipient would be likely to
          look for such a notice.

          You may add additional accurate notices of copyright ownership.




* Copyright for Debugger for Java 0.42.0 is held by Microsoft Corporation
  The software is licensed under the MIT License


        ------------------------------------------ START OF LICENSE -----------------------------------------

        vscode-java-debug

        Copyright (c) Microsoft Corporation

        All rights reserved.

        MIT License

        Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the ""Software""), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

        The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

        THE SOFTWARE IS PROVIDED *AS IS*, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

        ----------------------------------------------- END OF LICENSE ------------------------------------------


