[versions]
kotlin = "2.1.0"

kotlinx-serialization = "1.8.0"

ktor = "3.0.3"
exposed = "0.58.0"

slf4j = "2.0.16"
logback = "1.5.16"

netty = "4.1.117.Final"

asm = "9.7.1"
jackson = "2.18.2"
javaparser = "3.26.3"

runelite = "1.9.8"

[plugins]
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
shadow = { id = "com.gradleup.shadow", version = "8.3.5" }

[libraries]

kotlin-gradle-plugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin" }

kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version = "1.10.1" }
kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version = "0.6.1" }
kotlinx-atomicfu = { module = "org.jetbrains.kotlinx:atomicfu", version = "0.27.0" }

kotlinx-serialization-core = { module = "org.jetbrains.kotlinx:kotlinx-serialization-core", version.ref = "kotlinx-serialization" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinx-serialization" }
kotlinx-serialization-protobuf = { module = "org.jetbrains.kotlinx:kotlinx-serialization-protobuf", version.ref = "kotlinx-serialization" }

ktor-serialization-kotlinx-json = { module = "io.ktor:ktor-serialization-kotlinx-json", version.ref = "ktor" }
ktor-serialization-kotlinx-protobuf = { module = "io.ktor:ktor-serialization-kotlinx-protobuf", version.ref = "ktor" }

ktor-server-core = { module = "io.ktor:ktor-server-core", version.ref = "ktor" }
ktor-server-netty = { module = "io.ktor:ktor-server-netty", version.ref = "ktor" }
ktor-server-resources = { module = "io.ktor:ktor-server-resources", version.ref = "ktor" }
ktor-server-call-logging = { module = "io.ktor:ktor-server-call-logging", version.ref = "ktor" }
ktor-server-content-negotiation = { module = "io.ktor:ktor-server-content-negotiation", version.ref = "ktor" }
ktor-server-websockets = { module = "io.ktor:ktor-server-websockets", version.ref = "ktor" }

ktor-client-core = { module = "io.ktor:ktor-client-core", version.ref = "ktor" }
ktor-client-auth = { module = "io.ktor:ktor-client-auth", version.ref = "ktor" }
ktor-client-content-negotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "ktor" }
ktor-client-logging = { module = "io.ktor:ktor-client-logging", version.ref = "ktor" }
ktor-client-resources = { module = "io.ktor:ktor-client-resources", version.ref = "ktor" }
ktor-client-okhttp = { module = "io.ktor:ktor-client-okhttp", version.ref = "ktor" }

exposed-core = { module = "org.jetbrains.exposed:exposed-core", version.ref = "exposed" }
exposed-dao = { module = "org.jetbrains.exposed:exposed-dao", version.ref = "exposed" }
exposed-jdbc = { module = "org.jetbrains.exposed:exposed-jdbc", version.ref = "exposed" }
exposed-json = { module = "org.jetbrains.exposed:exposed-json", version.ref = "exposed" }
exposed-kotlin-datetime = { module = "org.jetbrains.exposed:exposed-kotlin-datetime", version.ref = "exposed" }

slf4j-api = { module = "org.slf4j:slf4j-api", version.ref = "slf4j" }
slf4j-simple = { module = "org.slf4j:slf4j-simple", version.ref = "slf4j" }

logback-classic = { module = "ch.qos.logback:logback-classic", version.ref = "logback" }

netty-buffer = { module = "io.netty:netty-buffer", version.ref = "netty" }
netty-codec = { module = "io.netty:netty-codec", version.ref = "netty" }
netty-codec-haproxy = { module = "io.netty:netty-codec-haproxy", version.ref = "netty" }
netty-transport = { module = "io.netty:netty-transport", version.ref = "netty" }
netty-transport-native-epoll = { module = "io.netty:netty-transport-native-epoll", version.ref = "netty" }
netty-transport-native-kqueue = { module = "io.netty:netty-transport-native-kqueue", version.ref = "netty" }
netty-handler = { module = "io.netty:netty-handler", version.ref = "netty" }
netty-resolver-dns-native-macos = { module = "io.netty:netty-resolver-dns-native-macos", version.ref = "netty" }
netty-incubator-transport-native-iouring = { module = "io.netty.incubator:netty-incubator-transport-native-io_uring", version = "0.0.26.Final" }
netty-tcnative-boringssl-static = { module = "io.netty:netty-tcnative-boringssl-static", version = "2.0.69.Final" }

asm-core = { module = "org.ow2.asm:asm", version.ref = "asm" }
asm-commons = { module = "org.ow2.asm:asm-commons", version.ref = "asm" }
asm-tree = { module = "org.ow2.asm:asm-tree", version.ref = "asm" }
asm-util = { module = "org.ow2.asm:asm-util", version.ref = "asm" }

jackson-core = { module = "com.fasterxml.jackson.core:jackson-core", version.ref = "jackson" }
jackson-databind = { module = "com.fasterxml.jackson.core:jackson-databind", version.ref = "jackson" }
jackson-datatype-jsr310 = { module = "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", version.ref = "jackson" }
jackson-module-kotlin = { module = "com.fasterxml.jackson.module:jackson-module-kotlin", version.ref = "jackson" }
jackson-module-afterburner = { module = "com.fasterxml.jackson.module:jackson-module-afterburner", version.ref = "jackson" }
jackson-dataformat-yaml = { module = "com.fasterxml.jackson.dataformat:jackson-dataformat-yaml", version.ref = "jackson" }
jackson-dataformat-toml = { module = "com.fasterxml.jackson.dataformat:jackson-dataformat-toml", version.ref = "jackson" }

javaparser-core = { module = "com.github.javaparser:javaparser-core", version.ref = "javaparser" }
javaparser-symbol-solver-core = { module = "com.github.javaparser:javaparser-symbol-solver-core", version.ref = "javaparser" }

hikari-cp = { module = "com.zaxxer:HikariCP", version = "6.2.1" }

apache-ant = { module = "org.apache.ant:ant", version = "1.10.15" }

apache-commons-io = { module = "commons-io:commons-io", version = "2.18.0" }
apache-commons-lang3 = { module = "org.apache.commons:commons-lang3", version = "3.17.0" }
apache-commons-compress = { module = "org.apache.commons:commons-compress", version = "1.27.1" }
apache-commons-cli = { module = "commons-cli:commons-cli", version = "1.9.0" }
apache-commons-codec = { module = "commons-codec:commons-codec", version = "1.17.2" }
apache-commons-text = { module = "org.apache.commons:commons-text", version = "1.13.0" }

google-guava = { module = "com.google.guava:guava", version = "33.4.0-jre" }
google-gson = { module = "com.google.code.gson:gson", version = "2.11.0" }

openhft-affinity = { module = "net.openhft:affinity", version = "3.27ea0" }
openhft-chronicle-threads = { module = "net.openhft:chronicle-threads", version = "2.27ea0" }

clikt = { module = "com.github.ajalt.clikt:clikt", version = "5.0.2" }
kotlinpoet = { module = "com.squareup:kotlinpoet", version = "2.0.0" }
jsoup = { module = "org.jsoup:jsoup", version = "1.18.3" }
kryo = { module = "com.esotericsoftware:kryo", version = "5.6.2" }
zip4j = { module = "net.lingala.zip4j:zip4j", version = "2.11.5" }
tradukisto = { module = "pl.allegro.finance:tradukisto", version = "3.5.0" }
kaml = { module = "com.charleskorn.kaml:kaml", version = "0.67.0" }
fastutil = { module = "it.unimi.dsi:fastutil", version = "8.5.15" }
classgraph = { module = "io.github.classgraph:classgraph", version = "4.8.179" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version = "4.12.0" }
checker-qual = { module = "org.checkerframework:checker-qual", version = "3.48.4" }
jctools-core = { module = "org.jctools:jctools-core", version = "4.0.5" }
#logstash-gelf = { module = "biz.paluch.logging:logstash-gelf", version = "1.15.1" }
mXparser = { module = "org.mariuszgromada.math:MathParser.org-mXparser", version = "6.1.0" }
kord-core = { module = "dev.kord:kord-core", version = "0.15.0" }
mysql-connector-j = { module = "com.mysql:mysql-connector-j", version = "9.2.0" }
postgresql = { module = "org.postgresql:postgresql", version = "42.7.5" }
mockk = { module = "io.mockk:mockk", version = "1.13.16" }

toml4j = { module = "io.hotmoka:toml4j", version = "0.7.3" }

runelite-api = { module = "net.runelite:runelite-api", version.ref = "runelite" }
runelite-cache = { module = "net.runelite:cache", version.ref = "runelite" }

[bundles]

ktor-server = [
    "ktor-server-netty",
    "ktor-server-core",
    "ktor-server-resources",
    "ktor-server-call-logging",
    "ktor-server-content-negotiation",
    "ktor-server-websockets",
    "ktor-serialization-kotlinx-json",
    "ktor-serialization-kotlinx-protobuf"
]

ktor-client = [
    "ktor-client-auth",
    "ktor-client-core",
    "ktor-client-okhttp",
    "ktor-client-content-negotiation",
    "ktor-client-logging",
    "ktor-client-resources",
    "ktor-serialization-kotlinx-json"
]

exposed = [
    "exposed-core", "exposed-dao", "exposed-jdbc", "exposed-json", "exposed-kotlin-datetime"
]

asm = [
    "asm-core",
    "asm-commons",
    "asm-tree",
    "asm-util",
]