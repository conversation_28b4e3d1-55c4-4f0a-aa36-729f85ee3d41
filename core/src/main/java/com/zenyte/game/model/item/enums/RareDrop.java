package com.zenyte.game.model.item.enums;

import com.near_reality.game.item.CustomItemId;
import com.zenyte.game.item.Item;
import com.zenyte.game.item.ItemId;
import it.unimi.dsi.fastutil.ints.IntOpenHashSet;
import it.unimi.dsi.fastutil.ints.IntSet;

import static com.zenyte.game.item.ItemId.*;

/**
 * <AUTHOR> | 4-2-2019 | 22:24
 * @see <a href="https://www.rune-server.ee/members/tommeh/">Rune-Server profile</a>}
 */
public enum RareDrop {
    CRYSTAL_TOOL_SEED(23953),
    ZALCANO_SHARD(ItemId.ZALCANO_SHARD),
    SMOLCANO(ItemId.SMOLCANO),
    STAFF_OF_THE_DEAD(11791),
    ZAMORAKIAN_SPEAR(11824),
    ZAMORAK_HILT(11816),
    SARADOMIN_SWORD(11838),
    ARMADY<PERSON>_CROSSBOW(11785),
    SARADOMIN_HILT(11814),
    BANDOS_BOOTS(11836),
    BANDOS_CHESTPLATE(11832),
    BANDOS_TASSETS(11834),
    BANDOS_HILT(11812),
    ARMADYL_HELMET(11826),
    ARMADYL_CHESTPLATE(11828),
    ARMADYL_CHAINSKIRT(11830),
    ARMADYL_HILT(11810),
    BLACK_MASK(8901),
    EERS_RING(6731),
    ARCHERS_RING(6733),
    WARRIOR_RING(6735),
    BERSERKER_RING(6737),
    DRAGON_AXE(6739),
    TANZANITE_FANG(12922),
    SERPENTINE_VISAGE(12927),
    MAGIC_FANG(12932),
    TANZANITE_MUTAGEN(13200),
    MAGMA_MUTAGEN(13201),
    RING_OF_THE_GODS(12601),
    TYRANNICAL_RING(12603),
    TREASONOUS_RING(12605),
    DRAGON_PICKAXE(11920),
    DRAGON_2H_SWORD(7158),
    UNCHARGED_TRIDENT(11908),
//    TRIDENT_OF_THE_SEAS_FULL(11905),
    KRAKEN_TENTACLE(12004),
    ETERNAL_CRYSTAL(13227),
    PEGASIAN_CRYSTAL(13229),
    PRIMORDIAL_CRYSTAL(13231),
    SMOULDERING_STONE(13233),
    HOLY_ELIXIR(12833),
    SPECTRAL_SIGIL(12823),
    ARCANE_SIGIL(12827),
    ELYSIAN_SIGIL(12819),
    WYVERN_VISAGE(ItemId.WYVERN_VISAGE),
    DRACONIC_VISAGE(11286),
    SKELETAL_VISAGE(22006),
    DRAGONBONE_NECKLACE(22111),
    THAMMARON_SCEPTRE(ItemId.THAMMARONS_SCEPTRE_U),
    CRAWS_BOW(ItemId.CRAWS_BOW_U),
    VIGGORA_MACE(ItemId.VIGGORAS_CHAINMACE_U),
    AMULET_OF_AVARICE(ItemId.AMULET_OF_AVARICE),
    UNSIRED(13273),
    ABYSSAL_DAGGER(13265),
    BLACK_TOURMALINE_CORE(21730),
    //GRANITE_HAMMER(21742),
    DRAGON_CHAINBODY(3140),
    OCCULT_NECKLACE(12002),
    ABYSSAL_WHIP(4151),
    DARK_BOW(11235),
    ZENYTE_SHARD(19529),
    DRAGON_WARHAMMER(13576),
    DRAGON_LUMP(22103),
    IMBUED_HEART(20724),
    ETERNAL_GEM(ItemId.ETERNAL_GEM),
    DRAGON_LIMBS(21918),
    //Chambers of Xeric
    DEXTEROUS_PRAYER_SCROLL(ItemId.DEXTEROUS_PRAYER_SCROLL),
    ARCANE_PRAYER_SCROLL(ItemId.ARCANE_PRAYER_SCROLL),
    TWISTED_BUCKLET(ItemId.TWISTED_BUCKLER),
    DRAGON_HUNTER_CROSSBOW(ItemId.DRAGON_HUNTER_CROSSBOW),
    DINHS_BULWARK(ItemId.DINHS_BULWARK),
    ANCESTRAL_HAT(ItemId.ANCESTRAL_HAT),
    ANCESTRAL_ROBE_TOP(ItemId.ANCESTRAL_ROBE_TOP),
    ANCESTRAL_ROBE_BOTTOM(ItemId.ANCESTRAL_ROBE_BOTTOM),
    DRAGON_CLAWS(ItemId.DRAGON_CLAWS),
    ELDER_MAUL(ItemId.ELDER_MAUL),
    KODAI_INSIGNIA(ItemId.KODAI_INSIGNIA),
    TWISTED_BOW(ItemId.TWISTED_BOW),
    METAMORPHIC_DUST(ItemId.METAMORPHIC_DUST),

    //(Alchemical) Hydra
    HYDRAS_EYE(ItemId.HYDRAS_EYE),
    HYDRAS_HEART(ItemId.HYDRAS_HEART),
    HYDRAS_FANG(ItemId.HYDRAS_FANG),
    HYDRAS_TAIL(ItemId.HYDRA_TAIL),
    HYDRAS_CLAW(ItemId.HYDRAS_CLAW),
    HYDRA_LEATHER(ItemId.HYDRA_LEATHER),
    HYDRA_HEAD(ItemId.ALCHEMICAL_HYDRA_HEADS),
    PHARAOH_SCEPTRE(ItemId.PHARAOHS_SCEPTRE_3),

    JAR_OF_CHEMICALS(ItemId.JAR_OF_CHEMICALS),
    JAR_OF_DARKNESS(ItemId.JAR_OF_DARKNESS),
    JAR_OF_DECAY(ItemId.JAR_OF_DECAY),
    JAR_OF_DIRT(ItemId.JAR_OF_DIRT),
    JAR_OF_MIASMA(ItemId.JAR_OF_MIASMA),
    JAR_OF_SAND(ItemId.JAR_OF_SAND),
    JAR_OF_SOULS(ItemId.JAR_OF_SOULS),
    JAR_OF_STONE(ItemId.JAR_OF_STONE),
    JAR_OF_SWAMP(ItemId.JAR_OF_SWAMP),

    DRAGON_KITE(CustomItemId.DRAGON_KITE),
    LAVA_WHIP(CustomItemId.LAVA_WHIP),
    LIME_WHIP(CustomItemId.LIME_WHIP),
    DEATH_CAPE(CustomItemId.DEATH_CAPE),

    BOOK_OF_DEATH(ItemId.BOOK_OF_THE_DEAD),
    XAMPHUR_PET(32083),

    //Theatre of Blood
    AVERNIC_DEFENDER_HILT(ItemId.AVERNIC_DEFENDER_HILT),
    GHRAZI_RAPIER(ItemId.GHRAZI_RAPIER),
    SANGUINESTI_STAFF_UNCHARGED(ItemId.SANGUINESTI_STAFF_UNCHARGED),
    JUSTICIAR_FACEGUARD(ItemId.JUSTICIAR_FACEGUARD),
    JUSTICIAR_CHESTGUARD(ItemId.JUSTICIAR_CHESTGUARD),
    JUSTICIAR_LEGGUARDS(ItemId.JUSTICIAR_LEGGUARDS),
    SCYTHE_OF_VITUR_UNCHARGED(ItemId.SCYTHE_OF_VITUR_UNCHARGED),
    HOLY_ORNAMENT_KIT(ItemId.HOLY_ORNAMENT_KIT),
    SANGUINE_ORNAMENT_KIT(ItemId.SANGUINE_ORNAMENT_KIT),
    SANGUINE_DUST(ItemId.SANGUINE_DUST),
    LIL_ZIK(ItemId.LIL_ZIK),

    //Larran's chest
    DAGONHAI_HAT(ItemId.DAGONHAI_HAT),
    DAGONHAI_ROBE_TOP(ItemId.DAGONHAI_ROBE_TOP),
    DAGONHAI_ROBE_BOTTOM(ItemId.DAGONHAI_ROBE_BOTTOM),
    VESTAS_LONGSWORD(ItemId.VESTAS_LONGSWORD),
    STATIUSS_WARHAMMER(ItemId.STATIUSS_WARHAMMER),
    ZURIELS_STAFF(ItemId.ZURIELS_STAFF),
    VESTAS_SPEAR(ItemId.VESTAS_SPEAR),

    //Ganodermic beast
    GANODERMIC_RUNT(CustomItemId.GANODERMIC_RUNT),
    POLYPORE_STAFF(CustomItemId.POLYPORE_STAFF_DEG),
    ANCIENT_EYE(CustomItemId.ANCIENT_EYE),

    //Rots
    SHIELD1(32192),
    SHIELD2(32195),
    SHIELD3(32198),

    //Nightmare
    INQUISITORS_MACE(ItemId.INQUISITORS_MACE),
    INQUISITORS_GREAT_HELM(ItemId.INQUISITORS_GREAT_HELM),
    INQUISITORS_HAUBERK(ItemId.INQUISITORS_HAUBERK),
    INQUISITORS_PLATESKIRT(ItemId.INQUISITORS_PLATESKIRT),
    NIGHTMARE_STAFF(ItemId.NIGHTMARE_STAFF),
    ELDRITCH_ORB(ItemId.ELDRITCH_ORB),
    VOLATILE_ORB(ItemId.VOLATILE_ORB),
    HARMONISED_ORB(ItemId.HARMONISED_ORB),

    //Sarachnis
    SARACHNIS_CUDGEL(ItemId.SARACHNIS_CUDGEL),
    SRARACHA(ItemId.SRARACHA),

    CLAWS_OF_CALLISTO(ItemId.CLAWS_OF_CALLISTO),
    FANGS_OF_VENENATIS(ItemId.FANGS_OF_VENENATIS),
    SKULL_OF_VETION(ItemId.SKULL_OF_VETION),
    VENATOR_SHARD(ItemId.VENATOR_SHARD),
    BASILISK_JAW(ItemId.BASILISK_JAW),
    FROZEN_CACHE(ItemId.FROZEN_CACHE),
    VOIDWAKER_BLADE(ItemId.VOIDWAKER_BLADE),
    VOIDWAKER_GEM(ItemId.VOIDWAKER_GEM),
    VOIDWAKER_HILT(ItemId.VOIDWAKER_HILT),
    ANCIENT_ICON(ItemId.ANCIENT_ICON),
    GWD_SOUL_CRYSTAL1(ItemId.BANDOS_SOUL_CRYSTAL),
    GWD_SOUL_CRYSTAL2(ItemId.ARMADYL_SOUL_CRYSTAL),
    GWD_SOUL_CRYSTAL3(ItemId.ZAMORAK_SOUL_CRYSTAL),
    GWD_SOUL_CRYSTAL4(ItemId.SARADOMIN_SOUL_CRYSTAL),
    JAR_OF_DREAMS(ItemId.JAR_OF_DREAMS),
    LITTLE_NIGHTMARE(ItemId.LITTLE_NIGHTMARE),
    MALEDICTION_SHARD_1(ItemId.MALEDICTION_SHARD_1),
    MALEDICTION_SHARD_2(ItemId.MALEDICTION_SHARD_2),
    MALEDICTION_SHARD_3(ItemId.MALEDICTION_SHARD_3),
    ODIUM_SHARD_1(ItemId.ODIUM_SHARD_1),
    ODIUM_SHARD_2(ItemId.ODIUM_SHARD_2),
    ODIUM_SHARD_3(ItemId.ODIUM_SHARD_3),

    EYE_OF_THE_DUKE(ItemId.EYE_OF_THE_DUKE),
    EXECUTIONERS_AXE_HEAD(ItemId.EXECUTIONERS_AXE_HEAD),
    MAGUS_ICON(ItemId.MAGUS_ICON),
    ULTOR_ICON(ItemId.ULTOR_ICON),
    VIRTUS_MASK(ItemId.VIRTUS_MASK),
    VIRTUS_ROBE_TOP(ItemId.VIRTUS_ROBE_TOP),
    VIRTUS_ROBE_LEGS(ItemId.VIRTUS_ROBE_LEGS),
    MAGUS_VESTIGE(ItemId.MAGUS_VESTIGE),
    ULTOR_VESTIGE(ItemId.ULTOR_VESTIGE),
    SLAYER1(ItemId.SLAYER_PLATESKIRT),
    SLAYER2(32230),
    SLAYER3(32228),
    SLAYER4(32227),
    // Araxxor
    POMMEL(NOXIOUS_POMMEL),
    POINT(NOXIOUS_POINT),
    BLADE(NOXIOUS_BLADE),
    FANG(ARAXYTE_FANG),
    HEAD(ARAXYTE_HEAD),
    VENOM_JAR(JAR_OF_VENOM),
    // Tormented Demons
    TORMENTED_SYNAPSE(ItemId.TORMENTED_SYNAPSE),
    BURNING_CLAW(ItemId.BURNING_CLAW),

    // Tombs of Amascut
    OSMUMTENS_FANG(ItemId.OSMUMTENS_FANG),
    LIGHTBEARER(ItemId.LIGHTBEARER),
    ELIDINIS_WARD(ItemId.ELIDINIS_WARD),
    MASORI_MASK(ItemId.MASORI_MASK),
    MASORI_BODY(ItemId.MASORI_BODY),
    MASORI_CHAPS(ItemId.MASORI_CHAPS),
    TUMEKENS_SHADOW_UNCHARGED(ItemId.TUMEKENS_SHADOW_UNCHARGED)
    ;

    private final int id;

    RareDrop(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    private static final IntSet STATIC_ITEM_IDS = IntOpenHashSet.of();
    private static final IntSet DYNAMIC_ITEM_IDS = IntOpenHashSet.of();

    static {
        for (final RareDrop drop : values())
            STATIC_ITEM_IDS.add(drop.getId());
    }

    public static boolean contains(final Item item) {
        return STATIC_ITEM_IDS.contains(item.getId()) || DYNAMIC_ITEM_IDS.contains(item.getId());
    }

    public static void addStatic(final int id) {
        STATIC_ITEM_IDS.add(id);
    }

    public static void addDynamic(final int id) {
        DYNAMIC_ITEM_IDS.add(id);
    }

    public static void removeDynamic(final int id) {
        DYNAMIC_ITEM_IDS.remove(id);
    }

    public static IntSet getDynamicItemIds() {
        return DYNAMIC_ITEM_IDS;
    }
}
