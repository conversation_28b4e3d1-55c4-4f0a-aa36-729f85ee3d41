package com.zenyte.game.model.ui.testinterfaces;

import com.zenyte.game.GameInterface;
import com.zenyte.game.model.ui.Interface;
import com.zenyte.game.world.entity.masks.UpdateFlag;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.SkillConstants;
import com.zenyte.game.world.entity.player.Skills;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.zenyte.game.world.region.area.wilderness.WildernessArea;
import mgi.types.config.enums.Enums;

import java.util.HashMap;
import java.util.Map;

/**
 * Skills tab with proper mapping from component IDs to skill IDs.
 */
public class SkillsTabInterface extends Interface {

    // Mapping from UI Component IDs to Skill IDs
    private static final Map<Integer, Integer> COMPONENT_ID_TO_SKILL = new HashMap<>();
    static {
        COMPONENT_ID_TO_SKILL.put(1, SkillConstants.ATTACK);
        COMPONENT_ID_TO_SKILL.put(2, SkillConstants.STRENGTH);
        COMPONENT_ID_TO_SKILL.put(3, SkillConstants.DEFENCE);
        COMPONENT_ID_TO_SKILL.put(4, SkillConstants.RANGED);
        COMPONENT_ID_TO_SKILL.put(5, SkillConstants.PRAYER);
        COMPONENT_ID_TO_SKILL.put(6, SkillConstants.MAGIC);
        COMPONENT_ID_TO_SKILL.put(7, SkillConstants.RUNECRAFTING);
        COMPONENT_ID_TO_SKILL.put(8, SkillConstants.CONSTRUCTION);
        COMPONENT_ID_TO_SKILL.put(9, SkillConstants.HITPOINTS);
        COMPONENT_ID_TO_SKILL.put(10, SkillConstants.AGILITY);
        COMPONENT_ID_TO_SKILL.put(11, SkillConstants.HERBLORE);
        COMPONENT_ID_TO_SKILL.put(12, SkillConstants.THIEVING);
        COMPONENT_ID_TO_SKILL.put(13, SkillConstants.CRAFTING);
        COMPONENT_ID_TO_SKILL.put(14, SkillConstants.FLETCHING);
        COMPONENT_ID_TO_SKILL.put(15, SkillConstants.SLAYER);
        COMPONENT_ID_TO_SKILL.put(16, SkillConstants.HUNTER);
        COMPONENT_ID_TO_SKILL.put(17, SkillConstants.MINING);
        COMPONENT_ID_TO_SKILL.put(18, SkillConstants.SMITHING);
        COMPONENT_ID_TO_SKILL.put(19, SkillConstants.FISHING);
        COMPONENT_ID_TO_SKILL.put(20, SkillConstants.COOKING);
        COMPONENT_ID_TO_SKILL.put(21, SkillConstants.FIREMAKING);
        COMPONENT_ID_TO_SKILL.put(22, SkillConstants.WOODCUTTING);
        COMPONENT_ID_TO_SKILL.put(23, SkillConstants.FARMING);
    }

    private static final Map<Integer, Integer> SKILL_ID_TO_GUIDE_ID = new HashMap<>();
    static {
        COMPONENT_ID_TO_SKILL.put(1, SkillConstants.ATTACK);
        COMPONENT_ID_TO_SKILL.put(2, SkillConstants.STRENGTH);
        COMPONENT_ID_TO_SKILL.put(3, SkillConstants.DEFENCE);
        COMPONENT_ID_TO_SKILL.put(4, SkillConstants.RANGED);
        COMPONENT_ID_TO_SKILL.put(5, SkillConstants.PRAYER);
        COMPONENT_ID_TO_SKILL.put(6, SkillConstants.MAGIC);
        COMPONENT_ID_TO_SKILL.put(7, SkillConstants.RUNECRAFTING);
        COMPONENT_ID_TO_SKILL.put(8, SkillConstants.CONSTRUCTION);
        COMPONENT_ID_TO_SKILL.put(9, SkillConstants.HITPOINTS);
        COMPONENT_ID_TO_SKILL.put(10, SkillConstants.AGILITY);
        COMPONENT_ID_TO_SKILL.put(11, SkillConstants.HERBLORE);
        COMPONENT_ID_TO_SKILL.put(12, SkillConstants.THIEVING);
        COMPONENT_ID_TO_SKILL.put(13, SkillConstants.CRAFTING);
        COMPONENT_ID_TO_SKILL.put(14, SkillConstants.FLETCHING);
        COMPONENT_ID_TO_SKILL.put(15, SkillConstants.SLAYER);
        COMPONENT_ID_TO_SKILL.put(16, SkillConstants.HUNTER);
        COMPONENT_ID_TO_SKILL.put(17, SkillConstants.MINING);
        COMPONENT_ID_TO_SKILL.put(18, SkillConstants.SMITHING);
        COMPONENT_ID_TO_SKILL.put(19, SkillConstants.FISHING);
        COMPONENT_ID_TO_SKILL.put(20, SkillConstants.COOKING);
        COMPONENT_ID_TO_SKILL.put(21, SkillConstants.FIREMAKING);
        COMPONENT_ID_TO_SKILL.put(22, SkillConstants.WOODCUTTING);
        COMPONENT_ID_TO_SKILL.put(23, SkillConstants.FARMING);
    }

    @Override
    protected DefaultClickHandler getDefaultHandler() {
        return (player, componentId, slotId, itemId, optionId) -> {
            if (optionId == 1) { // Mobile XP popup
                return;
            }
            if (player.isLocked()) {
                return;
            }
            if (player.isUnderCombat()) {
                player.sendMessage("You can't do this while in combat.");
                return;
            }

            Integer skill = COMPONENT_ID_TO_SKILL.get(componentId);
            if (skill == null) {
                return;
            }

            if (player.isDebugging) {
                player.sendMessage("componentId: " + componentId + " - componentIdToSkill: "+COMPONENT_ID_TO_SKILL.get(componentId)+" - skillId: " + skill);
            }
            player.getDialogueManager().start(new Dialogue(player) {
                @Override
                public void buildDialogue() {
                    options("Select an option for " + Skills.getSkillName(skill),
                            new DialogueOption("View Skill Guide", () -> player.getSkills().sendSkillMenu(Enums.SKILL_GUIDES_ENUM.getKey(SkillConstants.SKILLS[skill]).orElseThrow(RuntimeException::new), 0)),
                            new DialogueOption("Teleport", () -> {player.sendMessage("Coming Soon...");})
                    );
                }
            });
        };
    }

    @Override
    protected void attach() {

    }

    @Override
    public void open(final Player player) {
        player.getInterfaceHandler().sendInterface(this);
    }

    @Override
    protected void build() {

    }

    @Override
    public GameInterface getInterface() {
        return GameInterface.SKILLS_TAB;
    }
}
