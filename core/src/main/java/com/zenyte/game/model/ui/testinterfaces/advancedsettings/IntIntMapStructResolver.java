package com.zenyte.game.model.ui.testinterfaces.advancedsettings;

import java.util.Map;

/**
 * <AUTHOR>
 */
public final class IntIntMapStructResolver implements SettingStructResolver {

    private final Map<Integer, Integer> map;

    public IntIntMapStructResolver(Map<Integer, Integer> map) {
        this.map = map;
    }

    @Override
    public Integer forStruct(int struct) {
        return map.get(struct);
    }

}
