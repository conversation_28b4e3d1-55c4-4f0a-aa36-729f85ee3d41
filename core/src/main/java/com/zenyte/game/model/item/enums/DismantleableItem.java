package com.zenyte.game.model.item.enums;

import com.zenyte.game.item.ItemId;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import mgi.types.config.items.ItemDefinitions;

/**
 * <AUTHOR> | 4. apr 2018 : 16:23.45
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>}
 * @see <a href="https://rune-status.net/members/kris.354/">Rune-Status profile</a>}
 */
public enum DismantleableItem {

	ARMADYL_GODSWORD(11802, 11810, 11798, false),
	BANDOS_GODSWORD(11804, 11812, 11798, false),
	SARADOMIN_GODSWORD(11806, 11814, 11798, false),
	ZAMORAK_GODSWORD(11808, 11816, 11798, false),
	ANCIENT_GODSWORD(26233, 26370, 11798, false),
	ARMADYL_GODSWORD_OR(20368, 20068, 11802),
	BANDOS_GODSWORD_OR(20370, 20071, 11804),
	SARADOMIN_GODSWORD_OR(20372, 20074, 11806),
	ZAMORAK_GODSWORD_OR(20374, 20077, 11808),
	LIGHT_INFINITY_HAT(12419, 12530, 6918),
	LIGHT_INFINITY_TOP(12420, 12530, 6916),
	LIGHT_INFINITY_BOTTOMS(12421, 12530, 6924),
	DARK_INFINITY_HAT(12457, 12528, 6918),
	DARK_INFINITY_TOP(12458, 12528, 6916),
	DARK_INFINITY_BOTTOMS(12459, 12528, 6924),
	DRAGON_CHAINBODY_G(12414, 12534, 3140),
	DRAGON_PLATELEGS_G(12415, 12536, 4087),
	DRAGON_PLATESKIRT_G(12416, 12536, 4585),
	DRAGON_FULL_HELM_G(12417, 12538, 11335),
	DRAGON_SQ_SHIELD_G(12418, 12532, 1187),
	DRAGON_DEFENDER_G(19722, 20143, 12954),
	DRAGON_SCIMITAR_G(20000, 20002, 4587),
	DRAGON_BOOTS_G(22234, 22231, 11840),
	DRAGON_PLATEBODY_G(22242, 22236, 21892),
	DRAGON_KITESHIELD_G(22244, 22239, 21895),
	AMULET_OF_FURY_G(12436, 12526, 6585),
	OCCULT_NECKLACE_G(19720, 20065, 12002),
	AMULET_OF_TORTURE_G(20366, 20062, 19553),
	NECKLACE_OF_ANGUISH_G(22249, 22246, 19547),
    TORMENTED_BRACELET_G(23444, 23348, 19544),
	GUTHIX_SCIMITAR(ItemId.RUNE_SCIMITAR_23330, ItemId.RUNE_SCIMITAR_ORNAMENT_KIT_GUTHIX, ItemId.RUNE_SCIMITAR),
	SARADOMIN_SCIMITAR(ItemId.RUNE_SCIMITAR_23332, ItemId.RUNE_SCIMITAR_ORNAMENT_KIT_SARADOMIN, ItemId.RUNE_SCIMITAR),
	ZAMORAK_SCIMITAR(ItemId.RUNE_SCIMITAR_23334, ItemId.RUNE_SCIMITAR_ORNAMENT_KIT_ZAMORAK, ItemId.RUNE_SCIMITAR),
	RUNE_DEFENDER_ORNAMENT_KIT(ItemId.RUNE_DEFENDER_T, ItemId.RUNE_DEFENDER_ORNAMENT_KIT, ItemId.RUNE_DEFENDER),
	BERSERKER_NECKLACE_ORNAMENT_KIT(ItemId.BERSERKER_NECKLACE_OR, ItemId.BERSERKER_NECKLACE_ORNAMENT_KIT, ItemId.BERSERKER_NECKLACE),
	TZHAAR_KET_OM_ORNAMENT_KIT(ItemId.TZHAARKETOM_T, ItemId.TZHAARKETOM_ORNAMENT_KIT, ItemId.TZHAARKETOM),
	BANDOS_TASSETS_OR(ItemId.BANDOS_TASSETS_OR, ItemId.BANDOS_ORNAMENT_KIT, ItemId.BANDOS_TASSETS),
	BANDOS_CHESTPLATE_OR(ItemId.BANDOS_CHESTPLATE_OR, ItemId.BANDOS_ORNAMENT_KIT, ItemId.BANDOS_CHESTPLATE),
	OSMUMTENS_FANG_OR(ItemId.OSMUMTENS_FANG_OR, ItemId.CURSED_PHALANX, ItemId.OSMUMTENS_FANG, true),
	ELIDINIS_WARD_OR(ItemId.ELIDINIS_WARD_OR, ItemId.MENAPHITE_ORNAMENT_KIT, ItemId.ELIDINIS_WARD_F, true),
	ELIDINIS_WARD_F(ItemId.ELIDINIS_WARD_F, ItemId.ARCANE_SIGIL, ItemId.ELIDINIS_WARD, true),
	DRAGON_CLAWS_OR(ItemId.DRAGON_CLAWS_OR, ItemId.DRAGON_CLAWS_ORNAMENT_KIT, ItemId.DRAGON_CLAWS),
	DRAGON_WARHAMMER_OR(ItemId.DRAGON_WARHAMMER_OR, ItemId.DRAGON_WARHAMMER_ORNAMENT_KIT, ItemId.DRAGON_WARHAMMER),
	ELDER_MAUL_OR(ItemId.ELDER_MAUL_OR, ItemId.ELDER_MAUL_ORNAMENT_KIT, ItemId.ELDER_MAUL),
	HEAVY_BALLISTA_OR(ItemId.HEAVY_BALLISTA_OR, ItemId.HEAVY_BALLISTA_ORNAMENT_KIT, ItemId.HEAVY_BALLISTA),
	ABYSSAL_TENT_OR(26484, 26421, ItemId.ABYSSAL_TENTACLE, false),
	ABYSSAL_WHIP_OR(26482, 26421, ItemId.ABYSSAL_WHIP),
	BOOK_OF_BALANCE_OR(26488, 26421, ItemId.BOOK_OF_BALANCE),
	BOOK_OF_DARKNESS_OR(26490, 26421, ItemId.BOOK_OF_DARKNESS),
	BOOK_OF_LAW_OR(26492, 26421, ItemId.BOOK_OF_LAW),
	BOOK_OF_WAR_OR(26494, 26421, ItemId.BOOK_OF_WAR),
	HOLY_BOOK_OR(26496, 26421, ItemId.HOLY_BOOK),
	RUNE_CROSSBOW_OR(26486, 26421, ItemId.RUNE_CROSSBOW),
	UNHOLY_BOOK_OR(26498, 26421, ItemId.UNHOLY_BOOK),
	MYSTIC_HAT_OR(26531, 26541, ItemId.MYSTIC_HAT),
	MYSTIC_ROBE_TOP_OR(26533, 26541, ItemId.MYSTIC_ROBE_TOP),
	MYSTIC_ROBE_BOTTOM_OR(26535, 26541, ItemId.MYSTIC_ROBE_BOTTOM),
	MYSTIC_GLOVES_OR(26537, 26541, ItemId.MYSTIC_GLOVES),
	MYSTIC_BOOTS_OR(26539, 26541, ItemId.MYSTIC_BOOTS),
	VOID_KNIGHT_TOP_OR(26463, 26479, ItemId.VOID_KNIGHT_TOP, false),
	VOID_KNIGHT_ROBE_OR(26465, 26479, ItemId.VOID_KNIGHT_ROBE, false),
	VOID_KNIGHT_GLOVES_OR(26467, 26479, ItemId.VOID_KNIGHT_GLOVES, false),
	ELITE_VOID_TOP_OR(26469, 26479, ItemId.ELITE_VOID_TOP, false),
	ELITE_VOID_ROBE_OR(26471, 26479, ItemId.ELITE_VOID_ROBE, false),
	VOID_MAGE_HELM_OR(26473, 26479, ItemId.VOID_MAGE_HELM, false),
	VOID_RANGER_HELM_OR(26475, 26479, ItemId.VOID_RANGER_HELM, false),
	VOID_MELEE_HELM_OR(26477, 26479, ItemId.VOID_MELEE_HELM, false),
	CANNON_BASE_OR(26520, 26528, ItemId.CANNON_BASE),
	CANNON_STAND_OR(26522, 26528, ItemId.CANNON_STAND),
	CANNON_BARRELS_OR(26524, 26528, ItemId.CANNON_BARRELS),
	CANNON_FURNACE_OR(26526, 26528, ItemId.CANNON_FURNACE),
	MASORI_ASSEMBLER(ItemId.MASORI_ASSEMBLER, ItemId.MASORI_CRAFTING_KIT, ItemId.AVAS_ASSEMBLER, false),
	BLUE_TWISTED_BOW(ItemId.BLUE_TWISTED_BOW, ItemId.BLUE_TWISTED_BOW_PAINT, ItemId.TWISTED_BOW),
	PURPLE_TWISTED_BOW(ItemId.PURPLE_TWISTED_BOW, ItemId.PURPLE_TWISTED_BOW_PAINT, ItemId.TWISTED_BOW),
	RED_TWISTED_BOW(ItemId.RED_TWISTED_BOW, ItemId.RED_TWISTED_BOW_PAINT, ItemId.TWISTED_BOW),
	WHITE_TWISTED_BOW(ItemId.WHITE_TWISTED_BOW, ItemId.WHITE_TWISTED_BOW_PAINT, ItemId.TWISTED_BOW),
	DIVINE_KIT(ItemId.ELYSIAN_SPIRIT_SHIELD_OR, ItemId.DIVINE_KIT, ItemId.ELYSIAN_SPIRIT_SHIELD),
	TWISTED_ANCESTRAL_HAT(ItemId.TWISTED_ANCESTRAL_HAT, ItemId.TWISTED_ANCESTRAL_COLOUR_KIT, ItemId.ANCESTRAL_HAT),
	TWISTED_ANCESTRAL_ROBE_TOP(ItemId.TWISTED_ANCESTRAL_ROBE_TOP, ItemId.TWISTED_ANCESTRAL_COLOUR_KIT, ItemId.ANCESTRAL_ROBE_TOP),
	TWISTED_ANCESTRAL_ROBE_BOTTOMS(ItemId.TWISTED_ANCESTRAL_ROBE_BOTTOM, ItemId.TWISTED_ANCESTRAL_COLOUR_KIT, ItemId.ANCESTRAL_ROBE_BOTTOM),
	;

    public static final DismantleableItem[] VALUES = values();
    public static final Int2ObjectOpenHashMap<DismantleableItem> MAPPED_VALUES = new Int2ObjectOpenHashMap<>(VALUES.length);

    private final int completeItem, kit, baseItem;
    private final boolean splitOnDeath;
	private final String kitName, baseName;

	DismantleableItem(final int completeItem, final int kit, final int baseItem) {
		this(completeItem, kit, baseItem, true);
	}

    DismantleableItem(final int completeItem, final int kit, final int baseItem, boolean splitOnDeath) {
        this.completeItem = completeItem;
        this.kit = kit;
		this.kitName = ItemDefinitions.nameOfLowercase(kit);
		this.baseItem = baseItem;
		this.baseName = ItemDefinitions.nameOfLowercase(baseItem);
        this.splitOnDeath = splitOnDeath;
    }

    public int getCompleteItem() {
        return completeItem;
    }

    public int getKit() {
        return kit;
    }

    public int getBaseItem() {
        return baseItem;
    }

    public boolean isSplitOnDeath() {
        return splitOnDeath;
    }

	public String getKitName() {
		return kitName;
	}

	public String getBaseName() {
		return baseName;
	}

	static {
		for (final DismantleableItem val : VALUES) {
			MAPPED_VALUES.put(val.completeItem, val);
		}
	}

}
