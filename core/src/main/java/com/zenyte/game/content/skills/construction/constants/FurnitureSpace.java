package com.zenyte.game.content.skills.construction.constants;

import com.zenyte.game.world.object.WorldObject;

import java.util.HashMap;
import java.util.Map;

import static com.zenyte.game.content.skills.construction.constants.Furniture.*;

public enum FurnitureSpace {

	/**
	 * Garden
	 */
	SMALL_PLANT_SPACE_1(15366, PLANT, SMALL_FERN, FERN),
	SMALL_PLANT_SPACE_2(15367, DOCK_LEAF, THISTLE, REEDS),
	BIG_PLANT_SPACE_1(15364, FERN, BUSH, TALL_PLANT),
	BIG_PLANT_SPACE_2(15365, SHORT_PLANT, LARGE_LEAF_BUSH, HUGE_PLANT),
	TREE_SPACE(15363, DEAD_TREE, NICE_TREE, OAK_TREE, WILLOW_TREE, MAPLE_TREE, YEW_TREE, MAGIC_TREE),
	BIG_TREE_SPACE(15362, BIG_DEAD_TREE, BIG_NICE_TREE, BIG_OAK_TREE, BIG_WILLOW_TREE, BIG_MAPLE_TREE, BIG_YEW_TREE, BIG_MAGIC_TREE),
	CENTREPIECE_SPACE(15361, EXIT_PORTAL, DECORATIVE_ROCK, POND, IMP_STATUE, DUNGEON_ENTRANCE),
	TIP_JAR_SPACE(29119, TIP_JAR),
	/**
	 * Parlour
	 */
	CHAIR_SPACE1(4515, CRUDE_WOODEN_CHAIR, WOODEN_CHAIR, ROCKING_CHAIR, OAK_CHAIR, OAK_ARMCHAIR, TEAK_ARMCHAIR, MAHOGANY_ARMCHAIR),
	CHAIR_SPACE2(4516, CRUDE_WOODEN_CHAIR, WOODEN_CHAIR, ROCKING_CHAIR, OAK_CHAIR, OAK_ARMCHAIR, TEAK_ARMCHAIR, MAHOGANY_ARMCHAIR),
	CHAIR_SPACE3(4517, CRUDE_WOODEN_CHAIR, WOODEN_CHAIR, ROCKING_CHAIR, OAK_CHAIR, OAK_ARMCHAIR, TEAK_ARMCHAIR, MAHOGANY_ARMCHAIR),
	RUG_SPACE(new int[] { 4518, 4519, 4520 }, BROWN_RUG, RUG, OPULENT_RUG),
	CURTAIN_SPACE(4524, TORN_CURTAINS, CURTAINS, OPULENT_CURTAINS),
	FIREPLACE_SPACE(4523, CLAY_FIREPLACE, STONE_FIREPLACE, MARBLE_FIREPLACE),
	BOOKCASE_SPACE(4521, WOODEN_BOOKCASE, OAK_BOOKCASE, MAHOGANY_BOOKCASE),
	/**
	 * Kitchen
	 */
	TABLE_SPACE(15405, WOOD_KITCHEN_TABLE, OAK_KITCHEN_TABLE, TEAK_KITCHEN_TABLE),
	LARDER_SPACE(15403, WOODEN_LARDER, OAK_LARDER, TEAK_LARDER),
	SINK_SPACE(15404, PUMP_AND_DRAIN, PUMP_AND_TUB, SINK),
	SHELF_SPACE(15399, WOODEN_SHELVES_1, WOODEN_SHELVES_2, WOODEN_SHELVES_3, OAK_SHELVES_1, OAK_SHELVES_2, TEAK_SHELVES_1, TEAK_SHELVES_2),
	SHELF_SPACE2(15400, WOODEN_SHELVES_1, WOODEN_SHELVES_2, WOODEN_SHELVES_3, OAK_SHELVES_1, OAK_SHELVES_2, TEAK_SHELVES_1, TEAK_SHELVES_2),
	STOVE_SPACE(15398, FIREPIT, FIREPIT_WITH_HOOK, FIREPIT_WITH_POT, SMALL_OVEN, LARGE_OVEN, STEEL_RANGE, FANCY_RANGE),
	BARREL_SPACE(15401, BEER_BARREL, CIDER_BARREL, ASGARNIAN_ALE, GREENMANS_ALE, DRAGON_BITTER, CHEFS_DELIGHT),//TODO these have cooking requirements also
	CAT_BASKET_SPACE(15402, CAT_BLANKET, CAT_BASKET, CUSHIONED_BASKET),
	/**
	 * Workshop
	 */
	WORKBENCH_SPACE(15439, WOODEN_WORKBENCH, OAK_WORKBENCH, STEEL_FRAMED_BENCH, BENCH_WITH_VICE, BENCH_WITH_LATHE),
	HERALDRY_SPACE(15450, PLUMING_STAND, SHIELD_EASEL, BANNER_EASEL),
	REPAIR_SPACE(15448, REPAIR_BENCH, WHETSTONE, ARMOUR_STAND),
	TOOL_SPACE1(15444, TOOL_STORE_1, TOOL_STORE_2, TOOL_STORE_3, TOOL_STORE_4, TOOL_STORE_5),
	TOOL_SPACE2(15446, TOOL_STORE_1, TOOL_STORE_2, TOOL_STORE_3, TOOL_STORE_4, TOOL_STORE_5),
	TOOL_SPACE3(15443, TOOL_STORE_1, TOOL_STORE_2, TOOL_STORE_3, TOOL_STORE_4, TOOL_STORE_5),
	TOOL_SPACE4(15445, TOOL_STORE_1, TOOL_STORE_2, TOOL_STORE_3, TOOL_STORE_4, TOOL_STORE_5),
	TOOL_SPACE5(15447, TOOL_STORE_1, TOOL_STORE_2, TOOL_STORE_3, TOOL_STORE_4, TOOL_STORE_5),
	CLOCKMAKING_SPACE(15441, CRAFTING_TABLE_1, CRAFTING_TABLE_2, CRAFTING_TABLE_3, CRAFTING_TABLE_4),
	
	/**
	 * Bedroom
	 */
	BED_SPACE(15260, WOODEN_BED, OAK_BED, LARGE_OAK_BED, TEAK_BED, LARGE_TEAK_BED, REGULAR_4_POSTER, GILDED_4_POSTER),
	WARDROBE_SPACE(15261, SHOE_BOX, OAK_DRAWERS, OAK_WARDROBE, TEAK_DRAWERS, TEAK_WARDROBE, MAHOGANY_WARDROBE, GILDED_WARDROBE),
	DRESSER_SPACE(15262, SHAVING_STAND, OAK_SHAVING_STAND, OAK_DRESSER, TEAK_DRESSER, FANCY_TEAK_DRESSER, MAHOGANY_DRESSER, GILDED_DRESSER),
	BEDROOM_CURTAIN_SPACE(15263, TORN_CURTAINS, CURTAINS, OPULENT_CURTAINS),
	BEDROOM_FIREPLACE_SPACE(15267, CLAY_FIREPLACE, STONE_FIREPLACE, MARBLE_FIREPLACE),
	BEDROOM_RUG_SPACE(new int[] { 15264, 15265, 15266 }, BROWN_RUG, RUG, OPULENT_RUG),
	CORNER_SPACE(15268, OAK_CLOCK, TEAK_CLOCK, SERVANTS_MONEYBAG, GILDED_CLOCK),
	
	/**
	 * Dungeon
	 */
	TRAP_SPACE(15324, SPIKE_TRAP, MAN_TRAP, TANGLE_VINE, MARBLE_TRAP, TELEPORT_TRAP),
	TRAP_SPACE2(15325, SPIKE_TRAP, MAN_TRAP, TANGLE_VINE, MARBLE_TRAP, TELEPORT_TRAP),
	GUARD_SPACE1(15323, SKELETON_GUARD, GUARD_DOG, HOBGOBLIN_GUARD, BABY_RED_DRAGON, HUGE_SPIDER, TROLL_GUARD, HELLHOUND),
	GUARD_SPACE2(15354, SKELETON_GUARD, GUARD_DOG, HOBGOBLIN_GUARD, BABY_RED_DRAGON, HUGE_SPIDER, TROLL_GUARD, HELLHOUND),
	GUARD_SPACE3(15337, SKELETON_GUARD, GUARD_DOG, HOBGOBLIN_GUARD, BABY_RED_DRAGON, HUGE_SPIDER, TROLL_GUARD, HELLHOUND),
	GUARD_SPACE4(15336, SKELETON_GUARD, GUARD_DOG, HOBGOBLIN_GUARD, BABY_RED_DRAGON, HUGE_SPIDER, TROLL_GUARD, HELLHOUND),
	DOOR_SPACE(new int[] { 15326, 15327 }, OAK_DOOR, STEEL_PLATED_DOOR, MARBLE_DOOR),
	DOOR_SPACE2(new int[] { 15328, 15329 }, OAK_DOOR, STEEL_PLATED_DOOR, MARBLE_DOOR),
	DECORATION_SPACE(15331, DECORATIVE_BLOOD, DECORATIVE_PIPE, HANGING_SKELETON),
	LIGHTING_SPACE(15330, CANDLES, TORCHES, SKULL_TORCHES),
	LIGHTING_SPACE2(15355, CANDLES, TORCHES, SKULL_TORCHES),
	
	/**
	 * Oubliette
	 */
	FLOOR_SPACE(new int[] { 15350, 15351 }, SPIKES, TENTACLE_POOL, FLAME_PIT),
	FLOOR_SPACE_MID(15347, ROCNAR),
	PRISON_SPACE(new int[] { 15352, 15353 }, OAK_CAGE, OAK_AND_STEEL_CAGE, STEEL_CAGE, SPIKED_CAGE, BONE_CAGE),
	LADDER(15356, OAK_LADDER, TEAK_LADDER, MAHOGANY_LADDER),
	
	/**
	 * Dining room
	 */
	DINING_TABLE_SPACE(15298, WOOD_DINING_TABLE, OAK_DINING_TABLE, CARVED_OAK_TABLE, TEAK_TABLE, CARVED_TEAK_TABLE, MAHOGANY_TABLE, OPULENT_TABLE),
	DINING_DECORATION_SPACE(15303, OAK_DECORATION, TEAK_DECORATION, GILDED_DECORATION),
	SEATING_SPACE(15299, WOODEN_BENCH, OAK_BENCH, CARVED_OAK_BENCH, TEAK_DINING_BENCH, CARVED_TEAK_BENCH, MAHOGANY_BENCH, GILDED_BENCH),
	SEATING_SPACE2(15300, WOODEN_BENCH, OAK_BENCH, CARVED_OAK_BENCH, TEAK_DINING_BENCH, CARVED_TEAK_BENCH, MAHOGANY_BENCH, GILDED_BENCH),
	DINING_FIREPLACE_SPACE(15301, CLAY_FIREPLACE, STONE_FIREPLACE, MARBLE_FIREPLACE),
	DINING_CURTAIN_SPACE(15302, TORN_CURTAINS, CURTAINS, OPULENT_CURTAINS),
	BELL_PULL_SPACE(15304, ROPE_BELL_PULL, BELL_PULL, POSH_BELL_PULL),
	
	/**
	 * Skill hall
	 */
	SKILL_HALL_STAIRCASE(15380, OAK_STAIRCASE, TEAK_STAIRCASE, SPIRAL_STAIRCASE, MARBLE_STAIRCASE, MARBLE_SPIRAL),
	SKILL_HALL_STAIRCASE_DS(15381, OAK_STAIRCASE_DS, TEAK_STAIRCASE_DS, SPIRAL_STAIRCASE_DS, MARBLE_STAIRCASE_DS, MARBLE_SPIRAL_DS),
	CW_ARMOUR_SPACE(15385, CW_ARMOUR_1, CW_ARMOUR_2, CW_ARMOUR_3),
	ARMOUR_SPACE(15384, MITHRIL_ARMOUR, ADAMANT_ARMOUR, RUNITE_ARMOUR),
	HEAD_TROPHY_SPACE(15382, CRAWLING_HAND, COCKATRICE_HEAD, BASILISK_HEAD, KURASK_HEAD, ABYSSAL_HEAD, KBD_HEADS, KQ_HEAD, VORKATH_HEAD),
	FISHING_TROPHY_SPACE(15383, MOUNTED_BASS, MOUNTED_SWORDFISH, MOUNTED_SHARK),
	SKILL_RUG_SPACE(new int[] { 15377, 15378, 15379 }, RUG, OPULENT_RUG),
	RUNE_CASE_SPACE(15386, RUNE_CASE_1, RUNE_CASE_2, RUNE_CASE_3),
	
	/**
	 * Games room
	 */
	STONE_SPACE(15344,  CLAY_ATTACK_STONE, ATTACK_STONE, MARBLE_ATTACK_STONE),
	ELEMENTAL_BALANCE_SPACE(15345, MAGICAL_BALANCE_1, MAGICAL_BALANCE_2, MAGICAL_BALANCE_3),
	RANGING_GAME_SPACE(15346, HOOP_AND_STICK, DARTBOARD, ARCHERY_TARGET),
	GAME_SPACE(15342, JESTER, TREASURE_HUNT, HANGMAN),
	PRIZE_CHEST_SPACE(15343, OAK_PRIZE_CHEST, TEAK_PRIZE_CHEST, MAHOGANY_PRIZE_CHEST),
	
	/**
	 * Combat room
	 */
	COMBAT_RING_SPACE(new int[] { 15282, 15286, 15280, 15281, 15282, 15277, 15279, 15287, 15278, 15286, 15289, 15292, 15291, 15288, 15290, 15294, 15293, 15295 }, BOXING_RING, FENCING_RING, COMBAT_RING, RANGING_PEDESTALS, BALANCE_BEAM),
	STORAGE_SPACE(15296, GLOVE_RACK, WEAPONS_RACK, EXTRA_WEAPONS_RACK),
	COMBAT_DECORATION_SPACE(15297, OAK_DECORATION, TEAK_DECORATION, GILDED_DECORATION),
	COMBAT_DUMMY_SPACE(29335, COMBAT_DUMMY, UNDEAD_COMBAT_DUMMY),
	
	/**
	 * Quest hall
	 */
	QUEST_HALL_STAIRCASE(15390, OAK_STAIRCASE, TEAK_STAIRCASE, SPIRAL_STAIRCASE, MARBLE_STAIRCASE, MARBLE_SPIRAL),
	QUEST_HALL_STAIRCASE_DS(15391, OAK_STAIRCASE_DS, TEAK_STAIRCASE_DS, SPIRAL_STAIRCASE_DS, MARBLE_STAIRCASE_DS, MARBLE_SPIRAL_DS),
	GUILD_TROPHY_SPACE(15394, ANTI_DRAGON_SHIELD, AMULET_OF_GLORY, CAPE_OF_LEGENDS, MYTHICAL_CAPE),
	MAP_SPACE(15396, SMALL_MAP, MEDIUM_MAP, LARGE_MAP),
	PORTRAIT_SPACE(15392, KING_ARTHUR, ELENA, GIANT_DWARF, MISCELLANIANS),
	LANDSCAPE_SPACE(15393, LUMBRIDGE, THE_DESERT, MORYTANIA, KARAMJA, ISAFDAR),
	SWORD_SPACE(15395, SILVERLIGHT, EXCALIBUR, DARKLIGHT),
	QUEST_RUG_SPACE(new int[] { 15387, 15388, -1 }, RUG, OPULENT_RUG),
	QUEST_BOOKCASE_SPACE(15397, WOODEN_BOOKCASE, OAK_BOOKCASE, MAHOGANY_BOOKCASE),
	
	/**
	 * Menagerie
	 */
	SCRATCHING_POST_SPACE(26857, OAK_SCRATCHING_POST, TEAK_SCRATCHING_POST, MAHOGANY_SCRATCHING_POST),
	PET_FEEDER_SPACE(26869, OAK_FEEDER, TEAK_FEEDER, MAHOGANY_FEEDER),
	PET_HOUSE_SPACE(26296, OAK_HOUSE, TEAK_HOUSE, MAHOGANY_HOUSE, CONSECRATED_HOUSE, DESECRATED_HOUSE, NATURE_HOUSE),
	HABITAT_SPACE(new int[] { 26833, 26839, 26845, 26851 }, GRASSLAND_HABITAT, FOREST_HABITAT, DESERT_HABITAT, POLAR_HABITAT, VOLCANIC_HABITAT),
	ARENA_SPACE(new int[] { 26861, 26866 }, SIMPLE_ARENA, ADVANCED_ARENA, GLORIOUS_ARENA),
	PET_LIST_SPACE(26867, PET_LIST),
	
	/**
	 * Study
	 */
	GLOBE_SPACE(15421, GLOBE, ORNAMENTAL_GLOBE, LUNAR_GLOBE, CELESTIAL_GLOBE, ARMILLARY_SPHERE, SMALL_ORRERY, LARGE_ORRERY),
	LECTERN_SPACE(15420, OAK_LECTERN, EAGLE_LECTERN, DEMON_LECTERN, TEAK_EAGLE_LECTERN, TEAK_DEMON_LECTERN, MAHOGANY_EAGLE_LECTERN, MAHOGANY_DEMON_LECTERN),
	CRYSTAL_BALL_SPACE(15422, CRYSTAL_BALL, ELEMENTAL_SPHERE, CRYSTAL_OF_POWER),
	TELESCOPE_SPACE(15424, WOODEN_TELESCOPE, TEAK_TELESCOPE, MAHOGANY_TELESCOPE),
	WALL_CHART_SPACE(15423, ALCHEMICAL_CHART, ASTRONOMICAL_CHART, INFERNAL_CHART),
	STUDY_BOOKCASE_SPACE(15425, WOODEN_BOOKCASE, OAK_BOOKCASE, MAHOGANY_BOOKCASE),
	
	/**
	 * Costume room
	 */
	ARMOUR_CASE_SPACE(18815, OAK_ARMOUR_CASE, TEAK_ARMOUR_CASE, MAHOGANY_ARMOUR_CASE),
	CAPE_RACK_SPACE(18810, OAK_CAPE_RACK, TEAK_CAPE_RACK, MAHOGANY_CAPE_RACK, GILDED_CAPE_RACK, MARBLE_CAPE_RACK, MAGICAL_CAPE_RACK),
	COSTUME_BOX_SPACE(18814, OAK_COSTUME_BOX, TEAK_COSTUME_BOX, MAHOGANY_COSTUME_BOX),
	MAGICAL_WARDROBE_SPACE(18811, OAK_MAGIC_WARDROBE, CARVED_OAK_MAGIC_WARDROBE, TEAK_MAGIC_WARDROBE, CARVED_TEAK_MAGIC_WARDROBE, MAHOGANY_MAGIC_WARDROBE, GILDED_MAGIC_WARDROBE, MARBLE_MAGIC_WARDROBE),
	TOY_BOX_SPACE(18812, OAK_TOY_BOX, TEAK_TOY_BOX, MAHOGANY_TOY_BOX),
	TREASURE_CHEST_SPACE(18813, OAK_TREASURE_CHEST, TEAK_TREASURE_CHEST, MAHOGANY_TREASURE_CHEST),
	
	/**
	 * Chapel room
	 */
	LAMP_SPACE(15271, STEEL_TORCHES, WOODEN_TORCHES, STEEL_CANDLESTICKS, GOLD_CANDLESTICKS, INCENSE_BURNERS, MAHOGANY_BURNERS, MARBLE_BURNERS),
	ALTAR_SPACE(15270, OAK_ALTAR, TEAK_ALTAR, CLOTH_COVERED_ALTAR, MAHOGANY_ALTAR, LIMESTONE_ALTAR, MARBLE_ALTAR, GILDED_ALTAR),
	ICON_SPACE(15269, SARADOMIN_SYMBOL, ZAMORAK_SYMBOL, GUTHIX_SYMBOL, SARADOMIN_ICON, GUTHIX_ICON, ZAMORAK_ICON, ICON_OF_BOB),
	STATUES_SPACE(15275, SMALL_STATUES, MEDIUM_STATUES, LARGE_STATUES),
	MUSICAL_SPACE(15276, WINDCHIMES, BELLS, ORGAN),
	CHAPEL_WINDOW_SPACE(new int[] { 13730, 13731, 13732, 13733 }, SHUTTERED_WINDOW, DECORATIVE_WINDOW, STAINED_GLASS),
	CHAPEL_RUG_SPACE(new int[] { -1, 15273, 15274 }, BROWN_RUG, RUG, OPULENT_RUG),
	
	/**
	 * Portal chamber
	 */
	CENTERPIECE_SPACE(15409, TELEPORT_FOCUS, GREATER_FOCUS, SCRYING_POOL),
	PORTAL_SPACE1(15406, TEAK_PORTAL, MAHOGANY_PORTAL, MARBLE_PORTAL),
	PORTAL_SPACE2(15407, TEAK_PORTAL, MAHOGANY_PORTAL, MARBLE_PORTAL),
	PORTAL_SPACE3(15408, TEAK_PORTAL, MAHOGANY_PORTAL, MARBLE_PORTAL),
	
	/**
	 * Formal garden
	 */
	FORMAL_CENTERPIECE(15368, EXIT_PORTAL, GAZEBO, DUNGEON_ENTRANCE, SMALL_FOUNTAIN, LARGE_FOUNTAIN, POSH_FOUNTAIN),
	FORMAL_PLANT_1(15375, SMALL_SUNFLOWER, SMALL_MARIGOLDS, SMALL_ROSES),
	FORMAL_PLANT_2(15376, SMALL_ROSEMARY, SMALL_DAFFODILS, SMALL_BLUEBELLS),
	FORMAL_BIG_PLANT(15373, SUNFLOWER, MARIGOLDS, ROSES),
	FORMAL_BIG_PLANT_2(15374, ROSEMARY, DAFFODILS, BLUEBELLS),
	FORMAL_HEDGE(new int[] { 15370, 15371, 15372 }, THORNY_HEDGE, NICE_HEDGE, SMALL_BOX_HEDGE, TOPIARY_HEDGE, FANCY_HEDGE, TALL_FANCY_HEDGE, TALL_BOX_HEDGE),
	FORMAL_FENCING(15369, BOUNDARY_STONES, WOODEN_FENCE, STONE_WALL, IRON_RAILINGS, PICKET_FENCE, GARDEN_FENCE, MARBLE_WALL),
	
	/**
	 * Throne room
	 */
	THRONE_SPACE(15426, OAK_THRONE, TEAK_THRONE, MAHOGANY_THRONE, GILDED_THRONE, SKELETON_THRONE, CRYSTAL_THRONE, DEMONIC_THRONE),
	THRONE_DECORATION(15434, OAK_DECORATION, TEAK_DECORATION, GILDED_DECORATION, ROUND_SHIELD, SQUARE_SHIELD, KITE_SHIELD),
	THRONE_FLOOR(15427, FLOOR_DECORATION, THRONE_STEEL_CAGE, Furniture.TRAPDOOR, LESSER_MAGIC_CAGE, GREATER_MAGIC_CAGE),
	LEVER(15435, OAK_LEVER, TEAK_LEVER, MAHOGANY_LEVER),
	THRONE_SEATING(15436, CARVED_TEAK_BENCH, MAHOGANY_BENCH, GILDED_BENCH),
	THRONE_SEATING2(15437, CARVED_TEAK_BENCH, MAHOGANY_BENCH, GILDED_BENCH),
	TRAPDOOR(15438, OAK_TRAPDOOR, TEAK_TRAPDOOR, MAHOGANY_TRAPDOOR),
	
	/**
	 * Superior garden
	 */
	TELEPORT_SPACE(29120, SPIRIT_TREE, OBELISK, FAIRY_RING, SPIRIT_TREE_AND_FAIRY_RING),
	TOPIARY_SPACE(29121, TOPIARY_BUSH),
	POOL_SPACE(29122, RESTORATION_POOL, REVITALISATION_POOL, REJUVENATION_POOL, FANCY_REJUVENATION_POOL, ORNATE_REJUVENATION_POOL),
	THEME_SPACE(new int[] { 29123, 29124, 29125, 29126, 29127, 29128, 29129, 29130 }, ZEN_THEME, OTHERWORDLY_THEME, VOLCANIC_THEME),
	FENCING_SPACE(new int[] { 29131, 29132, 29133 }, REDWOOD_FENCE, MARBLE_WALL, OBSIDIAN_FENCE),
	SUPERIOR_SEATING_SPACE(new int[] { 29138, 29139 }, TEAK_GARDEN_BENCH, GNOME_BENCH, MARBLE_DECORATIVE_BENCH, OBSIDIAN_DECORATIVE_BENCH),
	SUPERIOR_SEATING_SPACE2(new int[] { 29136, 29137 }, TEAK_GARDEN_BENCH, GNOME_BENCH, MARBLE_DECORATIVE_BENCH, OBSIDIAN_DECORATIVE_BENCH),
	
	/**
	 * Treasure room
	 */
	MONSTER_SPACE(15257, DEMON, KALPHITE_SOLDIER, TOK_XIL, DAGANNOTH, STEEL_DRAGON, RUNE_DRAGON),
	TREASURE_SPACE(15256, WOODEN_CRATE, OAK_CHEST, TEAK_CHEST, MAHOGANY_CHEST, MAGIC_CHEST),
	TREASURE_DECORATION_SPACE(15259, ROUND_SHIELD, SQUARE_SHIELD, KITE_SHIELD),
	
	/**
	 * Achievement gallery
	 */
	ACHIEVEMENT_ALTAR_SPACE(29140, ANCIENT_ALTAR, LUNAR_ALTAR, DARK_ALTAR, OCCULT_ALTAR_ANCIENT),
	ADVENTURE_LOG_SPACE(29141, MAHOGANY_ADVENTURE_LOG, GILDED_ADVENTURE_LOG, MARBLE_ADVENTURE_LOG),
	JEWELLERY_BOX(29142, BASIC_JEWELLERY_BOX, FANCY_JEWELLERY_BOX, ORNATE_JEWELLERY_BOX),
	BOSS_LAIR_SPACE(29143, BOSS_LAIR_DISPLAY),
	DISPLAY_SPACE(29144, MOUNTED_EMBLEM, MOUNTED_COINS, CAPE_HANGER),
	QUEST_LIST_SPACE(29145, QUEST_LIST);
	
	private final Furniture[] furnitures;
	private final int[] spotIds;
	
	private static final Map<Integer, FurnitureSpace> SPACE = new HashMap<Integer, FurnitureSpace>();
	
	static {
		for (final FurnitureSpace space : values()) 
			for (final int i : space.spotIds) 
				SPACE.put(i, space);
	}

	FurnitureSpace(final int spotId, final Furniture... furnitures) {
		spotIds = new int[] { spotId };
		this.furnitures = furnitures;
	}
	
	FurnitureSpace(final int[] spotIds, final Furniture... furnitures) {
		this.spotIds = spotIds;
		this.furnitures = furnitures;
	}
	
	@Override
	public String toString() {
		return name().toLowerCase().replace("_", " ");
	}
	
	public static FurnitureSpace getSpaceByObject(final WorldObject object) {
		return SPACE.get(object.getId());
	}
	
	public Furniture[] getFurnitures() {
	    return furnitures;
	}
	
	public int[] getSpotIds() {
	    return spotIds;
	}

}
