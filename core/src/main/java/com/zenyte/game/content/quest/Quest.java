package com.zenyte.game.content.quest;

/**
 * <AUTHOR> | 16/06/2022
 */
public enum Quest {//TODO convert from structs to dbtable
    BLACK_KNIGHTS_FORTRESS(130, 10, false),
    COOKS_ASSISTANT(29, 17, false),
    DEMON_SLAYER(2561, 25, true),
    DORICS_QUEST(31, 30, false),
    DRAGON_SLAYER_I(176, 31, false),
    ERNEST_THE_CHICKEN(32, 44, false),
    GOBLIN_DIPLOMACY(2378, 64, true),
    IMP_CATCHER(160, 76, false),
    THE_KNIGHTS_SWORD(122, 83, false),
    PIRATES_TREASURE(71, 108, false),
    PRINCE_ALI_RESCUE(273, 112, false),
    THE_RESTLESS_GHOST(107, 120, false),
    ROMEO_JULIET(144, 121, false),
    RUNE_MYSTERIES(63, 125, false),
    SHEEP_SHEARER(179, 131, false),
    VAMPY<PERSON>_SLAYER(178, 155, false),
    WITCHS_POTION(67, 161, false),
    MISTHALIN_MYSTERY(3468, 94, true),
    THE_CORSAIR_CURSE(6071, 18, true),
    ANIMAL_MAGNETISM(3185, 0, true),
    BETWEEN_A_ROCK(299, 7, true),
    BIG_CHOMPY_BIRD_HUNTING(293, 8, false),
    BIOHAZARD(68, 9, false),
    CABIN_FEVER(655, 12, false),
    CLOCK_TOWER(10, 14, false),
    CONTACT(3274, 16, true),
    ZOGRE_FLESH_EATERS(487, 163, true),
    CREATURE_OF_FENKENSTRAIN(399, 19, false),
    DARKNESS_OF_HALLOWVALE(2573, 22, true),
    DEATH_TO_THE_DORGESHUUN(2258, 24, true),
    DEATH_PLATEAU(314, 23, false),
    DESERT_TREASURE(358, 27, true),
    DEVIOUS_MINDS(1465, 28, true),
    THE_DIG_SITE(131, 29, false),
    DRUIDIC_RITUAL(80, 34, false),
    DWARF_CANNON(0, 35, false),
    EADGARS_RUSE(335, 36, false),
    EAGLES_PEAK(2780, 37, true),
    ELEMENTAL_WORKSHOP_I_I(2639, 38, true),
    ENAKHRAS_LAMENT(1560, 40, true),
    ENLIGHTENED_JOURNEY(2866, 42, true),
    THE_EYES_OF_GLOUPHRIE(2497, 45, true),
    FAIRYTALE_I_GROWING_PAINS(1803, 46, true),
    FAIRYTALE_I_I_CURE_A_QUEEN(2326, 47, true),
    FAMILY_CREST(148, 48, false),
    THE_FEUD(334, 50, true),
    FIGHT_ARENA(17, 51, false),
    FISHING_CONTEST(11, 52, false),
    FORGETTABLE_TALE(822, 53, true),
    THE_FREMENNIK_TRIALS(347, 57, false),
    WATERFALL_QUEST(65, 158, false),
    GARDEN_OF_TRANQUILLITY(961, 58, true),
    GERTRUDES_CAT(180, 60, false),
    GHOSTS_AHOY(217, 62, true),
    THE_GIANT_DWARF(571, 63, true),
    THE_GOLEM(346, 65, true),
    THE_GRAND_TREE(150, 66, false),
    THE_HAND_IN_THE_SAND(1527, 69, true),
    HAUNTED_MINE(382, 70, false),
    HAZEEL_CULT(223, 71, false),
    HEROES_QUEST(188, 72, false),
    HOLY_GRAIL(5, 73, false),
    HORROR_FROM_THE_DEEP(34, 74, true),
    ICTHLARINS_LITTLE_HELPER(418, 75, true),
    IN_AID_OF_THE_MYREQUE(1990, 77, true),
    IN_SEARCH_OF_THE_MYREQUE(387, 79, false),
    JUNGLE_POTION(175, 80, false),
    LEGENDS_QUEST(139, 85, false),
    LOST_CITY(147, 86, false),
    THE_LOST_TRIBE(532, 87, true),
    LUNAR_DIPLOMACY(2448, 88, true),
    MAKING_HISTORY(1383, 92, true),
    MERLINS_CRYSTAL(14, 93, false),
    MONKEY_MADNESS_I(365, 95, false),
    MONKS_FRIEND(30, 97, false),
    MOUNTAIN_DAUGHTER(260, 98, true),
    MOURNINGS_END_PART_I(517, 99, false),
    MOURNINGS_END_PART_I_I(1103, 10, true),
    MURDER_MYSTERY(192, 101, false),
    MY_ARMS_BIG_ADVENTURE(2790, 102, true),
    NATURE_SPIRIT(307, 103, false),
    OBSERVATORY_QUEST(112, 105, false),
    ONE_SMALL_FAVOUR(416, 107, false),
    PLAGUE_CITY(165, 109, false),
    PRIEST_IN_PERIL(302, 111, false),
    RAG_AND_BONE_MAN_I(714, 114, false),
    RATCATCHERS(1404, 116, true),
    RECIPE_FOR_DISASTER(1850, 117, true),
    RECRUITMENT_DRIVE(657, 118, true),
    REGICIDE(328, 119, false),
    ROVING_ELVES(402, 122, false),
    ROYAL_TROUBLE(2140, 122, true),
    RUM_DEAL(600, 124, false),
    SCORPION_CATCHER(76, 126, false),
    SEA_SLUG(159, 127, false),
    THE_SLUG_MENACE(2610, 136, true),
    SHADES_OF_MORTTON(339, 128, false),
    SHADOW_OF_THE_STORM(1372, 129, true),
    SHEEP_HERDER(60, 130, false),
    SHILO_VILLAGE(116, 132, false),
    A_SOULS_BANE(2011, 138, true),
    SPIRITS_OF_THE_ELID(1444, 139, true),
    SWAN_SONG(2098, 140, true),
    TAI_BWO_WANNAI_TRIO(320, 141, false),
    A_TAIL_OF_TWO_CATS(1028, 142, true),
    TEARS_OF_GUTHIX(451, 145, true),
    TEMPLE_OF_IKOV(26, 146, false),
    THRONE_OF_MISCELLANIA(359, 147, false),
    THE_TOURIST_TRAP(197, 147, false),
    WITCHS_HOUSE(226, 160, false),
    TREE_GNOME_VILLAGE(111, 150, false),
    TRIBAL_TOTEM(200, 151, false),
    TROLL_ROMANCE(385, 152, false),
    TROLL_STRONGHOLD(317, 153, false),
    UNDERGROUND_PASS(161, 154, false),
    WANTED(1051, 156, true),
    WATCHTOWER(212, 157, false),
    COLD_WAR(3293, 15, true),
    THE_FREMENNIK_ISLES(3311, 56, true),
    TOWER_OF_LIFE(3337, 149, true),
    THE_GREAT_BRAIN_ROBBERY(980, 67, false),
    WHAT_LIES_BELOW(3523, 159, true),
    OLAFS_QUEST(3534, 106, true),
    ANOTHER_SLICE_OF_HAM(3550, 1, true),
    DREAM_MENTOR(3618, 33, true),
    GRIM_TALES(2783, 68, true),
    KINGS_RANSOM(3888, 82, true),
    MONKEY_MADNESS_I_I(5027, 96, true),
    CLIENT_OF_KOUREND(5619, 13, true),
    RAG_AND_BONE_MAN_I_I(714, 115, false),
    BONE_VOYAGE(5795, 11, true),
    THE_QUEEN_OF_THIEVES(6037, 113, true),
    THE_DEPTHS_OF_DESPAIR(6027, 26, true),
    DRAGON_SLAYER_I_I(6104, 32, true),
    TALE_OF_THE_RIGHTEOUS(6358, 143, true),
    A_TASTE_OF_HOPE(6396, 144, true),
    MAKING_FRIENDS_WITH_MY_ARM(6528, 91, true),
    THE_FORSAKEN_TOWER(7796, 54, true),
    THE_ASCENT_OF_ARCEUUS(7856, 3, true),
    ENTER_THE_ABYSS(492, 43, false),
    ARCHITECTURAL_ALLIANCE(4982, 2, true),
    BEAR_YOUR_SOUL(5078, 5, true),
    ALFRED_GRIMHANDS_BARCRAWL(77, 4, false),
    THE_GENERALS_SHADOW(3330, 59, true),
    IN_SEARCH_OF_KNOWLEDGE(8403, 78, true),
    SKIPPY_AND_THE_MOGRES(1344, 135, true),
    MAGE_ARENA_I(267, 89, false),
    LAIR_OF_TARN_RAZORLOR(3290, 84, true),
    FAMILY_PEST(5347, 49, true),
    MAGE_ARENA_I_I(6067, 90, true),
    DADDYS_HOME(10570, 21, true),
    X_MARKS_THE_SPOT(8063, 162, true),
    SONG_OF_THE_ELVES(9016, 137, true),
    THE_FREMENNIK_EXILES(9459, 55, true),
    SINS_OF_THE_FATHER(7255, 134, true),
    A_PORCINE_OF_INTEREST(10582, 110, true),
    GETTING_AHEAD(693, 61, true),
    BELOW_ICE_MOUNTAIN(12063, 6, true),
    A_NIGHT_AT_THE_THEATRE(12276, 104, true),
    A_KINGDOM_DIVIDED(12296, 81, true),
    ;

    public static Quest[] values = values();
    private final int variable, dbTableIndex;
    private final boolean isVarbit;

    Quest(final int variable, final int dbTableIndex, final boolean isVarbit) {
        this.variable = variable;
        this.dbTableIndex = dbTableIndex;
        this.isVarbit = isVarbit;
    }

    public int getVariable() {
        return variable;
    }

    public int getDbTableIndex() {
        return dbTableIndex;
    }

    public boolean isVarbit() {
        return isVarbit;
    }

}
