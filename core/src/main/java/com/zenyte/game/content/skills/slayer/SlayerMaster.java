package com.zenyte.game.content.skills.slayer;

import it.unimi.dsi.fastutil.ints.Int2ObjectMap;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> | 5. nov 2017 : 21:22.44
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>}
 */
public enum SlayerMaster {
    KRYSTILIA(7663, 1, 1, 25, "at Home"),
    TURAEL(401, 1, 1, 1, "at Home"),
    MAZCHNA(402, 1, 20, 2, "at Home"),
    VANNAKA(403, 1, 40, 4, "at Home"),
    CHAELDAR(404, 1, 70, 10, "at Home"),
    NIEVE(490, 1, 85, 12, "at Home"),
    DURADEL(405, 50, 100, 15, "at Home"),
    KONAR_QUO_MATEN(8623, 1, 75, 18, "at Home"),
    SUMON<PERSON>(16064, 92, 100, 15, "at Home"),
    ;

    private final int npcId;
    private final int slayerRequirement;
    private final int combatRequirement;
    private final int pointsPerTask;
    private final String location;

    SlayerMaster(int npcId, int slayerRequirement, int combatRequirement, int pointsPerTask, String location) {
        this.npcId = npcId;
        this.slayerRequirement = slayerRequirement;
        this.combatRequirement = combatRequirement;
        this.pointsPerTask = pointsPerTask;
        this.location = location;
    }

    public static boolean isMaster(final int id) {
        return mappedMasters.containsKey(id);
    }

    public final int getMultiplier(final int taskNum) {
        if (taskNum % 1000 == 0) {
            return 50;
        } else if (taskNum % 250 == 0) {
            return 35;
        } else if (taskNum % 100 == 0) {
            return 25;
        } else if (taskNum % 50 == 0) {
            return 15;
        } else if (taskNum % 10 == 0) {
            return 5;
        }
        return 1;
    }

    @Override
    public String toString() {
        if(this == SUMONA)
            return "Summona";
        return StringUtils.capitalize(name().replace('_', ' ').toLowerCase());
    }

    public int getNpcId() {
        return npcId;
    }

    public int getSlayerRequirement() {
        return slayerRequirement;
    }

    public int getCombatRequirement() {
        return combatRequirement;
    }

    public int getPointsPerTask() {
        return pointsPerTask;
    }

    public String getLocation() {
        return location;
    }

    public static final SlayerMaster[] values = values();
    public static final Int2ObjectMap<SlayerMaster> mappedMasters =
            new Int2ObjectOpenHashMap<>(values.length);

    static {
        for (final SlayerMaster master : values) {
            mappedMasters.put(master.npcId, master);
        }
    }

}
