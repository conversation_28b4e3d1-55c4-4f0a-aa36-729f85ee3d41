package com.zenyte.game.content.skills.hunter.object.plugin;

import com.zenyte.game.content.skills.hunter.HunterUtils;
import com.zenyte.game.content.skills.hunter.TrapState;
import com.zenyte.game.content.skills.hunter.actions.CheckPlacedTrap;
import com.zenyte.game.content.skills.hunter.actions.DismantlePlacedTrap;
import com.zenyte.game.content.skills.hunter.node.TrapPrey;
import com.zenyte.game.content.skills.hunter.node.TrapType;
import com.zenyte.game.content.skills.hunter.object.HunterTrap;
import com.zenyte.game.world.entity.pathfinding.events.player.ObjectEvent;
import com.zenyte.game.world.entity.pathfinding.events.player.TileEvent;
import com.zenyte.game.world.entity.pathfinding.strategy.ObjectStrategy;
import com.zenyte.game.world.entity.pathfinding.strategy.TileStrategy;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.object.WorldObject;
import com.zenyte.utils.TimeUnit;

/**
 * <AUTHOR> | 30/03/2020
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>
 */
public class BoxTrapObject implements HunterObjectPlugin {
    @Override
    public void handle(final Player player, final WorldObject object, final String name, final int optionId, final String option) {
        if (option.equalsIgnoreCase("Reset") || player.getLocation().matches(object)) {
            player.setRouteEvent(new TileEvent(player, new TileStrategy(object), getRunnable(player, object, name, optionId, option), getDelay()));
        } else {
            player.setRouteEvent(new ObjectEvent(player, new ObjectStrategy(object), getRunnable(player, object, name, optionId, option), getDelay()));
        }
    }

    @Override
    public void handleObjectAction(Player player, WorldObject object, String name, int optionId, String option) {
        final HunterTrap trap = HunterUtils.findTrap(TrapType.BOX_TRAP, object, null).orElseThrow(RuntimeException::new);
        switch (option) {
        case "Check": 
            player.getActionManager().setAction(new CheckPlacedTrap(trap, false));
            break;
        case "Investigate":
            investigateTrap(player, trap);
            break;
        case "Dismantle": 
            player.getActionManager().setAction(new DismantlePlacedTrap(trap));
            break;
        case "Reset": 
            player.getActionManager().setAction(new CheckPlacedTrap(trap, true));
            break;
        default: 
            throw new IllegalStateException(option);
        }
    }

    private void investigateTrap(Player player, HunterTrap trap) {
        final TrapState state = trap.getState();
        final int ticks = trap.getTicks();

        switch (state) {
            case ACTIVE:
                if (trap.isFreshTrap()) {
                    player.sendMessage("This trap was recently set up and is active.");
                } else {
                    player.sendMessage("This trap is active and waiting for prey.");
                }
                final long timeUntilCollapse = TimeUnit.MINUTES.toTicks(3) - ticks;
                if (timeUntilCollapse > 0) {
                    final long collapseSeconds = TimeUnit.TICKS.toSeconds(timeUntilCollapse);
                    player.sendMessage("This trap will collapse in " + collapseSeconds + " seconds.");
                } else {
                    player.sendMessage("This trap should have collapsed by now.");
                }
                break;
            case PROCESSING:
                player.sendMessage("This trap is currently being interacted with by prey.");
                break;
            default: player.sendMessage("[BUG]: This trap is in an unknown state: " + state.name()); break;
        }
    }

    @Override
    public TrapType type() {
        return TrapType.BOX_TRAP;
    }
}
