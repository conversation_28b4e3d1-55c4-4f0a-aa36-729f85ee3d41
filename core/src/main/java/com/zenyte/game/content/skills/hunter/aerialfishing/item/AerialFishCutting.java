package com.zenyte.game.content.skills.hunter.aerialfishing.item;

import com.zenyte.game.item.Item;
import com.zenyte.game.item.ItemId;
import com.zenyte.game.model.item.PairedItemOnItemPlugin;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.Action;
import com.zenyte.game.world.entity.player.SkillConstants;
import com.zenyte.game.world.entity.player.container.impl.Inventory;

/**
 * <AUTHOR>
 */

public class AerialFishCutting implements PairedItemOnItemPlugin {

    @Override
    public void handleItemOnItemAction(Player player, Item from, Item to, int fromSlot, int toSlot) {
        Item knifeItem = from.getId() == ItemId.KNIFE ? from : to;
        Item fishItem = knifeItem.getId() == from.getId() ? to : from;
        int fishId = fishItem.getId();
        if (!player.getInventory().containsItem(fishId, 1)) {
            return;
        }
        player.getActionManager().setAction(new FishCuttingAction(fishId));
    }

    /**
     * Action class that handles fish cutting with proper interruption support
     */
    private static class FishCuttingAction extends Action {
        private final int fishId;
        private int processedCount = 0;

        public FishCuttingAction(int fishId) {
            this.fishId = fishId;
        }

        @Override
        public boolean start() {
            if (!canContinue()) {
                return false;
            }
            return true;
        }

        @Override
        public boolean process() {
            return canContinue();
        }

        @Override
        public int processWithDelay() {
            if (!canContinue()) {
                return -1;
            }
            Inventory inventory = player.getInventory();
            inventory.deleteItem(fishId, 1);
            inventory.addItem(ItemId.FISH_CHUNKS, 1);
            player.getSkills().addXp(SkillConstants.COOKING, getCookingXP(fishId));
            String fishName = new Item(fishId).getName();
            player.sendFilteredMessage("You cut a " + fishName + " into chunks.");
            processedCount++;
            if (!player.getInventory().containsItem(fishId, 1)) {
                return -1;
            }
            return 1;
        }

        private boolean canContinue() {
            return player.getInventory().containsItem(fishId, 1);
        }
    }

    private static double getCookingXP(int fishItemId) {
        switch (fishItemId) {
            case ItemId.BLUEGILL:
                return AerialFish.BLUEGILL.cookingXP;
            case ItemId.COMMON_TENCH:
                return AerialFish.COMMON_TENCH.cookingXP;
            case ItemId.MOTTLED_EEL:
                return AerialFish.MOTTLED_EEL.cookingXP;
            case ItemId.GREATER_SIREN:
                return AerialFish.GREATER_SIREN.cookingXP;
            default:
                return 1;
        }
    }

    @Override
    public ItemPair[] getMatchingPairs() {
        return new ItemPair[]{
                ItemPair.of(ItemId.BLUEGILL, ItemId.KNIFE),
                ItemPair.of(ItemId.COMMON_TENCH, ItemId.KNIFE),
                ItemPair.of(ItemId.MOTTLED_EEL, ItemId.KNIFE),
                ItemPair.of(ItemId.GREATER_SIREN, ItemId.KNIFE),
        };
    }
}
