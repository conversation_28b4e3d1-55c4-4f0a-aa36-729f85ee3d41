package com.zenyte.game.content.achievementdiary;

import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.region.RSPolygon;

/**
 * <AUTHOR> | 23. sept 2018 : 21:35:32
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server
 * profile</a>
 * @see <a href="https://rune-status.net/members/kris.354/">Rune-Status
 * profile</a>
 */
public enum AreaPredicate {
    FORSAKEN_TOWER(new RSPolygon(new int[][] {{1366, 3840}, {1363, 3812}, {1397, 3808}, {1401, 3838}})),
    LOVAKENGJ_PUB(new RSPolygon(new int[][] {{1561, 3767}, {1561, 3756}, {1564, 3756}, {1564, 3751}, {1571, 3751}, {1571, 3756}, {1576, 3756}, {1576, 3760}, {1572, 3760}, {1572, 3762}, {1571, 3762}, {1571, 3767}})),
    MOUNT_KARUULM_MINE(new RSPolygon(new int[][] {{1277, 3820}, {1281, 3820}, {1286, 3815}, {1281, 3805}, {1272, 3812}})),
    MORYTANIA_TERRITORY(new RSPolygon(new int[][] {{3401, 3601}, {3402, 3509}, {3407, 3509}, {3408, 3508}, {3408, 3506}, {3410, 3504}, {3410, 3502}, {3412, 3500}, {3413, 3500}, {3415, 3498}, {3416, 3498}, {3416, 3495}, {3418, 3495}, {3418, 3494}, {3419, 3494}, {3419, 3482}, {3420, 3481}, {3422, 3481}, {3424, 3479}, {3424, 3476}, {3423, 3475}, {3422, 3475}, {3421, 3474}, {3421, 3472}, {3420, 3471}, {3418, 3471}, {3417, 3470}, {3417, 3469}, {3416, 3468}, {3413, 3468}, {3408, 3463}, {3407, 3448}, {3402, 3444}, {3406, 3437}, {3405, 3432}, {3401, 3428}, {3407, 3415}, {3401, 3406}, {3404, 3399}, {3399, 3389}, {3406, 3380}, {3396, 3361}, {3420, 3330}, {3418, 3327}, {3418, 3323}, {3419, 3313}, {3417, 3297}, {3413, 3288}, {3423, 3265}, {3423, 3208}, {3434, 3204}, {3459, 3204}, {3471, 3198}, {3474, 3191}, {3498, 3173}, {3501, 3173}, {3501, 3178}, {3507, 3180}, {3512, 3176}, {3519, 3174}, {3520, 3166}, {3533, 3162}, {3532, 3172}, {3543, 3178}, {3550, 3179}, {3562, 3173}, {3582, 3181}, {3588, 3169}, {3592, 3170}, {3593, 3177}, {3599, 3179}, {3614, 3175}, {3629, 3173}, {3636, 3182}, {3662, 3173}, {3669, 3178}, {3685, 3188}, {3701, 3183}, {3726, 3217}, {3701, 3244}, {3704, 3253}, {3687, 3259}, {3686, 3264}, {3677, 3264}, {3666, 3280}, {3660, 3282}, {3662, 3289}, {3672, 3286}, {3690, 3284}, {3722, 3268}, {3754, 3262}, {3765, 3288}, {3758, 3299}, {3773, 3307}, {3776, 3355}, {3764, 3362}, {3775, 3381}, {3770, 3408}, {3751, 3419}, {3724, 3400}, {3740, 3426}, {3726, 3450}, {3715, 3457}, {3704, 3477}, {3704, 3491}, {3717, 3491}, {3717, 3508}, {3696, 3508}, {3695, 3534}, {3685, 3534}, {3683, 3547}, {3630, 3552}, {3614, 3571}, {3596, 3573}, {3586, 3562}, {3583, 3583}, {3522, 3586}, {3486, 3579}})),
    FREMENNIK_TERRITORY(new RSPolygon(new int[][] {{2544, 3664}, {2624, 3583}, {2662, 3600}, {2669, 3596}, {2674, 3599}, {2677, 3599}, {2680, 3603}, {2684, 3603}, {2687, 3599}, {2708, 3597}, {2719, 3594}, {2727, 3594}, {2732, 3591}, {2740, 3591}, {2743, 3586}, {2753, 3590}, {2758, 3590}, {2767, 3577}, {2783, 3574}, {2808, 3623}, {2778, 3647}, {2754, 3648}, {2753, 3663}, {2728, 3734}, {2706, 3739}, {2692, 3731}, {2682, 3744}, {2660, 3738}, {2646, 3722}, {2651, 3715}, {2636, 3711}, {2642, 3702}, {2624, 3698}, {2574, 3666}})),
    LUMBRIDGE_TERRITORY(new RSPolygon(new int[][] {{3129, 3202}, {3140, 3158}, {3134, 3148}, {3137, 3136}, {3166, 3141}, {3211, 3133}, {3245, 3139}, {3259, 3195}, {3264, 3201}, {3267, 3204}, {3267, 3223}, {3268, 3224}, {3268, 3232}, {3267, 3233}, {3267, 3239}, {3266, 3240}, {3266, 3247}, {3265, 3248}, {3265, 3255}, {3266, 3255}, {3266, 3297}, {3265, 3298}, {3267, 3298}, {3267, 3322}, {3271, 3326}, {3271, 3329}, {3267, 3329}, {3266, 3330}, {3253, 3330}, {3252, 3331}, {3251, 3331}, {3250, 3332}, {3250, 3333}, {3248, 3335}, {3238, 3335}, {3234, 3331}, {3227, 3331}, {3224, 3334}, {3216, 3334}, {3210, 3340}, {3209, 3340}, {3207, 3342}, {3156, 3349}, {3153, 3346}, {3153, 3344}, {3152, 3343}, {3152, 3339}, {3153, 3338}, {3153, 3335}, {3154, 3334}, {3154, 3331}, {3153, 3330}, {3153, 3327}, {3152, 3326}, {3152, 3323}, {3153, 3322}, {3153, 3319}, {3154, 3318}, {3154, 3316}, {3156, 3314}, {3139, 3307}, {3138, 3306}, {3148, 3233}})),
    ARDOUGNE_TERRITORY(new RSPolygon(new int[][] {{2431, 3357}, {2419, 3149}, {2360, 3144}, {2310, 3040}, {2421, 3015}, {2464, 3065}, {2693, 3063}, {2689, 3226}, {2802, 3273}, {2806, 3324}, {2782, 3415}, {2646, 3404}, {2581, 3371}, {2536, 3357}})),
    KEBOS_TERRITORY(new RSPolygon(new int[][] {{1152, 4096}, {1152, 3392}, {1920, 3392}, {1920, 4096}})),
    KARAMJA_TERRITORY(new RSPolygon(new int[][] {{2749, 3130}, {2749, 2886}, {2791, 2876}, {2973, 2876}, {2989, 2917}, {2969, 2947}, {2992, 2964}, {2985, 2981}, {2972, 3004}, {2991, 3011}, {2991, 3027}, {3010, 3029}, {3004, 3066}, {2952, 3072}, {2940, 3114}, {2965, 3147}, {2962, 3161}, {2902, 3203}, {2893, 3184}, {2842, 3215}, {2819, 3204}, {2765, 3248}, {2740, 3251}, {2684, 3218}, {2688, 3198}, {2712, 3180}, {2699, 3151}})),
    WESTERN_PROVINCES_TERRITORY(new RSPolygon(new int[][] {{2303, 3706}, {2368, 3706}, {2368, 3669}, {2353, 3654}, {2414, 3596}, {2398, 3559}, {2384, 3565}, {2379, 3562}, {2387, 3552}, {2368, 3517}, {2365, 3462}, {2348, 3457}, {2342, 3405}, {2362, 3359}, {2381, 3335}, {2393, 3335}, {2432, 3324}, {2432, 3304}, {2434, 3201}, {2388, 3213}, {2365, 3185}, {2365, 3087}, {2349, 3076}, {2330, 3081}, {2284, 3032}, {2214, 3025}, {2140, 3073}, {2141, 3137}, {2110, 3149}, {2117, 3172}, {2147, 3161}, {2149, 3148}, {2159, 3157}, {2158, 3206}, {2151, 3224}, {2164, 3259}, {2157, 3269}, {2165, 3306}, {2161, 3373}, {2116, 3399}, {2116, 3439}, {2137, 3462}, {2267, 3464}, {2275, 3529}, {2261, 3575}, {2255, 3607}})),
    CANIFIS_BANK(new RSPolygon(new int[][] {{3513, 3474}, {3513, 3475}, {3514, 3476}, {3515, 3476}, {3517, 3478}, {3517, 3484}, {3509, 3484}, {3509, 3483}, {3508, 3482}, {3508, 3479}, {3509, 3478}, {3509, 3474}})),
    MOS_LE_HARMLESS(new RSPolygon(new int[][] {{3656, 3069}, {3635, 2954}, {3671, 2924}, {3689, 2940}, {3806, 2935}, {3829, 2926}, {3852, 2942}, {3858, 3026}, {3832, 3043}, {3845, 3063}, {3829, 3076}, {3798, 3065}, {3738, 3073}})),
    ELVEN_LANDS(new RSPolygon(new int[][] {{2173, 3072}, {2147, 3065}, {2140, 3127}, {2159, 3211}, {2154, 3219}, {2157, 3390}, {2309, 3392}, {2313, 3245}, {2318, 3223}, {2323, 3207}, {2319, 3196}, {2365, 3196}, {2366, 3143}, {2318, 3142}, {2291, 3115}, {2287, 3112}, {2266, 3128}, {2260, 3129}, {2256, 3125}, {2260, 3122}, {2260, 3118}, {2255, 3113}, {2243, 3125}, {2238, 3122}, {2237, 3125}, {2228, 3128}, {2219, 3123}, {2217, 3121}, {2218, 3117}, {2209, 3116}, {2206, 3121}, {2203, 3121}, {2203, 3119}, {2198, 3119}, {2195, 3121}, {2192, 3120}, {2186, 3115}, {2183, 3113}, {2175, 3113}, {2172, 3109}, {2168, 3102}, {2171, 3097}, {2171, 3094}, {2174, 3089}, {2170, 3083}})),
    PISCATORIS_MINING_AREA(new RSPolygon(new int[][] {{2319, 3648}, {2322, 3639}, {2333, 3628}, {2341, 3626}, {2349, 3643}, {2338, 3649}})),
    MORT_MYRE_SWAMP(new RSPolygon(new int[][] {{3477, 3468}, {3481, 3458}, {3482, 3449}, {3494, 3440}, {3489, 3412}, {3482, 3410}, {3482, 3388}, {3479, 3383}, {3486, 3375}, {3485, 3353}, {3475, 3325}, {3424, 3325}, {3397, 3365}, {3406, 3380}, {3400, 3391}, {3404, 3401}, {3402, 3407}, {3408, 3415}, {3402, 3427}, {3407, 3435}, {3403, 3445}, {3410, 3448}, {3415, 3456}, {3418, 3456}, {3419, 3457}, {3421, 3457}, {3423, 3459}, {3425, 3459}, {3427, 3457}, {3431, 3457}, {3432, 3456}, {3436, 3456}, {3437, 3457}, {3441, 3457}, {3442, 3458}, {3445, 3458}, {3447, 3456}, {3448, 3456}, {3449, 3457}, {3453, 3457}, {3454, 3456}, {3455, 3456}, {3459, 3460}, {3463, 3462}, {3466, 3466}, {3470, 3465}})),
    PORT_PHASMATYS(new RSPolygon(new int[][] {{3683, 3516}, {3677, 3516}, {3668, 3508}, {3655, 3508}, {3653, 3506}, {3653, 3473}, {3650, 3470}, {3650, 3458}, {3652, 3456}, {3660, 3456}, {3662, 3454}, {3665, 3454}, {3666, 3453}, {3675, 3453}, {3676, 3455}, {3693, 3455}, {3695, 3453}, {3701, 3453}, {3708, 3454}, {3711, 3457}, {3711, 3460}, {3703, 3472}, {3703, 3485}, {3705, 3485}, {3705, 3494}, {3714, 3494}, {3714, 3500}, {3705, 3500}, {3705, 3508}, {3694, 3508}, {3694, 3534}, {3687, 3534}, {3687, 3516}})),
    KELDAGRIM(new RSPolygon(new int[][] {{2816, 10240}, {2816, 10114}, {2946, 10114}, {2943, 10240}})),
    COAL_TRUCKS(new RSPolygon(new int[][] {{2573, 3490}, {2591, 3490}, {2593, 3475}, {2576, 3469}, {2571, 3480}})),
    WATERFALL_DUNGEON(new RSPolygon(new int[][] {{2557, 9918}, {2558, 9860}, {2598, 9860}, {2597, 9885}, {2577, 9918}})),
    SEERS_VILLAGE(new RSPolygon(new int[][] {{2676, 3512}, {2681, 3491}, {2672, 3467}, {2689, 3451}, {2721, 3451}, {2752, 3464}, {2752, 3478}, {2743, 3482}, {2743, 3508}, {2709, 3518}})),
    SEERS_VILLAGE_ESTATE_AGENT(new RSPolygon(new int[][] {{2735, 3506}, {2735, 3499}, {2740, 3499}, {2740, 3506}})),
    SEERS_VILLAGE_BANK(new RSPolygon(new int[][] {{2719, 3497}, {2719, 3494}, {2721, 3494}, {2721, 3490}, {2724, 3490}, {2724, 3487}, {2728, 3487}, {2728, 3490}, {2731, 3490}, {2731, 3498}, {2721, 3498}, {2721, 3497}})),
    SEERS_VILLAGE_FLAX_FIELD(new RSPolygon(new int[][] {{2736, 3450}, {2738, 3452}, {2739, 3452}, {2741, 3454}, {2743, 3454}, {2744, 3453}, {2745, 3453}, {2746, 3452}, {2752, 3452}, {2752, 3437}, {2747, 3437}, {2746, 3436}, {2742, 3436}, {2741, 3437}, {2739, 3437}, {2737, 3439}, {2737, 3445}, {2736, 3446}})),
    SEERS_VILLAGE_CATHERBY_TERRITORY(new RSPolygon(new int[][] {{2789, 3410}, {2806, 3408}, {2809, 3428}, {2830, 3432}, {2824, 3422}, {2845, 3408}, {2868, 3424}, {2865, 3436}, {2845, 3452}, {2827, 3492}, {2817, 3496}, {2799, 3488}, {2786, 3502}, {2786, 3519}, {2739, 3519}, {2734, 3508}, {2706, 3519}, {2680, 3492}, {2677, 3488}, {2677, 3484}, {2671, 3478}, {2709, 3426}, {2787, 3419}})),
    EAST_ARDOUGNE_CHURCH(new RSPolygon(new int[][] {{2609, 3311}, {2609, 3304}, {2622, 3304}, {2622, 3311}})),
    WEST_ARDOUGNE(new RSPolygon(new int[][] {{2462, 3324}, {2462, 3336}, {2559, 3336}, {2559, 3263}, {2509, 3263}, {2509, 3278}, {2459, 3278}, {2459, 3304}, {2434, 3304}, {2434, 3324}})),
    COMBAT_TRAINING_CAMP(new RSPolygon(new int[][] {{2511, 3387}, {2510, 3386}, {2509, 3386}, {2509, 3385}, {2508, 3384}, {2507, 3384}, {2503, 3380}, {2503, 3366}, {2508, 3361}, {2515, 3361}, {2516, 3360}, {2516, 3358}, {2517, 3357}, {2519, 3357}, {2520, 3358}, {2520, 3360}, {2521, 3361}, {2527, 3361}, {2534, 3368}, {2534, 3380}, {2533, 3381}, {2533, 3382}, {2530, 3385}, {2529, 3385}, {2527, 3387}})),
    WITCHHAVEN(new RSPolygon(new int[][] {{2688, 3282}, {2703, 3274}, {2706, 3268}, {2722, 3264}, {2736, 3265}, {2743, 3276}, {2742, 3313}, {2737, 3313}, {2735, 3306}, {2725, 3299}, {2727, 3315}, {2715, 3315}})),
    YANILLE(new RSPolygon(new int[][] {{2533, 3084}, {2536, 3081}, {2536, 3076}, {2537, 3075}, {2540, 3075}, {2542, 3073}, {2581, 3073}, {2583, 3071}, {2590, 3071}, {2592, 3073}, {2620, 3073}, {2622, 3075}, {2622, 3099}, {2610, 3111}, {2551, 3111}, {2551, 3119}, {2543, 3119}, {2543, 3111}, {2542, 3111}, {2540, 3109}, {2537, 3109}, {2536, 3108}, {2536, 3103}, {2533, 3100}})),
    BEDABIN_CAMP(new RSPolygon(new int[][] {{3158, 3021}, {3140, 3044}, {3165, 3066}, {3196, 3046}, {3184, 3024}})),
    VARROCK_MINE(new RSPolygon(new int[][] {{3291, 3371}, {3280, 3371}, {3281, 3370}, {3281, 3366}, {3278, 3363}, {3278, 3360}, {3279, 3359}, {3283, 3359}, {3284, 3360}, {3287, 3360}, {3291, 3356}, {3293, 3356}, {3294, 3357}, {3295, 3357}, {3295, 3360}, {3293, 3362}, {3293, 3363}, {3292, 3364}, {3292, 3365}, {3291, 3366}})),
    BARBARIAN_FISHING_SPOTS(new RSPolygon(new int[][] {{3108, 3422}, {3101, 3422}, {3107, 3438}, {3111, 3436}, {3112, 3432}, {3107, 3427}})),
    VARROCK_PALACE_ALTAR_ROOM(new RSPolygon(new int[][] {{3207, 3492}, {3207, 3498}, {3214, 3498}, {3214, 3492}}, 1)),
    VARROCK_WEST_BANK(new RSPolygon(new int[][] {{3180, 3448}, {3180, 3433}, {3191, 3433}, {3191, 3448}})),
    LUMBERYARD(new RSPolygon(new int[][] {{3300, 3492}, {3300, 3493}, {3296, 3497}, {3296, 3501}, {3293, 3504}, {3293, 3513}, {3298, 3518}, {3322, 3518}, {3326, 3514}, {3326, 3505}, {3314, 3493}, {3307, 3493}, {3306, 3492}})),
    DRAYNOR_VILLAGE(new RSPolygon(new int[][] {{3065, 3250}, {3065, 3254}, {3068, 3257}, {3068, 3267}, {3069, 3268}, {3069, 3272}, {3070, 3273}, {3070, 3285}, {3096, 3296}, {3116, 3296}, {3118, 3298}, {3133, 3291}, {3137, 3230}, {3103, 3235}, {3086, 3219}})),
    AL_KHARID_MINE(new RSPolygon(new int[][] {{3290, 3275}, {3289, 3300}, {3296, 3320}, {3302, 3320}, {3306, 3314}, {3306, 3310}, {3308, 3304}, {3308, 3302}, {3305, 3298}, {3309, 3292}, {3304, 3287}, {3309, 3282}, {3307, 3275}})),
    LUMBRIDGE_KITCHEN(new RSPolygon(new int[][] {{3205, 3218}, {3205, 3212}, {3213, 3212}, {3213, 3218}})),
    LUMBRIDGE_COW_PEN(new RSPolygon(new int[][] {{3253, 3255}, {3266, 3255}, {3266, 3297}, {3264, 3299}, {3262, 3299}, {3261, 3300}, {3257, 3300}, {3256, 3299}, {3241, 3299}, {3240, 3298}, {3240, 3296}, {3241, 3295}, {3241, 3294}, {3242, 3293}, {3242, 3290}, {3241, 3289}, {3241, 3288}, {3240, 3287}, {3240, 3285}, {3244, 3281}, {3244, 3280}, {3246, 3278}, {3249, 3278}, {3251, 3276}, {3251, 3274}, {3253, 3272}})),
    AL_KHARID_FISHING_AREA(new RSPolygon(new int[][] {{3282, 3135}, {3268, 3142}, {3264, 3153}, {3276, 3148}})),
    DRAYNOR_SEWERS(new RSPolygon(new int[][] {{3080, 9674}, {3082, 9676}, {3107, 9676}, {3109, 9678}, {3109, 9682}, {3108, 9683}, {3107, 9683}, {3104, 9686}, {3104, 9695}, {3107, 9698}, {3118, 9698}, {3121, 9695}, {3121, 9686}, {3118, 9683}, {3117, 9683}, {3116, 9682}, {3116, 9678}, {3118, 9676}, {3125, 9676}, {3127, 9674}, {3127, 9644}, {3126, 9643}, {3115, 9643}, {3115, 9650}, {3119, 9650}, {3120, 9651}, {3120, 9667}, {3118, 9669}, {3082, 9669}, {3080, 9671}})),
    AL_KHARID_PALACE(new RSPolygon(new int[][] {{3281, 3179}, {3281, 3158}, {3305, 3158}, {3305, 3179}})),
    LUMBRIDGE_BASEMENT(new RSPolygon(new int[][] {{3207, 9625}, {3207, 9614}, {3221, 9614}, {3221, 9625}, {3218, 9625}, {3218, 9627}, {3210, 9627}, {3210, 9625}})),
    CLAN_WARS(new RSPolygon(new int[][] {{3346, 3161}, {3347, 3165}, {3356, 3169}, {3358, 3175}, {3368, 3181}, {3376, 3178}, {3391, 3190}, {3402, 3189}, {3412, 3196}, {3414, 3189}, {3420, 3185}, {3413, 3172}, {3411, 3163}, {3407, 3160}, {3397, 3164}, {3393, 3163}, {3394, 3147}, {3386, 3137}, {3382, 3124}, {3376, 3126}, {3375, 3129}, {3367, 3131}, {3364, 3128}, {3353, 3124}, {3352, 3126}, {3364, 3136}, {3363, 3139}, {3352, 3148}})),
    MAGE_TRAINING_ARENA(new RSPolygon(new int[][] {{3353, 3296}, {3354, 3295}, {3361, 3295}, {3361, 3294}, {3366, 3294}, {3366, 3295}, {3373, 3295}, {3374, 3296}, {3374, 3324}, {3373, 3325}, {3354, 3325}, {3353, 3324}})),
    WISE_OLD_MAN_HOUSE(new RSPolygon(new int[][] {{3087, 3256}, {3087, 3251}, {3095, 3251}, {3095, 3256}})),
    BRIMHAVEN(new RSPolygon(new int[][] {{2735, 3131}, {2774, 3150}, {2773, 3165}, {2796, 3142}, {2816, 3152}, {2816, 3200}, {2795, 3220}, {2777, 3226}, {2777, 3237}, {2759, 3243}, {2746, 3216}, {2754, 3246}, {2739, 3253}, {2705, 3215}, {2688, 3222}, {2689, 3199}, {2702, 3199}, {2700, 3191}, {2717, 3183}, {2699, 3152}})),
    BRIMHAVEN_DUNGEON(new RSPolygon(new int[][] {{2751, 9506}, {2751, 9409}, {2627, 9409}, {2627, 9469}, {2649, 9473}, {2635, 9481}, {2635, 9495}, {2644, 9503}, {2640, 9511}, {2628, 9518}, {2628, 9549}, {2647, 9565}, {2631, 9584}, {2654, 9602}, {2688, 9597}, {2689, 9580}, {2716, 9566}, {2731, 9525}})),
    TZHAAR_CITY(new RSPolygon(new int[][] {{2398, 5177}, {2398, 5183}, {2429, 5183}, {2432, 5177}, {2435, 5177}, {2443, 5186}, {2460, 5179}, {2509, 5182}, {2514, 5169}, {2538, 5168}, {2534, 5152}, {2564, 5130}, {2551, 5117}, {2543, 5097}, {2548, 5087}, {2541, 5070}, {2501, 5055}, {2452, 5083}, {2430, 5121}, {2438, 5160}, {2420, 5177}})),
    HORSESHOE_MINE(new RSPolygon(new int[][] {{2731, 3226}, {2733, 3228}, {2735, 3228}, {2737, 3226}, {2737, 3224}, {2735, 3222}, {2732, 3222}, {2731, 3223}})),
    KARAMJA_FISHING_AREA(new RSPolygon(new int[][] {{2922, 3178}, {2923, 3182}, {2930, 3182}, {2926, 3173}, {2923, 3175}})),
    FOUNTAIN_OF_RUNE(new RSPolygon(new int[][] {{3367, 3901}, {3367, 3890}, {3380, 3890}, {3380, 3901}})),
    WILDERNESS_WEST_CHAOS_ALTAR(new RSPolygon(new int[][] {{2947, 3823}, {2948, 3823}, {2949, 3824}, {2949, 3825}, {2950, 3825}, {2951, 3826}, {2952, 3825}, {2953, 3825}, {2953, 3824}, {2954, 3823}, {2958, 3823}, {2958, 3819}, {2954, 3819}, {2953, 3818}, {2953, 3817}, {2952, 3817}, {2951, 3816}, {2950, 3817}, {2949, 3817}, {2949, 3818}, {2948, 3819}, {2947, 3819}})),
    WILDERNESS_RESOURCE_AREA(new RSPolygon(new int[][] {{3174, 3944}, {3174, 3925}, {3175, 3924}, {3196, 3924}, {3197, 3925}, {3197, 3944}, {3196, 3945}, {3175, 3945}})),
    JATISZO_MINE(new RSPolygon(new int[][] {{2369, 10236}, {2375, 10179}, {2427, 10185}, {2429, 10232}})),
    LAVA_DRAGON_ISLE(new RSPolygon(new int[][] {{3194, 3855}, {3187, 3847}, {3197, 3843}, {3197, 3838}, {3188, 3845}, {3178, 3836}, {3188, 3825}, {3186, 3824}, {3180, 3830}, {3173, 3821}, {3175, 3816}, {3173, 3811}, {3179, 3802}, {3183, 3804}, {3189, 3801}, {3192, 3799}, {3195, 3801}, {3198, 3807}, {3200, 3809}, {3209, 3803}, {3212, 3803}, {3224, 3813}, {3225, 3822}, {3226, 3825}, {3232, 3831}, {3232, 3837}, {3235, 3842}, {3231, 3848}, {3215, 3854}, {3212, 3856}, {3198, 3856}}));
    private final RSPolygon polygon;

    AreaPredicate(RSPolygon polygon) {
        this.polygon = polygon;
    }

    public static boolean inAreas(final Player player, final AreaPredicate... areas) {
        for (final AreaPredicate area : areas) {
            if (area.getPolygon().contains(player.getLocation())) {
                return true;
            }
        }
        return false;
    }

    public boolean inArea(final Player player) {
        return polygon.contains(player.getLocation());
    }

    public RSPolygon getPolygon() {
        return polygon;
    }
}
