package com.zenyte.game.content.skills.herblore;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static com.zenyte.game.content.skills.herblore.PoisonAttachement.*;

/**
 * <AUTHOR> | 17-3-2019 | 19:46
 * @see <a href="https://www.rune-server.ee/members/tommeh/">Rune-Server profile</a>}
 */
public enum PoisonableWeapon {
    BRONZE_DART_P(806, 812, POISON), IRON_DART_P(807, 813, POISON), STEEL_DART_P(808, 814, POISON), MITHRIL_DART_P(809, 815, POISON), ADAMANT_DART_P(810, 816, POISON), RUNE_DART_P(811, 817, POISON), BRONZE_JAVELIN_P(825, 831, POISON), IRON_JAVELIN_P(826, 832, PO<PERSON><PERSON>), STEEL_JAVELIN_P(827, 833, <PERSON>O<PERSON><PERSON>), MITHRIL_JAVELIN_P(828, 834, POISON), ADAMANT_JAVELIN_P(829, 835, POISON), RUNE_JAVELIN_P(830, 836, POISON), BRONZE_KNIFE_P(864, 870, POISON), IRON_KNIFE_P(863, 871, POISON), STEEL_KNIFE_P(865, 872, POISON), MITHRIL_KNIFE_P(866, 873, POISON), BLACK_KNIFE_P(869, 874, POISON), ADAMANT_KNIFE_P(867, 875, POISON), RUNE_KNIFE_P(868, 876, POISON), BRONZE_BOLTS_P(877, 878, POISON), BRONZE_ARROW_P(882, 883, POISON), IRON_ARROW_P(884, 885, POISON), STEEL_ARROW_P(886, 887, POISON), MITHRIL_ARROW_P(888, 889, POISON), ADAMANT_ARROW_P(890, 891, POISON), RUNE_ARROW_P(892, 893, POISON), IRON_DAGGER_P(1203, 1219, POISON), BRONZE_DAGGER_P(1205, 1221, POISON), STEEL_DAGGER_P(1207, 1223, POISON), MITHRIL_DAGGER_P(1209, 1225, POISON), ADAMANT_DAGGER_P(1211, 1227, POISON), RUNE_DAGGER_P(1213, 1229, POISON), DRAGON_DAGGER_P(1215, 1231, POISON), BLACK_DAGGER_P(1217, 1233, POISON), BRONZE_SPEAR_P(1237, 1251, POISON), IRON_SPEAR_P(1239, 1253, POISON), STEEL_SPEAR_P(1241, 1255, POISON), MITHRIL_SPEAR_P(1243, 1257, POISON), ADAMANT_SPEAR_P(1245, 1259, POISON), RUNE_SPEAR_P(1247, 1261, POISON), DRAGON_SPEAR_P(1249, 1263, POISON), BLACK_DART_P(3093, 3094, POISON), BLACK_SPEAR_P(4580, 4582, POISON), BRONZE_ARROW_P_PLUS(882, 5616, POISON_PLUS), IRON_ARROW_P_PLUS(884, 5617, POISON_PLUS), STEEL_ARROW_P_PLUS(886, 5618, POISON_PLUS), MITHRIL_ARROW_P_PLUS(888, 5619, POISON_PLUS), ADAMANT_ARROW_P_PLUS(890, 5620, POISON_PLUS), RUNE_ARROW_P_PLUS(892, 5621, POISON_PLUS), BRONZE_ARROW_P_PLUS_PLUS(882, 5622, POISON_PLUS_PLUS), IRON_ARROW_P_PLUS_PLUS(884, 5623, POISON_PLUS_PLUS), STEEL_ARROW_P_PLUS_PLUS(886, 5624, POISON_PLUS_PLUS), MITHRIL_ARROW_P_PLUS_PLUS(888, 5625, POISON_PLUS_PLUS), ADAMANT_ARROW_P_PLUS_PLUS(890, 5626, POISON_PLUS_PLUS), RUNE_ARROW_P_PLUS_PLUS(892, 5627, POISON_PLUS_PLUS), BRONZE_DART_P_PLUS(806, 5628, POISON_PLUS), IRON_DART_P_PLUS(807, 5629, POISON_PLUS), STEEL_DART_P_PLUS(808, 5630, POISON_PLUS), BLACK_DART_P_PLUS(3093, 5631, POISON_PLUS), MITHRIL_DART_P_PLUS(809, 5632, POISON_PLUS), ADAMANT_DART_P_PLUS(810, 5633, POISON_PLUS), RUNE_DART_P_PLUS(811, 5634, POISON_PLUS), BRONZE_DART_P_PLUS_PLUS(806, 5635, POISON_PLUS_PLUS), IRON_DART_P_PLUS_PLUS(807, 5636, POISON_PLUS_PLUS), STEEL_DART_P_PLUS_PLUS(808, 5637, POISON_PLUS_PLUS), BLACK_DART_P_PLUS_PLUS(3093, 5638, POISON_PLUS_PLUS), MITHRIL_DART_P_PLUS_PLUS(809, 5639, POISON_PLUS_PLUS), ADAMANT_DART_P_PLUS_PLUS(810, 5640, POISON_PLUS_PLUS), RUNE_DART_P_PLUS_PLUS(811, 5641, POISON_PLUS_PLUS), BRONZE_JAVELIN_P_PLUS(825, 5642, POISON_PLUS), IRON_JAVELIN_P_PLUS(826, 5643, POISON_PLUS), STEEL_JAVELIN_P_PLUS(827, 5644, POISON_PLUS), MITHRIL_JAVELIN_P_PLUS(828, 5645, POISON_PLUS), ADAMANT_JAVELIN_P_PLUS(829, 5646, POISON_PLUS), RUNE_JAVELIN_P_PLUS(830, 5647, POISON_PLUS), BRONZE_JAVELIN_P_PLUS_PLUS(825, 5648, POISON_PLUS_PLUS), IRON_JAVELIN_P_PLUS_PLUS(826, 5649, POISON_PLUS_PLUS), STEEL_JAVELIN_P_PLUS_PLUS(827, 5650, POISON_PLUS_PLUS), MITHRIL_JAVELIN_P_PLUS_PLUS(828, 5651, POISON_PLUS_PLUS), ADAMANT_JAVELIN_P_PLUS_PLUS(829, 5652, POISON_PLUS_PLUS), RUNE_JAVELIN_P_PLUS_PLUS(830, 5653, POISON_PLUS_PLUS), BRONZE_KNIFE_P_PLUS(864, 5654, POISON_PLUS), IRON_KNIFE_P_PLUS(863, 5655, POISON_PLUS), STEEL_KNIFE_P_PLUS(865, 5656, POISON_PLUS), MITHRIL_KNIFE_P_PLUS(866, 5657, POISON_PLUS), BLACK_KNIFE_P_PLUS(869, 5658, POISON_PLUS), ADAMANT_KNIFE_P_PLUS(867, 5659, POISON_PLUS), RUNE_KNIFE_P_PLUS(868, 5660, POISON_PLUS), BRONZE_KNIFE_P_PLUS_PLUS(864, 5661, POISON_PLUS_PLUS), IRON_KNIFE_P_PLUS_PLUS(863, 5662, POISON_PLUS_PLUS), STEEL_KNIFE_P_PLUS_PLUS(865, 5663, POISON_PLUS_PLUS), MITHRIL_KNIFE_P_PLUS_PLUS(866, 5664, POISON_PLUS_PLUS), BLACK_KNIFE_P_PLUS_PLUS(869, 5665, POISON_PLUS_PLUS), ADAMANT_KNIFE_P_PLUS_PLUS(867, 5666, POISON_PLUS_PLUS), RUNE_KNIFE_P_PLUS_PLUS(868, 5667, POISON_PLUS_PLUS), IRON_DAGGER_P_PLUS(1203, 5668, POISON_PLUS), BRONZE_DAGGER_P_PLUS(1205, 5670, POISON_PLUS), STEEL_DAGGER_P_PLUS(1207, 5672, POISON_PLUS), MITHRIL_DAGGER_P_PLUS(1209, 5674, POISON_PLUS), ADAMANT_DAGGER_P_PLUS(1211, 5676, POISON_PLUS), RUNE_DAGGER_P_PLUS(1213, 5678, POISON_PLUS), DRAGON_DAGGER_P_PLUS(1215, 5680, POISON_PLUS), BLACK_DAGGER_P_PLUS(1217, 5682, POISON_PLUS), IRON_DAGGER_P_PLUS_PLUS(1203, 5686, POISON_PLUS_PLUS), BRONZE_DAGGER_P_PLUS_PLUS(1205, 5688, POISON_PLUS_PLUS), STEEL_DAGGER_P_PLUS_PLUS(1207, 5690, POISON_PLUS_PLUS), MITHRIL_DAGGER_P_PLUS_PLUS(1209, 5692, POISON_PLUS_PLUS), ADAMANT_DAGGER_P_PLUS_PLUS(1211, 5694, POISON_PLUS_PLUS), RUNE_DAGGER_P_PLUS_PLUS(1213, 5696, POISON_PLUS_PLUS), DRAGON_DAGGER_P_PLUS_PLUS(1215, 5698, POISON_PLUS_PLUS), BLACK_DAGGER_P_PLUS_PLUS(1217, 5700, POISON_PLUS_PLUS), BRONZE_SPEAR_P_PLUS(1237, 5704, POISON_PLUS), IRON_SPEAR_P_PLUS(1239, 5706, POISON_PLUS), STEEL_SPEAR_P_PLUS(1241, 5708, POISON_PLUS), MITHRIL_SPEAR_P_PLUS(1243, 5710, POISON_PLUS), ADAMANT_SPEAR_P_PLUS(1245, 5712, POISON_PLUS), RUNE_SPEAR_P_PLUS(1247, 5714, POISON_PLUS), DRAGON_SPEAR_P_PLUS(1249, 5716, POISON_PLUS), BRONZE_SPEAR_P_PLUS_PLUS(1237, 5718, POISON_PLUS_PLUS), IRON_SPEAR_P_PLUS_PLUS(1239, 5720, POISON_PLUS_PLUS), STEEL_SPEAR_P_PLUS_PLUS(1241, 5722, POISON_PLUS_PLUS), MITHRIL_SPEAR_P_PLUS_PLUS(1243, 5724, POISON_PLUS_PLUS), ADAMANT_SPEAR_P_PLUS_PLUS(1245, 5726, POISON_PLUS_PLUS), RUNE_SPEAR_P_PLUS_PLUS(1247, 5728, POISON_PLUS_PLUS), DRAGON_SPEAR_P_PLUS_PLUS(1249, 5730, POISON_PLUS_PLUS), BLACK_SPEAR_P_PLUS(4580, 5734, POISON_PLUS), BLACK_SPEAR_P_PLUS_PLUS(4580, 5736, POISON_PLUS_PLUS), BRONZE_BOLTS_P_PLUS(877, 6061, POISON_PLUS), BRONZE_BOLTS_P_PLUS_PLUS(877, 6062, POISON_PLUS_PLUS), WHITE_DAGGER_P(6591, 6593, POISON), WHITE_DAGGER_P_PLUS(6591, 6595, POISON_PLUS), WHITE_DAGGER_P_PLUS_PLUS(6591, 6597, POISON_PLUS_PLUS), BONE_DAGGER_P(8872, 8874, POISON), BONE_DAGGER_P_PLUS(8872, 8876, POISON_PLUS), BONE_DAGGER_P_PLUS_PLUS(8872, 8878, POISON_PLUS_PLUS), BLURITE_BOLTS_P(9139, 9286, POISON), IRON_BOLTS_P(9140, 9287, POISON), STEEL_BOLTS_P(9141, 9288, POISON), MITHRIL_BOLTS_P(9142, 9289, POISON), ADAMANT_BOLTS_P(9143, 9290, POISON), RUNITE_BOLTS_P(9144, 9291, POISON), SILVER_BOLTS_P(9145, 9292, POISON), BLURITE_BOLTS_P_PLUS(9139, 9293, POISON_PLUS), IRON_BOLTS_P_PLUS(9140, 9294, POISON_PLUS), STEEL_BOLTS_P_PLUS(9141, 9295, POISON_PLUS), MITHRIL_BOLTS_P_PLUS(9142, 9296, POISON_PLUS), ADAMANT_BOLTS_P_PLUS(9143, 9297, POISON_PLUS), RUNITE_BOLTS_P_PLUS(9144, 9298, POISON_PLUS), SILVER_BOLTS_P_PLUS(9145, 9299, POISON_PLUS), BLURITE_BOLTS_P_PLUS_PLUS(9139, 9300, POISON_PLUS_PLUS), IRON_BOLTS_P_PLUS_PLUS(9140, 9301, POISON_PLUS_PLUS), STEEL_BOLTS_P_PLUS_PLUS(9141, 9302, POISON_PLUS_PLUS), MITHRIL_BOLTS_P_PLUS_PLUS(9142, 9303, POISON_PLUS_PLUS), ADAMANT_BOLTS_P_PLUS_PLUS(9143, 9304, POISON_PLUS_PLUS), RUNITE_BOLTS_P_PLUS_PLUS(9144, 9305, POISON_PLUS_PLUS), SILVER_BOLTS_P_PLUS_PLUS(9145, 9306, POISON_PLUS_PLUS), KERIS_P(10581, 10582, POISON), KERIS_P_PLUS(10581, 10583, POISON_PLUS), KERIS_P_PLUS_PLUS(10581, 10584, POISON_PLUS_PLUS), DRAGON_ARROW_P(11212, 11227, POISON), DRAGON_ARROW_P_PLUS(11212, 11228, POISON_PLUS), DRAGON_ARROW_P_PLUS_PLUS(11212, 11229, POISON_PLUS_PLUS), DRAGON_DART_P(11230, 11231, POISON), DRAGON_DART_P_PLUS(11230, 11233, POISON_PLUS), DRAGON_DART_P_PLUS_PLUS(11230, 11234, POISON_PLUS_PLUS), BRONZE_HASTA_P(11367, 11379, POISON), BRONZE_HASTA_P_PLUS(11367, 11382, POISON_PLUS), BRONZE_HASTA_P_PLUS_PLUS(11367, 11384, POISON_PLUS_PLUS), IRON_HASTA_P(11369, 11386, POISON), IRON_HASTA_P_PLUS(11369, 11389, POISON_PLUS), IRON_HASTA_P_PLUS_PLUS(11369, 11391, POISON_PLUS_PLUS), STEEL_HASTA_P(11371, 11393, POISON), STEEL_HASTA_P_PLUS(11371, 11396, POISON_PLUS), STEEL_HASTA_P_PLUS_PLUS(11371, 11398, POISON_PLUS_PLUS), MITHRIL_HASTA_P(11373, 11400, POISON), MITHRIL_HASTA_P_PLUS(11373, 11403, POISON_PLUS), MITHRIL_HASTA_P_PLUS_PLUS(11373, 11405, POISON_PLUS_PLUS), ADAMANT_HASTA_P(11375, 11407, POISON), ADAMANT_HASTA_P_PLUS(11375, 11410, POISON_PLUS), ADAMANT_HASTA_P_PLUS_PLUS(11375, 11412, POISON_PLUS_PLUS), RUNE_HASTA_P(11377, 11414, POISON), RUNE_HASTA_P_PLUS(11377, 11417, POISON_PLUS), RUNE_HASTA_P_PLUS_PLUS(11377, 11419, POISON_PLUS_PLUS), ABYSSAL_DAGGER_P(13265, 13267, POISON), ABYSSAL_DAGGER_P_PLUS(13265, 13269, POISON_PLUS), ABYSSAL_DAGGER_P_PLUS_PLUS(13265, 13271, POISON_PLUS_PLUS), DRAGON_JAVELIN_P(19484, 19486, POISON), DRAGON_JAVELIN_P_PLUS(19484, 19488, POISON_PLUS), DRAGON_JAVELIN_P_PLUS_PLUS(19484, 19490, POISON_PLUS_PLUS), AMETHYST_JAVELIN_P(21318, 21320, POISON), AMETHYST_JAVELIN_P_PLUS(21318, 21322, POISON_PLUS), AMETHYST_JAVELIN_P_PLUS_PLUS(21318, 21324, POISON_PLUS_PLUS), AMETHYST_ARROW_P(21326, 21332, POISON), AMETHYST_ARROW_P_PLUS(21326, 21334, POISON_PLUS), AMETHYST_ARROW_P_PLUS_PLUS(21326, 21336, POISON_PLUS_PLUS), DRAGON_BOLTS_P(21905, 21924, POISON), DRAGON_BOLTS_P_PLUS(21905, 21926, POISON_PLUS), DRAGON_BOLTS_P_PLUS_PLUS(21905, 21928, POISON_PLUS_PLUS), DRAGON_HASTA_P(22731, 22734, POISON), DRAGON_HASTA_P_PLUS(22731, 22737, POISON_PLUS), DRAGON_HASTA_P_PLUS_PLUS(22731, 22740, POISON_PLUS_PLUS), DRAGON_KNIFE_P(22804, 22806, POISON), DRAGON_KNIFE_P_PLUS(22804, 22808, POISON_PLUS), DRAGON_KNIFE_P_PLUS_PLUS(22804, 22810, POISON_PLUS_PLUS);
    private final int base;
    private final int id;
    private final PoisonAttachement attachement;
    public static final Set<PoisonableWeapon> ALL = EnumSet.allOf(PoisonableWeapon.class);
    private static final Map<Integer, PoisonableWeapon> MAP = new HashMap<>();

    static {
        for (final PoisonableWeapon weapon : ALL) {
            MAP.put(weapon.getId(), weapon);
        }
    }

    public static PoisonableWeapon get(final int id) {
        return MAP.get(id);
    }

    public static PoisonableWeapon get(final int id, final PoisonAttachement attachement) {
        for (final PoisonableWeapon weapon : ALL) {
            if (weapon.getBase() == id && weapon.getAttachement().equals(attachement)) {
                return weapon;
            }
        }
        return null;
    }

    PoisonableWeapon(int base, int id, PoisonAttachement attachement) {
        this.base = base;
        this.id = id;
        this.attachement = attachement;
    }

    public int getBase() {
        return base;
    }

    public int getId() {
        return id;
    }

    public PoisonAttachement getAttachement() {
        return attachement;
    }
}
