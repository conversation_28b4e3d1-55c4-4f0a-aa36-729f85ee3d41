package com.zenyte.game.content.minigame.zmi_altar;

import com.zenyte.game.content.achievementdiary.DiaryComplexity;
import com.zenyte.game.content.achievementdiary.diaries.ArdougneDiary;
import com.zenyte.game.item.Item;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.entity.masks.Animation;
import com.zenyte.game.world.entity.masks.Graphics;
import com.zenyte.game.world.entity.player.Action;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.SkillConstants;

/**
 * <AUTHOR> (Discord: imslickk) - 7/25/2025
 */
public class ZmiRunecraftingAction extends Action {
    
    private static final Animation RUNECRAFTING_ANIM = new Animation(791);
    private static final Graphics RUNECRAFTING_GFX = new Graphics(186, 0, 96);
    
    /**
     * Enum representing all runes that can be created at the ZMI altar
     *   along with the Ardougne medium diary bonus rates
     */

    public enum ZmiRune {
        AIR(556, 5.0, 0.25),
        MIND(558, 5.5, 0.25),
        WATER(555, 6.0, 0.25),
        EARTH(557, 6.5, 0.25),
        FIRE(554, 7.0, 0.25),
        BODY(559, 7.5, 0.25),
        COSMIC(564, 8.0, 0.25),
        CHAOS(562, 8.5, 0.25),
        ASTRAL(9075, 8.7, 0.25),
        NATURE(561, 9.0, 0.225),
        LAW(563, 9.5, 0.20),
        DEATH(560, 10.0, 0.175),
        BLOOD(565, 10.5, 0.15),
        SOUL(566, 29.7, 0.10);

        private final int runeId;
        private final double experience;
        private final double ardougneBonusChance;

        public static final ZmiRune[] VALUES = values();

        ZmiRune(int runeId, double experience, double ardougneBonusChance) {
            this.runeId = runeId;
            this.experience = experience;
            this.ardougneBonusChance = ardougneBonusChance;
        }

        public int getRuneId() {
            return runeId;
        }

        public double getExperience() {
            return experience;
        }

        public double getArdougneBonusChance() {
            return ardougneBonusChance;
        }
    }

    /**
     * Enum representing essence types that can be used at the ZMI altar
     */

    public enum ZmiEssence {
        PURE_ESSENCE(7936, 1.0, "pure essence"),
        DAEYALT_ESSENCE(24704, 1.5, "daeyalt essence");

        private final int itemId;
        private final double experienceMultiplier;
        private final String displayName;

        public static final ZmiEssence[] VALUES = values();

        ZmiEssence(int itemId, double experienceMultiplier, String displayName) {
            this.itemId = itemId;
            this.experienceMultiplier = experienceMultiplier;
            this.displayName = displayName;
        }

        public int getItemId() {
            return itemId;
        }

        public double getExperienceMultiplier() {
            return experienceMultiplier;
        }

        public String getDisplayName() {
            return displayName;
        }

        public static ZmiEssence getByItemId(int itemId) {
            for (ZmiEssence essence : VALUES) {
                if (essence.getItemId() == itemId) {
                    return essence;
                }
            }
            return null;
        }
    }

    @Override
    public boolean start() {
        if (!player.getInventory().containsItem(ZmiEssence.PURE_ESSENCE.getItemId(), 1) &&
            !player.getInventory().containsItem(ZmiEssence.DAEYALT_ESSENCE.getItemId(), 1)) {
            player.sendMessage("You need pure essence or daeyalt essence to craft runes at this altar.");
            return false;
        }

        player.setAnimation(RUNECRAFTING_ANIM);
        player.setGraphics(RUNECRAFTING_GFX);
        return true;
    }

    @Override
    public boolean process() {
        return true;
    }

    @Override
    public int processWithDelay() {
        ZmiEssence essence = null;
        if (player.getInventory().containsItem(ZmiEssence.DAEYALT_ESSENCE.getItemId(), 1)) {
            essence = ZmiEssence.DAEYALT_ESSENCE;
        } else if (player.getInventory().containsItem(ZmiEssence.PURE_ESSENCE.getItemId(), 1)) {
            essence = ZmiEssence.PURE_ESSENCE;
        }
        if (essence == null) {
            return -1;
        }
        int essenceAmount = player.getInventory().getAmountOf(essence.getItemId());
        if (essenceAmount == 0) {
            return -1;
        }
        player.getInventory().deleteItem(essence.getItemId(), essenceAmount);
        player.sendFilteredMessage("You bind the temple's power into runes.");
        double totalExperience = 0;
        int totalRunes = 0;
        for (int i = 0; i < essenceAmount; i++) {
            ZmiRune randomRune = getRandomRune(player.getSkills().getLevel(SkillConstants.RUNECRAFTING));
            double experience = randomRune.getExperience() * 1.7 * essence.getExperienceMultiplier();
            totalExperience += experience;
            int runeAmount = 1;
            if (player.getAchievementDiaries().isAllSetCompleted(DiaryComplexity.MEDIUM, ArdougneDiary.VALUES)) {
                if (Utils.randomDouble() < randomRune.getArdougneBonusChance()) {
                    runeAmount = 2; // Double the rune
                    player.sendFilteredMessage("Your medium level knowledge of the local area allows you to bind additional runes.");
                }
            }
            player.getInventory().addItem(new Item(randomRune.getRuneId(), runeAmount));
            totalRunes += runeAmount;
        }
        player.getSkills().addXp(SkillConstants.RUNECRAFTING, totalExperience);
        return -1;
    }
    
    /**
     * Gets a random rune based on the player's Runecrafting level using the array from the wiki
     */

    private ZmiRune getRandomRune(int runecraftingLevel) {
        int levelBand = Math.min(runecraftingLevel / 10, 9);
        if (runecraftingLevel >= 99) {
            levelBand = 10;
        }
        double[][] distributions = getRuneDistributions();
        double[] currentDistribution = distributions[levelBand];
        double random = Utils.randomDouble() * 100.0;
        double cumulative = 0.0;
        for (int i = 0; i < currentDistribution.length; i++) {
            cumulative += currentDistribution[i];
            if (random <= cumulative) {
                return ZmiRune.VALUES[i];
            }
        }
        return ZmiRune.AIR;
    }
    

    
    /**
     * Returns the rune distribution array from the wiki
     * Each row represents a level band and each column represents a rune type
     */

    private double[][] getRuneDistributions() {
        return new double[][] {
            // Level 1-9: Soul, Blood, Death, Law, Nature, Astral, Chaos, Cosmic, Body, Fire, Earth, Water, Mind, Air
            {0.03, 0.05, 0.08, 0.15, 0.30, 0.45, 0.60, 0.85, 1.50, 3.00, 6.00, 12.00, 25.00, 50.00},
            // Level 10-19
            {0.04, 0.06, 0.12, 0.24, 0.40, 0.60, 0.80, 1.75, 6.00, 12.00, 24.00, 21.00, 18.00, 15.00},
            // Level 20-29
            {0.09, 0.15, 0.32, 0.55, 1.10, 2.10, 4.20, 8.00, 16.00, 15.00, 14.00, 13.50, 13.00, 12.00},
            // Level 30-39
            {0.21, 0.40, 0.60, 1.30, 2.50, 5.00, 10.00, 20.00, 13.00, 12.00, 11.00, 9.00, 8.00, 7.00},
            // Level 40-49
            {0.41, 0.80, 1.20, 2.60, 5.00, 10.00, 20.00, 15.00, 10.00, 8.00, 7.50, 7.00, 6.50, 6.00},
            // Level 50-59
            {0.81, 1.70, 3.50, 7.00, 13.50, 15.00, 11.00, 10.00, 7.50, 7.00, 6.50, 6.00, 5.50, 5.00},
            // Level 60-69
            {1.01, 2.00, 4.00, 8.00, 15.50, 14.00, 10.50, 9.50, 7.50, 7.00, 6.00, 5.50, 5.00, 4.50},
            // Level 70-79
            {2.01, 5.00, 10.00, 18.00, 15.00, 12.00, 9.00, 7.00, 5.00, 4.00, 4.00, 3.00, 3.00, 3.00},
            // Level 80-89
            {4.01, 6.00, 14.50, 14.50, 13.50, 10.50, 8.00, 7.00, 6.00, 9.00, 3.00, 2.00, 1.00, 1.00},
            // Level 90-98
            {6.51, 10.00, 16.50, 14.50, 13.50, 10.00, 7.00, 6.00, 5.00, 4.00, 3.00, 2.00, 1.00, 1.00},
            // Level 99+
            {9.01, 13.00, 15.50, 14.50, 13.50, 9.50, 6.00, 5.00, 4.00, 3.00, 3.00, 2.00, 1.00, 1.00}
        };
    }
}
