package com.zenyte.game.content.magicstorageunit.enums;

import com.zenyte.game.content.magicstorageunit.StorableSetPiece;
import com.zenyte.game.content.magicstorageunit.StorageUnitElement;
import com.zenyte.game.item.ItemId;

/**
 * <AUTHOR> | 15/09/2020
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>
 */
public enum StorableToys implements StorageUnitElement {
    BUNNY_EARS(ItemId.BUNNY_EARS_10734, new StorableSetPiece(ItemId.BUNNY_EARS)),
    SCYTHE(ItemId.SCYTHE_10735, new StorableSetPiece(ItemId.SCYTHE)),
    WAR_SHIP(ItemId.WAR_SHIP, new StorableSetPiece(ItemId.WAR_SHIP)),
    YO_YO(ItemId.YOYO_10733, new StorableSetPiece(ItemId.YOYO)),
    RUBBER_CHICKEN(ItemId.RUBBER_CHICKEN_10732, new StorableSetPiece(ItemId.RUBBER_CHICKEN)),
    ZOMBIE_HEAD(ItemId.ZOMBIE_HEAD_10731, new StorableSetPiece(ItemId.ZOMBIE_HEAD)),
    BLUE_MARIONETTE(ItemId.BLUE_MARIONETTE_10730, new StorableSetPiece(ItemId.BLUE_MARIONETTE)),
    GREEN_MARIONETTE(ItemId.GREEN_MARIONETTE, new StorableSetPiece(ItemId.GREEN_MARIONETTE)),
    RED_MARIONETTE(ItemId.RED_MARIONETTE, new StorableSetPiece(ItemId.RED_MARIONETTE)),
    BOBBLE_HAT(ItemId.BOBBLE_HAT_9815, new StorableSetPiece(ItemId.BOBBLE_HAT)),
    JESTER_HAT(ItemId.JESTER_HAT, new StorableSetPiece(ItemId.JESTER_HAT)),
    TRI_JESTER_HAT(ItemId.TRIJESTER_HAT, new StorableSetPiece(ItemId.TRIJESTER_HAT)),
    WOOLLY_HAT(ItemId.WOOLLY_HAT, new StorableSetPiece(ItemId.WOOLLY_HAT)),
    BOBBLE_SCARF(ItemId.BOBBLE_SCARF_9816, new StorableSetPiece(ItemId.BOBBLE_SCARF)),
    JESTER_SCARF(ItemId.JESTER_SCARF, new StorableSetPiece(ItemId.JESTER_SCARF)),
    TRI_JESTER_SCARF(ItemId.TRIJESTER_SCARF, new StorableSetPiece(ItemId.TRIJESTER_SCARF)),
    WOOLLY_SCARF(ItemId.WOOLLY_SCARF, new StorableSetPiece(ItemId.WOOLLY_SCARF)),
    EASTER_RING(ItemId.EASTER_RING_10729, new StorableSetPiece(ItemId.EASTER_RING)),
    JACK_LANTERN_MASK(ItemId.JACK_LANTERN_MASK_10723, new StorableSetPiece(ItemId.JACK_LANTERN_MASK)),
    SKELETON_OUTFIT(ItemId.SKELETON_MASK_10728, new StorableSetPiece(ItemId.SKELETON_MASK), new StorableSetPiece(ItemId.SKELETON_SHIRT), new StorableSetPiece(ItemId.SKELETON_LEGGINGS), new StorableSetPiece(ItemId.SKELETON_GLOVES_10725), new StorableSetPiece(ItemId.SKELETON_BOOTS_10724)),
    REINDEER_HAT(ItemId.REINDEER_HAT_10722, new StorableSetPiece(ItemId.REINDEER_HAT)),
    CHICKEN_OUTFIT(ItemId.CHICKEN_HEAD, new StorableSetPiece(ItemId.CHICKEN_HEAD_11021), new StorableSetPiece(ItemId.CHICKEN_WINGS_11020), new StorableSetPiece(ItemId.CHICKEN_LEGS_11022), new StorableSetPiece(ItemId.CHICKEN_FEET_11019)),
    BLACK_HWEEN_MASK(ItemId.BLACK_HWEEN_MASK, new StorableSetPiece(ItemId.BLACK_HWEEN_MASK)),
    BLACK_PARTYHAT(ItemId.BLACK_PARTYHAT, new StorableSetPiece(ItemId.BLACK_PARTYHAT)),
    RAINBOW_PARTYHAT(ItemId.RAINBOW_PARTYHAT, new StorableSetPiece(ItemId.RAINBOW_PARTYHAT)),
    COW_OUTFIT(ItemId.COW_MASK, new StorableSetPiece(ItemId.COW_MASK), new StorableSetPiece(ItemId.COW_TOP), new StorableSetPiece(ItemId.COW_TROUSERS), new StorableSetPiece(ItemId.COW_SHOES), new StorableSetPiece(ItemId.COW_GLOVES)),
    EASTER_BASKET(ItemId.EASTER_BASKET, new StorableSetPiece(ItemId.EASTER_BASKET)),
    DRUIDIC_WREATH(ItemId.DRUIDIC_WREATH, new StorableSetPiece(ItemId.DRUIDIC_WREATH)),
    GRIM_REAPER_HOOD(ItemId.GRIM_REAPER_HOOD, new StorableSetPiece(ItemId.GRIM_REAPER_HOOD)),
    SANTA_OUTFIT(ItemId.SANTA_MASK, new StorableSetPiece(ItemId.SANTA_MASK), new StorableSetPiece(ItemId.SANTA_JACKET), new StorableSetPiece(ItemId.SANTA_PANTALOONS), new StorableSetPiece(ItemId.SANTA_GLOVES), new StorableSetPiece(ItemId.SANTA_BOOTS)),
    ANTISANTA_OUTFIT(ItemId.ANTISANTA_MASK, new StorableSetPiece(ItemId.ANTISANTA_MASK), new StorableSetPiece(ItemId.ANTISANTA_JACKET), new StorableSetPiece(ItemId.ANTISANTA_PANTALOONS), new StorableSetPiece(ItemId.ANTISANTA_GLOVES), new StorableSetPiece(ItemId.ANTISANTA_BOOTS)),
    BUNNY_FEET(ItemId.BUNNY_FEET, new StorableSetPiece(ItemId.BUNNY_FEET)),
    MASK_OF_BALANCE(ItemId.MASK_OF_BALANCE, new StorableSetPiece(ItemId.MASK_OF_BALANCE)),
    TIGER_TOY(ItemId.TIGER_TOY, new StorableSetPiece(ItemId.TIGER_TOY)),
    LION_TOY(ItemId.LION_TOY, new StorableSetPiece(ItemId.LION_TOY)),
    SNOW_LEOPARD_TOY(ItemId.SNOW_LEOPARD_TOY, new StorableSetPiece(ItemId.SNOW_LEOPARD_TOY)),
    AMUR_LEOPARD_TOY(ItemId.AMUR_LEOPARD_TOY, new StorableSetPiece(ItemId.AMUR_LEOPARD_TOY)),
    ANTI_PANTIES(ItemId.ANTIPANTIES, new StorableSetPiece(ItemId.ANTIPANTIES)),
    GRAVEDIGGER_OUTFIT(ItemId.GRAVEDIGGER_MASK, new StorableSetPiece(ItemId.GRAVEDIGGER_MASK), new StorableSetPiece(ItemId.GRAVEDIGGER_TOP), new StorableSetPiece(ItemId.GRAVEDIGGER_LEGGINGS), new StorableSetPiece(ItemId.GRAVEDIGGER_BOOTS), new StorableSetPiece(ItemId.GRAVEDIGGER_GLOVES)),
    BLACK_SANTA_HAT(ItemId.BLACK_SANTA_HAT, new StorableSetPiece(ItemId.BLACK_SANTA_HAT)),
    INVERTED_SANTA_HAT(ItemId.INVERTED_SANTA_HAT, new StorableSetPiece(ItemId.INVERTED_SANTA_HAT)),
    GNOME_CHILD_HAT(ItemId.GNOME_CHILD_HAT, new StorableSetPiece(ItemId.GNOME_CHILD_HAT)),
    BUNNY_TOP(ItemId.BUNNY_TOP, new StorableSetPiece(ItemId.BUNNY_TOP)),
    BUNNY_LEGS(ItemId.BUNNY_LEGS, new StorableSetPiece(ItemId.BUNNY_LEGS)),
    BUNNY_PAWS(ItemId.BUNNY_PAWS, new StorableSetPiece(ItemId.BUNNY_PAWS)),
    HORNWOOD_HELM(ItemId.HORNWOOD_HELM, new StorableSetPiece(ItemId.HORNWOOD_HELM)),
    BANSHEE_OUTFIT(ItemId.BANSHEE_MASK, new StorableSetPiece(ItemId.BANSHEE_MASK), new StorableSetPiece(ItemId.BANSHEE_TOP), new StorableSetPiece(ItemId.BANSHEE_ROBE)),
    HUNTING_KNIFE(ItemId.HUNTING_KNIFE, new StorableSetPiece(ItemId.HUNTING_KNIFE)),
    SNOW_GLOBE(ItemId.SNOW_GLOBE, new StorableSetPiece(ItemId.SNOW_GLOBE)),
    SACK_OF_PRESENTS(ItemId.SACK_OF_PRESENTS, new StorableSetPiece(ItemId.SACK_OF_PRESENTS)),
    GIANT_PRESENT(ItemId.GIANT_PRESENT, new StorableSetPiece(ItemId.GIANT_PRESENT)),
    FOURTH_BIRTHDAY_HAT(ItemId._4TH_BIRTHDAY_HAT, new StorableSetPiece(ItemId._4TH_BIRTHDAY_HAT)),
    BIRTHDAY_BALLOONS(ItemId.BIRTHDAY_BALLOONS, new StorableSetPiece(ItemId.BIRTHDAY_BALLOONS)),
    EASTER_EGG_HELM(ItemId.EASTER_EGG_HELM, new StorableSetPiece(ItemId.EASTER_EGG_HELM)),
    RAINBOW_SCARF(ItemId.RAINBOW_SCARF, new StorableSetPiece(ItemId.RAINBOW_SCARF)),
    HAND_FAN(ItemId.HAND_FAN, new StorableSetPiece(ItemId.HAND_FAN)),
    RUNEFEST_SHIELD(ItemId.RUNEFEST_SHIELD, new StorableSetPiece(ItemId.RUNEFEST_SHIELD)),
    JONAS_MASK(ItemId.JONAS_MASK_21720, new StorableSetPiece(ItemId.JONAS_MASK_21720)),
    SNOW_IMP_COSTUME(ItemId.SNOW_IMP_COSTUME_HEAD_21847, new StorableSetPiece(ItemId.SNOW_IMP_COSTUME_HEAD_21847), new StorableSetPiece(ItemId.SNOW_IMP_COSTUME_BODY_21849), new StorableSetPiece(ItemId.SNOW_IMP_COSTUME_LEGS_21851),
            new StorableSetPiece(ItemId.SNOW_IMP_COSTUME_GLOVES_21855), new StorableSetPiece(ItemId.SNOW_IMP_COSTUME_FEET_21857), new StorableSetPiece(ItemId.SNOW_IMP_COSTUME_TAIL_21853)),
    WISE_OLD_MANS_SANTA_HAT(ItemId.WISE_OLD_MANS_SANTA_HAT, new StorableSetPiece(ItemId.WISE_OLD_MANS_SANTA_HAT)),
    BUNNYMAN_MASK(ItemId.BUNNYMAN_MASK, new StorableSetPiece(ItemId.BUNNYMAN_MASK)),
    CLOWN_BOW_TIE(ItemId.CLOWN_BOW_TIE, new StorableSetPiece(ItemId.CLOWN_BOW_TIE)),
    CLOWN_MASK(ItemId.CLOWN_MASK, new StorableSetPiece(ItemId.CLOWN_MASK)),
    CLOWN_GOWN(ItemId.CLOWN_GOWN, new StorableSetPiece(ItemId.CLOWN_GOWN)),
    CLOWN_SHOES(ItemId.CLOWN_SHOES, new StorableSetPiece(ItemId.CLOWN_SHOES)),
    CLOWN_TROUSERS(ItemId.CLOWN_TROUSERS, new StorableSetPiece(ItemId.CLOWN_TROUSERS)),
    TREE_TOP(ItemId.TREE_TOP, new StorableSetPiece(ItemId.TREE_TOP)),
    TREE_SKIRT(ItemId.TREE_SKIRT, new StorableSetPiece(ItemId.TREE_SKIRT)),
    STAR_FACE(ItemId.STARFACE, new StorableSetPiece(ItemId.STARFACE)),
    EGGSHELL_PLATEBODY(ItemId.EGGSHELL_PLATEBODY, new StorableSetPiece(ItemId.EGGSHELL_PLATEBODY)),
    EGGSHELL_PLATELEGS(ItemId.EGGSHELL_PLATELEGS, new StorableSetPiece(ItemId.EGGSHELL_PLATELEGS)),
    GIANT_EASTER_EGG(ItemId.GIANT_EASTER_EGG, new StorableSetPiece(ItemId.GIANT_EASTER_EGG)),
    PROP_SWORD(ItemId.PROP_SWORD, new StorableSetPiece(ItemId.PROP_SWORD)),
    CRUCIFEROUS_CODEX(ItemId.CRUCIFEROUS_CODEX, new StorableSetPiece(ItemId.CRUCIFEROUS_CODEX)),
    BIRTHDAY_CAKE(ItemId.BIRTHDAY_CAKE, new StorableSetPiece(ItemId.BIRTHDAY_CAKE)),
    CABBAGE_CAPE(ItemId.CABBAGE_CAPE, new StorableSetPiece(ItemId.CABBAGE_CAPE)),
    EEK(ItemId.EEK, new StorableSetPiece(ItemId.EEK)),
    GOLDEN_TENCH(ItemId.GOLDEN_TENCH, new StorableSetPiece(ItemId.GOLDEN_TENCH)),
    JAR_OF_SWAMP(ItemId.JAR_OF_SWAMP, new StorableSetPiece(ItemId.JAR_OF_SWAMP)),
    JAR_OF_DECAY(ItemId.JAR_OF_DECAY, new StorableSetPiece(ItemId.JAR_OF_DECAY)),
    JAR_OF_CHEMICALS(ItemId.JAR_OF_CHEMICALS, new StorableSetPiece(ItemId.JAR_OF_CHEMICALS)),
    JAR_OF_SAND(ItemId.JAR_OF_SAND, new StorableSetPiece(ItemId.JAR_OF_SAND)),
    JAR_OF_DARKNESS(ItemId.JAR_OF_DARKNESS, new StorableSetPiece(ItemId.JAR_OF_DARKNESS)),
    JAR_OF_STONE(ItemId.JAR_OF_STONE, new StorableSetPiece(ItemId.JAR_OF_STONE)),
    JAR_OF_SOULS(ItemId.JAR_OF_SOULS, new StorableSetPiece(ItemId.JAR_OF_SOULS)),
    JAR_OF_MIASMA(ItemId.JAR_OF_MIASMA, new StorableSetPiece(ItemId.JAR_OF_MIASMA)),
    JAR_OF_DIRT(ItemId.JAR_OF_DIRT, new StorableSetPiece(ItemId.JAR_OF_DIRT)),

    ;

    private final int displayItem;
    private final StorableSetPiece[] pieces;

    StorableToys(final int displayItem, final StorableSetPiece... pieces) {
        this.displayItem = displayItem;
        this.pieces = pieces;
    }

    public int getDisplayItem() {
        return displayItem;
    }

    public StorableSetPiece[] getPieces() {
        return pieces;
    }
}
