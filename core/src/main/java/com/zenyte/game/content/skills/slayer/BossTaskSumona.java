package com.zenyte.game.content.skills.slayer;

import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.npc.NPC;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.SkillConstants;
import mgi.utilities.StringFormatUtil;
import org.apache.commons.lang3.ArrayUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;

import static com.zenyte.game.content.skills.slayer.SlayerMaster.DURADEL;

public enum BossTaskSumona implements SlayerTask {
	KREE_ARRA_SUMONA(357, p -> p.getSkills().getLevelForXp(SkillConstants.RANGED) >= 70, "Kree'arra",
		"Requires 70+ Ranged and Armadyl items for protection. Use ranged attacks and prayer switching.", false,
		List.of(
			new SlayerLocationInfo("God Wars Dungeon - Armadyl", new Location(2839, 5296, 2))
		)),
	KRIL_TSUTSAROTH_SUMONA(350.5F, p -> p.getSkills().getLevelForXp(SkillConstants.HITPOINTS) >= 70, "K'ril Tsutsaroth",
		"Requires 70+ Hitpoints and Zamorak items for protection. Beware of poison attacks.", false,
		List.of(
			new SlayerLocationInfo("God Wars Dungeon - Zamorak", new Location(2925, 5331, 2))
		)),
	COMMANDER_ZILYANA_SUMONA(350, p -> p.getSkills().getLevelForXp(SkillConstants.AGILITY) >= 70, "Commander Zilyana",
		"Requires 70+ Agility and Saradomin items for protection. Uses magic attacks.", false,
		List.of(
			new SlayerLocationInfo("God Wars Dungeon - Saradomin", new Location(2907, 5265, 0))
		)),
	GENERAL_GRAARDOR_SUMONA(338, p -> p.getSkills().getLevelForXp(SkillConstants.STRENGTH) >= 70, "General Graardor",
		"Requires 70+ Strength and Bandos items for protection. Strong melee attacks.", false,
		List.of(
			new SlayerLocationInfo("God Wars Dungeon - Bandos", new Location(2864, 5354, 2))
		)),
	DAGANNOTH_KINGS_SUMONA(331.5F, p -> true, "Dagannoth Kings",
		"Three powerful dagannoth bosses: Rex (melee), Prime (magic), Supreme (ranged). Bring friends and tribrid combat styles.", false,
		List.of(
			new SlayerLocationInfo("Waterbirth Island Dungeon", new Location(2545, 10143, 0))
		)) {
		@Override
		public boolean validate(@NotNull final String name, final NPC npc) {
			if (name == null) {
				throw new NullPointerException("name is marked non-null but is null");
			}
			return name.equalsIgnoreCase("Dagannoth Rex") || name.equalsIgnoreCase("Dagannoth Prime") || name.equalsIgnoreCase("Dagannoth Supreme");
		}

		@Override
		public float getExperience(final NPC npc) {
			return npc.getDefinitions().getName().equalsIgnoreCase("Dagannoth Supreme") ? 255 : 331.5F;
		}
	},
	KING_BLACK_DRAGON_SUMONA(258, p -> true, "King Black Dragon",
		"Powerful three-headed dragon. Bring antifire protection and high-level combat gear. Located deep in the Wilderness.", false,
		List.of(
			new SlayerLocationInfo("King Black Dragon Lair", new Location(2271, 4680, 0))
		)),
	VORKATH_SUMONA(750, p -> true, "Vorkath",
		"Powerful undead dragon boss with devastating attacks. Use antifire and prayer switching for survival.", false,
		List.of(
			new SlayerLocationInfo("Ungael Island", new Location(2272, 4049, 0))
		)),
	VETION_SUMONA(312, p -> true, "Vet'ion reborn",
		"Extremely dangerous PvP area. Skeletal hellhound boss with two forms.", true,
		List.of(
			new SlayerLocationInfo("Wilderness - Bone Yard", new Location(3200, 3785, 0))
		)),
	CRAZY_ARCHEAOLOGIST_SUMONA(275, p -> true, "Crazy archaeologist",
		"Dangerous PvP area. Uses magic attacks and explosive books.", true,
		List.of(
			new SlayerLocationInfo("Wilderness - Ruins", new Location(2984, 3713, 0))
		)),
	ZULRAH_SUMONA(500, p -> true, "Zulrah",
		"Serpentine boss with rotating forms and attack patterns. Requires memorizing rotations. Use prayer switching and antivenoms.", false,
		List.of(
			new SlayerLocationInfo("Zul-Andra", new Location(2268, 3069, 0))
		)),
	CERBERUS_SUMONA(690, p -> p.getSkills().getLevelForXp(SkillConstants.SLAYER) >= 91, "Cerberus",
		"Three-headed hellhound boss. Requires 91+ Slayer and spectral spirit shield recommended for special attacks.", false,
		List.of(
			new SlayerLocationInfo("Cerberus Lair", new Location(1240, 1226, 0))
		)),
	GIANT_MOLE_SUMONA(215, p -> true, "Giant Mole",
		"Bring a spade and light source. The mole burrows around the underground tunnels.", false,
		List.of(
			new SlayerLocationInfo("Falador Park", new Location(1752, 5237, 0))
		)),
	CALLISTO_SUMONA(312, p -> true, "Callisto",
		"Extremely dangerous PvP area. Bring minimal risk gear and be prepared for player killers.", true,
		List.of(
			new SlayerLocationInfo("Wilderness - Demonic Ruins", new Location(3288, 3886, 0))
		)),
	CHAOS_ELEMENTAL_SUMONA(250, p -> true, "Chaos elemental",
		"High PvP risk area. Teleports players randomly and has unpredictable attacks.", true,
		List.of(
			new SlayerLocationInfo("Wilderness - Rogues' Castle", new Location(3107, 3937, 0))
		)),
	SCORPIA_SUMONA(260, p -> true, "Scorpia",
		"Deep Wilderness location. Dangerous PvP area with poison attacks. Bring antipoison.", true,
		List.of(
			new SlayerLocationInfo("Wilderness - Scorpion Pit", new Location(3233, 3949, 0))
		)),
	KRAKEN_SUMONA(255, p -> p.getSkills().getLevelForXp(SkillConstants.SLAYER) >= 87, "Kraken",
		"Requires 87+ Slayer. Use magic attacks and bring plenty of food and prayer potions.", false,
		List.of(
			new SlayerLocationInfo("Kraken Cove", new Location(2280, 10022, 0))
		)),
	ABYSSAL_SIRE_SUMONA(450, p -> p.getSkills().getLevelForXp(SkillConstants.SLAYER) >= 85, "Abyssal sire",
		"Requires 85+ Slayer. Multi-phase boss with special mechanics. Bring DWH/BGS.", false,
		List.of(
			new SlayerLocationInfo("Abyssal Nexus", new Location(3420, 3888, 0))
		)),
	ARAXXOR_SUMONA(1708, p -> p.getSkills().getLevelForXp(SkillConstants.SLAYER) >= 92, "Araxxor",
			"Requires 92+ Slayer. A deadly and poisonous spidermother of the Araxytes!", false,
			List.of(
					new SlayerLocationInfo("Araxxor's Lair", new Location(3658, 9816, 0)) // TODO: Add Araxxor boss lair coordinates
			)),
	KALPHITE_QUEEN_SUMONA(535.5F, p -> true, "Kalphite Queen",
		"Two-phase boss with different combat styles. Bring rope and antipoison.", false,
		List.of(
			new SlayerLocationInfo("Kalphite Lair", new Location(3507, 9494, 0))
		)),
	PHANTOM_MUSPAH(1763.7F, p -> true, "Phantom Muspah", "Powerful shapeshifting boss with multiple phases.", false),
	VENENATIS_SUMONA(388.8F, p -> true, "Venenatis",
		"Extremely dangerous PvP area. Giant spider boss with web attacks and high damage.", true,
		List.of(
			new SlayerLocationInfo("Wilderness - Venenatis Lair", new Location(3319, 3757, 0))
		)),
	CHAOS_FANATIC_SUMONA(253, p -> true, "Chaos Fanatic",
		"High PvP risk area. Uses magic attacks and teleports players.", true,
		List.of(
			new SlayerLocationInfo("Wilderness - Chaos Fanatic Lair", new Location(2979, 3611, 0))
		)),
	THERMONUCLEAR_SMOKE_DEVIL_SUMONA(240, p -> p.getSkills().getLevelForXp(SkillConstants.SLAYER) >= 93, "Thermonuclear smoke devil",
		"Requires 93+ Slayer and facemask/slayer helmet. Bring antifire.", false,
		List.of(
			new SlayerLocationInfo("Smoke Devil Dungeon", new Location(3207, 9377, 0))
		)),
	GROTESQUE_GUARDIANS_SUMONA(1350, p -> p.getSkills().getLevelForXp(SkillConstants.SLAYER) >= 75 && p.getNumericAttribute("brittle-entrance_unlocked").intValue() == 1, "Grotesque Guardians",
		"Requires 75+ Slayer and brittle key. Dusk and Dawn boss fight.", false,
		List.of(
			new SlayerLocationInfo("Slayer Tower Rooftop", new Location(3428, 3538, 3))
		)) {
		@Override
		public boolean validate(@NotNull final String name, final NPC npc) {
			if (name == null) {
				throw new NullPointerException("name is marked non-null but is null");
			}
			return name.equals("Dusk");
		}
	},
	BARROWS_SUMONA(255, p -> true, "Barrows Brothers",
		"Six different brothers with unique combat styles. Bring prayer potions and food.", false,
		List.of(
			new SlayerLocationInfo("Barrows", new Location(3565, 3314, 0))
		)) {
		@Override
		public boolean validate(@NotNull final String name, final NPC npc) {
			if (name == null) {
				throw new NullPointerException("name is marked non-null but is null");
			}
			return name.equals("Ahrim the Blighted") || name.equals("Dharok the Wretched") || name.equals("Guthan the Infested") || name.equals("Karil the Tainted") || name.equals("Torag the Corrupted") || name.equals("Verac the Defiled");
		}
	},
	CORPOREAL_BEAST_SUMONA(700, p -> true, "Corporeal beast",
		"Extremely powerful boss with high defence. Requires team coordination and spears/halberds.", false,
		List.of(
			new SlayerLocationInfo("Corporeal Beast Lair", new Location(-1, -1, -1)) // TODO: Add Corporeal Beast lair coordinates
		)),
	ALCHEMICAL_HYDRA_SUMONA(1320, p -> true, "Alchemical hydra",
		"Multi-phase boss with different attack styles. Requires 95+ Slayer.", false,
		List.of(
			new SlayerLocationInfo("Karuulm Slayer Dungeon", new Location(-1, -1, -1)) // TODO: Add Karuulm Slayer Dungeon Hydra coordinates
		)),
	GAUNTLET_REG_SUMONA(600, p -> true, "Crystalline Hunllef",
		"Solo minigame boss requiring resource gathering and preparation. High-level PvM content.", false,
		List.of(
			new SlayerLocationInfo("The Gauntlet", new Location(-1, -1, -1)) // TODO: Add Gauntlet coordinates
		)),
	GAUNTLET_CORRUPT_SUMONA(800, p -> true, "Corrupted Hunllef",
		"Harder version with increased damage and speed. Elite PvM challenge.", false,
		List.of(
			new SlayerLocationInfo("Corrupted Gauntlet", new Location(-1, -1, -1)) // TODO: Add Corrupted Gauntlet coordinates
		)),
	SARACHNIS_SUMONA(200, p -> true, "Sarachnis",
		"Spider boss with web-based attacks. Underground dungeon location with unique mechanics.", false,
		List.of(
			new SlayerLocationInfo("Forthos Dungeon", new Location(-1, -1, -1)) // TODO: Add Forthos Dungeon Sarachnis coordinates
		)),
	NIGHTMARE_SUMONA(1000, p -> true, "The Nightmare",
		"Group boss with multiple phases and special attacks. Requires team coordination.", false,
		List.of(
			new SlayerLocationInfo("Sisterhood Sanctuary", new Location(-1, -1, -1)) // TODO: Add Sisterhood Sanctuary coordinates
		)),
	NIGHTMARE_PHOSANIS_SUMONA(700, p -> true, "Phosani's Nightmare",
		"Solo version of The Nightmare with modified mechanics. Elite solo PvM content.", false,
		List.of(
			new SlayerLocationInfo("Phosani's Nightmare Chamber", new Location(-1, -1, -1)) // TODO: Add Phosani's Nightmare coordinates
		)),
	NEX_SUMONA(1300, p -> true, "Nex",
		"Powerful Zarosian general with multiple phases. Requires high-level gear and team coordination.", false,
		List.of(
			new SlayerLocationInfo("Ancient Prison", new Location(-1, -1, -1)) // TODO: Add Ancient Prison coordinates
		)),
	OLM_SUMONA(2500, p -> true, "Great Olm",
		"Final boss of the raid with multiple phases. Requires team coordination and high-level PvM skills.", false,
		List.of(
			new SlayerLocationInfo("Chambers of Xeric", new Location(-1, -1, -1)) // TODO: Add Chambers of Xeric coordinates
		)),
	TOB_SUMONA(2500, p -> true, "Verzik Vitur",
		"Final boss of the raid with multiple phases. Elite team-based PvM content.", false,
		List.of(
			new SlayerLocationInfo("Theatre of Blood", new Location(-1, -1, -1)) // TODO: Add Theatre of Blood coordinates
		)),
	;

	BossTaskSumona(final float xp, final Predicate<Player> predicate, final String name, final String tip, final boolean wildernessTask) {
		this(xp, predicate, name, tip, wildernessTask, new ArrayList<>());
	}

	BossTaskSumona(final float xp, final Predicate<Player> predicate, final String name, final String tip, final boolean wildernessTask, final List<SlayerLocationInfo> locations) {
		this.xp = xp;
		this.predicate = predicate;
		monsters = new String[]{name.toLowerCase()};
		this.name = name;
		this.tip = tip;
		this.wildernessTask = wildernessTask;
		this.locations = locations != null ? locations : new ArrayList<>();
	}

	private final float xp;
	private final Predicate<Player> predicate;
	private final String name;
	private final String tip;
	private final String[] monsters;
	private final boolean wildernessTask;

	// Location data for slayer tips and teleports
	private final List<SlayerLocationInfo> locations;
	public static final BossTaskSumona[] VALUES = values();
	public static final Map<String, BossTaskSumona> MAPPED_VALUES = new HashMap<>(VALUES.length);

	static {
		for (final BossTaskSumona value : VALUES) {
			MAPPED_VALUES.put(value.name.toLowerCase(), value);
		}
	}

	@Override
	public boolean validate(@NonNull final String name, final NPC npc) {
		if (name == null) {
			throw new NullPointerException("name is marked non-null but is null");
		}
		if (ArrayUtils.contains(this.getMonsterIds(), npc.getId())) {
			return true;
		}
		return name.equalsIgnoreCase(this.name);
	}

	@Override
	public float getExperience(final NPC npc) {
		return xp;
	}

	@Override
	public int getTaskId() {
		return 98;
	}

	public int[] getMonsterIds() {
		return new int[0];
	}

	@Override
	public int getSlayerRequirement() {
		return 0;
	}

	@Override
	public String getTip() {
		return tip;
	}

	@Override
	public String toString() {
		return StringFormatUtil.formatString(name);
	}

	@Override
	public String getTaskName() {
		return name;
	}

	@Override
	public String getEnumName() {
		return name();
	}

	public float getXp() {
		return xp;
	}

	public Predicate<Player> getPredicate() {
		return predicate;
	}

	public String[] getMonsters() {
		return monsters;
	}

	public boolean isWildernessTask() {
		return wildernessTask;
	}

	public List<SlayerLocationInfo> getLocations() {
		return locations;
	}

	/**
	 * Data class to hold location information for Sumona boss slayer tasks
	 */
	public static class SlayerLocationInfo {
		private final String locationName;
		private final Location teleportLocation;

		public SlayerLocationInfo(String locationName, Location teleportLocation) {
			this.locationName = locationName;
			this.teleportLocation = teleportLocation;
		}

		public String getLocationName() { return locationName; }
		public Location getTeleportLocation() { return teleportLocation; }
	}

}
