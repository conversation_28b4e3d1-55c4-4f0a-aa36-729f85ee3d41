package com.zenyte.game.content.skills.agility;

import com.zenyte.game.world.entity.player.Player;

/**
 * <AUTHOR> (Discord: imslickk)
 * Utility class for tracking wilderness agility lap counts
 * This class is in the core module so it can be accessed by Player.java
 */
public class WildernessAgilityLapTracker {

    private static final String LAP_COUNT_ATTRIBUTE = "wilderness_agility_laps";

    /**
     * Increments and returns the player's current lap count for this session
     * Uses temporary attributes so it resets on logout
     */
    public static int incrementLapCount(Player player) {
        int currentLaps = player.getNumericTemporaryAttribute(LAP_COUNT_ATTRIBUTE).intValue();
        int newLapCount = currentLaps + 1;
        player.addTemporaryAttribute(LAP_COUNT_ATTRIBUTE, newLapCount);
        if (newLapCount == 1) {
            player.sendMessage("You've completed your first lap!");
        } else if (newLapCount % 10 == 0) {
            player.sendMessage("Lap streak milestone reached! Current streak: " + newLapCount);
        } else if (newLapCount == 16 || newLapCount == 31 || newLapCount == 61) {
            player.sendMessage("Lap streak: " + newLapCount + " - Your rewards have improved!");
        }
        return newLapCount;
    }

    /**
     * Gets the player's current lap count without incrementing
     */
    public static int getCurrentLapCount(Player player) {
        return player.getNumericTemporaryAttribute(LAP_COUNT_ATTRIBUTE).intValue();
    }

    /**
     * Resets the player's lap count (called when leaving course area)
     * This is called from the WildernessCourseDoorObject.openDoor() method
     */
    public static void resetLapCount(Player player) {
        int currentLaps = player.getNumericTemporaryAttribute(LAP_COUNT_ATTRIBUTE).intValue();
        if (currentLaps > 0) {
            player.addTemporaryAttribute(LAP_COUNT_ATTRIBUTE, 0);
            player.sendMessage("Your wilderness agility lap streak has been reset.");
        }
    }
}
