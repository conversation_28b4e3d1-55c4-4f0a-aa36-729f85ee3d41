package com.zenyte.game.content.well;

import com.zenyte.game.content.serverevent.WorldBoostType;
import com.zenyte.game.world.broadcasts.BroadcastType;

public enum WellPerk implements WorldBoostType {

    BONUS_XP("+50% Exp", 20_000_000),
    DOUBLE_VOTE_POINTS("Double Vote Points", 25_000_000),
    DOUBLE_BLOOD_MONEY("Double Blood Money", 25_000_000),
    DOUBLE_UNIQUES("Double Unique Drops", 50_000_000L);

    public static final WellPerk[] VALUES = values();

    private final String message;

    private final long amount;

    public long getAmount() {
        return amount;
    }

    WellPerk(String message, long amount) {
        this.message = message;
        this.amount = amount;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public BroadcastType getBroadcastType() {
        return BroadcastType.WELL_OF_GOODWILL;
    }
}
