package com.zenyte.game.content.skills.farming.actions;

import com.zenyte.game.content.skills.farming.CompostBin;
import com.zenyte.game.content.skills.farming.CompostBinType;
import com.zenyte.game.content.skills.farming.FarmingSpot;
import com.zenyte.game.content.skills.farming.PatchType;
import com.zenyte.game.item.Item;
import com.zenyte.game.world.entity.SoundEffect;
import com.zenyte.game.world.entity.masks.Animation;
import com.zenyte.game.world.entity.player.Action;
import com.zenyte.game.world.entity.player.SkillConstants;

/**
 * <AUTHOR>
 */
public class BinClearing extends Action {
    private static final Animation animation = new Animation(832);
    private static final SoundEffect sound = new SoundEffect(2436);
    private final FarmingSpot spot;
    private final Item item;

    private CompostBinType binType;
    private Item bucket;
    private int compostType;

    public BinClearing(FarmingSpot spot, Item item) {
        this.spot = spot;
        this.item = item;
    }

    @Override
    public boolean start() {
        assert spot.getPatch().getType() == PatchType.COMPOST_BIN;
        assert !spot.getCompostBin().isEmpty();

        if (!process()) {
            return false;
        }
        play();
        delay(3);
        return true;
    }

    @Override
    public boolean process() {
        final CompostBin bin = spot.getCompostBin();
        binType = bin.getType().orElseThrow(RuntimeException::new);

        if (binType == CompostBinType.TOMATOES && !player.getInventory().checkSpace()) {
            return false;
        }

        bucket = findBucket();
        if (bucket == null && !player.getInventory().containsItem(1925, 1)) {
            player.sendMessage("You need a suitable bucket to do that.");
            return false;
        }

        Item nextCompostItem = spot.peekCompostableItem();
        if (nextCompostItem == null) {
            player.sendMessage("The compost bin is empty.");
            return false;
        }

        compostType = determineCompostType(nextCompostItem.getId());
        if (compostType == -1) {
            player.sendMessage("You can't store that type of compost in your bucket.");
            return false;
        }
            if (item != null && item.getId() == 1925) {
                return true;
            }

        if (bucket != null) {
            int bucketType = bucket.getCharges() == 0 ? -1 : (bucket.getCharges() >> 16);
            if (bucketType != -1 && bucketType != compostType) {
                player.sendMessage("You can only fill your bottomless compost bucket with one type of compost at a time.");
                return false;
            }
        }
        return true;
    }

    @Override
    public int processWithDelay() {
        Item compostItem = spot.removeCompostableItem();
            if (item != null && item.getId() == 1925) {
                if (binType != CompostBinType.TOMATOES) {
                    player.getInventory().deleteItem(1925, 1);
                }
                player.getInventory().addItem(compostItem);
            } else if (bucket != null) {
                int existing = bucket.getCharges() & 65535;
                if (existing >= 10000) {
                    player.sendMessage("Your bottomless compost bucket is full.");
                    return -1;
                }

                bucket.setCharges((compostType << 16) | (existing + 2));
                if (bucket.getId() == 22994) {
                    bucket.setId(22997);
                }
                player.getInventory().refreshAll();
                player.sendMessage("You store the compost in your bottomless compost bucket.");

            } else {
                if (binType != CompostBinType.TOMATOES) {
                    player.getInventory().deleteItem(1925, 1);
                }
                player.getInventory().addItem(compostItem);
            }

        if (spot.isCompostEmpty()) {
            player.sendMessage("The compost bin is now empty.");
            return -1;
        }

        if (binType == CompostBinType.ULTRACOMPOST) {
            player.getSkills().addXp(SkillConstants.FARMING, 10);
        } else if (binType == CompostBinType.SUPERCOMPOST) {
            player.getSkills().addXp(SkillConstants.FARMING, 8.5);
        }

        if (!process()) {
            return -1;
        }

        play();
        return 2;
    }

    private Item findBucket() {
        for (Item item : player.getInventory().getContainer().getItems().values()) {
            if (item != null && (item.getId() == 22994 || item.getId() == 22997)) {
                return item;
            }
        }
        return null;
    }

    private int determineCompostType(int compostId) {
        if (compostId == 6032 || compostId == 6033) return 0;
        if (compostId == 6034 || compostId == 6035) return 1;
        if (compostId == 21483 || compostId == 21484) return 2;
        return -1;
    }

    private void play() {
        player.setAnimation(animation);
        player.sendSound(sound);
    }
}
