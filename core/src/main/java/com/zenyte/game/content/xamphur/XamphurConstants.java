package com.zenyte.game.content.xamphur;

import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.masks.Animation;
import com.zenyte.game.world.entity.masks.Graphics;

public class XamphurConstants {

    public static final int XAMPHUR_NORAML = 10951;
    public static final int XAMPHUR_TRANSFORMED_1 = 10_953;
    public static final int XAMPHUR_TRANSFORMED_2 = 10954;
    public static final int XAMPHUR_FIGHT_HANDS = 10955;
    public static final int XAMPHUR_FIGHT_NO_HANDS = 10956;
    public static final Location SPAWN_LOCATION = new Location(3359, 7070, 0);
    public static final int HAND_L = 10957;
    public static final int HAND_R = 10958;
    public static final Animation SPAWN_ANIM = new Animation(9060);
    public static final Animation SPAWN_ANIM_2 = new Animation(9062);
    public static final Animation STAND_ANIM = new Animation(9063);

    public static final Animation ATTACK_ANIMATION = new Animation(9064);
    public static final Animation DROP_HANDS_ANIMATION = new Animation(9065);

    public static final Graphics HAND_DROP = new Graphics(1919);
    public static final Graphics HAND_DROP2 = new Graphics(1918);

    public static final Graphics HAND_RISE_MAGIC = new Graphics(1915);
    public static final Graphics HAND_RISE_MELEE = new Graphics(2092);
    public static final Graphics HAND_RISE_RANGED = new Graphics(2093);



    public static final int MARK_OF_DARKNESS_OBJECT_ID = 41881;

}
