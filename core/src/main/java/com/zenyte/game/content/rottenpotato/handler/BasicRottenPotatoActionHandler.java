package com.zenyte.game.content.rottenpotato.handler;

import com.zenyte.game.content.rottenpotato.RottenPotatoActionType;
import com.zenyte.game.world.entity.player.Player;

/**
 * <AUTHOR>
 * @since 3/23/2020
 */
public interface BasicRottenPotatoActionHandler extends RottenPotatoActionHandler {
    void execute(final Player player);

    @Override
    default RottenPotatoActionType type() {
        return RottenPotatoActionType.ITEM;
    }
}
