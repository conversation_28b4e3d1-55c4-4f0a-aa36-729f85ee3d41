package com.zenyte.game.content.boons;

public class BoonPriceTable {
    public static final int v_UnholyIntervention = 1500;
    public static final int v_BarbarianFisher = 2000;
    public static final int v_HardWorkPaysOff = 2500;
    public static final int v_BoneCruncher = 2000;
    public static final int v_BountifulSacrifice = 3000;
    public static final int v_NoOnesHome = 2500;
    public static final int v_DrawPartner = 10000;
    public static final int v_SliceNDice = 8000;
    public static final int v_DharoksBlessing = 7500;
    public static final int v_MasterOfTheCraft = 3000;
    public static final int v_RevItUp = 2500;
    public static final int v_FirstImpressions = 2000;
    public static final int v_ClueCollector = 3000;
    public static final int v_NoPetDebt = 10000;
    public static final int v_CrystalCatalyst = 4000;
    public static final int v_ImRubberYoureGlue = 4000;
    public static final int v_LunarEnthusiast = 5000;
    public static final int v_HammerDown = 6000;
    public static final int v_SlayersSpite = 4000;
    public static final int v_DagaWHO = 1500;
    public static final int v_IceForTheEyeless = 1500;
    public static final int v_RunForrestRun = 1500;
    public static final int v_LessIsMore = 2500;
    public static final int v_HoarderMentality = 2000;
    public static final int v_CorporealScrutiny = 3500;
    public static final int v_CryptKeeper = 2000;
    public static final int v_RelentlessPrecision = 35000;
    public static final int v_FourSure = 35000;
    public static final int v_DivineHealing = 35000;
    public static final int v_IWantItAll = 4000;
    public static final int v_DoubleTap = 2500;
    public static final int v_Locksmith = 2500;
    public static final int v_LethalAttunement = 2000;
    public static final int v_CrushingBlow = 4000;
    public static final int v_SousChef = 5000;
    public static final int v_Woodsman = 5000;
    public static final int v_Pyromaniac = 5000;
    public static final int v_Mixologist = 5000;
    public static final int v_MinerFortyNiner = 5000;
    public static final int v_Botanist = 5000;
    public static final int v_TrackStar = 2500;
    public static final int v_NoShardRequired = 15000;
    public static final int v_SwissArmyMan = 3500;
    public static final int v_Enlightened = 50000;
    public static final int v_Enraged = 50000;
    public static final int v_SpecialBreed = 5000;
    public static final int v_AnimalTamer = 10000;
    public static final int v_IVoted = 1500;
    public static final int v_BurnBabyBurn = 1500;
    public static final int v_ArcaneKnowledge = 1500;
    public static final int v_SuperiorSorcery = 2500;
    public static final int v_SleightOfHand = 3000;
    public static final int v_Alchoholic = 1500;
    public static final int v_SustainedAggression = 1500;
    public static final int v_HoleyMoley = 1500;
    public static final int v_TheRedeemer = 3000;
    public static final int v_TorvasGluttony = 7500;
    public static final int v_ThePointyEnd = 4000;
    public static final int v_HashSlingingSlasher = 4000;
    public static final int v_HolierThanThou = 1500;
    public static final int v_AshesToAshes = 3000;
    public static final int v_BrawnOfJustice = 7500;
    public static final int v_VigourOfInquisition = 25000;
    public static final int v_DoubleChins = 4000;
    public static final int v_EndlessQuiver = 2000;
    public static final int v_InfallibleShackles = 6000;
    public static final int v_IgnoranceIsBliss = 3000;
    public static final int v_ContractKiller = 6000;
    public static final int v_SlayersFavor = 5000;
    public static final int v_FarmersFortune = 1500;
    public static final int v_SoulStealer = 7500;
    public static final int v_SlayersSovereignty = 10000;
    public static final int v_HolyInterventionI = 5000;
    public static final int v_HolyInterventionII = 10000;
    public static final int v_HolyInterventionIII = 20000;
    public static final int v_MinionsMight = 15000;
    public static final int v_FamiliarsFortune = 10000;
    public static final int v_NoShardRequiredII = 20000;
    public static final int v_NoShardRequiredIII = 20000;
    public static final int v_TheLegendaryFisherman = 2500;
    public static final int v_CantBeAxed = 3500;
    public static final int v_EyeDontSeeYou = 5000;
    public static final int v_AllGassedUp = 5000;
    public static final int v_JabbasRightHand = 12500;
}
