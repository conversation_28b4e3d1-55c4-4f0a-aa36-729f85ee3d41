package com.zenyte.game.content.minigame.zmi_altar;

import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.object.ObjectAction;
import com.zenyte.game.world.object.WorldObject;

/**
 * <AUTHOR> (Discord: imslickk) - 7/25/2025
 */
public class ZmiAltarObject implements ObjectAction {

    @Override
    public void handleObjectAction(Player player, WorldObject object, String name, int optionId, String option) {
        if (option.equals("Craft-rune")) {
            player.getActionManager().setAction(new ZmiRunecraftingAction());
        }
    }

    @Override
    public Object[] getObjects() {
        return new Object[] { 29631 };
    }
}
