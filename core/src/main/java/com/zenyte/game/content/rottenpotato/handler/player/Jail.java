package com.zenyte.game.content.rottenpotato.handler.player;

import com.near_reality.api.service.sanction.SanctionPlayerExtKt;
import com.zenyte.game.content.rottenpotato.handler.PlayerRottenPotatoActionHandler;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.player.LogLevel;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.privilege.PlayerPrivilege;

/**
 * <AUTHOR> (Discord: imslickk)
 * @since 7/4/2025
 */
public class Jail implements PlayerRottenPotatoActionHandler {
    @Override
    public void execute(Player user, Player target) {
        target.setLocation(new Location(3226, 3407, 0));
        target.setJailed(true);
        target.log(LogLevel.INFO, "Jailed by " + user.getName() + ".");
        user.sendMessage("You have jailed <col=C22731>" + target.getUsername() + "</col>!");
        target.sendMessage("<col=C22731>You have been jailed by " + user.getUsername() + "!</col>");
    }

    @Override
    public String option() {
        return "Jail";
    }

    @Override
    public PlayerPrivilege getPrivilege() {
        return PlayerPrivilege.MODERATOR;
    }
}
