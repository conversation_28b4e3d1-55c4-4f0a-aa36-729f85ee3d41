package com.zenyte.game.content;

import com.near_reality.game.item.CustomItemId;

public enum DonatorBond {
    DONATOR_BOND_10(CustomItemId.DONATOR_BOND_10, 130, 10),
    DONATOR_BOND_25(CustomItemId.DONATOR_BOND_25, 325, 25),
    DONATOR_BOND_50(CustomItemId.DONATOR_BOND_50, 650, 50),
    DONATOR_BOND_100(CustomItemId.DONATOR_BOND_100, 1300, 100);

    private int itemId;
    private int credits;
    private int amount;

    DonatorBond(int itemId, int credits, int amount) {
        this.itemId = itemId;
        this.credits = credits;
        this.amount = amount;
    }

    public int getItemId() {
        return itemId;
    }

    public int getCredits() {
        return credits;
    }

    public int getAmount() {
        return amount;
    }
    public static DonatorBond forId(int id) {
        for (DonatorBond value : values()) {
            if (value.itemId == id)
                return value;
        }
        return null;
    }
}