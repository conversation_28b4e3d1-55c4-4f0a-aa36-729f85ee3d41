package com.zenyte.game.content.magicstorageunit.enums;

import com.zenyte.game.content.magicstorageunit.StorableSetPiece;
import com.zenyte.game.content.magicstorageunit.StorageUnitElement;
import com.zenyte.game.item.ItemId;

/**
 * <AUTHOR> | 15/09/2020
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>
 */
public enum StorableTreasure implements StorageUnitElement {
    BLACK_HERALDIC_KITESHIELD1(ItemId.BLACK_SHIELD_H1_10665, new StorableSetPiece(ItemId.BLACK_SHIELD_H1)),
    BLACK_HERALDIC_KITESHIELD2(ItemId.BLACK_SHIELD_H2_10668, new StorableSetPiece(ItemId.BLACK_SHIELD_H2)),
    BLACK_HERALDIC_KITESHIELD3(ItemId.BLACK_SHIELD_H3_10671, new StorableSetPiece(ItemId.BLACK_SHIELD_H3)),
    BLACK_HERALDIC_KITESHIELD4(ItemId.BLACK_SHIELD_H4_10674, new StorableSetPiece(ItemId.BLACK_SHIELD_H4)),
    BLACK_HERALDIC_KITESHIELD5(ItemId.BLACK_SHIELD_H5_10677, new StorableSetPiece(ItemId.BLACK_SHIELD_H5)),
    GOLD_TRIMMED_STUDDED_LEATHER(ItemId.STUDDED_BODY_G_10680, new StorableSetPiece(ItemId.STUDDED_BODY_G), new StorableSetPiece(ItemId.STUDDED_CHAPS_G)),
    FUR_TRIMMED_STUDDED_LEATHER(ItemId.STUDDED_BODY_T_10681, new StorableSetPiece(ItemId.STUDDED_BODY_T), new StorableSetPiece(ItemId.STUDDED_CHAPS_T)),
    BLUE_WIZARD_GOLD(ItemId.WIZARD_ROBE_G, new StorableSetPiece(ItemId.BLUE_WIZARD_HAT_G), new StorableSetPiece(ItemId.BLUE_WIZARD_ROBE_G), new StorableSetPiece(ItemId.BLUE_SKIRT_G)),
    BLUE_WIZARD_TRIM(ItemId.WIZARD_ROBE_T, new StorableSetPiece(ItemId.BLUE_WIZARD_HAT_T), new StorableSetPiece(ItemId.BLUE_WIZARD_ROBE_T), new StorableSetPiece(ItemId.BLUE_SKIRT_T)),
    BLACK_WIZARD_GOLD(ItemId.BLACK_WIZARD_HAT_G, new StorableSetPiece(ItemId.BLACK_WIZARD_HAT_G), new StorableSetPiece(ItemId.BLACK_WIZARD_ROBE_G), new StorableSetPiece(ItemId.BLACK_SKIRT_G)),
    BLACK_WIZARD_TRIM(ItemId.BLACK_WIZARD_HAT_T, new StorableSetPiece(ItemId.BLACK_WIZARD_HAT_T), new StorableSetPiece(ItemId.BLACK_WIZARD_ROBE_T), new StorableSetPiece(ItemId.BLACK_SKIRT_T)),
    TRIMMED_BLACK_ARMOUR(ItemId.BLACK_PLATEBODY_T_10690, new StorableSetPiece(ItemId.BLACK_FULL_HELM_T), new StorableSetPiece(ItemId.BLACK_PLATEBODY_T), new StorableSetPiece(ItemId.BLACK_PLATELEGS_T, ItemId.BLACK_PLATESKIRT_T), new StorableSetPiece(ItemId.BLACK_KITESHIELD_T)),
    GOLD_TRIMMED_BLACK_ARMOUR(ItemId.BLACK_PLATEBODY_G_10691, new StorableSetPiece(ItemId.BLACK_FULL_HELM_G), new StorableSetPiece(ItemId.BLACK_PLATEBODY_G), new StorableSetPiece(ItemId.BLACK_PLATELEGS_G, ItemId.BLACK_PLATESKIRT_G), new StorableSetPiece(ItemId.BLACK_KITESHIELD_G)),
    HIGHWAYMAN_MASK(ItemId.HIGHWAYMAN_MASK_10692, new StorableSetPiece(ItemId.HIGHWAYMAN_MASK)),
    BLUE_BERET(ItemId.BLUE_BERET_10693, new StorableSetPiece(ItemId.BLUE_BERET)),
    BLACK_BERET(ItemId.BLACK_BERET_10694, new StorableSetPiece(ItemId.BLACK_BERET)),
    WHITE_BERET(ItemId.WHITE_BERET_10695, new StorableSetPiece(ItemId.WHITE_BERET)),
    RED_BERET(ItemId.RED_BERET, new StorableSetPiece(ItemId.RED_BERET)),
    BLACK_HERALDIC_HELM1(ItemId.BLACK_HELM_H1_10699, new StorableSetPiece(ItemId.BLACK_HELM_H1)),
    BLACK_HERALDIC_HELM2(ItemId.BLACK_HELM_H2_10700, new StorableSetPiece(ItemId.BLACK_HELM_H2)),
    BLACK_HERALDIC_HELM3(ItemId.BLACK_HELM_H3_10701, new StorableSetPiece(ItemId.BLACK_HELM_H3)),
    BLACK_HERALDIC_HELM4(ItemId.BLACK_HELM_H4_10702, new StorableSetPiece(ItemId.BLACK_HELM_H4)),
    BLACK_HERALDIC_HELM5(ItemId.BLACK_HELM_H5_10703, new StorableSetPiece(ItemId.BLACK_HELM_H5)),
    TRIMMED_AMULET_OF_MAGIC(ItemId.AMULET_OF_MAGIC_T_10738, new StorableSetPiece(ItemId.AMULET_OF_MAGIC_T)),
    PANTALOONS(ItemId.PANTALOONS_10744, new StorableSetPiece(ItemId.PANTALOONS)),
    WIG(ItemId.A_POWDERED_WIG_10740, new StorableSetPiece(ItemId.A_POWDERED_WIG)),
    FLARED_PANTS(ItemId.FLARED_TROUSERS_10742, new StorableSetPiece(ItemId.FLARED_TROUSERS)),
    SLEEPING_CAP(ItemId.SLEEPING_CAP_10746, new StorableSetPiece(ItemId.SLEEPING_CAP)),
    BOB_THE_CAT_SHIRT_RED(ItemId.BOBS_RED_SHIRT_10714, new StorableSetPiece(ItemId.BOBS_RED_SHIRT)),
    BOB_THE_CAT_SHIRT_BLUE(ItemId.BOBS_BLUE_SHIRT_10715, new StorableSetPiece(ItemId.BOBS_BLUE_SHIRT)),
    BOB_THE_CAT_SHIRT_GREEN(ItemId.BOBS_GREEN_SHIRT_10716, new StorableSetPiece(ItemId.BOBS_GREEN_SHIRT)),
    BOB_THE_CAT_SHIRT_BLACK(ItemId.BOBS_BLACK_SHIRT_10717, new StorableSetPiece(ItemId.BOBS_BLACK_SHIRT)),
    BOB_THE_CAT_SHIRT_PURPLE(ItemId.BOBS_PURPLE_SHIRT_10718, new StorableSetPiece(ItemId.BOBS_PURPLE_SHIRT)),
    ELEGANT_CLOTHES_RED(ItemId.RED_ELEGANT_SHIRT_10750, new StorableSetPiece(ItemId.RED_ELEGANT_SHIRT, ItemId.RED_ELEGANT_BLOUSE), new StorableSetPiece(ItemId.RED_ELEGANT_LEGS, ItemId.RED_ELEGANT_SKIRT)),
    ELEGANT_CLOTHES_GREEN(ItemId.GREEN_ELEGANT_SHIRT_10754, new StorableSetPiece(ItemId.GREEN_ELEGANT_SHIRT, ItemId.GREEN_ELEGANT_BLOUSE), new StorableSetPiece(ItemId.GREEN_ELEGANT_LEGS, ItemId.GREEN_ELEGANT_SKIRT)),
    ELEGANT_CLOTHES_BLUE(ItemId.BLUE_ELEGANT_SHIRT_10752, new StorableSetPiece(ItemId.BLUE_ELEGANT_SHIRT, ItemId.BLUE_ELEGANT_BLOUSE), new StorableSetPiece(ItemId.BLUE_ELEGANT_LEGS, ItemId.BLUE_ELEGANT_SKIRT)),
    ELEGANT_CLOTHES_BLACK(ItemId.BLACK_ELEGANT_SHIRT_10748, new StorableSetPiece(ItemId.BLACK_ELEGANT_SHIRT, ItemId.WHITE_ELEGANT_BLOUSE), new StorableSetPiece(ItemId.BLACK_ELEGANT_LEGS, ItemId.WHITE_ELEGANT_SKIRT)),
    ELEGANT_CLOTHES_PURPLE(ItemId.PURPLE_ELEGANT_SHIRT_10756, new StorableSetPiece(ItemId.PURPLE_ELEGANT_SHIRT, ItemId.PURPLE_ELEGANT_BLOUSE), new StorableSetPiece(ItemId.PURPLE_ELEGANT_LEGS, ItemId.PURPLE_ELEGANT_SKIRT)),
    ELEGANT_CLOTHES_PINK(ItemId.PINK_ELEGANT_SHIRT, new StorableSetPiece(ItemId.PINK_ELEGANT_SHIRT, ItemId.PINK_ELEGANT_BLOUSE), new StorableSetPiece(ItemId.PINK_ELEGANT_LEGS, ItemId.PINK_ELEGANT_SKIRT)),
    ELEGANT_CLOTHES_GOLD(ItemId.GOLD_ELEGANT_SHIRT, new StorableSetPiece(ItemId.GOLD_ELEGANT_SHIRT, ItemId.GOLD_ELEGANT_BLOUSE), new StorableSetPiece(ItemId.GOLD_ELEGANT_LEGS, ItemId.GOLD_ELEGANT_SKIRT)),
    BERET_MASK(ItemId.BERET_MASK, new StorableSetPiece(ItemId.BERET_MASK_11282)),
    BRONZE_ARMOUR_TRIM(ItemId.BRONZE_PLATEBODY_T, new StorableSetPiece(ItemId.BRONZE_FULL_HELM_T), new StorableSetPiece(ItemId.BRONZE_PLATEBODY_T), new StorableSetPiece(ItemId.BRONZE_PLATELEGS_T, ItemId.BRONZE_PLATESKIRT_T), new StorableSetPiece(ItemId.BRONZE_KITESHIELD_T)),
    BRONZE_ARMOUR_GOLD(ItemId.BRONZE_PLATEBODY_G, new StorableSetPiece(ItemId.BRONZE_FULL_HELM_G), new StorableSetPiece(ItemId.BRONZE_PLATEBODY_G), new StorableSetPiece(ItemId.BRONZE_PLATELEGS_G, ItemId.BRONZE_PLATESKIRT_G), new StorableSetPiece(ItemId.BRONZE_KITESHIELD_G)),
    IRON_ARMOUR_TRIM(ItemId.IRON_PLATEBODY_T, new StorableSetPiece(ItemId.IRON_FULL_HELM_T), new StorableSetPiece(ItemId.IRON_PLATEBODY_T), new StorableSetPiece(ItemId.IRON_PLATELEGS_T, ItemId.IRON_PLATESKIRT_T), new StorableSetPiece(ItemId.IRON_KITESHIELD_T)),
    IRON_ARMOUR_GOLD(ItemId.IRON_PLATEBODY_G, new StorableSetPiece(ItemId.IRON_FULL_HELM_G), new StorableSetPiece(ItemId.IRON_PLATEBODY_G), new StorableSetPiece(ItemId.IRON_PLATELEGS_G, ItemId.IRON_PLATESKIRT_G), new StorableSetPiece(ItemId.IRON_KITESHIELD_G)),
    STEEL_ARMOUR_TRIM(ItemId.STEEL_PLATEBODY_T, new StorableSetPiece(ItemId.STEEL_FULL_HELM_T), new StorableSetPiece(ItemId.STEEL_PLATEBODY_T), new StorableSetPiece(ItemId.STEEL_PLATELEGS_T, ItemId.STEEL_PLATESKIRT_T), new StorableSetPiece(ItemId.STEEL_KITESHIELD_T)),
    STEEL_ARMOUR_GOLD(ItemId.STEEL_PLATEBODY_G, new StorableSetPiece(ItemId.STEEL_FULL_HELM_G), new StorableSetPiece(ItemId.STEEL_PLATEBODY_G), new StorableSetPiece(ItemId.STEEL_PLATELEGS_G, ItemId.STEEL_PLATESKIRT_G), new StorableSetPiece(ItemId.STEEL_KITESHIELD_G)),
    BEANIE(ItemId.BEANIE, new StorableSetPiece(ItemId.BEANIE)),
    IMP_MASK(ItemId.IMP_MASK, new StorableSetPiece(ItemId.IMP_MASK)),
    GOBLIN_MASK(ItemId.GOBLIN_MASK, new StorableSetPiece(ItemId.GOBLIN_MASK)),
    BLACK_CANE(ItemId.BLACK_CANE, new StorableSetPiece(ItemId.BLACK_CANE)),
    BLACK_PICKAXE(ItemId.BLACK_PICKAXE, new StorableSetPiece(ItemId.BLACK_PICKAXE)),
    LARGE_SPADE(ItemId.LARGE_SPADE, new StorableSetPiece(ItemId.LARGE_SPADE)),
    WOODEN_SHIELD_GOLD(ItemId.WOODEN_SHIELD_G, new StorableSetPiece(ItemId.WOODEN_SHIELD_G)),
    GOLDEN_CHEFS_HAT(ItemId.GOLDEN_CHEFS_HAT, new StorableSetPiece(ItemId.GOLDEN_CHEFS_HAT)),
    GOLDEN_APRON(ItemId.GOLDEN_APRON, new StorableSetPiece(ItemId.GOLDEN_APRON)),
    MONK_ROBES_GOLD(ItemId.MONKS_ROBE_TOP_G, new StorableSetPiece(ItemId.MONKS_ROBE_TOP_G), new StorableSetPiece(ItemId.MONKS_ROBE_G)),
    RED_STRAW_BOATER(ItemId.RED_BOATER_10758, new StorableSetPiece(ItemId.RED_BOATER)),
    ORANGE_STRAW_BOATER(ItemId.ORANGE_BOATER_10760, new StorableSetPiece(ItemId.ORANGE_BOATER)),
    GREEN_STRAW_BOATER(ItemId.GREEN_BOATER_10762, new StorableSetPiece(ItemId.GREEN_BOATER)),
    BLUE_STRAW_BOATER(ItemId.BLUE_BOATER_10764, new StorableSetPiece(ItemId.BLUE_BOATER)),
    BLACK_STRAW_BOATER(ItemId.BLACK_BOATER_10766, new StorableSetPiece(ItemId.BLACK_BOATER)),
    PINK_STRAW_BOATER(ItemId.PINK_BOATER, new StorableSetPiece(ItemId.PINK_BOATER)),
    PURPLE_STRAW_BOATER(ItemId.PURPLE_BOATER, new StorableSetPiece(ItemId.PURPLE_BOATER)),
    WHITE_STRAW_BOATER(ItemId.WHITE_BOATER, new StorableSetPiece(ItemId.WHITE_BOATER)),
    ADAMANT_HERALDIC_KITESHIELD1(ItemId.ADAMANT_SHIELD_H1_10666, new StorableSetPiece(ItemId.ADAMANT_SHIELD_H1)),
    ADAMANT_HERALDIC_KITESHIELD2(ItemId.ADAMANT_SHIELD_H2_10669, new StorableSetPiece(ItemId.ADAMANT_SHIELD_H2)),
    ADAMANT_HERALDIC_KITESHIELD3(ItemId.ADAMANT_SHIELD_H3_10672, new StorableSetPiece(ItemId.ADAMANT_SHIELD_H3)),
    ADAMANT_HERALDIC_KITESHIELD4(ItemId.ADAMANT_SHIELD_H4_10675, new StorableSetPiece(ItemId.ADAMANT_SHIELD_H4)),
    ADAMANT_HERALDIC_KITESHIELD5(ItemId.ADAMANT_SHIELD_H5_10678, new StorableSetPiece(ItemId.ADAMANT_SHIELD_H5)),
    GREEN_DRAGONHIDE_GOLD(ItemId.DHIDE_BODY_G, new StorableSetPiece(ItemId.GREEN_DHIDE_BODY_G), new StorableSetPiece(ItemId.GREEN_DHIDE_CHAPS_G)),
    GREEN_DRAGONHIDE_TRIM(ItemId.DHIDE_BODY_T, new StorableSetPiece(ItemId.GREEN_DHIDE_BODY_T), new StorableSetPiece(ItemId.GREEN_DHIDE_CHAPS_T)),
    RANGER_BOOTS(ItemId.RANGER_BOOTS_10696, new StorableSetPiece(ItemId.RANGER_BOOTS)),
    TRIMMED_ADAMANTITE_ARMOUR(ItemId.ADAMANT_PLATEBODY_T_10697, new StorableSetPiece(ItemId.ADAMANT_FULL_HELM_T), new StorableSetPiece(ItemId.ADAMANT_PLATEBODY_T), new StorableSetPiece(ItemId.ADAMANT_PLATELEGS_T, ItemId.ADAMANT_PLATESKIRT_T), new StorableSetPiece(ItemId.ADAMANT_KITESHIELD_T)),
    GOLD_TRIMMED_ADAMANTITE_ARMOUR(ItemId.ADAMANT_PLATEBODY_G_10698, new StorableSetPiece(ItemId.ADAMANT_FULL_HELM_G), new StorableSetPiece(ItemId.ADAMANT_PLATEBODY_G), new StorableSetPiece(ItemId.ADAMANT_PLATELEGS_G, ItemId.ADAMANT_PLATESKIRT_G), new StorableSetPiece(ItemId.ADAMANT_KITESHIELD_G)),
    RED_HEADBAND(ItemId.RED_HEADBAND_10768, new StorableSetPiece(ItemId.RED_HEADBAND)),
    BLACK_HEADBAND(ItemId.BLACK_HEADBAND_10770, new StorableSetPiece(ItemId.BLACK_HEADBAND)),
    BROWN_HEADBANG(ItemId.BROWN_HEADBAND_10772, new StorableSetPiece(ItemId.BROWN_HEADBAND)),
    WHITE_HEADBAND(ItemId.WHITE_HEADBAND, new StorableSetPiece(ItemId.WHITE_HEADBAND)),
    BLUE_HEADBAND(ItemId.BLUE_HEADBAND, new StorableSetPiece(ItemId.BLUE_HEADBAND)),
    GOLD_HEADBAND(ItemId.GOLD_HEADBAND, new StorableSetPiece(ItemId.GOLD_HEADBAND)),
    PINK_HEADBAND(ItemId.PINK_HEADBAND, new StorableSetPiece(ItemId.PINK_HEADBAND)),
    GREEN_HEADBAND(ItemId.GREEN_HEADBAND, new StorableSetPiece(ItemId.GREEN_HEADBAND)),
    ADAMANT_HERALDIC_HELM1(ItemId.ADAMANT_HELM_H1_10709, new StorableSetPiece(ItemId.ADAMANT_HELM_H1)),
    ADAMANT_HERALDIC_HELM2(ItemId.ADAMANT_HELM_H2_10710, new StorableSetPiece(ItemId.ADAMANT_HELM_H2)),
    ADAMANT_HERALDIC_HELM3(ItemId.ADAMANT_HELM_H3_10711, new StorableSetPiece(ItemId.ADAMANT_HELM_H3)),
    ADAMANT_HERALDIC_HELM4(ItemId.ADAMANT_HELM_H4_10712, new StorableSetPiece(ItemId.ADAMANT_HELM_H4)),
    ADAMANT_HERALDIC_HELM5(ItemId.ADAMANT_HELM_H5_10713, new StorableSetPiece(ItemId.ADAMANT_HELM_H5)),
    TRIMMED_AMULET_OF_STRENGTH(ItemId.STRENGTH_AMULET_T_10736, new StorableSetPiece(ItemId.STRENGTH_AMULET_T)),
    WIZARD_BOOTS(ItemId.WIZARD_BOOTS_10689, new StorableSetPiece(ItemId.WIZARD_BOOTS)),
    MITHRIL_ARMOUR_GOLD(ItemId.MITHRIL_PLATEBODY_G, new StorableSetPiece(ItemId.MITHRIL_FULL_HELM_G), new StorableSetPiece(ItemId.MITHRIL_PLATEBODY_G), new StorableSetPiece(ItemId.MITHRIL_PLATELEGS_G, ItemId.MITHRIL_PLATESKIRT_G), new StorableSetPiece(ItemId.MITHRIL_KITESHIELD_G)),
    MITHRIL_ARMOUR_TRIM(ItemId.MITHRIL_PLATEBODY_T, new StorableSetPiece(ItemId.MITHRIL_FULL_HELM_T), new StorableSetPiece(ItemId.MITHRIL_PLATEBODY_T), new StorableSetPiece(ItemId.MITHRIL_PLATELEGS_T, ItemId.MITHRIL_PLATESKIRT_T), new StorableSetPiece(ItemId.MITHRIL_KITESHIELD_T)),
    LEPRECHAUN_HAT(ItemId.LEPRECHAUN_HAT, new StorableSetPiece(ItemId.LEPRECHAUN_HAT)),
    BLACK_LEPRECHAUN_HAT(ItemId.BLACK_LEPRECHAUN_HAT, new StorableSetPiece(ItemId.BLACK_LEPRECHAUN_HAT)),
    CAT_MASK(ItemId.CAT_MASK, new StorableSetPiece(ItemId.CAT_MASK)),
    PENGUIN_MASK(ItemId.PENGUIN_MASK, new StorableSetPiece(ItemId.PENGUIN_MASK)),
    BLACK_UNICORN_MASK(ItemId.BLACK_UNICORN_MASK, new StorableSetPiece(ItemId.BLACK_UNICORN_MASK)),
    WHITE_UNICORN_MASK(ItemId.WHITE_UNICORN_MASK, new StorableSetPiece(ItemId.WHITE_UNICORN_MASK)),
    ARMADYL_VESTMENTS(ItemId.ARMADYL_ROBE_TOP, new StorableSetPiece(ItemId.ARMADYL_MITRE), new StorableSetPiece(ItemId.ARMADYL_ROBE_TOP), new StorableSetPiece(ItemId.ARMADYL_ROBE_LEGS), new StorableSetPiece(ItemId.ARMADYL_CLOAK), new StorableSetPiece(ItemId.ARMADYL_CROZIER), new StorableSetPiece(ItemId.ARMADYL_STOLE)),
    ANCIENT_VESTMENTS(ItemId.ANCIENT_ROBE_TOP, new StorableSetPiece(ItemId.ANCIENT_MITRE), new StorableSetPiece(ItemId.ANCIENT_ROBE_TOP), new StorableSetPiece(ItemId.ANCIENT_ROBE_LEGS), new StorableSetPiece(ItemId.ANCIENT_CLOAK), new StorableSetPiece(ItemId.ANCIENT_CROZIER), new StorableSetPiece(ItemId.ANCIENT_STOLE)),
    BANDOS_VESTMENTS(ItemId.BANDOS_ROBE_TOP, new StorableSetPiece(ItemId.BANDOS_MITRE), new StorableSetPiece(ItemId.BANDOS_ROBE_TOP), new StorableSetPiece(ItemId.BANDOS_ROBE_LEGS), new StorableSetPiece(ItemId.BANDOS_CLOAK), new StorableSetPiece(ItemId.BANDOS_CROZIER), new StorableSetPiece(ItemId.BANDOS_STOLE)),
    TOWN_CRIER_HAT(ItemId.CRIER_HAT, new StorableSetPiece(ItemId.CRIER_HAT)),
    TOWN_CRIER_BELL(ItemId.CRIER_BELL, new StorableSetPiece(ItemId.CRIER_BELL)),
    TOWN_CRIER_COAT(ItemId.CRIER_COAT, new StorableSetPiece(ItemId.CRIER_COAT)),
    ADAMANT_CANE(ItemId.ADAMANT_CANE, new StorableSetPiece(ItemId.ADAMANT_CANE)),
    HOLY_SANDALS(ItemId.HOLY_SANDALS, new StorableSetPiece(ItemId.HOLY_SANDALS)),
    CLUELESS_SCROLL(ItemId.CLUELESS_SCROLL, new StorableSetPiece(ItemId.CLUELESS_SCROLL)),
    ARCEUUS_BANNER(ItemId.ARCEUUS_BANNER, new StorableSetPiece(ItemId.ARCEUUS_BANNER)),
    HOSIDIUS_BANNER(ItemId.HOSIDIUS_BANNER, new StorableSetPiece(ItemId.HOSIDIUS_BANNER)),
    LOVAKENGJ_BANNER(ItemId.LOVAKENGJ_BANNER, new StorableSetPiece(ItemId.LOVAKENGJ_BANNER)),
    PISCARILIUS_BANNER(ItemId.PISCARILIUS_BANNER, new StorableSetPiece(ItemId.PISCARILIUS_BANNER)),
    SHAYZIEN_BANNER(ItemId.SHAYZIEN_BANNER, new StorableSetPiece(ItemId.SHAYZIEN_BANNER)),
    CABBAGE_ROUND_SHIELD(ItemId.CABBAGE_ROUND_SHIELD, new StorableSetPiece(ItemId.CABBAGE_ROUND_SHIELD)),
    RUNE_HERALDIC_KITESHIELD1(ItemId.RUNE_SHIELD_H1_10667, new StorableSetPiece(ItemId.RUNE_SHIELD_H1)),
    RUNE_HERALDIC_KITESHIELD2(ItemId.RUNE_SHIELD_H2_10670, new StorableSetPiece(ItemId.RUNE_SHIELD_H2)),
    RUNE_HERALDIC_KITESHIELD3(ItemId.RUNE_SHIELD_H3_10673, new StorableSetPiece(ItemId.RUNE_SHIELD_H3)),
    RUNE_HERALDIC_KITESHIELD4(ItemId.RUNE_SHIELD_H4_10676, new StorableSetPiece(ItemId.RUNE_SHIELD_H4)),
    RUNE_HERALDIC_KITESHIELD5(ItemId.RUNE_SHIELD_H5_10679, new StorableSetPiece(ItemId.CHAOS_RUNE_7560)),
    BLUE_DRAGONHIDE_GOLD(ItemId.DHIDE_BODY_G_10684, new StorableSetPiece(ItemId.BLUE_DHIDE_BODY_G), new StorableSetPiece(ItemId.BLUE_DHIDE_CHAPS_G)),
    BLUE_DRAGONHIDE_TRIM(ItemId.DHIDE_BODY_T_10685, new StorableSetPiece(ItemId.BLUE_DHIDE_BODY_T), new StorableSetPiece(ItemId.BLUE_DHIDE_CHAPS_T)),
    ENCHANTED_ROBES(ItemId.ENCHANTED_TOP_10688, new StorableSetPiece(ItemId.ENCHANTED_HAT), new StorableSetPiece(ItemId.ENCHANTED_TOP), new StorableSetPiece(ItemId.ENCHANTED_ROBE)),
    ROBIN_HOOD_HAT(ItemId.ROBIN_HOOD_HAT_10796, new StorableSetPiece(ItemId.ROBIN_HOOD_HAT)),
    GOLD_TRIMMED_RUNE_ARMOUR(ItemId.RUNE_PLATEBODY_G_10798, new StorableSetPiece(ItemId.RUNE_FULL_HELM_G), new StorableSetPiece(ItemId.RUNE_PLATEBODY_G), new StorableSetPiece(ItemId.RUNE_PLATELEGS_G, ItemId.RUNE_PLATESKIRT_G), new StorableSetPiece(ItemId.RUNE_KITESHIELD_G)),
    TRIMMED_RUNE_ARMOUR(ItemId.RUNE_PLATEBODY_T_10800, new StorableSetPiece(ItemId.RUNE_FULL_HELM_T), new StorableSetPiece(ItemId.RUNE_PLATEBODY_T), new StorableSetPiece(ItemId.RUNE_PLATELEGS_T, ItemId.RUNE_PLATESKIRT_T), new StorableSetPiece(ItemId.RUNE_KITESHIELD_T)),
    BROWN_CAVALIER(ItemId.TAN_CAVALIER_10802, new StorableSetPiece(ItemId.TAN_CAVALIER)),
    DARK_BROWN_CAVALIER(ItemId.DARK_CAVALIER_10804, new StorableSetPiece(ItemId.DARK_CAVALIER)),
    BLACK_CAVALIER(ItemId.BLACK_CAVALIER_10806, new StorableSetPiece(ItemId.BLACK_CAVALIER)),
    RED_CAVALIER(ItemId.RED_CAVALIER, new StorableSetPiece(ItemId.RED_CAVALIER)),
    BLUE_CAVALIER(ItemId.NAVY_CAVALIER, new StorableSetPiece(ItemId.NAVY_CAVALIER)),
    WHITE_CAVALIER(ItemId.WHITE_CAVALIER, new StorableSetPiece(ItemId.WHITE_CAVALIER)),
    PIRATE_HAT(ItemId.PIRATES_HAT_10774, new StorableSetPiece(ItemId.PIRATE_HAT)),
    ZAMORAK_RUNE_ARMOUR(ItemId.ZAMORAK_PLATEBODY_10776, new StorableSetPiece(ItemId.ZAMORAK_FULL_HELM), new StorableSetPiece(ItemId.ZAMORAK_PLATEBODY), new StorableSetPiece(ItemId.ZAMORAK_PLATELEGS, ItemId.ZAMORAK_PLATESKIRT), new StorableSetPiece(ItemId.ZAMORAK_KITESHIELD)),
    SARADOMIN_RUNE_ARMOUR(ItemId.SARADOMIN_PLATE, new StorableSetPiece(ItemId.SARADOMIN_FULL_HELM), new StorableSetPiece(ItemId.SARADOMIN_PLATEBODY), new StorableSetPiece(ItemId.SARADOMIN_PLATELEGS, ItemId.SARADOMIN_PLATESKIRT), new StorableSetPiece(ItemId.SARADOMIN_KITESHIELD)),
    GUTHIX_RUNE_ARMOUR(ItemId.GUTHIX_PLATEBODY_10780, new StorableSetPiece(ItemId.GUTHIX_FULL_HELM), new StorableSetPiece(ItemId.GUTHIX_PLATEBODY), new StorableSetPiece(ItemId.GUTHIX_PLATELEGS, ItemId.GUTHIX_PLATESKIRT), new StorableSetPiece(ItemId.GUTHIX_KITESHIELD)),
    GOLD_PLATED_RUNE_ARMOUR(ItemId.GILDED_PLATEBODY_10782, new StorableSetPiece(ItemId.GILDED_FULL_HELM), new StorableSetPiece(ItemId.GILDED_PLATEBODY), new StorableSetPiece(ItemId.GILDED_PLATELEGS, ItemId.GILDED_PLATESKIRT), new StorableSetPiece(ItemId.GILDED_KITESHIELD)),
    RUNE_HERALDIC_HELM1(ItemId.RUNE_HELM_H1_10704, new StorableSetPiece(ItemId.RUNE_HELM_H1)),
    RUNE_HERALDIC_HELM2(ItemId.RUNE_HELM_H2_10705, new StorableSetPiece(ItemId.RUNE_HELM_H2)),
    RUNE_HERALDIC_HELM3(ItemId.RUNE_HELM_H3_10706, new StorableSetPiece(ItemId.RUNE_HELM_H3)),
    RUNE_HERALDIC_HELM4(ItemId.RUNE_HELM_H4_10707, new StorableSetPiece(ItemId.RUNE_HELM_H4)),
    RUNE_HERALDIC_HELM5(ItemId.RUNE_HELM_H5_10708, new StorableSetPiece(ItemId.RUNE_HELM_H5)),
    TRIMMED_AMULET_OF_GLORY(ItemId.AMULET_OF_GLORY_T_10719, new StorableSetPiece(ItemId.AMULET_OF_GLORY_T)),
    SARADOMIN_VESTMENTS(ItemId.SARADOMIN_ROBE_TOP_10784, new StorableSetPiece(ItemId.SARADOMIN_MITRE), new StorableSetPiece(ItemId.SARADOMIN_ROBE_TOP), new StorableSetPiece(ItemId.SARADOMIN_ROBE_LEGS), new StorableSetPiece(ItemId.SARADOMIN_CLOAK), new StorableSetPiece(ItemId.SARADOMIN_CROZIER), new StorableSetPiece(ItemId.SARADOMIN_STOLE)),
    GUTHIX_VESTMENTS(ItemId.GUTHIX_ROBE_TOP_10788, new StorableSetPiece(ItemId.GUTHIX_MITRE), new StorableSetPiece(ItemId.GUTHIX_ROBE_TOP), new StorableSetPiece(ItemId.GUTHIX_ROBE_LEGS), new StorableSetPiece(ItemId.GUTHIX_CLOAK), new StorableSetPiece(ItemId.GUTHIX_CROZIER), new StorableSetPiece(ItemId.GUTHIX_STOLE)),
    ZAMORAK_VESTMENTS(ItemId.ZAMORAK_ROBE_TOP_10786, new StorableSetPiece(ItemId.ZAMORAK_MITRE), new StorableSetPiece(ItemId.ZAMORAK_ROBE_TOP), new StorableSetPiece(ItemId.ZAMORAK_ROBE_LEGS), new StorableSetPiece(ItemId.ZAMORAK_CLOAK), new StorableSetPiece(ItemId.ZAMORAK_CROZIER), new StorableSetPiece(ItemId.ZAMORAK_STOLE)),
    SARADOMIN_BLESSED_DRAGONHIDE(ItemId.SARADOMIN_DHIDE_10792, new StorableSetPiece(ItemId.SARADOMIN_COIF), new StorableSetPiece(ItemId.SARADOMIN_DHIDE), new StorableSetPiece(ItemId.SARADOMIN_CHAPS), new StorableSetPiece(ItemId.SARADOMIN_BRACERS)),
    GUTHIX_BLESSED_DRAGONHIDE(ItemId.GUTHIX_DRAGONHIDE, new StorableSetPiece(ItemId.GUTHIX_COIF), new StorableSetPiece(ItemId.GUTHIX_DHIDE), new StorableSetPiece(ItemId.GUTHIX_CHAPS), new StorableSetPiece(ItemId.GUTHIX_BRACERS)),
    ZAMORAK_BLESSED_DRAGONHIDE(ItemId.ZAMORAK_DHIDE_10790, new StorableSetPiece(ItemId.ZAMORAK_COIF), new StorableSetPiece(ItemId.ZAMORAK_DHIDE), new StorableSetPiece(ItemId.ZAMORAK_CHAPS), new StorableSetPiece(ItemId.ZAMORAK_BRACERS)),
    CAVALIER_MASK(ItemId.CAVALIER_MASK, new StorableSetPiece(ItemId.CAVALIER_MASK_11280)),
    RED_DRAGONHIDE_GOLD(ItemId.RED_DHIDE_BODY_G, new StorableSetPiece(ItemId.RED_DHIDE_BODY_G), new StorableSetPiece(ItemId.RED_DHIDE_CHAPS_G)),
    RED_DRAGONHIDE_TRIM(ItemId.RED_DHIDE_BODY_T, new StorableSetPiece(ItemId.RED_DHIDE_BODY_T), new StorableSetPiece(ItemId.RED_DHIDE_CHAPS_T)),
    PITH_HELMET(ItemId.PITH_HELMET, new StorableSetPiece(ItemId.PITH_HELMET)),
    EXPLORER_BACKPACK(ItemId.EXPLORER_BACKPACK, new StorableSetPiece(ItemId.EXPLORER_BACKPACK)),
    ARMADYL_RUNE_ARMOUR(ItemId.ARMADYL_PLATEBODY, new StorableSetPiece(ItemId.ARMADYL_FULL_HELM), new StorableSetPiece(ItemId.ARMADYL_PLATEBODY), new StorableSetPiece(ItemId.ARMADYL_PLATELEGS, ItemId.ARMADYL_PLATESKIRT), new StorableSetPiece(ItemId.ARMADYL_KITESHIELD)),
    BANDOS_RUNE_ARMOUR(ItemId.BANDOS_PLATEBODY, new StorableSetPiece(ItemId.BANDOS_FULL_HELM), new StorableSetPiece(ItemId.BANDOS_PLATEBODY), new StorableSetPiece(ItemId.BANDOS_PLATELEGS, ItemId.BANDOS_PLATESKIRT), new StorableSetPiece(ItemId.BANDOS_KITESHIELD)),
    ANCIENT_RUNE_ARMOUR(ItemId.ANCIENT_PLATEBODY, new StorableSetPiece(ItemId.ANCIENT_FULL_HELM), new StorableSetPiece(ItemId.ANCIENT_PLATEBODY), new StorableSetPiece(ItemId.ANCIENT_PLATELEGS, ItemId.ANCIENT_PLATESKIRT), new StorableSetPiece(ItemId.ANCIENT_KITESHIELD)),
    ARMADYL_BLESSED_DRAGONHIDE(ItemId.ARMADYL_DHIDE, new StorableSetPiece(ItemId.ARMADYL_COIF), new StorableSetPiece(ItemId.ARMADYL_DHIDE), new StorableSetPiece(ItemId.ARMADYL_CHAPS), new StorableSetPiece(ItemId.ARMADYL_BRACERS)),
    BANDOS_BLESSED_DRAGONHIDE(ItemId.BANDOS_DHIDE, new StorableSetPiece(ItemId.BANDOS_COIF), new StorableSetPiece(ItemId.BANDOS_DHIDE), new StorableSetPiece(ItemId.BANDOS_CHAPS), new StorableSetPiece(ItemId.BANDOS_BRACERS)),
    ANCIENT_BLESSED_DRAGONHIDE(ItemId.ANCIENT_DHIDE, new StorableSetPiece(ItemId.ANCIENT_COIF), new StorableSetPiece(ItemId.ANCIENT_DHIDE), new StorableSetPiece(ItemId.ANCIENT_CHAPS), new StorableSetPiece(ItemId.ANCIENT_BRACERS)),
    GREEN_DRAGON_MASK(ItemId.GREEN_DRAGON_MASK, new StorableSetPiece(ItemId.GREEN_DRAGON_MASK)),
    BLUE_DRAGON_MASK(ItemId.BLUE_DRAGON_MASK, new StorableSetPiece(ItemId.BLUE_DRAGON_MASK)),
    RED_DRAGON_MASK(ItemId.RED_DRAGON_MASK, new StorableSetPiece(ItemId.RED_DRAGON_MASK)),
    BLACK_DRAGON_MASK(ItemId.BLACK_DRAGON_MASK, new StorableSetPiece(ItemId.BLACK_DRAGON_MASK)),
    RUNE_CANE(ItemId.RUNE_CANE, new StorableSetPiece(ItemId.RUNE_CANE)),
    ZOMBIE_HEAD(ItemId.ZOMBIE_HEAD_19912, new StorableSetPiece(ItemId.ZOMBIE_HEAD_19912)),
    CYCLOPS_HEAD(ItemId.CYCLOPS_HEAD, new StorableSetPiece(ItemId.CYCLOPS_HEAD)),
    GILDED_RUNE_MED_HELM(ItemId.GILDED_MED_HELM, new StorableSetPiece(ItemId.GILDED_MED_HELM)),
    GILDED_RUNE_CHAINBODY(ItemId.GILDED_CHAINBODY, new StorableSetPiece(ItemId.GILDED_CHAINBODY)),
    GILDED_RUNE_SQ_SHIELD(ItemId.GILDED_SQ_SHIELD, new StorableSetPiece(ItemId.GILDED_SQ_SHIELD)),
    GILDED_RUNE_2H_SWORD(ItemId.GILDED_2H_SWORD, new StorableSetPiece(ItemId.GILDED_2H_SWORD)),
    GILDED_RUNE_SPEAR(ItemId.GILDED_SPEAR, new StorableSetPiece(ItemId.GILDED_SPEAR)),
    GILDED_RUNE_HASTA(ItemId.GILDED_HASTA, new StorableSetPiece(ItemId.GILDED_HASTA)),
    NUNCHAKU(ItemId.NUNCHAKU, new StorableSetPiece(ItemId.NUNCHAKU)),
    SARADOMIN_DHIDE_BOOTS(ItemId.SARADOMIN_DHIDE_BOOTS, new StorableSetPiece(ItemId.SARADOMIN_DHIDE_BOOTS)),
    BANDOS_DHIDE_BOOTS(ItemId.BANDOS_DHIDE_BOOTS, new StorableSetPiece(ItemId.BANDOS_DHIDE_BOOTS)),
    ARMADYL_DHIDE_BOOTS(ItemId.ARMADYL_DHIDE_BOOTS, new StorableSetPiece(ItemId.ARMADYL_DHIDE_BOOTS)),
    GUTHIX_DHIDE_BOOTS(ItemId.GUTHIX_DHIDE_BOOTS, new StorableSetPiece(ItemId.GUTHIX_DHIDE_BOOTS)),
    ZAMORAK_DHIDE_BOOTS(ItemId.ZAMORAK_DHIDE_BOOTS, new StorableSetPiece(ItemId.ZAMORAK_DHIDE_BOOTS)),
    ANCIENT_DHIDE_BOOTS(ItemId.ANCIENT_DHIDE_BOOTS, new StorableSetPiece(ItemId.ANCIENT_DHIDE_BOOTS)),
    THIRD_AGE_RANGER_KIT(ItemId._3RD_AGE_RANGE_TOP, new StorableSetPiece(ItemId._3RD_AGE_RANGE_COIF), new StorableSetPiece(ItemId._3RD_AGE_RANGE_TOP), new StorableSetPiece(ItemId._3RD_AGE_RANGE_LEGS), new StorableSetPiece(ItemId._3RD_AGE_VAMBRACES)),
    THIRD_AGE_MAGE_ROBES(ItemId._3RD_AGE_ROBE_TOP, new StorableSetPiece(ItemId._3RD_AGE_MAGE_HAT), new StorableSetPiece(ItemId._3RD_AGE_ROBE_TOP), new StorableSetPiece(ItemId._3RD_AGE_ROBE), new StorableSetPiece(ItemId._3RD_AGE_AMULET)),
    THIRD_AGE_ARMOUR(ItemId._3RD_AGE_PLATEBODY, new StorableSetPiece(ItemId._3RD_AGE_FULL_HELMET), new StorableSetPiece(ItemId._3RD_AGE_PLATEBODY), new StorableSetPiece(ItemId._3RD_AGE_PLATELEGS), new StorableSetPiece(ItemId._3RD_AGE_KITESHIELD)),
    DRAGON_CANE(ItemId.DRAGON_CANE, new StorableSetPiece(ItemId.DRAGON_CANE)),
    BRIEFCASE(ItemId.BRIEFCASE, new StorableSetPiece(ItemId.BRIEFCASE)),
    SAGACIOUS_SPECTACLES(ItemId.SAGACIOUS_SPECTACLES, new StorableSetPiece(ItemId.SAGACIOUS_SPECTACLES)),
    ROYAL_OUTFIT(ItemId.ROYAL_GOWN_TOP, new StorableSetPiece(ItemId.ROYAL_CROWN), new StorableSetPiece(ItemId.ROYAL_GOWN_TOP), new StorableSetPiece(ItemId.ROYAL_GOWN_BOTTOM), new StorableSetPiece(ItemId.ROYAL_SCEPTRE)),
    BRONZE_DRAGON_MASK(ItemId.BRONZE_DRAGON_MASK, new StorableSetPiece(ItemId.BRONZE_DRAGON_MASK)),
    IRON_DRAGON_MASK(ItemId.IRON_DRAGON_MASK, new StorableSetPiece(ItemId.IRON_DRAGON_MASK)),
    STEEL_DRAGON_MASK(ItemId.STEEL_DRAGON_MASK, new StorableSetPiece(ItemId.STEEL_DRAGON_MASK)),
    MITHRIL_DRAGON_MASK(ItemId.MITHRIL_DRAGON_MASK, new StorableSetPiece(ItemId.MITHRIL_DRAGON_MASK)),
    LAVA_DRAGON_MASK(ItemId.LAVA_DRAGON_MASK, new StorableSetPiece(ItemId.LAVA_DRAGON_MASK)),
    AFRO(ItemId.AFRO, new StorableSetPiece(ItemId.AFRO)),
    KATANA(ItemId.KATANA, new StorableSetPiece(ItemId.KATANA)),
    BIG_PIRATE_HAT(ItemId.BIG_PIRATE_HAT, new StorableSetPiece(ItemId.BIG_PIRATE_HAT)),
    TOP_HAT(ItemId.TOP_HAT, new StorableSetPiece(ItemId.TOP_HAT)),
    MONOCLE(ItemId.MONOCLE, new StorableSetPiece(ItemId.MONOCLE)),
    BLACK_DRAGONHIDE_GOLD(ItemId.BLACK_DHIDE_BODY_G, new StorableSetPiece(ItemId.BLACK_DHIDE_BODY_G), new StorableSetPiece(ItemId.BLACK_DHIDE_CHAPS_G)),
    BLACK_DRAGONHIDE_TRIM(ItemId.BLACK_DHIDE_BODY_T, new StorableSetPiece(ItemId.BLACK_DHIDE_BODY_T), new StorableSetPiece(ItemId.BLACK_DHIDE_CHAPS_T)),
    MUSKETEER_OUTFIT(ItemId.MUSKETEER_TABARD, new StorableSetPiece(ItemId.MUSKETEER_HAT), new StorableSetPiece(ItemId.MUSKETEER_TABARD), new StorableSetPiece(ItemId.MUSKETEER_PANTS)),
    PARTYHAT_AND_SPECS(ItemId.PARTYHAT__SPECS, new StorableSetPiece(ItemId.PARTYHAT__SPECS)),
    PIRATE_HAT_AND_PATCH(ItemId.PIRATE_HAT__PATCH, new StorableSetPiece(ItemId.PIRATE_HAT__PATCH)),
    TOP_HAT_AND_MONOCLE(ItemId.TOP_HAT__MONOCLE, new StorableSetPiece(ItemId.TOP_HAT__MONOCLE)),
    DEERSTALKER(ItemId.DEERSTALKER, new StorableSetPiece(ItemId.DEERSTALKER)),
    HEAVY_CASKET(ItemId.HEAVY_CASKET, new StorableSetPiece(ItemId.HEAVY_CASKET)),
    ARCEUUS_HOUSE_SCARF(ItemId.ARCEUUS_SCARF, new StorableSetPiece(ItemId.ARCEUUS_SCARF)),
    HOSIDIUS_HOUSE_SCARF(ItemId.HOSIDIUS_SCARF, new StorableSetPiece(ItemId.HOSIDIUS_SCARF)),
    LOVAKENGJ_HOUSE_SCARF(ItemId.LOVAKENGJ_SCARF, new StorableSetPiece(ItemId.LOVAKENGJ_SCARF)),
    PISCARILIUS_HOUSE_SCARF(ItemId.PISCARILIUS_SCARF, new StorableSetPiece(ItemId.PISCARILIUS_SCARF)),
    SHAYZIEN_HOUSE_SCARF(ItemId.SHAYZIEN_SCARF, new StorableSetPiece(ItemId.SHAYZIEN_SCARF)),
    BLACKSMITHS_HELM(ItemId.BLACKSMITHS_HELM, new StorableSetPiece(ItemId.BLACKSMITHS_HELM)),
    BUCKET_HELM(ItemId.BUCKET_HELM, new StorableSetPiece(ItemId.BUCKET_HELM)),
    RANGER_GLOVES(ItemId.RANGER_GLOVES, new StorableSetPiece(ItemId.RANGER_GLOVES)),
    HOLY_WRAPS(ItemId.HOLY_WRAPS, new StorableSetPiece(ItemId.HOLY_WRAPS)),
    RING_OF_NATURE(ItemId.RING_OF_NATURE, new StorableSetPiece(ItemId.RING_OF_NATURE)),
    THIRD_AGE_WAND(ItemId._3RD_AGE_WAND, new StorableSetPiece(ItemId._3RD_AGE_WAND)),
    THIRD_AGE_BOW(ItemId._3RD_AGE_BOW, new StorableSetPiece(ItemId._3RD_AGE_BOW)),
    THIRD_AGE_LONGSWORD(ItemId._3RD_AGE_LONGSWORD, new StorableSetPiece(ItemId._3RD_AGE_LONGSWORD)),
    DARK_TUXEDO_OUTFIT(ItemId.DARK_BOW_TIE, new StorableSetPiece(ItemId.DARK_TUXEDO_JACKET), new StorableSetPiece(ItemId.DARK_TROUSERS), new StorableSetPiece(ItemId.DARK_TUXEDO_CUFFS), new StorableSetPiece(ItemId.DARK_TUXEDO_SHOES), new StorableSetPiece(ItemId.DARK_BOW_TIE)),
    LIGHT_TUXEDO_OUTFIT(ItemId.LIGHT_BOW_TIE, new StorableSetPiece(ItemId.LIGHT_TUXEDO_JACKET), new StorableSetPiece(ItemId.LIGHT_TROUSERS), new StorableSetPiece(ItemId.LIGHT_TUXEDO_CUFFS), new StorableSetPiece(ItemId.LIGHT_TUXEDO_SHOES), new StorableSetPiece(ItemId.LIGHT_BOW_TIE)),
    FANCY_TIARA(ItemId.FANCY_TIARA, new StorableSetPiece(ItemId.FANCY_TIARA)),
    THIRD_AGE_AXE(ItemId._3RD_AGE_AXE, new StorableSetPiece(ItemId._3RD_AGE_AXE)),
    THIRD_AGE_PICKAXE(ItemId._3RD_AGE_PICKAXE, new StorableSetPiece(ItemId._3RD_AGE_PICKAXE)),
    RING_OF_COINS(ItemId.RING_OF_COINS, new StorableSetPiece(ItemId.RING_OF_COINS)),
    LESSER_DEMON_MASK(ItemId.LESSER_DEMON_MASK, new StorableSetPiece(ItemId.LESSER_DEMON_MASK)),
    GREATER_DEMON_MASK(ItemId.GREATER_DEMON_MASK, new StorableSetPiece(ItemId.GREATER_DEMON_MASK)),
    BLACK_DEMON_MASK(ItemId.BLACK_DEMON_MASK, new StorableSetPiece(ItemId.BLACK_DEMON_MASK)),
    OLD_DEMON_MASK(ItemId.OLD_DEMON_MASK, new StorableSetPiece(ItemId.OLD_DEMON_MASK)),
    JUNGLE_DEMON_MASK(ItemId.JUNGLE_DEMON_MASK, new StorableSetPiece(ItemId.JUNGLE_DEMON_MASK)),
    OBSIDIAN_CAPE_RED(ItemId.OBSIDIAN_CAPE_R, new StorableSetPiece(ItemId.OBSIDIAN_CAPE_R)),
    HALF_MOON_SPECTACLES(ItemId.HALF_MOON_SPECTACLES, new StorableSetPiece(ItemId.HALF_MOON_SPECTACLES)),
    ALE_OF_THE_GODS(ItemId.ALE_OF_THE_GODS, new StorableSetPiece(ItemId.ALE_OF_THE_GODS)),
    BUCKET_HELM_GOLD(ItemId.BUCKET_HELM_G, new StorableSetPiece(ItemId.BUCKET_HELM_G)),
    BOWL_WIG(ItemId.BOWL_WIG, new StorableSetPiece(ItemId.BOWL_WIG)),
    SHAYZIEN_HOUSE_HOOD(ItemId.SHAYZIEN_HOOD, new StorableSetPiece(ItemId.SHAYZIEN_HOOD)),
    HOSIDIUS_HOUSE_HOOD(ItemId.HOSIDIUS_HOOD, new StorableSetPiece(ItemId.HOSIDIUS_HOOD)),
    ARCEUUS_HOUSE_HOOD(ItemId.ARCEUUS_HOOD, new StorableSetPiece(ItemId.ARCEUUS_HOOD)),
    PISCARILIUS_HOUSE_HOOD(ItemId.PISCARILIUS_HOOD, new StorableSetPiece(ItemId.PISCARILIUS_HOOD)),
    LOVAKENGJ_HOUSE_HOOD(ItemId.LOVAKENGJ_HOOD, new StorableSetPiece(ItemId.LOVAKENGJ_HOOD)),
    MOLE_SLIPPERS(ItemId.MOLE_SLIPPERS, new StorableSetPiece(ItemId.MOLE_SLIPPERS)),
    GILDED_SPADE(ItemId.GILDED_SPADE, new StorableSetPiece(ItemId.GILDED_SPADE)),
    GILDED_PICKAXE(ItemId.GILDED_PICKAXE, new StorableSetPiece(ItemId.GILDED_PICKAXE)),
    GILDED_SCIMITAR(ItemId.GILDED_SCIMITAR, new StorableSetPiece(ItemId.GILDED_SCIMITAR)),
    GILDED_AXE(ItemId.GILDED_AXE, new StorableSetPiece(ItemId.GILDED_AXE)),
    GILDED_DHIDE_BODY(ItemId.GILDED_DHIDE_BODY, new StorableSetPiece(ItemId.GILDED_DHIDE_BODY)),
    GILDED_DHIDE_CHAPS(ItemId.GILDED_DHIDE_CHAPS, new StorableSetPiece(ItemId.GILDED_DHIDE_CHAPS)),
    GILDED_BOOTS(ItemId.GILDED_BOOTS, new StorableSetPiece(ItemId.GILDED_BOOTS)),
    GILDED_COIF(ItemId.GILDED_COIF, new StorableSetPiece(ItemId.GILDED_COIF)),
    GILDED_DHIDE_VAMBS(ItemId.GILDED_DHIDE_VAMBS, new StorableSetPiece(ItemId.GILDED_DHIDE_VAMBS)),
    FROG_SLIPPERS(ItemId.FROG_SLIPPERS, new StorableSetPiece(ItemId.FROG_SLIPPERS)),
    WOLF_CLOAK(ItemId.WOLF_CLOAK, new StorableSetPiece(ItemId.WOLF_CLOAK)),
    CAPE_OF_SKULLS(ItemId.CAPE_OF_SKULLS, new StorableSetPiece(ItemId.CAPE_OF_SKULLS)),
    HAM_JOINT(ItemId.HAM_JOINT, new StorableSetPiece(ItemId.HAM_JOINT)),
    BEAR_FEET(ItemId.BEAR_FEET, new StorableSetPiece(ItemId.BEAR_FEET)),
    DUAL_SAI(ItemId.DUAL_SAI, new StorableSetPiece(ItemId.DUAL_SAI)),
    ARMADYL_DHIDE_SHIELD(ItemId.ARMADYL_DHIDE_SHIELD, new StorableSetPiece(ItemId.ARMADYL_DHIDE_SHIELD)),
    CLIMBING_BOOTS_G(ItemId.CLIMBING_BOOTS_G, new StorableSetPiece(ItemId.CLIMBING_BOOTS_G)),
    RANGERS_TUNIC(ItemId.RANGERS_TUNIC, new StorableSetPiece(ItemId.RANGERS_TUNIC)),
    RANGERS_TIGHTS(ItemId.RANGERS_TIGHTS, new StorableSetPiece(ItemId.RANGERS_TIGHTS)),
    STAFF_OF_BOB_THE_CAT(ItemId.STAFF_OF_BOB_THE_CAT, new StorableSetPiece(ItemId.STAFF_OF_BOB_THE_CAT)),
    SCROLL_SACK(ItemId.SCROLL_SACK, new StorableSetPiece(ItemId.SCROLL_SACK)),
    MONKS_ROBE_T(ItemId.MONKS_ROBE_T, new StorableSetPiece(ItemId.MONKS_ROBE_T)),
    THIEVING_BAG(ItemId.THIEVING_BAG, new StorableSetPiece(ItemId.THIEVING_BAG)),
    AMULET_OF_POWER_T(ItemId.AMULET_OF_POWER_T, new StorableSetPiece(ItemId.AMULET_OF_POWER_T)),
    RUNE_DRAGON_MASK(ItemId.RUNE_DRAGON_MASK, new StorableSetPiece(ItemId.RUNE_DRAGON_MASK)),
    SANDWICH_LADY_TOP(ItemId.SANDWICH_LADY_TOP, new StorableSetPiece(ItemId.SANDWICH_LADY_TOP)),
    SANDWICH_LADY_BOTTOM(ItemId.SANDWICH_LADY_BOTTOM, new StorableSetPiece(ItemId.SANDWICH_LADY_BOTTOM)),
    LEATHER_BODY_G(ItemId.LEATHER_BODY_G, new StorableSetPiece(ItemId.LEATHER_BODY_G)),
    LEATHER_CHAPS_G(ItemId.LEATHER_CHAPS_G, new StorableSetPiece(ItemId.LEATHER_CHAPS_G)),
    FREMENNIK_KILT(ItemId.FREMENNIK_KILT, new StorableSetPiece(ItemId.FREMENNIK_KILT)),
    SARADOMIN_DHIDE_SHIELD(ItemId.SARADOMIN_DHIDE_SHIELD, new StorableSetPiece(ItemId.SARADOMIN_DHIDE_SHIELD)),
    GIANT_BOOT(ItemId.GIANT_BOOT, new StorableSetPiece(ItemId.GIANT_BOOT)),
    ZAMORAK_DHIDE_SHIELD(ItemId.ZAMORAK_DHIDE_SHIELD, new StorableSetPiece(ItemId.ZAMORAK_DHIDE_SHIELD)),
    SPIKED_MANACLES(ItemId.SPIKED_MANACLES, new StorableSetPiece(ItemId.SPIKED_MANACLES)),
    URIS_HAT(ItemId.URIS_HAT, new StorableSetPiece(ItemId.URIS_HAT)),
    SANDWICH_LADY_HAT(ItemId.SANDWICH_LADY_HAT, new StorableSetPiece(ItemId.SANDWICH_LADY_HAT)),
    BANDOS_DHIDE_SHIELD(ItemId.BANDOS_DHIDE_SHIELD, new StorableSetPiece(ItemId.BANDOS_DHIDE_SHIELD)),
    MONKS_ROBE_TOP_T(ItemId.MONKS_ROBE_TOP_T, new StorableSetPiece(ItemId.MONKS_ROBE_TOP_T)),
    SHOULDER_PARROT(ItemId.SHOULDER_PARROT, new StorableSetPiece(ItemId.SHOULDER_PARROT)),
    ANCIENT_DHIDE_SHIELD(ItemId.ANCIENT_DHIDE_SHIELD, new StorableSetPiece(ItemId.ANCIENT_DHIDE_SHIELD)),
    ADAMANT_DRAGON_MASK(ItemId.ADAMANT_DRAGON_MASK, new StorableSetPiece(ItemId.ADAMANT_DRAGON_MASK)),
    AMULET_OF_DEFENCE_T(ItemId.AMULET_OF_DEFENCE_T, new StorableSetPiece(ItemId.AMULET_OF_DEFENCE_T)),
    WOLF_MASK(ItemId.WOLF_MASK, new StorableSetPiece(ItemId.WOLF_MASK)),
    DEMON_FEET(ItemId.DEMON_FEET, new StorableSetPiece(ItemId.DEMON_FEET)),
    GUTHIX_DHIDE_SHIELD(ItemId.GUTHIX_DHIDE_SHIELD, new StorableSetPiece(ItemId.GUTHIX_DHIDE_SHIELD)),
    RAIN_BOW(ItemId.RAIN_BOW, new StorableSetPiece(ItemId.RAIN_BOW)),
    LEFT_EYE_PATCH(ItemId.LEFT_EYE_PATCH, new StorableSetPiece(ItemId.LEFT_EYE_PATCH)),
    JESTER_CAPE(ItemId.JESTER_CAPE, new StorableSetPiece(ItemId.JESTER_CAPE)),
    _3RD_AGE_DRUIDIC_CLOAK(ItemId._3RD_AGE_DRUIDIC_CLOAK, new StorableSetPiece(ItemId._3RD_AGE_DRUIDIC_CLOAK)),
    _3RD_AGE_DRUIDIC_ROBE_TOP(ItemId._3RD_AGE_DRUIDIC_ROBE_TOP, new StorableSetPiece(ItemId._3RD_AGE_DRUIDIC_ROBE_TOP)),
    _3RD_AGE_DRUIDIC_ROBE_BOTTOMS(ItemId._3RD_AGE_DRUIDIC_ROBE_BOTTOMS, new StorableSetPiece(ItemId._3RD_AGE_DRUIDIC_ROBE_BOTTOMS)),
    _3RD_AGE_DRUIDIC_STAFF(ItemId._3RD_AGE_DRUIDIC_STAFF, new StorableSetPiece(ItemId._3RD_AGE_DRUIDIC_STAFF)),
    _3RD_AGE_CLOAK(ItemId._3RD_AGE_CLOAK, new StorableSetPiece(ItemId._3RD_AGE_CLOAK)),
    RING_OF_3RD_AGE(ItemId.RING_OF_3RD_AGE, new StorableSetPiece(ItemId.RING_OF_3RD_AGE)),
    _3RD_AGE_PLATESKIRT(ItemId._3RD_AGE_PLATESKIRT, new StorableSetPiece(ItemId._3RD_AGE_PLATESKIRT)),
    SAMURAI_OUTFIT(ItemId.SAMURAI_SHIRT, new StorableSetPiece(ItemId.SAMURAI_KASA), new StorableSetPiece(ItemId.SAMURAI_SHIRT), new StorableSetPiece(ItemId.SAMURAI_GREAVES), new StorableSetPiece(ItemId.SAMURAI_GLOVES), new StorableSetPiece(ItemId.SAMURAI_BOOTS)),
    MUMMY_OUTFIT(ItemId.MUMMYS_HEAD, new StorableSetPiece(ItemId.MUMMYS_HEAD), new StorableSetPiece(ItemId.MUMMYS_BODY), new StorableSetPiece(ItemId.MUMMYS_LEGS), new StorableSetPiece(ItemId.MUMMYS_HANDS), new StorableSetPiece(ItemId.MUMMYS_FEET)),
    ANKOU_OUTFIT(ItemId.ANKOU_MASK, new StorableSetPiece(ItemId.ANKOU_MASK), new StorableSetPiece(ItemId.ANKOU_TOP), new StorableSetPiece(ItemId.ANKOUS_LEGGINGS), new StorableSetPiece(ItemId.ANKOU_GLOVES), new StorableSetPiece(ItemId.ANKOU_SOCKS)),
    ROBES_OF_DARKNESS(ItemId.HOOD_OF_DARKNESS, new StorableSetPiece(ItemId.HOOD_OF_DARKNESS), new StorableSetPiece(ItemId.ROBE_TOP_OF_DARKNESS), new StorableSetPiece(ItemId.ROBE_BOTTOM_OF_DARKNESS), new StorableSetPiece(ItemId.GLOVES_OF_DARKNESS), new StorableSetPiece(ItemId.BOOTS_OF_DARKNESS)),
    BLACK_PLATEBODY_H1(ItemId.BLACK_PLATEBODY_H1, new StorableSetPiece(ItemId.BLACK_PLATEBODY_H1)),
    BLACK_PLATEBODY_H2(ItemId.BLACK_PLATEBODY_H2, new StorableSetPiece(ItemId.BLACK_PLATEBODY_H2)),
    BLACK_PLATEBODY_H3(ItemId.BLACK_PLATEBODY_H3, new StorableSetPiece(ItemId.BLACK_PLATEBODY_H3)),
    BLACK_PLATEBODY_H4(ItemId.BLACK_PLATEBODY_H4, new StorableSetPiece(ItemId.BLACK_PLATEBODY_H4)),
    BLACK_PLATEBODY_H5(ItemId.BLACK_PLATEBODY_H5, new StorableSetPiece(ItemId.BLACK_PLATEBODY_H5)),
    ADAMANT_PLATEBODY_H1(ItemId.ADAMANT_PLATEBODY_H1, new StorableSetPiece(ItemId.ADAMANT_PLATEBODY_H1)),
    ADAMANT_PLATEBODY_H2(ItemId.ADAMANT_PLATEBODY_H2, new StorableSetPiece(ItemId.ADAMANT_PLATEBODY_H2)),
    ADAMANT_PLATEBODY_H3(ItemId.ADAMANT_PLATEBODY_H3, new StorableSetPiece(ItemId.ADAMANT_PLATEBODY_H3)),
    ADAMANT_PLATEBODY_H4(ItemId.ADAMANT_PLATEBODY_H4, new StorableSetPiece(ItemId.ADAMANT_PLATEBODY_H4)),
    ADAMANT_PLATEBODY_H5(ItemId.ADAMANT_PLATEBODY_H5, new StorableSetPiece(ItemId.ADAMANT_PLATEBODY_H5)),
    RUNE_PLATEBODY_H1(ItemId.RUNE_PLATEBODY_H1, new StorableSetPiece(ItemId.RUNE_PLATEBODY_H1)),
    RUNE_PLATEBODY_H2(ItemId.RUNE_PLATEBODY_H2, new StorableSetPiece(ItemId.RUNE_PLATEBODY_H2)),
    RUNE_PLATEBODY_H3(ItemId.RUNE_PLATEBODY_H3, new StorableSetPiece(ItemId.RUNE_PLATEBODY_H3)),
    RUNE_PLATEBODY_H4(ItemId.RUNE_PLATEBODY_H4, new StorableSetPiece(ItemId.RUNE_PLATEBODY_H4)),
    RUNE_PLATEBODY_H5(ItemId.RUNE_PLATEBODY_H5, new StorableSetPiece(ItemId.RUNE_PLATEBODY_H5)),
    ;

    private final int displayItem;
    private final StorableSetPiece[] pieces;

    StorableTreasure(final int displayItem, final StorableSetPiece... pieces) {
        this.displayItem = displayItem;
        this.pieces = pieces;
    }
    
    public int getDisplayItem() {
        return displayItem;
    }
    
    public StorableSetPiece[] getPieces() {
        return pieces;
    }
}