package com.zenyte.game.content.rottenpotato.handler.player;

import com.zenyte.game.content.rottenpotato.handler.PlayerRottenPotatoActionHandler;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.player.LogLevel;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.privilege.PlayerPrivilege;

/**
 * <AUTHOR> (Discord: imslickk)
 * @since 7/4/2025
 */
public class UnJail implements PlayerRottenPotatoActionHandler {
    @Override
    public void execute(Player user, Player target) {
        if (!target.isJailed()) {
            target.sendMessage("That player is not jailed.");
            return;
        }
        target.setJailed(false);
        target.setLocation(new Location(3087, 3496, 0));
        target.log(LogLevel.INFO, "Unjailed by " + user.getName() + ".");
        user.sendMessage("You have released <col=C22731>" + target.getUsername() + "</col> from jail.");
        target.sendMessage("<col=00FF00>You have been released from jail by " + user.getUsername() + "!</col>");

    }

    @Override
    public String option() {
        return "Unjail";
    }

    @Override
    public PlayerPrivilege getPrivilege() {
        return PlayerPrivilege.MODERATOR;
    }
}
