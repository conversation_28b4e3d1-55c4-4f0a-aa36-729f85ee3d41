package com.zenyte.game.content.boons.impl;

import com.zenyte.game.content.boons.Boon;
import com.zenyte.game.content.boons.BoonPriceTable;

public class <PERSON><PERSON> extends <PERSON>on {
    @Override
    public String name() {
        return "<PERSON><PERSON>";
    }

    @Override
    public int price() {
        return BoonPriceTable.v_Woodsman;
    }

    @Override
    public String description() {
        return "Provides a 100% increase to woodcutting speed and raises bird nest rates.";
    }

    @Override
    public int item() {
        return 6739;
    }
}
