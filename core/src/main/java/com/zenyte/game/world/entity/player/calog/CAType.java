package com.zenyte.game.world.entity.player.calog;

import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import org.jetbrains.annotations.NotNull;

import java.util.*;

/**
 * <AUTHOR>
 */
public enum CAType {

	NOXIOUS_FOE(0, "Noxious Foe", CATierType.EASY, 66),
	BARROWS_NOVICE(24, "Barrows Novice", CATierType.EASY, 3),
	DEFENCE_WHAT_DEFENCE(28, "Defence? What Defence?", CATierType.EASY, 3),
	BIG_BLACK_AND_FIERY(32, "Big, Black and Fiery", CATierType.EASY, 62),
	THE_DEMONIC_PUNCHING_BAG(33, "The Demonic Punching Bag", CATierType.EASY, 65),
	BRYOPHYTA_NOVICE(35, "Bryophyta Novice", CATierType.EASY, 4),
	PROTECTION_FROM_MOSS(37, "Protection from Moss", CATierType.EASY, 4),
	PREPARATION_IS_KEY(39, "Preparation Is Key", CATierType.EASY, 4),
	A_SLOW_DEATH(40, "A Slow Death", CATierType.EASY, 4),
	DERANGED_ARCHAEOLOGIST_NOVICE(76, "Deranged Archaeologist Novice", CATierType.EASY, 17),
	THE_WALKING_VOLCANO(80, "The Walking Volcano", CATierType.EASY, 63),
	INTO_THE_DEN_OF_GIANTS(398, "Into the Den of Giants", CATierType.EASY, 73, true),
	A_GREATER_FOE(120, "A Greater Foe", CATierType.EASY, 59),
	NOT_SO_GREAT_AFTER_ALL(121, "Not So Great After All", CATierType.EASY, 59, true),
	A_DEMONS_BEST_FRIEND(122, "A Demon's Best Friend", CATierType.EASY, 64),
	OBOR_NOVICE(129, "Obor Novice", CATierType.EASY, 34),
	SLEEPING_GIANT(131, "Sleeping Giant", CATierType.EASY, 34),
	KING_BLACK_DRAGON_NOVICE(162, "King Black Dragon Novice", CATierType.EASY, 25),
	A_SCALEY_ENCOUNTER(174, "A Scaley Encounter", CATierType.EASY, 60),
	SHAYZIEN_PROTECTOR(175, "Shayzien Protector", CATierType.EASY, 60, true),
	GIANT_MOLE_NOVICE(177, "Giant Mole Novice", CATierType.EASY, 21),
	SARACHNIS_NOVICE(206, "Sarachnis Novice", CATierType.EASY, 35),
	WINTERTODT_NOVICE(279, "Wintertodt Novice", CATierType.EASY, 52),
	MUMMY(281, "Mummy!", CATierType.EASY, 52),
	HANDYMAN(282, "Handyman", CATierType.EASY, 52),
	COSY(285, "Cosy", CATierType.EASY, 52),
	A_SLITHERY_ENCOUNTER(287, "A Slithery Encounter", CATierType.EASY, 61),
	TEMPOROSS_NOVICE(353, "Tempoross Novice", CATierType.EASY, 38, true),
	MASTER_OF_BUCKETS(356, "Master of Buckets", CATierType.EASY, 38, true),
	CALM_BEFORE_THE_STORM(357, "Calm Before the Storm", CATierType.EASY, 38, true),
	FIRE_IN_THE_HOLE(360, "Fire in the Hole!", CATierType.EASY, 38, true),
	SIT_BACK_AND_RELAX(399, "Sit Back and Relax", CATierType.MEDIUM, 58),
	BARROWS_CHAMPION(25, "Barrows Champion", CATierType.MEDIUM, 3),
	CANT_TOUCH_ME(26, "Can't Touch Me", CATierType.MEDIUM, 3),
	PRAY_FOR_SUCCESS(27, "Pray for Success", CATierType.MEDIUM, 3, true),
	BRUTAL_BIG_BLACK_AND_FIREY(34, "Brutal, Big, Black and Firey", CATierType.MEDIUM, 72),
	BRYOPHYTA_CHAMPION(36, "Bryophyta Champion", CATierType.MEDIUM, 4),
	QUICK_CUTTER(38, "Quick Cutter", CATierType.MEDIUM, 4, true),
	DEMONIC_WEAKENING(46, "Demonic Weakening", CATierType.MEDIUM, 37),
	DEMONBANE_WEAPONRY(48, "Demonbane Weaponry", CATierType.MEDIUM, 37),
	CHAOS_FANATIC_CHAMPION(61, "Chaos Fanatic Champion", CATierType.MEDIUM, 8),
	SORRY_WHAT_WAS_THAT(63, "Sorry, What Was That?", CATierType.MEDIUM, 8, true),
	CRAZY_ARCHAEOLOGIST_CHAMPION(70, "Crazy Archaeologist Champion", CATierType.MEDIUM, 9),
	MAGE_OF_THE_RUINS(72, "Mage of the Ruins", CATierType.MEDIUM, 9, true),
	ID_RATHER_NOT_LEARN(73, "I'd Rather Not Learn", CATierType.MEDIUM, 9, true),
	DERANGED_ARCHAEOLOGIST_CHAMPION(77, "Deranged Archaeologist Champion", CATierType.MEDIUM, 17),
	MAGE_OF_THE_SWAMP(78, "Mage of the Swamp", CATierType.MEDIUM, 17),
	ID_RATHER_BE_ILLITERATE(79, "I'd Rather Be Illiterate", CATierType.MEDIUM, 17),
	A_SMASHING_TIME(97, "A Smashing Time", CATierType.MEDIUM, 69),
	OBOR_CHAMPION(130, "Obor Champion", CATierType.MEDIUM, 34),
	BACK_TO_THE_WALL(132, "Back to the Wall", CATierType.MEDIUM, 34),
	SQUASHING_THE_GIANT(133, "Squashing the Giant", CATierType.MEDIUM, 34),
	KING_BLACK_DRAGON_CHAMPION(163, "King Black Dragon Champion", CATierType.MEDIUM, 25),
	CLAW_CLIPPER(164, "Claw Clipper", CATierType.MEDIUM, 25, true),
	HIDE_PENETRATION(165, "Hide Penetration", CATierType.MEDIUM, 25),
	ANTIFIRE_PROTECTION(166, "Antifire Protection", CATierType.MEDIUM, 25),
	MASTER_OF_BROAD_WEAPONRY(173, "Master of Broad Weaponry", CATierType.MEDIUM, 71),
	GIANT_MOLE_CHAMPION(178, "Giant Mole Champion", CATierType.MEDIUM, 21),
	AVOIDING_THOSE_LITTLE_ARMS(182, "Avoiding Those Little Arms", CATierType.MEDIUM, 21, true),
	DAGANNOTH_PRIME_CHAMPION(197, "Dagannoth Prime Champion", CATierType.MEDIUM, 14),
	DAGANNOTH_REX_CHAMPION(201, "Dagannoth Rex Champion", CATierType.MEDIUM, 15),
	A_FROZEN_KING(204, "A Frozen King", CATierType.MEDIUM, 15),
	SARACHNIS_CHAMPION(207, "Sarachnis Champion", CATierType.MEDIUM, 35),
	NEWSPAPER_ENTHUSIAST(210, "Newspaper Enthusiast", CATierType.MEDIUM, 35),
	A_FROZEN_FOE_FROM_THE_PAST(223, "A Frozen Foe from the Past", CATierType.MEDIUM, 70),
	DAGANNOTH_SUPREME_CHAMPION(233, "Dagannoth Supreme Champion", CATierType.MEDIUM, 16),
	WINTERTODT_CHAMPION(280, "Wintertodt Champion", CATierType.MEDIUM, 52),
	CAN_WE_FIX_IT(283, "Can We Fix It?", CATierType.MEDIUM, 52),
	LEAVING_NO_ONE_BEHIND(284, "Leaving No One Behind", CATierType.MEDIUM, 52),
	SKOTIZO_CHAMPION(44, "Skotizo Champion", CATierType.MEDIUM, 37),
	TEMPOROSS_CHAMPION(354, "Tempoross Champion", CATierType.MEDIUM, 38, true),
	THE_LONE_ANGLER(359, "The Lone Angler", CATierType.MEDIUM, 38, true),
	ABYSSAL_ADEPT(1, "Abyssal Adept", CATierType.HARD, 1),
	THEY_GROW_UP_TOO_FAST(3, "They Grow Up Too Fast", CATierType.HARD, 1),
	DONT_WHIP_ME(5, "Don't Whip Me", CATierType.HARD, 1),
	DONT_STOP_MOVING(7, "Don't Stop Moving", CATierType.HARD, 1),
	KREEARRA_ADEPT(9, "Kree'arra Adept", CATierType.HARD, 27),
	AIRBORNE_SHOWDOWN(12, "Airborne Showdown", CATierType.HARD, 27),
	GENERAL_GRAARDOR_ADEPT(16, "General Graardor Adept", CATierType.HARD, 20),
	OURG_FREEZER(18, "Ourg Freezer", CATierType.HARD, 20),
	GENERAL_SHOWDOWN(20, "General Showdown", CATierType.HARD, 20),
	JUST_LIKE_THAT(29, "Just Like That", CATierType.HARD, 3),
	FAITHLESS_CRYPT_RUN(30, "Faithless Crypt Run", CATierType.HARD, 3),
	CALLISTO_ADEPT(42, "Callisto Adept", CATierType.HARD, 5),
	SKOTIZO_ADEPT(45, "Skotizo Adept", CATierType.HARD, 37),
	CHAOS_ELEMENTAL_ADEPT(57, "Chaos Elemental Adept", CATierType.HARD, 7),
	HOARDER(59, "Hoarder", CATierType.HARD, 7),
	THE_FLINCHER(60, "The Flincher", CATierType.HARD, 7),
	CHAOS_FANATIC_ADEPT(62, "Chaos Fanatic Adept", CATierType.HARD, 8),
	PRAYING_TO_THE_GODS(64, "Praying to the Gods", CATierType.HARD, 8, true),
	CRAZY_ARCHAEOLOGIST_ADEPT(71, "Crazy Archaeologist Adept", CATierType.HARD, 9),
	GROTESQUE_GUARDIANS_ADEPT(82, "Grotesque Guardians Adept", CATierType.HARD, 22),
	DONT_LOOK_AT_THE_ECLIPSE(84, "Don't Look at the Eclipse", CATierType.HARD, 22),
	PRISON_BREAK(85, "Prison Break", CATierType.HARD, 22),
	GRANITE_FOOTWORK(86, "Granite Footwork", CATierType.HARD, 22),
	HEAL_NO_MORE(87, "Heal No More", CATierType.HARD, 22),
	STATIC_AWARENESS(88, "Static Awareness", CATierType.HARD, 22),
	HESPORI_ADEPT(123, "Hespori Adept", CATierType.HARD, 23),
	HESPORISNT(124, "Hesporisn't", CATierType.HARD, 23),
	WEED_WHACKER(125, "Weed Whacker", CATierType.HARD, 23, true),
	KALPHITE_QUEEN_ADEPT(157, "Kalphite Queen Adept", CATierType.HARD, 24),
	CHITIN_PENETRATOR(159, "Chitin Penetrator", CATierType.HARD, 24, true),
	WHO_IS_THE_KING_NOW(167, "Who Is the King Now?", CATierType.HARD, 25),
	KRAKEN_ADEPT(168, "Kraken Adept", CATierType.HARD, 26),
	UNNECESSARY_OPTIMIZATION(169, "Unnecessary Optimization", CATierType.HARD, 26),
	KRAKANT_HURT_ME(170, "Krakan't Hurt Me", CATierType.HARD, 26),
	WHY_ARE_YOU_RUNNING(179, "Why Are You Running?", CATierType.HARD, 21),
	WHACK_A_MOLE(181, "Whack-a-Mole", CATierType.HARD, 21, true),
	NIGHTMARE_ADEPT(183, "Nightmare Adept", CATierType.HARD, 31),
	DAGANNOTH_PRIME_ADEPT(198, "Dagannoth Prime Adept", CATierType.HARD, 14),
	DAGANNOTH_REX_ADEPT(202, "Dagannoth Rex Adept", CATierType.HARD, 15),
	READY_TO_POUNCE(208, "Ready to Pounce", CATierType.HARD, 35, true),
	INSPECT_REPELLENT(209, "Inspect Repellent", CATierType.HARD, 35, true),
	COMMANDER_ZILYANA_ADEPT(211, "Commander Zilyana Adept", CATierType.HARD, 13),
	COMMANDER_SHOWDOWN(213, "Commander Showdown", CATierType.HARD, 13),
	SCORPIA_ADEPT(218, "Scorpia Adept", CATierType.HARD, 36),
	I_CANT_REACH_THAT(220, "I Can't Reach That", CATierType.HARD, 36, true),
	GUARDIANS_NO_MORE(221, "Guardians No More", CATierType.HARD, 36, true),
	ZULRAH_ADEPT(224, "Zulrah Adept", CATierType.HARD, 54, true),
	DAGANNOTH_SUPREME_ADEPT(234, "Dagannoth Supreme Adept", CATierType.HARD, 16),
	THEATRE_OF_BLOOD_SM_ADEPT(397, "Theatre of Blood: SM Adept", CATierType.HARD, 39, true),
	VENENATIS_ADEPT(264, "Venenatis Adept", CATierType.HARD, 49),
	VETION_ADEPT(266, "Vet'ion Adept", CATierType.HARD, 50, true),
	WHY_FLETCH(286, "Why Fletch?", CATierType.HARD, 52),
	KRIL_TSUTSAROTH_ADEPT(332, "K'ril Tsutsaroth Adept", CATierType.HARD, 28),
	YARR_NO_MORE(334, "Yarr No More", CATierType.HARD, 28),
	DEMONIC_SHOWDOWN(335, "Demonic Showdown", CATierType.HARD, 28),
	DEMONBANE_WEAPONRY_II(339, "Demonbane Weaponry II", CATierType.HARD, 28),
	DRESS_LIKE_YOU_MEAN_IT(355, "Dress Like You Mean It", CATierType.HARD, 38, true),
	WHY_COOK(358, "Why Cook?", CATierType.HARD, 38, true),
	NOVICE_TOMB_EXPLORER(461, "Novice Tomb Explorer", CATierType.HARD, 43, true),
	NOVICE_TOMB_LOOTER(462, "Novice Tomb Looter", CATierType.HARD, 43, true),
	MOVIN_ON_UP(464, "Movin' on up", CATierType.HARD, 43, true),
	CONFIDENT_RAIDER(465, "Confident Raider", CATierType.HARD, 43, true),
	PHANTOM_MUSPAH_ADEPT(478, "Phantom Muspah Adept", CATierType.HARD, 32),
	ABYSSAL_VETERAN(2, "Abyssal Veteran", CATierType.ELITE, 1),
	RESPIRATORY_RUNNER(4, "Respiratory Runner", CATierType.ELITE, 1),
	DEMONIC_REBOUND(6, "Demonic Rebound", CATierType.ELITE, 1),
	PERFECT_SIRE(8, "Perfect Sire", CATierType.ELITE, 1, true),
	KREEARRA_VETERAN(10, "Kree'arra Veteran", CATierType.ELITE, 27),
	GENERAL_GRAARDOR_VETERAN(17, "General Graardor Veteran", CATierType.ELITE, 20),
	OURG_FREEZER_II(19, "Ourg Freezer II", CATierType.ELITE, 20),
	REFLECTING_ON_THIS_ENCOUNTER(31, "Reflecting on This Encounter", CATierType.ELITE, 68),
	CALLISTO_VETERAN(43, "Callisto Veteran", CATierType.ELITE, 5),
	DEMON_EVASION(47, "Demon Evasion", CATierType.ELITE, 37, true),
	UP_FOR_THE_CHALLENGE(49, "Up for the Challenge", CATierType.ELITE, 37),
	CERBERUS_VETERAN(51, "Cerberus Veteran", CATierType.ELITE, 6),
	GHOST_BUSTER(54, "Ghost Buster", CATierType.ELITE, 6),
	UNREQUIRED_ANTIFIRE(55, "Unrequired Antifire", CATierType.ELITE, 6, true),
	ANTI_BITE_MECHANICS(56, "Anti-Bite Mechanics", CATierType.ELITE, 6, true),
	CHAOS_ELEMENTAL_VETERAN(58, "Chaos Elemental Veteran", CATierType.ELITE, 7),
	CORPOREAL_BEAST_VETERAN(65, "Corporeal Beast Veteran", CATierType.ELITE, 12),
	HOT_ON_YOUR_FEET(67, "Hot on Your Feet", CATierType.ELITE, 12, true),
	FINDING_THE_WEAK_SPOT(68, "Finding the Weak Spot", CATierType.ELITE, 12),
	CHICKEN_KILLER(69, "Chicken Killer", CATierType.ELITE, 12),
	IF_GORILLAS_COULD_FLY(74, "If Gorillas Could Fly", CATierType.ELITE, 67),
	HITTING_THEM_WHERE_IT_HURTS(75, "Hitting Them Where It Hurts", CATierType.ELITE, 67),
	GROTESQUE_GUARDIANS_VETERAN(83, "Grotesque Guardians Veteran", CATierType.ELITE, 22),
	DONE_BEFORE_DUSK(89, "Done before Dusk", CATierType.ELITE, 22),
	PERFECT_GROTESQUE_GUARDIANS(90, "Perfect Grotesque Guardians", CATierType.ELITE, 22),
	GROTESQUE_GUARDIANS_SPEED_TRIALIST(92, "Grotesque Guardians Speed-Trialist", CATierType.ELITE, 22),
	FROM_DUSK(95, "From Dusk...", CATierType.ELITE, 22),
	CORRUPTED_GAUNTLET_VETERAN(98, "Corrupted Gauntlet Veteran", CATierType.ELITE, 19),
	three_2_1__MAGE(101, "3, 2, 1 - Mage", CATierType.ELITE, 19, true),
	GAUNTLET_VETERAN(109, "Gauntlet Veteran", CATierType.ELITE, 18),
	three_2_1__RANGE(111, "3, 2, 1 - Range", CATierType.ELITE, 18, true),
	EGNIOL_DIET(114, "Egniol Diet", CATierType.ELITE, 18, true),
	CRYSTALLINE_WARRIOR(115, "Crystalline Warrior", CATierType.ELITE, 18, true),
	WOLF_PUNCHER(116, "Wolf Puncher", CATierType.ELITE, 18, true),
	PLANT_BASED_DIET(126, "Plant-Based Diet", CATierType.ELITE, 23),
	HESPORI_SPEED_TRIALIST(127, "Hespori Speed-Trialist", CATierType.ELITE, 23),
	ALCHEMICAL_VETERAN(135, "Alchemical Veteran", CATierType.ELITE, 2),
	FIGHT_CAVES_VETERAN(147, "Fight Caves Veteran", CATierType.ELITE, 48),
	FACING_JAD_HEAD_ON(153, "Facing Jad Head-on", CATierType.ELITE, 48),
	KALPHITE_QUEEN_VETERAN(158, "Kalphite Queen Veteran", CATierType.ELITE, 24),
	INSECT_DEFLECTION(160, "Insect Deflection", CATierType.ELITE, 24, true),
	PRAYER_SMASHER(161, "Prayer Smasher", CATierType.ELITE, 24, true),
	TEN_TACLES(171, "Ten-tacles", CATierType.ELITE, 26),
	MIMIC_VETERAN(176, "Mimic Veteran", CATierType.ELITE, 29),
	HARD_HITTER(180, "Hard Hitter", CATierType.ELITE, 21, true),
	NIGHTMARE_VETERAN(184, "Nightmare Veteran", CATierType.ELITE, 31),
	EXPLOSION(187, "Explosion!", CATierType.ELITE, 31),
	SLEEP_TIGHT(189, "Sleep Tight", CATierType.ELITE, 31),
	NIGHTMARE__SOLO__SPEED_TRIALIST(191, "Nightmare (Solo) Speed-Trialist", CATierType.ELITE, 31),
	NIGHTMARE__5_SCALE__SPEED_TRIALIST(194, "Nightmare (5-Scale) Speed-Trialist", CATierType.ELITE, 31, true),
	DEATH_TO_THE_SEER_KING(199, "Death to the Seer King", CATierType.ELITE, 14),
	FROM_ONE_KING_TO_ANOTHER(200, "From One King to Another", CATierType.ELITE, 14, true),
	DEATH_TO_THE_WARRIOR_KING(203, "Death to the Warrior King", CATierType.ELITE, 15),
	TOPPLING_THE_DIARCHY(205, "Toppling the Diarchy", CATierType.ELITE, 15),
	COMMANDER_ZILYANA_VETERAN(212, "Commander Zilyana Veteran", CATierType.ELITE, 13),
	REMINISCE(216, "Reminisce", CATierType.ELITE, 13, true),
	SCORPIA_VETERAN(219, "Scorpia Veteran", CATierType.ELITE, 36),
	ZULRAH_VETERAN(225, "Zulrah Veteran", CATierType.ELITE, 54, true),
	SNAKE_REBOUND(227, "Snake Rebound", CATierType.ELITE, 54, true),
	SNAKE_SNAKE_SNAAAAAAKE(228, "Snake. Snake!? Snaaaaaake!", CATierType.ELITE, 54),
	ZULRAH_SPEED_TRIALIST(230, "Zulrah Speed-Trialist", CATierType.ELITE, 54),
	DEATH_TO_THE_ARCHER_KING(235, "Death to the Archer King", CATierType.ELITE, 16),
	RAPID_SUCCESSION(236, "Rapid Succession", CATierType.ELITE, 16),
	ANTICOAGULANTS(386, "Anticoagulants", CATierType.ELITE, 39, true),
	APPROPRIATE_TOOLS(387, "Appropriate Tools", CATierType.ELITE, 39, true),
	THEY_WONT_EXPECT_THIS(388, "They Won't Expect This", CATierType.ELITE, 39, true),
	CHALLY_TIME(389, "Chally Time", CATierType.ELITE, 39, true),
	NYLOCAS_ON_THE_ROCKS(390, "Nylocas, On the Rocks", CATierType.ELITE, 39, true),
	JUST_TO_BE_SAFE(391, "Just To Be Safe", CATierType.ELITE, 39, true),
	DONT_LOOK_AT_ME(392, "Don't Look at Me!", CATierType.ELITE, 39, true),
	NO_PILLAR(393, "No-Pillar", CATierType.ELITE, 39, true),
	ATTACK_STEP_WAIT(394, "Attack, Step, Wait", CATierType.ELITE, 39, true),
	PASS_IT_ON(395, "Pass It On", CATierType.ELITE, 39, true),
	THEATRE_OF_BLOOD_VETERAN(237, "Theatre of Blood Veteran", CATierType.ELITE, 40),
	THERMONUCLEAR_VETERAN(261, "Thermonuclear Veteran", CATierType.ELITE, 42),
	HAZARD_PREVENTION(262, "Hazard Prevention", CATierType.ELITE, 42, true),
	SPECD_OUT(263, "Spec'd Out", CATierType.ELITE, 42),
	VENENATIS_VETERAN(265, "Venenatis Veteran", CATierType.ELITE, 49),
	VETERAN(267, "Vet'eran", CATierType.ELITE, 50, true),
	VORKATH_VETERAN(268, "Vorkath Veteran", CATierType.ELITE, 51),
	STICK_EM_WITH_THE_POINTY_END(273, "Stick 'em With the Pointy End", CATierType.ELITE, 51),
	ZOMBIE_DESTROYER(271, "Zombie Destroyer", CATierType.ELITE, 51),
	DUST_SEEKER(292, "Dust Seeker", CATierType.ELITE, 11),
	CHAMBERS_OF_XERIC_VETERAN(299, "Chambers of Xeric Veteran", CATierType.ELITE, 10),
	PERFECTLY_BALANCED(302, "Perfectly Balanced", CATierType.ELITE, 10),
	TOGETHER_WELL_FALL(303, "Together We'll Fall", CATierType.ELITE, 10, true),
	REDEMPTION_ENTHUSIAST(308, "Redemption Enthusiast", CATierType.ELITE, 10, true),
	MUTTA_DIET(307, "Mutta-diet", CATierType.ELITE, 10, true),
	DANCING_WITH_STATUES(311, "Dancing with Statues", CATierType.ELITE, 10, true),
	UNDYING_RAID_TEAM(312, "Undying Raid Team", CATierType.ELITE, 10),
	SHAYZIEN_SPECIALIST(314, "Shayzien Specialist", CATierType.ELITE, 10),
	CRYO_NO_MORE(316, "Cryo No More", CATierType.ELITE, 10, true),
	BLIZZARD_DODGER(320, "Blizzard Dodger", CATierType.ELITE, 10, true),
	KILL_IT_WITH_FIRE(321, "Kill It with Fire", CATierType.ELITE, 10, true),
	ZALCANO_VETERAN(328, "Zalcano Veteran", CATierType.ELITE, 53),
	PERFECT_ZALCANO(329, "Perfect Zalcano", CATierType.ELITE, 53),
	TEAM_PLAYER(330, "Team Player", CATierType.ELITE, 53),
	THE_SPURNED_HERO(331, "The Spurned Hero", CATierType.ELITE, 53),
	KRIL_TSUTSAROTH_VETERAN(333, "K'ril Tsutsaroth Veteran", CATierType.ELITE, 28),
	DEMONIC_DEFENCE(337, "Demonic Defence", CATierType.ELITE, 28, true),
	THE_BANE_OF_DEMONS(336, "The Bane of Demons", CATierType.ELITE, 28, true),
	HALF_WAY_THERE(341, "Half-Way There", CATierType.ELITE, 47),
	A_NEAR_MISS(149, "A Near Miss!", CATierType.ELITE, 48),
	THE_II_JAD_CHALLENGE(361, "The II Jad Challenge", CATierType.ELITE, 46, true),
	TZHAAR_KET_RAKS_SPEED_TRIALIST(364, "TzHaar-Ket-Rak's Speed-Trialist", CATierType.ELITE, 46, true),
	FACING_JAD_HEAD_ON_III(367, "Facing Jad Head-on III", CATierType.ELITE, 46, true),
	PHOSANIS_VETERAN(400, "Phosani's Veteran", CATierType.ELITE, 33),
	NEX_VETERAN(410, "Nex Veteran", CATierType.ELITE, 30),
	NEX_SURVIVORS(412, "Nex Survivors", CATierType.ELITE, 30),
	NOVICE_TOMB_RAIDER(463, "Novice Tomb Raider", CATierType.ELITE, 43, true),
	TOMB_EXPLORER(468, "Tomb Explorer", CATierType.ELITE, 44, true),
	EXPERT_TOMB_EXPLORER(469, "Expert Tomb Explorer", CATierType.ELITE, 45, true),
	HARDCORE_RAIDERS(430, "Hardcore Raiders", CATierType.ELITE, 44, true),
	HARDCORE_TOMBS(431, "Hardcore Tombs", CATierType.ELITE, 44, true),
	HELPFUL_SPIRIT_WHO(425, "Helpful spirit who?", CATierType.ELITE, 44, true),
	DROPPED_THE_BALL(449, "Dropped the ball", CATierType.ELITE, 44, true),
	NO_SKIPPING_ALLOWED(451, "No skipping allowed", CATierType.ELITE, 44, true),
	DOWN_DO_SPECS(458, "Down Do Specs", CATierType.ELITE, 44, true),
	PERFECT_HET(432, "Perfect Het", CATierType.ELITE, 44, true),
	PERFECT_APMEKEN(435, "Perfect Apmeken", CATierType.ELITE, 44, true),
	PERFECT_CRONDIS(438, "Perfect Crondis", CATierType.ELITE, 44, true),
	IM_IN_A_RUSH(452, "I'm in a rush", CATierType.ELITE, 44, true),
	PHANTOM_MUSPAH_SPEED_TRIALIST(475, "Phantom Muspah Speed-Trialist", CATierType.ELITE, 32),
	PHANTOM_MUSPAH_VETERAN(479, "Phantom Muspah Veteran", CATierType.ELITE, 32),
	CANT_ESCAPE(473, "Can't Escape", CATierType.ELITE, 32),
	VERSATILE_DRAINER(482, "Versatile Drainer", CATierType.ELITE, 32, true),
	COLLATERAL_DAMAGE(11, "Collateral Damage", CATierType.MASTER, 27),
	SWOOP_NO_MORE(13, "Swoop No More", CATierType.MASTER, 27),
	PRECISE_POSITIONING(50, "Precise Positioning", CATierType.MASTER, 37, true),
	CERBERUS_MASTER(52, "Cerberus Master", CATierType.MASTER, 6),
	CORPOREAL_BEAST_MASTER(66, "Corporeal Beast Master", CATierType.MASTER, 12),
	PERFECT_GROTESQUE_GUARDIANS_II(91, "Perfect Grotesque Guardians II", CATierType.MASTER, 22),
	GROTESQUE_GUARDIANS_SPEED_CHASER(93, "Grotesque Guardians Speed-Chaser", CATierType.MASTER, 22),
	_TIL_DAWN(96, "... 'til Dawn", CATierType.MASTER, 22, true),
	CORRUPTED_GAUNTLET_MASTER(99, "Corrupted Gauntlet Master", CATierType.MASTER, 19),
	DEFENCE_DOESNT_MATTER_II(103, "Defence Doesn't Matter II", CATierType.MASTER, 19),
	CORRUPTED_WARRIOR(105, "Corrupted Warrior", CATierType.MASTER, 19),
	CORRUPTED_GAUNTLET_SPEED_CHASER(107, "Corrupted Gauntlet Speed-Chaser", CATierType.MASTER, 19),
	GAUNTLET_MASTER(110, "Gauntlet Master", CATierType.MASTER, 18),
	DEFENCE_DOESNT_MATTER(113, "Defence Doesn't Matter", CATierType.MASTER, 18),
	GAUNTLET_SPEED_CHASER(117, "Gauntlet Speed-Chaser", CATierType.MASTER, 18),
	HESPORI_SPEED_CHASER(128, "Hespori Speed-Chaser", CATierType.MASTER, 23),
	ALCHEMICAL_MASTER(136, "Alchemical Master", CATierType.MASTER, 2),
	UNREQUIRED_ANTIPOISONS(137, "Unrequired Antipoisons", CATierType.MASTER, 2),
	LIGHTNING_LURE(138, "Lightning Lure", CATierType.MASTER, 2),
	DONT_FLAME_ME(139, "Don't Flame Me", CATierType.MASTER, 2),
	MIXING_CORRECTLY(140, "Mixing Correctly", CATierType.MASTER, 2),
	ALCLEANICAL_HYDRA(142, "Alcleanical Hydra", CATierType.MASTER, 2),
	ALCHEMICAL_SPEED_CHASER(144, "Alchemical Speed-Chaser", CATierType.MASTER, 2),
	WORKING_OVERTIME(146, "Working Overtime", CATierType.MASTER, 2),
	FIGHT_CAVES_MASTER(148, "Fight Caves Master", CATierType.MASTER, 48),
	ONE_HUNDRED_TENTACLES(172, "One Hundred Tentacles", CATierType.MASTER, 26),
	NIGHTMARE_MASTER(185, "Nightmare Master", CATierType.MASTER, 31),
	NIGHTMARE__SOLO__SPEED_CHASER(192, "Nightmare (Solo) Speed-Chaser", CATierType.MASTER, 31),
	NIGHTMARE__5_SCALE__SPEED_CHASER(195, "Nightmare (5-Scale) Speed-Chaser", CATierType.MASTER, 31),
	ZULRAH_MASTER(226, "Zulrah Master", CATierType.MASTER, 54),
	ZULRAH_SPEED_CHASER(231, "Zulrah Speed-Chaser", CATierType.MASTER, 54),
	TWO_DOWN(240, "Two-Down", CATierType.MASTER, 40),
	A_TIMELY_SNACK(242, "A Timely Snack", CATierType.MASTER, 40),
	CAN_YOU_DANCE(251, "Can You Dance?", CATierType.MASTER, 40),
	BACK_IN_MY_DAY(253, "Back in My Day...", CATierType.MASTER, 40),
	THEATRE__TRIO__SPEED_CHASER(255, "Theatre (Trio) Speed-Chaser", CATierType.MASTER, 40),
	THEATRE__4_SCALE__SPEED_CHASER(257, "Theatre (4-Scale) Speed-Chaser", CATierType.MASTER, 40),
	THEATRE__5_SCALE__SPEED_CHASER(259, "Theatre (5-Scale) Speed-Chaser", CATierType.MASTER, 40),
	THEATRE_OF_BLOOD_MASTER(238, "Theatre of Blood Master", CATierType.MASTER, 40),
	VORKATH_MASTER(269, "Vorkath Master", CATierType.MASTER, 51),
	DODGING_THE_DRAGON(272, "Dodging the Dragon", CATierType.MASTER, 51),
	VORKATH_SPEED_CHASER(276, "Vorkath Speed-Chaser", CATierType.MASTER, 51),
	EXTENDED_ENCOUNTER(278, "Extended Encounter", CATierType.MASTER, 51),
	CHAMBERS_OF_XERIC_CM_MASTER(288, "Chambers of Xeric: CM Master", CATierType.MASTER, 11),
	IMMORTAL_RAID_TEAM(290, "Immortal Raid Team", CATierType.MASTER, 11),
	IMMORTAL_RAIDER(291, "Immortal Raider", CATierType.MASTER, 11),
	CHAMBERS_OF_XERIC_CM__TRIO__SPEED_CHASER(297, "Chambers of Xeric: CM (Trio) Speed-Chaser", CATierType.MASTER, 11),
	CHAMBERS_OF_XERIC_MASTER(300, "Chambers of Xeric Master", CATierType.MASTER, 10),
	NO_TIME_FOR_DEATH(304, "No Time for Death", CATierType.MASTER, 10),
	PUTTING_IT_OLM_ON_THE_LINE(305, "Putting It Olm on the Line", CATierType.MASTER, 10),
	STOP_DROP_AND_ROLL(309, "Stop Drop and Roll", CATierType.MASTER, 10),
	ANVIL_NO_MORE(310, "Anvil No More", CATierType.MASTER, 10),
	UNDYING_RAIDER(313, "Undying Raider", CATierType.MASTER, 10),
	PLAYING_WITH_LASERS(315, "Playing with Lasers", CATierType.MASTER, 10),
	BLIND_SPOT(319, "Blind Spot", CATierType.MASTER, 10, true),
	CHAMBERS_OF_XERIC__SOLO__SPEED_CHASER(322, "Chambers of Xeric (Solo) Speed-Chaser", CATierType.MASTER, 10),
	CHAMBERS_OF_XERIC__TRIO__SPEED_CHASER(326, "Chambers of Xeric (Trio) Speed-Chaser", CATierType.MASTER, 10),
	CHAMBERS_OF_XERIC__5_SCALE__SPEED_CHASER(324, "Chambers of Xeric (5-Scale) Speed-Chaser", CATierType.MASTER, 10),
	THE_IV_JAD_CHALLENGE(362, "The IV Jad Challenge", CATierType.MASTER, 46, true),
	TZHAAR_KET_RAKS_SPEED_CHASER(365, "TzHaar-Ket-Rak's Speed-Chaser", CATierType.MASTER, 46, true),
	FACING_JAD_HEAD_ON_IV(368, "Facing Jad Head-on IV", CATierType.MASTER, 46, true),
	SUPPLIES_WHO_NEEDS_EM(369, "Supplies? Who Needs 'em?", CATierType.MASTER, 46, true),
	MULTI_STYLE_SPECIALIST(371, "Multi-Style Specialist", CATierType.MASTER, 46, true),
	HARD_MODE_COMPLETED_IT(381, "Hard Mode? Completed It", CATierType.MASTER, 41, true),
	THEATRE_OF_BLOOD_SM_SPEED_CHASER(396, "Theatre of Blood: SM Speed-Chaser", CATierType.MASTER, 39, true),
	PHOSANIS_MASTER(401, "Phosani's Master", CATierType.MASTER, 33),
	PHOSANIS_SPEEDCHASER(404, "Phosani's Speedchaser", CATierType.MASTER, 33),
	CRUSH_HOUR(406, "Crush Hour", CATierType.MASTER, 33),
	I_WOULD_SIMPLY_REACT(407, "I Would Simply React", CATierType.MASTER, 33),
	DREAMLAND_EXPRESS(408, "Dreamland Express", CATierType.MASTER, 33),
	NEX_MASTER(411, "Nex Master", CATierType.MASTER, 30),
	A_SIPHON_WILL_SOLVE_THIS(413, "A siphon will solve this", CATierType.MASTER, 30),
	CONTAIN_THIS(414, "Contain this!", CATierType.MASTER, 30),
	THERE_IS_NO_ESCAPE(415, "There is no escape!", CATierType.MASTER, 30),
	NEX_TRIO(416, "Nex Trio", CATierType.MASTER, 30),
	SHADOWS_MOVE(420, "Shadows Move...", CATierType.MASTER, 30),
	YOU_ARE_NOT_PREPARED(424, "You are not prepared", CATierType.MASTER, 44, true),
	TOMB_LOOTER(466, "Tomb Looter", CATierType.MASTER, 44, true),
	TOMB_RAIDER(467, "Tomb Raider", CATierType.MASTER, 44, true),
	TOMBS_SPEED_RUNNER(421, "Tombs Speed Runner", CATierType.MASTER, 44, true),
	SOMETHING_OF_AN_EXPERT_MYSELF(447, "Something of an expert myself", CATierType.MASTER, 45, true),
	EXPERT_TOMB_LOOTER(470, "Expert Tomb Looter", CATierType.MASTER, 45, true),
	BA_BANANZA(453, "Ba-Bananza", CATierType.MASTER, 45, true),
	ROCKIN_AROUND_THE_CROC(455, "Rockin' around the croc", CATierType.MASTER, 45, true),
	DOESNT_BUG_ME(456, "Doesn't bug me", CATierType.MASTER, 45, true),
	ALL_OUT_OF_MEDICS(457, "All out of medics", CATierType.MASTER, 45, true),
	BETTER_GET_MOVIN(459, "Better get movin'", CATierType.MASTER, 44, true),
	WARDENT_YOU_BELIEVE_IT(460, "Warden't you believe it", CATierType.MASTER, 45, true),
	RESOURCEFUL_RAIDER(426, "Resourceful Raider", CATierType.MASTER, 45, true),
	BUT____DAMAGE(427, "But... Damage", CATierType.MASTER, 45, true),
	CHOMPINGTON(428, "Chompington", CATierType.MASTER, 44, true),
	FANCY_FEET(429, "Fancy feet", CATierType.MASTER, 45, true),
	PERFECT_AKKHA(433, "Perfect Akkha", CATierType.MASTER, 44, true),
	PERFECT_BA_BA(436, "Perfect Ba-Ba", CATierType.MASTER, 44, true),
	PERFECT_ZEBAK(439, "Perfect Zebak", CATierType.MASTER, 44, true),
	PERFECT_SCABARAS(441, "Perfect Scabaras", CATierType.MASTER, 44, true),
	PERFECT_KEPHRI(442, "Perfect Kephri", CATierType.MASTER, 44, true),
	PERFECT_WARDENS(444, "Perfect Wardens", CATierType.MASTER, 44, true),
	MORE_THAN_JUST_A_RANGED_WEAPON(472, "More than just a ranged weapon", CATierType.MASTER, 32),
	ESSENCE_FARMER(474, "Essence Farmer", CATierType.MASTER, 32),
	PHANTOM_MUSPAH_SPEED_CHASER(476, "Phantom Muspah Speed-Chaser", CATierType.MASTER, 32),
	PHANTOM_MUSPAH_MASTER(480, "Phantom Muspah Master", CATierType.MASTER, 32),
	WALK_STRAIGHT_PRAY_TRUE(483, "Walk Straight Pray True", CATierType.MASTER, 32, true),
	SPACE_IS_TIGHT(481, "Space is Tight", CATierType.MASTER, 32, true),
	THE_WORST_RANGED_WEAPON(14, "The Worst Ranged Weapon", CATierType.GRANDMASTER, 27, true),
	FEATHER_HUNTER(15, "Feather Hunter", CATierType.GRANDMASTER, 27),
	KEEP_AWAY(22, "Keep Away", CATierType.GRANDMASTER, 20),
	OURG_KILLER(23, "Ourg Killer", CATierType.GRANDMASTER, 20),
	CORRUPTED_GAUNTLET_GRANDMASTER(100, "Corrupted Gauntlet Grandmaster", CATierType.GRANDMASTER, 19),
	WOLF_PUNCHER_II(106, "Wolf Puncher II", CATierType.GRANDMASTER, 19, true),
	GAUNTLET_SPEED_RUNNER(118, "Gauntlet Speed-Runner", CATierType.GRANDMASTER, 18),
	NO_PRESSURE(143, "No Pressure", CATierType.GRANDMASTER, 2),
	ALCHEMICAL_SPEED_RUNNER(145, "Alchemical Speed-Runner", CATierType.GRANDMASTER, 2),
	FIGHT_CAVES_SPEED_RUNNER(156, "Fight Caves Speed-Runner", CATierType.GRANDMASTER, 48),
	NIGHTMARE__SOLO__SPEED_RUNNER(193, "Nightmare (Solo) Speed-Runner", CATierType.GRANDMASTER, 31),
	ANIMAL_WHISPERER(214, "Animal Whisperer", CATierType.GRANDMASTER, 13),
	PEACH_CONJURER(217, "Peach Conjurer", CATierType.GRANDMASTER, 13),
	ZULRAH_SPEED_RUNNER(232, "Zulrah Speed-Runner", CATierType.GRANDMASTER, 54),
	MORYTANIA_ONLY(252, "Morytania Only", CATierType.GRANDMASTER, 40),
	CHAMBERS_OF_XERIC__CM_GRANDMASTER(289, "Chambers of Xeric: CM Grandmaster", CATierType.GRANDMASTER, 11),
	DEMON_WHISPERER(338, "Demon Whisperer", CATierType.GRANDMASTER, 28),
	ASH_COLLECTOR(340, "Ash Collector", CATierType.GRANDMASTER, 28),
	PHOSANIS_GRANDMASTER(402, "Phosani's Grandmaster", CATierType.GRANDMASTER, 33),
	NEX_DUO(417, "Nex Duo", CATierType.GRANDMASTER, 30),
	I_SHOULD_SEE_A_DOCTOR(418, "I should see a doctor", CATierType.GRANDMASTER, 30),
	GROTESQUE_GUARDIANS_SPEED_RUNNER(94, "Grotesque Guardians Speed-Runner", CATierType.GRANDMASTER, 22, true),
	THEATRE_OF_BLOOD_GRANDMASTER(239, "Theatre of Blood Grandmaster", CATierType.GRANDMASTER, 40, true),
	FAITHLESS_ENCOUNTER(274, "Faithless Encounter", CATierType.GRANDMASTER, 51, true),
	THE_FREMENNIK_WAY(275, "The Fremennik Way", CATierType.GRANDMASTER, 51, true),
	VORKATH_SPEED_RUNNER(277, "Vorkath Speed-Runner", CATierType.GRANDMASTER, 51, true),
	CHAMBERS_OF_XERIC__CM__5_SCALE__SPEED_RUNNER(296, "Chambers of Xeric: CM (5-Scale) Speed-Runner", CATierType.GRANDMASTER, 11, true),
	CHAMBERS_OF_XERIC__CM__TRIO__SPEED_RUNNER(298, "Chambers of Xeric: CM (Trio) Speed-Runner", CATierType.GRANDMASTER, 11, true),
	CHAMBERS_OF_XERIC_GRANDMASTER(301, "Chambers of Xeric Grandmaster", CATierType.GRANDMASTER, 10, true),
	INFERNO_GRANDMASTER(342, "Inferno Grandmaster", CATierType.GRANDMASTER, 47, true),
	INFERNO_SPEED_RUNNER(352, "Inferno Speed-Runner", CATierType.GRANDMASTER, 47, true),
	THE_VI_JAD_CHALLENGE(363, "The VI Jad Challenge", CATierType.GRANDMASTER, 46, true),
	TZHAAR_KET_RAKS_SPEED_RUNNER(366, "TzHaar-Ket-Rak's Speed-Runner", CATierType.GRANDMASTER, 46, true),
	IT_WASNT_A_FLUKE(370, "It Wasn't a Fluke", CATierType.GRANDMASTER, 46, true),
	STOP_RIGHT_THERE(372, "Stop Right There!", CATierType.GRANDMASTER, 41, true),
	PERSONAL_SPACE(373, "Personal Space", CATierType.GRANDMASTER, 41, true),
	ROYAL_AFFAIRS(374, "Royal Affairs", CATierType.GRANDMASTER, 41, true),
	HARDER_MODE_I(375, "Harder Mode I", CATierType.GRANDMASTER, 41, true),
	HARDER_MODE_II(376, "Harder Mode II", CATierType.GRANDMASTER, 41, true),
	NYLO_SNIPER(377, "Nylo Sniper", CATierType.GRANDMASTER, 41, true),
	TEAM_WORK_MAKES_THE_DREAM_WORK(378, "Team Work Makes the Dream Work", CATierType.GRANDMASTER, 41, true),
	HARDER_MODE_III(379, "Harder Mode III", CATierType.GRANDMASTER, 41, true),
	PACK_LIKE_A_YAK(380, "Pack Like a Yak", CATierType.GRANDMASTER, 41, true),
	THEATRE__HM__TRIO__SPEED_RUNNER(382, "Theatre: HM (Trio) Speed-Runner", CATierType.GRANDMASTER, 41, true),
	THEATRE__HM__4_SCALE__SPEED_RUNNER(383, "Theatre: HM (4-Scale) Speed-Runner", CATierType.GRANDMASTER, 41, true),
	THEATRE__HM__5_SCALE__SPEED_RUNNER(384, "Theatre: HM (5-Scale) Speed-Runner", CATierType.GRANDMASTER, 41, true),
	THEATRE_OF_BLOOD__HM_GRANDMASTER(385, "Theatre of Blood: HM Grandmaster", CATierType.GRANDMASTER, 41, true),
	CANT_WAKE_UP(409, "Can't Wake Up", CATierType.GRANDMASTER, 33, true),
	TOMBS_SPEED_RUNNER_II(422, "Tombs Speed Runner II", CATierType.GRANDMASTER, 45, true),
	TOMBS_SPEED_RUNNER_III(423, "Tombs Speed Runner III", CATierType.GRANDMASTER, 45, true),
	AMASCUTS_REMNANT(446, "Amascut's Remnant", CATierType.GRANDMASTER, 45, true),
	MAYBE_IM_THE_BOSS_(448, "Maybe I'm the boss.", CATierType.GRANDMASTER, 45, true),
	EXPERT_TOMB_RAIDER(471, "Expert Tomb Raider", CATierType.GRANDMASTER, 45, true),
	AKKHANT_DO_IT(450, "Akkhan't Do it", CATierType.GRANDMASTER, 45, true),
	ALL_PRAISE_ZEBAK(454, "All Praise Zebak", CATierType.GRANDMASTER, 45, true),
	PERFECTION_OF_HET(434, "Perfection of Het", CATierType.GRANDMASTER, 45, true),
	PERFECTION_OF_APMEKEN(437, "Perfection of Apmeken", CATierType.GRANDMASTER, 45, true),
	PERFECTION_OF_CRONDIS(440, "Perfection of Crondis", CATierType.GRANDMASTER, 45, true),
	PERFECTION_OF_SCABARAS(443, "Perfection of Scabaras", CATierType.GRANDMASTER, 45, true),
	INSANITY(445, "Insanity", CATierType.GRANDMASTER, 45, true),
	PHANTOM_MUSPAH_MANIPULATOR(484, "Phantom Muspah Manipulator", CATierType.GRANDMASTER, 32, true),
	PHANTOM_MUSPAH_SPEED_RUNNER(477, "Phantom Muspah Speed-Runner", CATierType.GRANDMASTER, 32, true),
	;

	public static final CAType[] values = values();
	public static final Map<CATierType, List<CAType>> tierToType = new HashMap<>();

	public static final Int2ObjectOpenHashMap<CAType> varToType = new Int2ObjectOpenHashMap<>();

	static {
		for (CAType type : values) {
			List<CAType> tasks = tierToType.computeIfAbsent(type.getTier(), k -> new ArrayList<>());
			tasks.add(type);

			varToType.put(type.varIndex, type);
		}
	}

	private final int varIndex;
	private final String name;
	private final CATierType tier;
	private final int bossNumber;
	private final boolean disabled;

	CAType(int varIndex, String name, CATierType tier, int bossNumber, boolean disabled) {
		this.varIndex = varIndex;
		this.name = name;
		this.tier = tier;
		this.bossNumber = bossNumber;
		this.disabled = disabled;
	}

	CAType(int varIndex, String name, CATierType tier, int bossNumber) {
		this(varIndex, name, tier, bossNumber, false);
	}

	public final int getVarIndex() { return varIndex; }
	public final String getName() { return name; }
	public final CATierType getTier() { return tier; }
	public final int getBossNumber() { return bossNumber; }

	public boolean isDisabled() {
		return disabled;
	}

	public static Optional<CAType> getByVar(int varImpl) {
		if(varToType.containsKey(varImpl))
			return Optional.of(varToType.get(varImpl));
		else return Optional.empty();
	}
}