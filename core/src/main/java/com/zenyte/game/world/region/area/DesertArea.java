package com.zenyte.game.world.region.area;

import com.zenyte.game.world.region.RSPolygon;

/**
 * <AUTHOR> | 27 sep. 2018 | 19:45:26
 * @see <a href="https://www.rune-server.ee/members/tommeh/">Rune-Server
 *      profile</a>
 */
public class DesertArea extends KingdomOfMisthalin {

    public static final RSPolygon polygon =  new RSPolygon(new int[][] { { 3136, 2880 }, { 3200, 2880 }, { 3200, 2752 }, { 3328, 2752 }, { 3328, 2816 }, { 3392, 2816 }, { 3392, 2880 },
            { 3456, 2880 }, { 3456, 3072 }, { 3518, 3072 }, { 3510, 3113 }, { 3464, 3154 }, { 3418, 3194 }, { 3426, 3205 }, { 3421, 3215 }, { 3423, 3220 }, { 3420, 3230 }, { 3423, 3240 },
            { 3420, 3246 }, { 3422, 3253 }, { 3420, 3272 }, { 3413, 3285 }, { 3417, 3308 }, { 3413, 3318 }, { 3402, 3321 }, { 3397, 3330 }, { 3376, 3330 }, { 3375, 3329 },
            { 3371, 3329 }, { 3370, 3330 }, { 3361, 3330 }, { 3360, 3329 }, { 3357, 3329 }, { 3356, 3328 }, { 3351, 3328 }, { 3350, 3329 }, { 3347, 3329 }, { 3346, 3330 },
            { 3340, 3330 }, { 3338, 3328 }, { 3337, 3328 }, { 3336, 3329 }, { 3335, 3329 }, { 3334, 3330 }, { 3323, 3330 }, { 3322, 3331 }, { 3320, 3331 }, { 3319, 3330 },
            { 3317, 3330 }, { 3316, 3329 }, { 3308, 3329 }, { 3307, 3330 }, { 3305, 3330 }, { 3304, 3331 }, { 3301, 3331 }, { 3300, 3330 }, { 3272, 3330 }, { 3271, 3329 },
            { 3271, 3326 }, { 3267, 3322 }, { 3267, 3298 }, { 3265, 3298 }, { 3266, 3297 }, { 3266, 3255 }, { 3265, 3255 }, { 3265, 3248 }, { 3266, 3247 }, { 3266, 3240 },
            { 3267, 3239 }, { 3267, 3233 }, { 3268, 3232 }, { 3268, 3224 }, { 3267, 3223 }, { 3267, 3204 }, { 3263, 3200 }, { 3251, 3166 }, { 3261, 3148 }, { 3279, 3134 },
            { 3268, 3137 }, { 3236, 3122 }, { 3216, 3134 }, { 3205, 3131 }, { 3202, 3121 }, { 3204, 3096 }, { 3197, 3072 }, { 3173, 3062 }, { 3161, 3063 }, { 3145, 3047 },
            { 3144, 3031 }, { 3156, 3020 }, { 3136, 3008 }, { 3142, 2979 }, { 3134, 2968 }, { 3147, 2962 }, { 3146, 2945 }, { 3133, 2937 } });

	@Override
	public RSPolygon[] polygons() {
		return new RSPolygon[] { polygon };
	}

	@Override
	public String name() {
		return "Desert";
	}

}
