package com.zenyte.game.world.region.area;

import com.zenyte.game.GameInterface;
import com.zenyte.game.content.skills.magic.spells.teleports.Teleport;
import com.zenyte.game.content.skills.prayer.Prayer;
import com.zenyte.game.model.HintArrow;
import com.zenyte.game.task.WorldTasksManager;
import com.zenyte.game.world.World;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.region.PolygonRegionArea;
import com.zenyte.game.world.region.RSPolygon;
import com.zenyte.game.world.region.area.plugins.PrayerPlugin;
import com.zenyte.game.world.region.area.plugins.RandomEventRestrictionPlugin;
import com.zenyte.game.world.region.area.plugins.TeleportPlugin;
import com.zenyte.game.world.region.area.plugins.TradePlugin;
import com.zenyte.plugins.renewednpc.ExilesGuide;



/**
 * <AUTHOR> | 1-2-2019 | 16:03
 * @see <a href="https://www.rune-server.ee/members/tommeh/">Rune-Server profile</a>}
 */
public class RegisterArea extends PolygonRegionArea implements TeleportPlugin, TradePlugin, PrayerPlugin, RandomEventRestrictionPlugin {

    public static final Location EXILES_GUIDE_LOCATION = new Location(3074, 3505, 0);

    @Override
    public RSPolygon[] polygons() {
        return new RSPolygon[] { new RSPolygon(new int[][] { {3076, 3502}, {3074, 3502}, {3073, 3503}, {3073, 3506}, {3074,3507}, {3076,3507} })};
    }

    @Override
    public void enter(Player player) {
        if(!player.getBooleanAttribute("registered")) {
            player.freeze(Integer.MAX_VALUE);
            World.findNPC(ExilesGuide.NPC_ID, EXILES_GUIDE_LOCATION, 10)
                    .ifPresent(npc -> player.getPacketDispatcher().sendHintArrow(new HintArrow(npc)));
            WorldTasksManager.schedule(() -> GameInterface.CHARACTER_DESIGN.open(player), 1);
        }
    }

    @Override
    public void leave(Player player, boolean logout) {
    }

    @Override
    public String name() {
        return "Register Area";
    }

    @Override
    public boolean canTeleport(final Player player, final Teleport teleport) {
        return false;
    }

    @Override
    public boolean canTrade(final Player player, final Player partner) {
        return false;
    }

    public boolean processMovement(Player player, int x, int y) {
        return false;
    }

    @Override
    public boolean activatePrayer(final Player player, final Prayer prayer) {
        return false;
    }

}

