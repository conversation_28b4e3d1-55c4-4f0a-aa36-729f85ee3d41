package com.zenyte.game.world.entity.player.container.impl.bank;

import com.google.common.collect.ImmutableMap;

import static com.zenyte.game.item.ItemId.*;

/**
 * <AUTHOR> | 12/10/2019
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>
 */
public class PlaceholderRedirections {

    public static final ImmutableMap<Integer, Integer> builder = ImmutableMap.<Integer, Integer>builder()

            .put(DHAROKS_GREATAXE_100, DHAROKS_GREATAXE).put(DHAROKS_GREATAXE_75, DHAROKS_GREATAXE).put(DHAROKS_GREATAXE_50, DHA<PERSON>KS_GREATAXE).put(DHAROKS_GREATAXE_25, DHA<PERSON><PERSON>_GREATAXE)
            .put(DHAROKS_HELM_100, DHAROKS_HELM).put(D<PERSON><PERSON>KS_HELM_75, DHARO<PERSON>_HELM).put(D<PERSON><PERSON><PERSON>_HELM_50, <PERSON><PERSON><PERSON><PERSON>_HELM).put(D<PERSON><PERSON><PERSON>_HELM_25, D<PERSON><PERSON><PERSON>_HELM)
            .put(<PERSON><PERSON><PERSON><PERSON>_PLATEBODY_100, DHAROKS_PLATEBODY).put(DHAROKS_PLATEBODY_75, DHAROKS_PLATEBODY).put(DHAROKS_PLATEBODY_50, DHAROKS_PLATEBODY).put(DHAROKS_PLATEBODY_25, DHAROKS_PLATEBODY)
            .put(DHAROKS_PLATELEGS_100, DHAROKS_PLATELEGS).put(DHAROKS_PLATELEGS_75, DHAROKS_PLATELEGS).put(DHAROKS_PLATELEGS_50, DHAROKS_PLATELEGS).put(DHAROKS_PLATELEGS_25, DHAROKS_PLATELEGS)

            .put(AHRIMS_HOOD_100, AHRIMS_HOOD).put(AHRIMS_HOOD_75, AHRIMS_HOOD).put(AHRIMS_HOOD_50, AHRIMS_HOOD).put(AHRIMS_HOOD_25, AHRIMS_HOOD)
            .put(AHRIMS_ROBESKIRT_100, AHRIMS_ROBESKIRT).put(AHRIMS_ROBESKIRT_75, AHRIMS_ROBESKIRT).put(AHRIMS_ROBESKIRT_50, AHRIMS_ROBESKIRT).put(AHRIMS_ROBESKIRT_25, AHRIMS_ROBESKIRT)
            .put(AHRIMS_ROBETOP_100, AHRIMS_ROBETOP).put(AHRIMS_ROBETOP_75, AHRIMS_ROBETOP).put(AHRIMS_ROBETOP_50, AHRIMS_ROBETOP).put(AHRIMS_ROBETOP_25, AHRIMS_ROBETOP)
            .put(AHRIMS_STAFF_100, AHRIMS_STAFF).put(AHRIMS_STAFF_75, AHRIMS_STAFF).put(AHRIMS_STAFF_50, AHRIMS_STAFF).put(AHRIMS_STAFF_25, AHRIMS_STAFF)

            .put(TORAGS_HAMMERS_100, TORAGS_HAMMERS).put(TORAGS_HAMMERS_75, TORAGS_HAMMERS).put(TORAGS_HAMMERS_50, TORAGS_HAMMERS).put(TORAGS_HAMMERS_25, TORAGS_HAMMERS)
            .put(TORAGS_HELM_100, TORAGS_HELM).put(TORAGS_HELM_75, TORAGS_HELM).put(TORAGS_HELM_50, TORAGS_HELM).put(TORAGS_HELM_25, TORAGS_HELM)
            .put(TORAGS_PLATEBODY_100, TORAGS_PLATEBODY).put(TORAGS_PLATEBODY_75, TORAGS_PLATEBODY).put(TORAGS_PLATEBODY_50, TORAGS_PLATEBODY).put(TORAGS_PLATEBODY_25, TORAGS_PLATEBODY)
            .put(TORAGS_PLATELEGS_100, TORAGS_PLATELEGS).put(TORAGS_PLATELEGS_75, TORAGS_PLATELEGS).put(TORAGS_PLATELEGS_50, TORAGS_PLATELEGS).put(TORAGS_PLATELEGS_25, TORAGS_PLATELEGS)

            .put(GUTHANS_CHAINSKIRT_100, GUTHANS_CHAINSKIRT).put(GUTHANS_CHAINSKIRT_75, GUTHANS_CHAINSKIRT).put(GUTHANS_CHAINSKIRT_50, GUTHANS_CHAINSKIRT).put(GUTHANS_CHAINSKIRT_25, GUTHANS_CHAINSKIRT)
            .put(GUTHANS_HELM_100, GUTHANS_HELM).put(GUTHANS_HELM_75, GUTHANS_HELM).put(GUTHANS_HELM_50, GUTHANS_HELM).put(GUTHANS_HELM_25, GUTHANS_HELM)
            .put(GUTHANS_PLATEBODY_100, GUTHANS_PLATEBODY).put(GUTHANS_PLATEBODY_75, GUTHANS_PLATEBODY).put(GUTHANS_PLATEBODY_50, GUTHANS_PLATEBODY).put(GUTHANS_PLATEBODY_25, GUTHANS_PLATEBODY)
            .put(GUTHANS_WARSPEAR_100, GUTHANS_WARSPEAR).put(GUTHANS_WARSPEAR_75, GUTHANS_WARSPEAR).put(GUTHANS_WARSPEAR_50, GUTHANS_WARSPEAR).put(GUTHANS_WARSPEAR_25, GUTHANS_WARSPEAR)

            .put(VERACS_BRASSARD_100, VERACS_BRASSARD).put(VERACS_BRASSARD_75, VERACS_BRASSARD).put(VERACS_BRASSARD_50, VERACS_BRASSARD).put(VERACS_BRASSARD_25, VERACS_BRASSARD)
            .put(VERACS_FLAIL_100, VERACS_FLAIL).put(VERACS_FLAIL_75, VERACS_FLAIL).put(VERACS_FLAIL_50, VERACS_FLAIL).put(VERACS_FLAIL_25, VERACS_FLAIL)
            .put(VERACS_HELM_100, VERACS_HELM).put(VERACS_HELM_75, VERACS_HELM).put(VERACS_HELM_50, VERACS_HELM).put(VERACS_HELM_25, VERACS_HELM)
            .put(VERACS_PLATESKIRT_100, VERACS_PLATESKIRT).put(VERACS_PLATESKIRT_75, VERACS_PLATESKIRT).put(VERACS_PLATESKIRT_50, VERACS_PLATESKIRT).put(VERACS_PLATESKIRT_25, VERACS_PLATESKIRT)

            .put(KARILS_COIF_100, KARILS_COIF).put(KARILS_COIF_75, KARILS_COIF).put(KARILS_COIF_50, KARILS_COIF).put(KARILS_COIF_25, KARILS_COIF)
            .put(KARILS_CROSSBOW_100, KARILS_CROSSBOW).put(KARILS_CROSSBOW_75, KARILS_CROSSBOW).put(KARILS_CROSSBOW_50, KARILS_CROSSBOW).put(KARILS_CROSSBOW_25, KARILS_CROSSBOW)
            .put(KARILS_LEATHERSKIRT_100, KARILS_LEATHERSKIRT).put(KARILS_LEATHERSKIRT_75, KARILS_LEATHERSKIRT).put(KARILS_LEATHERSKIRT_50, KARILS_LEATHERSKIRT).put(KARILS_LEATHERSKIRT_25, KARILS_LEATHERSKIRT)
            .put(KARILS_LEATHERTOP_100, KARILS_LEATHERTOP).put(KARILS_LEATHERTOP_75, KARILS_LEATHERTOP).put(KARILS_LEATHERTOP_50, KARILS_LEATHERTOP).put(KARILS_LEATHERTOP_25, KARILS_LEATHERTOP)

            .put(IBANS_STAFF_U, IBANS_STAFF).put(IBANS_STAFF_1410, IBANS_STAFF)

            .put(TRIDENT_OF_THE_SEAS, TRIDENT_OF_THE_SEAS_FULL).put(UNCHARGED_TRIDENT, TRIDENT_OF_THE_SEAS_FULL)
            .put(UNCHARGED_TOXIC_TRIDENT, TRIDENT_OF_THE_SWAMP)
            .put(UNCHARGED_TRIDENT_E, TRIDENT_OF_THE_SEAS_E)
            .put(UNCHARGED_TOXIC_TRIDENT_E, TRIDENT_OF_THE_SWAMP_E)

            .put(TOME_OF_FIRE_EMPTY, TOME_OF_FIRE)

            .put(TOXIC_BLOWPIPE_EMPTY, TOXIC_BLOWPIPE)
            .put(MAGMA_HELM_UNCHARGED, MAGMA_HELM)
            .put(TANZANITE_HELM_UNCHARGED, TANZANITE_HELM)
            .put(SERPENTINE_HELM_UNCHARGED, SERPENTINE_HELM)

            .put(INFERNAL_PICKAXE_UNCHARGED, INFERNAL_PICKAXE)
            .put(INFERNAL_AXE_UNCHARGED, INFERNAL_AXE)

            .put(MAX_CAPE_13342, MAX_CAPE)

            .put(AMULET_OF_GLORY2, AMULET_OF_GLORY1).put(AMULET_OF_GLORY3, AMULET_OF_GLORY1).put(AMULET_OF_GLORY4, AMULET_OF_GLORY1).put(AMULET_OF_GLORY5, AMULET_OF_GLORY1)
            .put(AMULET_OF_GLORY_T2, AMULET_OF_GLORY_T1).put(AMULET_OF_GLORY_T3, AMULET_OF_GLORY_T1).put(AMULET_OF_GLORY_T4, AMULET_OF_GLORY_T1).put(AMULET_OF_GLORY_T5, AMULET_OF_GLORY_T1)
            .put(BURNING_AMULET2, BURNING_AMULET1).put(BURNING_AMULET3, BURNING_AMULET1).put(BURNING_AMULET4, BURNING_AMULET1)
            .put(GAMES_NECKLACE2, GAMES_NECKLACE1).put(GAMES_NECKLACE3, GAMES_NECKLACE1).put(GAMES_NECKLACE4, GAMES_NECKLACE1).put(GAMES_NECKLACE5, GAMES_NECKLACE1).put(GAMES_NECKLACE6,
                    GAMES_NECKLACE1).put(GAMES_NECKLACE7, GAMES_NECKLACE1)
            .put(NECKLACE_OF_PASSAGE2, NECKLACE_OF_PASSAGE1).put(NECKLACE_OF_PASSAGE3, NECKLACE_OF_PASSAGE1).put(NECKLACE_OF_PASSAGE4, NECKLACE_OF_PASSAGE1)
            .put(DIGSITE_PENDANT_2, DIGSITE_PENDANT_1).put(DIGSITE_PENDANT_3, DIGSITE_PENDANT_1).put(DIGSITE_PENDANT_4, DIGSITE_PENDANT_1)
            .put(SKILLS_NECKLACE2, SKILLS_NECKLACE1).put(SKILLS_NECKLACE3, SKILLS_NECKLACE1).put(SKILLS_NECKLACE4, SKILLS_NECKLACE1).put(SKILLS_NECKLACE5, SKILLS_NECKLACE1)
            .put(RING_OF_DUELING2, RING_OF_DUELING1).put(RING_OF_DUELING3, RING_OF_DUELING1).put(RING_OF_DUELING4, RING_OF_DUELING1).put(RING_OF_DUELING5, RING_OF_DUELING1).put(RING_OF_DUELING6,
                    RING_OF_DUELING1).put(RING_OF_DUELING7, RING_OF_DUELING1)
            .put(RING_OF_RETURNING2, RING_OF_RETURNING1).put(RING_OF_RETURNING3, RING_OF_RETURNING1).put(RING_OF_RETURNING4, RING_OF_RETURNING1)
            .put(RING_OF_WEALTH_2, RING_OF_WEALTH_1).put(RING_OF_WEALTH_3, RING_OF_WEALTH_1).put(RING_OF_WEALTH_4, RING_OF_WEALTH_1)
            .put(RING_OF_WEALTH_I2, RING_OF_WEALTH_I1).put(RING_OF_WEALTH_I3, RING_OF_WEALTH_I1).put(RING_OF_WEALTH_I4, RING_OF_WEALTH_I1)
            .put(SLAYER_RING_2, SLAYER_RING_1).put(SLAYER_RING_3, SLAYER_RING_1).put(SLAYER_RING_4, SLAYER_RING_1).put(SLAYER_RING_5, SLAYER_RING_1).put(SLAYER_RING_6, SLAYER_RING_1).put(SLAYER_RING_7, SLAYER_RING_1)
            .put(CASTLE_WARS_BRACELET2, CASTLE_WARS_BRACELET1)
            .put(ABYSSAL_BRACELET2, ABYSSAL_BRACELET1).put(ABYSSAL_BRACELET3, ABYSSAL_BRACELET1).put(ABYSSAL_BRACELET4, ABYSSAL_BRACELET1)
            .put(COMBAT_BRACELET2, COMBAT_BRACELET1).put(COMBAT_BRACELET3, COMBAT_BRACELET1).put(COMBAT_BRACELET4, COMBAT_BRACELET1).put(COMBAT_BRACELET5, COMBAT_BRACELET1)


            .build();


}
