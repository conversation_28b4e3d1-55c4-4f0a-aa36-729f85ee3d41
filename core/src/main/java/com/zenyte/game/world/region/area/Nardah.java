package com.zenyte.game.world.region.area;

import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.teleportsystem.PortalTeleport;
import com.zenyte.game.world.region.RSPolygon;

/**
 * <AUTHOR> | 15/04/2019 17:23
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>
 */
public class Nardah extends SouthernDesertArea {
    @Override
    public RSPolygon[] polygons() {
        return new RSPolygon[] {
                new RSPolygon(new int[][]{
                        { 3392, 2944 },
                        { 3392, 2880 },
                        { 3456, 2880 },
                        { 3456, 2944 }
                })
        };
    }

    @Override
    public void enter(final Player player) {
        super.enter(player);
        player.getTeleportManager().unlock(PortalTeleport.NARDAH);
    }

    @Override
    public void process() {

    }

    @Override
    public String name() {
        return "<PERSON>rdah";
    }
}
