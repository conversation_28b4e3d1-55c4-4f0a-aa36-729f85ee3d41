package com.zenyte.game.world.entity.player.container.impl.bank;

import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.region.RSPolygon;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> | 24/01/2019 13:56
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>
 */
public class BankPolygons {
    private static final int[][][][] BANK_COORDS = new int[][][][] {new int[][][] {
    /** Mos Le'Harmless bank. */
    new int[][] {{3679, 2985}, {3679, 2980}, {3680, 2980}, {3681, 2981}, {3682, 2981}, {3683, 2980}, {3684, 2980}, {3684, 2985}, {3683, 2985}, {3682, 2984}, {3681, 2984}, {3680, 2985}}, 
    /** <PERSON><PERSON><PERSON>'s bank. */
    new int[][] {{3424, 2895}, {3424, 2887}, {3427, 2887}, {3427, 2889}, {3431, 2889}, {3431, 2895}}, 
    /** Al-kharid bank */
    new int[][] {{3265, 3174}, {3265, 3161}, {3273, 3161}, {3273, 3174}}, 
    /** Theatre of Blood bank. */
    new int[][] {{3653, 3216}, {3656, 3214}, {3656, 3204}, {3644, 3204}, {3642, 3206}, {3642, 3209}, {3643, 3209}, {3644, 3210}, {3644, 3212}, {3645, 3213}, {3647, 3213}, {3647, 3215}, {3646, 3216}, {3646, 3217}, {3648, 3217}, {3649, 3218}, {3652, 3218}, {3653, 3217}}, 
    /** Port Phasmatys bank. */
    new int[][] {{3686, 3472}, {3686, 3461}, {3700, 3461}, {3700, 3472}}, 
    /** Canifis bank. */
    new int[][] {{3509, 3484}, {3509, 3483}, {3508, 3482}, {3508, 3479}, {3509, 3478}, {3509, 3474}, {3513, 3474}, {3513, 3475}, {3514, 3476}, {3514, 3478}, {3517, 3478}, {3517, 3484}}, 
    /** Ape Atoll bank. */
    new int[][] {{2779, 2786}, {2779, 2781}, {2783, 2781}, {2783, 2786}}, 
    /** Pest Control bank. */
    new int[][] {{2665, 2656}, {2665, 2651}, {2670, 2651}, {2670, 2656}}, 
    /** Varrock Eastern bank. */
    new int[][] {{3250, 3425}, {3258, 3425}, {3258, 3416}, {3250, 3416}}, 
    /** Varrock western bank. */
    new int[][] {{3180, 3448}, {3180, 3433}, {3191, 3433}, {3191, 3448}}, 
    /** Tutorial island bank. */
    new int[][] {{3118, 3126}, {3130, 3126}, {3130, 3123}, {3125, 3123}, {3125, 3121}, {3126, 3121}, {3126, 3119}, {3118, 3119}, {3118, 3121}, {3119, 3121}, {3119, 3123}, {3118, 3123}}, 
    /** Draynor Village bank. */
    new int[][] {{3088, 3247}, {3088, 3240}, {3098, 3240}, {3098, 3247}}, 
    /** Falador Eastern bank. */
    new int[][] {{3009, 3359}, {3019, 3359}, {3019, 3357}, {3022, 3357}, {3022, 3353}, {3009, 3353}}, 
    /** Falador Western bank. */
    new int[][] {{2943, 3374}, {2943, 3368}, {2945, 3368}, {2945, 3366}, {2946, 3366}, {2946, 3362}, {2945, 3362}, {2945, 3359}, {2949, 3359}, {2949, 3366}, {2950, 3366}, {2950, 3370}, {2948, 3370}, {2948, 3374}}, 
    /* new int[][] { { 3091, 3500 }, { 3091, 3498 }, { 3090, 3497 }, { 3090, 3494 }, { 3091, 3493 }, { 3091,
                    3488 }, { 3099, 3488 },
                    { 3099, 3500 } },*/
    /** Edgeville bank. */
    /** Shilo Village bank. */
    new int[][] {{2844, 2957}, {2843, 2956}, {2843, 2953}, {2844, 2952}, {2847, 2952}, {2848, 2953}, {2849, 2953}, {2851, 2951}, {2854, 2951}, {2856, 2953}, {2858, 2953}, {2859, 2952}, {2861, 2952}, {2862, 2953}, {2862, 2956}, {2861, 2957}, {2859, 2957}, {2858, 2956}, {2856, 2956}, {2854, 2958}, {2851, 2958}, {2849, 2956}, {2847, 2956}, {2846, 2957}}, 
    /** Catherby bank. */
    new int[][] {{2806, 3446}, {2806, 3438}, {2813, 3438}, {2813, 3446}}, 
    /** Warriors' guild bank. */
    new int[][] {{2841, 3546}, {2841, 3540}, {2843, 3540}, {2843, 3537}, {2845, 3537}, {2846, 3536}, {2847, 3536}, {2848, 3537}, {2849, 3537}, {2849, 3546}}, 
    /** Seers' Village bank. */
    new int[][] {{2721, 3498}, {2721, 3497}, {2719, 3497}, {2719, 3494}, {2721, 3494}, {2721, 3490}, {2724, 3490}, {2724, 3487}, {2728, 3487}, {2728, 3490}, {2731, 3490}, {2731, 3498}}, 
    /** Fishing guild bank. */
    new int[][] {{2584, 3423}, {2583, 3422}, {2583, 3419}, {2584, 3418}, {2587, 3418}, {2588, 3417}, {2588, 3414}, {2589, 3413}, {2590, 3413}, {2591, 3412}, {2591, 3408}, {2593, 3408}, {2594, 3409}, {2594, 3410}, {2593, 3411}, {2593, 3419}, {2592, 3420}, {2591, 3420}, {2590, 3421}, {2590, 3422}, {2589, 3423}}, 
    /** Ardougne Western bank. */
    new int[][] {{2616, 3335}, {2615, 3335}, {2615, 3336}, {2612, 3336}, {2612, 3330}, {2622, 3330}, {2622, 3336}, {2619, 3336}, {2619, 3335}}, 
    /** Ardougne Eastern bank. */
    new int[][] {{2649, 3288}, {2649, 3280}, {2659, 3280}, {2659, 3288}}, 
    /** Yanille bank. */
    new int[][] {{2609, 3098}, {2609, 3088}, {2617, 3088}, {2617, 3098}}, 
    /** Corsair Cove bank. */
    new int[][] {{2569, 2868}, {2568, 2867}, {2569, 2866}, {2569, 2865}, {2568, 2864}, {2569, 2863}, {2572, 2863}, {2573, 2864}, {2572, 2865}, {2572, 2866}, {2573, 2867}, {2572, 2868}}, 
    /** Lletya bank. */
    new int[][] {{2351, 3167}, {2351, 3165}, {2350, 3164}, {2350, 3162}, {2351, 3161}, {2354, 3161}, {2355, 3162}, {2355, 3164}, {2357, 3166}, {2357, 3167}}, 
    /** Piscatoris fishing colony bank. */
    new int[][] {{2327, 3694}, {2327, 3686}, {2333, 3686}, {2333, 3694}}, 
    /** Lunar island bank. */
    new int[][] {{2097, 3922}, {2097, 3917}, {2105, 3917}, {2105, 3922}}, 
    /** Neitiznot bank. */
    new int[][] {{2334, 3809}, {2334, 3805}, {2340, 3805}, {2340, 3809}}, 
    /** Jatizso bank. */
    new int[][] {{2415, 3804}, {2414, 3803}, {2414, 3802}, {2415, 3801}, {2415, 3800}, {2416, 3799}, {2418, 3799}, {2419, 3800}, {2419, 3801}, {2420, 3802}, {2420, 3803}, {2419, 3804}}, 
    /** Etceteria bank. */
    new int[][] {{2618, 3897}, {2618, 3893}, {2622, 3893}, {2622, 3897}}, 
    /** Dorgesh-Kaan bank. */
    new int[][] {{2697, 5353}, {2697, 5347}, {2700, 5347}, {2701, 5346}, {2701, 5345}, {2707, 5345}, {2707, 5347}, {2708, 5348}, {2708, 5350}, {2707, 5351}, {2707, 5355}, {2701, 5355}, {2701, 5354}, {2700, 5353}}}, new int[][][] {
    /** Grand tree bank. */
    new int[][] {{2443, 3489}, {2443, 3490}, {2438, 3490}, {2438, 3487}, {2443, 3487}, {2443, 3488}, {2447, 3488}, {2447, 3486}, {2449, 3486}, {2449, 3483}, {2448, 3483}, {2448, 3478}, {2451, 3478}, {2451, 3483}, {2450, 3483}, {2450, 3486}, {2452, 3486}, {2452, 3491}, {2447, 3491}, {2447, 3489}}}, new int[][][] {new int[][] {{3207, 3223}, {3207, 3215}, {3211, 3215}, {3211, 3223}}}, new int[][][] {}};
    /**
     * Lumbridge bank.
     */
    private static final List<RSPolygon> polygons = new ArrayList<>(50);

    static {
        for (int z = 0; z < 4; z++) {
            final int[][][] coords = BANK_COORDS[z];
            for (final int[][] coord : coords) {
                polygons.add(new RSPolygon(coord, z));
            }
        }
    }

    public static final boolean contains(final Location location) {
        for (int i = polygons.size() - 1; i >= 0; i--) {
            final RSPolygon poly = polygons.get(i);
            if (poly.contains(location)) return true;
        }
        return false;
    }
}
