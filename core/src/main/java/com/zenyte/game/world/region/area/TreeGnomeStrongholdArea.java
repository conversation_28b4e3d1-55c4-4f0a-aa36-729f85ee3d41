package com.zenyte.game.world.region.area;

import com.zenyte.game.world.region.RSPolygon;

/**
 * <AUTHOR> | 7. juuni 2018 : 02:25:49
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>}
 * @see <a href="https://rune-status.net/members/kris.354/">Rune-Status profile</a>}
 */
public final class TreeGnomeStrongholdArea extends KingdomOfKandarin {

	@Override
	public RSPolygon[] polygons() {
		return new RSPolygon[] { new RSPolygon(new int[][] { { 2380, 3520 }, { 2380, 3509 }, { 2377, 3506 }, { 2377, 3500 }, { 2375, 3498 },
				{ 2375, 3487 }, { 2377, 3485 }, { 2377, 3479 }, { 2380, 3476 }, { 2381, 3473 }, { 2381, 3464 }, { 2375, 3458 },
				{ 2375, 3447 }, { 2371, 3443 }, { 2371, 3434 }, { 2369, 3432 }, { 2369, 3423 }, { 2375, 3417 }, { 2375, 3414 },
				{ 2376, 3413 }, { 2376, 3412 }, { 2378, 3410 }, { 2379, 3410 }, { 2381, 3408 }, { 2383, 3408 }, { 2384, 3407 },
				{ 2387, 3407 }, { 2395, 3411 }, { 2403, 3410 }, { 2403, 3412 }, { 2410, 3412 }, { 2414, 3408 }, { 2414, 3401 },
				{ 2416, 3399 }, { 2416, 3398 }, { 2421, 3393 }, { 2427, 3393 }, { 2429, 3391 }, { 2432, 3391 }, { 2435, 3388 },
				{ 2439, 3388 }, { 2440, 3389 }, { 2441, 3389 }, { 2443, 3391 }, { 2446, 3391 }, { 2447, 3390 }, { 2449, 3390 },
				{ 2450, 3391 }, { 2455, 3391 }, { 2457, 3389 }, { 2457, 3386 }, { 2459, 3384 }, { 2464, 3384 }, { 2466, 3386 },
				{ 2466, 3389 }, { 2468, 3391 }, { 2471, 3391 }, { 2472, 3390 }, { 2476, 3390 }, { 2477, 3389 }, { 2481, 3389 },
				{ 2482, 3390 }, { 2485, 3390 }, { 2486, 3389 }, { 2488, 3389 }, { 2488, 3390 }, { 2489, 3391 }, { 2493, 3391 },
				{ 2494, 3390 }, { 2501, 3390 }, { 2502, 3391 }, { 2505, 3391 }, { 2506, 3392 }, { 2506, 3395 }, { 2505, 3396 },
				{ 2502, 3396 }, { 2494, 3404 }, { 2494, 3408 }, { 2495, 3409 }, { 2495, 3412 }, { 2496, 3413 }, { 2496, 3416 },
				{ 2498, 3418 }, { 2498, 3429 }, { 2494, 3433 }, { 2494, 3438 }, { 2496, 3440 }, { 2496, 3456 }, { 2496, 3520 },
				{ 2479, 3519 }, { 2473, 3514 }, { 2468, 3512 }, { 2461, 3513 }, { 2453, 3519 }, { 2448, 3520 }, { 2432, 3520 },
				{ 2429, 3523 }, { 2428, 3523 }, { 2427, 3522 }, { 2426, 3522 }, { 2425, 3521 }, { 2415, 3521 }, { 2414, 3522 },
				{ 2407, 3522 }, { 2406, 3523 }, { 2405, 3523 }, { 2403, 3525 }, { 2400, 3525 }, { 2399, 3524 }, { 2394, 3524 },
				{ 2393, 3523 }, { 2385, 3523 }, { 2384, 3522 }, { 2383, 3522 }, { 2382, 3521 }, { 2381, 3521 } }) };
	}

	@Override
	public String name() {
		return "Tree Gnome Stronghold";
	}

}
