package com.zenyte.game.world.region.area;

import com.google.common.eventbus.Subscribe;
import com.zenyte.game.world.World;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.object.ObjectId;
import com.zenyte.game.world.object.WorldObject;
import com.zenyte.game.world.region.RSPolygon;
import com.zenyte.game.world.region.area.plugins.CannonRestrictionPlugin;
import com.zenyte.plugins.events.ServerLaunchEvent;

/**
 * <AUTHOR> | 26/03/2019 14:06
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>
 */
public class EdgevilleArea extends KingdomOfMisthalin implements CannonRestrictionPlugin {

    @Subscribe
    public static void onServerLaunched(ServerLaunchEvent event) {
        World.spawnObject(new WorldObject(50081, 10, 0, new Location( 3091, 3504))); //pool
        World.spawnObject(new WorldObject(24911, 10, 1, new Location( 3089, 3498))); //altar spellbook
        World.spawnObject(new WorldObject(50082, 10, 1, new Location( 3083, 3494))); //teleporter
        World.spawnObject(new WorldObject(41728, 10, 0, new Location( 3097, 3506))); //hiscores table
        World.spawnObject(new WorldObject(33020, 10, 0, new Location( 3078, 3508))); //upgrade rack
        World.spawnObject(new WorldObject(34662, 10, 2, new Location( 3107, 3515))); //brimstone chest
        World.spawnObject(new WorldObject(36582, 10, 3, new Location( 3107, 3516))); //elven c key chest
        World.spawnObject(new WorldObject(50092, 10, 1, new Location( 3078, 3488))); //well of goodwill
        World.spawnObject(new WorldObject(60000, 10, 2, new Location( 3081, 3488))); //magical storage (POH)
        World.spawnObject(new WorldObject(50055, 10, 1, new Location( 3097, 3479))); //stall beginner
        World.spawnObject(new WorldObject(50056, 10, 1, new Location( 3097, 3476))); //stall easy
        World.spawnObject(new WorldObject(50057, 10, 1, new Location( 3097, 3473))); //stall medium
        World.spawnObject(new WorldObject(50058, 10, 1, new Location( 3097, 3470))); //stall hard
        World.spawnObject(new WorldObject(50059, 10, 1, new Location( 3097, 3467))); //stall master
        World.spawnObject(new WorldObject(40448, 10, 3, new Location( 3084, 3488))); //challenge scoreboard
        World.spawnObject(new WorldObject(55000, 10, 0, new Location( 3082, 3507))); //breakdown bench
        World.spawnObject(new WorldObject(26645, 10, 1, new Location( 3090, 3509))); //ffa portal
        World.spawnObject(new WorldObject(35003, 10, 0, new Location( 3083, 3504))); //spirit tree/ring
        World.spawnObject(new WorldObject(6802, 10, 1, new Location( 3091, 3510))); //repair stand
        World.spawnObject(new WorldObject(50091, 10, 0, new Location( 3078, 3484))); //event board
        World.spawnObject(new WorldObject(41724, 10, 3, new Location( 3077, 3480))); //event portal
        World.spawnObject(new WorldObject(14888, 10, 3, new Location( 3107, 3496))); //pottery oven
        World.spawnObject(new WorldObject(14887, 10, 3, new Location( 3106, 3496))); //pottery wheel
        World.spawnObject(new WorldObject(14889, 10, 2, new Location( 3105, 3496))); //spinning wheel
        World.spawnObject(new WorldObject(50103, 10, 1, new Location( 3106, 3514))); //slayer statue

        //World.spawnObject(new WorldObject(31617, 10, 0, new Location( 3100, 3499))); //afk barrier
        //World.spawnObject(new WorldObject(31617, 10, 0, new Location( 3100, 3498))); //afk barrier
        //World.spawnObject(new WorldObject(55071, 0, 0, new Location( 3100, 3499))); //afk barrier v2
        //World.spawnObject(new WorldObject(55071, 0, 0, new Location( 3100, 3498))); //afk barrier v2
        //World.spawnObject(new WorldObject(50084, 10, 0, new Location( 3101, 3501))); //afk farming
        //World.spawnObject(new WorldObject(50085, 10, 0, new Location( 3101, 3495))); //afk fire
        //World.spawnObject(new WorldObject(50087, 10, 0, new Location( 3106, 3495))); //afk rocks
        //World.spawnObject(new WorldObject(50089, 10, 0, new Location( 3113, 3498))); //afk thieving
        //World.spawnObject(new WorldObject(50061, 22, 1, new Location( 3106, 3498))); //afk agility
        //World.spawnObject(new WorldObject(50061, 22, 3, new Location( 3109, 3498))); //afk agility
        //World.spawnObject(new WorldObject(3553, 22, 3, new Location( 3107, 3498))); //log balance
        //World.spawnObject(new WorldObject(3553, 22, 3, new Location( 3108, 3498))); //log balance
        //World.spawnObject(new WorldObject(50090, 10, 0, new Location( 3111, 3501))); //afk tree
        //World.spawnObject(new WorldObject(50093, 10, 0, new Location( 3105, 3503))); //afk spinning wheel
        //World.spawnObject(new WorldObject(50094, 10, 0, new Location( 3104, 3494))); //afk furnace
        //World.spawnObject(new WorldObject(50095, 10, 0, new Location( 3108, 3501))); //afk runecrafting
        //World.spawnObject(new WorldObject(50096, 10, 0, new Location( 3112, 3495))); //afk fishing
        //World.spawnObject(new WorldObject(38426, 10, 0, new Location( 3074, 3480))); //death's entrance

    }

    @Override
    public RSPolygon[] polygons() {
        return new RSPolygon[]{
                new RSPolygon(new int[][]{
                        {3072, 3521},
                        {3072, 3449},
                        {3099, 3449},
                        {3104, 3461},
                        {3109, 3462},
                        {3120, 3462},
                        {3123, 3465},
                        {3132, 3465},
                        {3135, 3468},
                        {3141, 3468},
                        {3138, 3472},
                        {3138, 3483},
                        {3141, 3485},
                        {3141, 3491},
                        {3138, 3494},
                        {3138, 3514},
                        {3142, 3518},
                        {3142, 3521}
                })
        };
    }

    @Override
    public String name() {
        return "Edgeville";
    }

    @Override
    public String restrictionMessage() {
        return "The Grand Exchange staff prefer not to have heavy artillery operated around their premises.";
    }
}
