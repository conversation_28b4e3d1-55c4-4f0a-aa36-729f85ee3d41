package com.zenyte.game.world.entity.masks;

import com.zenyte.net.io.RSBuffer;

/**
 * <AUTHOR> | 28. march 2018 : 0:37.40
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>}
 * @see <a href="https://rune-status.net/members/kris.354/">Rune-Status profile</a>}
 * <p>
 * Mark id must be a positive integer!
 */
public enum HitType {
    MISSED(12, 13),
    //Regular does not invoke special effects such as vengeance.
    REGULAR(16, 17),
    //Used for damage not applied by any direct combat (thieving)
    TYPELESS(16, 17),
    //Default invokes special effects such as vengeance.
    DEFAULT(16, 17),
    <PERSON><PERSON><PERSON>(16, 17),
    <PERSON><PERSON><PERSON>(16, 17),
    RANGED(16, 17),
    P<PERSON><PERSON><PERSON>(2),
    YELLOW(3),
    DISEASED(4),
    VENOM(5),
    HEALED(6),
    SHIELD_CHARGE(11),
    PALM_LOWER(15),

    <PERSON><PERSON><PERSON>(18, 19),
    <PERSON><PERSON><PERSON>(20, 21),
    <PERSON><PERSON><PERSON>(22, 23),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(24, 25),
    CORR<PERSON><PERSON><PERSON>(0),
    SHIELD_DOWN(60),
    <PERSON><PERSON>ENS(53, 54),
    PRAYER_DRAIN(60),
    BLEED(67),
    SANITY_DRAIN(71),
    SANITY_RESTORE(72),
    DOOM(73),
    BURN(74)
    ;

    private final int id;
    private final int dynamicID;

    public static final HitType[] values = values();

    HitType(int id, int dynamicID) {
        this.id = id;
        this.dynamicID = dynamicID;
    }

    HitType(int id) {
        this(id, id);
    }

    public void writeMask(RSBuffer buffer, boolean useDynamic) {
        buffer.writeSmart(useDynamic ? dynamicID : id);
    }

}
