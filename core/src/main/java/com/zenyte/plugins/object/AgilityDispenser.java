package com.zenyte.plugins.object;

import com.zenyte.game.item.Item;
import com.zenyte.game.world.entity.masks.Animation;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.SkillConstants;
import com.zenyte.game.world.object.ObjectAction;
import com.zenyte.game.world.object.ObjectId;
import com.zenyte.game.world.object.WorldObject;
import com.zenyte.utils.TimeUnit;

/**
 * <AUTHOR> | 10/10/2019
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>
 */
public class AgilityDispenser implements ObjectAction {

    private static final int WILDERNESS_AGILITY_TICKET = 29460;
    private static final Animation REDEEM_ANIMATION = new Animation(832);

    @Override
    public void handleObjectAction(Player player, WorldObject object, String name, int optionId, String option) {
            int availableTickets = player.getInventory().getAmountOf(WILDERNESS_AGILITY_TICKET);
            if (availableTickets == 0) {
                player.sendMessage("You don't have any wilderness agility tickets to redeem.");
                return;
            }
            player.sendInputInt("How many wilderness agility tickets would you like to redeem? (You have " + availableTickets + ")", amount -> {
                redeemTickets(player, amount);
            });
    }

    /**
     * Redeems wilderness agility tickets for experience based on OSRS wiki rates
     */
    private void redeemTickets(Player player, int amount) {
        int availableTickets = player.getInventory().getAmountOf(WILDERNESS_AGILITY_TICKET);
        if (amount <= 0) {
            player.sendMessage("You must redeem at least 1 ticket.");
            return;
        }
        if (amount > availableTickets) {
            amount = availableTickets;
        }
        if (availableTickets == 0) {
            player.sendMessage("You don't have any wilderness agility tickets to redeem.");
            return;
        }
        player.getInventory().deleteItem(WILDERNESS_AGILITY_TICKET, amount);
        double experiencePerTicket = getExperiencePerTicket(amount);
        double totalExperience = experiencePerTicket * amount;
        player.getSkills().addXp(SkillConstants.AGILITY, totalExperience);
        player.setAnimation(REDEEM_ANIMATION);
        String bonusText = getBonusText(amount);
        player.sendMessage("You redeem " + amount + " wilderness agility ticket" + (amount == 1 ? "" : "s") +
                          " for " + String.format("%.1f", totalExperience) + " Agility experience" + bonusText + ".");
    }

    /**
     * Gets experience per ticket based on quantity redeemed (OSRS wiki rates)
     */
    private double getExperiencePerTicket(int amount) {
        if (amount >= 101) {
            return 230.0; // 15% bonus
        } else if (amount >= 51) {
            return 220.0; // 10% bonus
        } else if (amount >= 11) {
            return 210.0; // 5% bonus
        } else {
            return 200.0; // Base rate
        }
    }

    /**
     * Gets bonus text for the confirmation message
     */
    private String getBonusText(int amount) {
        if (amount >= 101) {
            return " (15% bonus)";
        } else if (amount >= 51) {
            return " (10% bonus)";
        } else if (amount >= 11) {
            return " (5% bonus)";
        } else {
            return "";
        }
    }

    @Override
    public Object[] getObjects() {
        return new Object[] { ObjectId.AGILITY_DISPENSER };
    }
}
