package com.zenyte.plugins.object;

import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.zenyte.game.world.object.ObjectAction;
import com.zenyte.game.world.object.WorldObject;
import com.zenyte.game.world.entity.player.cutscene.FadeScreen;

import java.util.Objects;

public class Rowboat implements ObjectAction {

    private static final int NORTH_OF_ISLAND = 30915;
    private static final int OUT_TO_SEA = 30919;
    private static final int CAMP = 30914;

    @Override
    public void handleObjectAction(Player player, WorldObject object, String name, int optionId, String option) {
        int objectId = object.getId();
        if(Objects.equals(option, "Dive")) {
                new FadeScreen(player, () -> {
                    player.setLocation(new Location(3731, 10281, 1));
                    player.sendMessage("You dive underwater and find a cavern.");
                }).fade(3);
                return;
        }

        player.getDialogueManager().start(new Dialogue(player) {
            @Override
            public void buildDialogue() {

                // Rowboat at the North of Fossil Island
                if (objectId == NORTH_OF_ISLAND) {
                    options("Where would you like to row?",
                            new DialogueOption("Row to the camp", () -> {
                                new FadeScreen(player, () -> {
                                    player.setLocation(new Location(3724, 3808, 0));
                                    player.sendMessage("You row your boat to camp.");
                                }).fade(3);
                            }),
                            new DialogueOption("Row out to sea", () -> {
                                new FadeScreen(player, () -> {
                                    player.setLocation(new Location(3768, 3898, 0));
                                    player.sendMessage("You row your boat out to sea.");
                                }).fade(3);
                            })
                    );
                }

                // Rowboat out on the island off Fossil Island
                else if (objectId == OUT_TO_SEA) {
                    options("Where would you like to row?",
                            new DialogueOption("Row to the north of the island", () -> {
                                new FadeScreen(player, () -> {
                                    player.setLocation(new Location(3734, 3893, 0));
                                    player.sendMessage("You row your boat to the north side of Fossil Island.");
                                }).fade(3);
                            }),
                            new DialogueOption("Row to the camp", () -> {
                                new FadeScreen(player, () -> {
                                    player.setLocation(new Location(3724, 3808, 0));
                                    player.sendMessage("You row your boat to camp.");
                                }).fade(3);
                            }),
                            new DialogueOption("Dive into the sea", () -> {
                                new FadeScreen(player, () -> {
                                    player.setLocation(new Location(3731, 10281, 1));
                                    player.sendMessage("You dive underwater and find a cavern.");
                                }).fade(3);
                            })
                    );
                }

                // Rowboat at the Fossil Island Camp
                else if (objectId == CAMP) {
                    options("Where would you like to row?",
                            new DialogueOption("Row to the north of the island", () -> {
                                new FadeScreen(player, () -> {
                                    player.setLocation(new Location(3734, 3893, 0));
                                    player.sendMessage("You row your boat to the north side of Fossil Island.");
                                }).fade(3);
                            }),
                            new DialogueOption("Row out to sea", () -> {
                                new FadeScreen(player, () -> {
                                    player.setLocation(new Location(3768, 3898, 0));
                                    player.sendMessage("You row your boat out to sea.");
                                }).fade(3);
                            })
                    );
                }
            }
        });
    }

    @Override
    public Object[] getObjects() {
        return new Object[] { CAMP, NORTH_OF_ISLAND, OUT_TO_SEA };
    }
}
