package com.zenyte.plugins.item;

import com.zenyte.game.content.DonatorBond;
import com.zenyte.game.item.Item;
import com.zenyte.game.model.item.pluginextensions.ItemPlugin;
import com.zenyte.game.util.Colour;
import com.zenyte.game.world.entity.player.container.RequestResult;

import java.util.ArrayList;
import java.util.List;

public class RedeemDonatorBond extends ItemPlugin {

    @Override
    public void handle() {
        bind("Redeem", (player, item, slotId) -> {
            DonatorBond pin = DonatorBond.forId(item.getId());
            if (pin == null) {
                player.sendMessage("This is not a valid Donator Bond.");
                return;
            }
            if (player.getInventory().deleteItem(new Item(pin.getItemId())).getResult() != RequestResult.SUCCESS) {
                player.sendMessage("Failed to remove the Donator Bond from your inventory.");
                return;
            }
            player.addDonorPoints(pin.getCredits()); // Instead of setDonorPoints, we add the points.
            player.addTotalSpent(pin.getAmount());
            player.sendMessage(Colour.RS_GREEN.wrap("You have successfully redeemed your Donator Bond!"));
            player.sendMessage(Colour.RS_GREEN.wrap(pin.getCredits() + " Donor Points have been added to your account."));
            player.sendMessage(Colour.RS_GREEN.wrap("You now have " + player.getDonorPoints() + " Donor Points."));
            player.sendMessage(Colour.TURQOISE.wrap("Your total donated amount is now: $" + player.getTotalSpent()));
        });
    }

    /**
     * Get all item IDs for valid Donator Bonds.
     *
     * @return An array of item IDs for Donator Bonds.
     */
    @Override
    public int[] getItems() {
        List<Integer> donatorBondIds = new ArrayList<>();
        for (DonatorBond pin : DonatorBond.values()) {
            donatorBondIds.add(pin.getItemId());
        }
        return donatorBondIds.stream().mapToInt(Integer::intValue).toArray();
    }
}