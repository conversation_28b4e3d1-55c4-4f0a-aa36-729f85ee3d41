package com.zenyte.plugins.renewednpc;

import com.near_reality.api.service.vote.VotePlayerAttributesKt;
import com.near_reality.game.item.CustomItemId;
import com.zenyte.game.item.Item;
import com.zenyte.game.item.ItemId;
import com.zenyte.game.util.Colour;
import com.zenyte.game.world.entity.npc.NPC;
import com.zenyte.game.world.entity.npc.NpcId;
import com.zenyte.game.world.entity.npc.actions.NPCPlugin;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.container.impl.Inventory;
import com.zenyte.game.world.entity.player.dialogue.impl.NPCChat;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> | 16-12-2018 | 20:58
 * @see <a href="https://www.rune-server.ee/members/tommeh/">Rune-Server profile</a>}
 */
public class HomeShopNPC extends NPCPlugin {

    private static final Map<Integer, String> SHOPS = new HashMap<>() {

        {
            put(NpcId.ARNAS, "Melee Armoury Shop");
            put(NpcId.TRISTAN, "Melee Store");
            put(NpcId.FAE, "Ranged Store");
            put(NpcId.BABA_YAGA, "Magic Store");
            put(NpcId.JOHN_16007, "Consumables Store");
            put(NpcId.HERQUIN, "Tools Store");
        }
    };

    @Override
    public void handle() {
        bind("Trade", new OptionHandler() {

            @Override
            public void handle(Player player, NPC npc) {
                player.stopAll();
                player.faceEntity(npc);
                if (npc.getId() == NpcId.TRISTAN && player.getNumericAttribute("demon_kills").intValue() == 100) {
                    player.openShop("Melee Store<Alternative>");
                } else {
                    player.openShop(SHOPS.get(npc.getId()));
                }
            }

            @Override
            public void execute(final Player player, final NPC npc) {
                player.stopAll();
//                player.setFaceEntity(npc);
                handle(player, npc);
//                npc.setInteractingWith(player);
            }
        });
        bind("Vote shop", new OptionHandler() {

            @Override
            public void handle(final Player player, final NPC npc) {
                player.openShop("Vote Shop");
                player.sendMessage("You currently have " + Colour.RED.wrap(VotePlayerAttributesKt.getTotalVoteCredits(player)) + " vote points.");
            }

            @Override
            public void execute(final Player player, final NPC npc) {
                player.stopAll();
                player.setFaceEntity(npc);
                handle(player, npc);
            }
        });
        bind("Loyalty shop", new OptionHandler() {

            @Override
            public void handle(final Player player, final NPC npc) {
                player.openShop("Loyalty Shop");
                player.sendMessage("You currently have " + Colour.RED.wrap(player.getLoyaltyManager().getLoyaltyPoints()) + " loyalty points.");
            }

            @Override
            public void execute(final Player player, final NPC npc) {
                player.stopAll();
                player.setFaceEntity(npc);
                handle(player, npc);
            }
        });
        bind("Donator store", new OptionHandler() {

            @Override
            public void handle(final Player player, final NPC npc) {
                player.openShop("Donator store");
                player.sendMessage("You currently have " + Colour.RED.wrap(player.getDonorPoints()) + " donator points.");
            }

            @Override
            public void execute(final Player player, final NPC npc) {
                player.stopAll();
                player.setFaceEntity(npc);
                handle(player, npc);
            }
        });
        bind("Jewellery", (player, npc) -> player.openShop("Jewellery Store"));
    }

    @Override
    public int[] getNPCs() {
        return new int[] { NpcId.ARNAS, NpcId.ROBIN_HOOD, NpcId.FAE, NpcId.JOHN_16007, NpcId.JACKIE, NpcId.FRANK, NpcId.TRISTAN, NpcId.HERQUIN, NpcId.BABA_YAGA };
    }
}
