package com.zenyte.plugins.renewednpc;

import com.zenyte.game.content.skills.magic.spells.teleports.Teleport;
import com.zenyte.game.content.skills.magic.spells.teleports.TeleportType;
import com.zenyte.game.item.Item;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.npc.NPC;
import com.zenyte.game.world.entity.npc.actions.NPCPlugin;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.zenyte.plugins.dialogue.OptionsMenuD;

public class HuntingExpert extends NPCPlugin {

	public enum HuntingTeleport {
		BIRDHOUSES("Birdhouses", new Location(3764, 3869, 1)),
		PURO_PURO("Puro Puro", new Location(2427, 4445, 0)),
		AERIAL_FISHING("Aerial Fishing", new Location(1368, 3630, 0)),
		RELLEKKA_HUNTER("Rellekka Hunter area", new Location(2721, 3781, 0)),
		PISCATORIS("Piscatoris", new Location(2334, 3584, 0)),
		KARAMJA_HUNTER("Karamja Hunter Area", new Location(2777, 3005, 0)),
		GWENITH_HUNTER("Gwenith Hunter area (Prifddinas)", new Location(2264, 3409, 0)),
		SWAMP_SALAMANDERS("Swamp Salamanders", new Location(3538, 3449, 0)),
		ORANGE_SALAMANDERS("Orange Salamanders (Need Waterskins)", new Location(3412, 3088, 0)),
		RED_SALAMANDERS("Red Salamanders", new Location(2464, 3222, 0));

		private final String displayName;
		private final Location location;

		HuntingTeleport(String displayName, Location location) {
			this.displayName = displayName;
			this.location = location;
		}

		public String getDisplayName() {
			return displayName;
		}

		public Location getLocation() {
			return location;
		}

		public static HuntingTeleport[] values = values();
	}

	@Override
	public void handle() {
		bind("Talk-to", new OptionHandler() {
			@Override
			public void handle(final Player player, final NPC npc) {
				player.getDialogueManager().start(new Dialogue(player, npc) {
					@Override
					public void buildDialogue() {
						npc("Hello there, adventurer! I'm the Hunting Expert. How can I help you today?");
						options("What would you like to do?",
							new DialogueOption("See what you have to trade", () -> {
								player.openShop("Hunter Shop");
							}),
							new DialogueOption("See where I can teleport you to", () -> showTeleportMenu(player)));
					}
				});
			}

			@Override
			public void execute(final Player player, final NPC npc) {
				player.stopAll();
				player.setFaceEntity(npc);
				handle(player, npc);
			}
		});

		bind("Teleport", (player, npc) -> showTeleportMenu(player));

		// Add individual teleport binds for direct access
		bind("1", (player, npc) -> teleportToLocation(player, 0));
		bind("2", (player, npc) -> teleportToLocation(player, 1));
		bind("3", (player, npc) -> teleportToLocation(player, 2));
		bind("4", (player, npc) -> teleportToLocation(player, 3));
		bind("5", (player, npc) -> teleportToLocation(player, 4));
		bind("6", (player, npc) -> teleportToLocation(player, 5));
		bind("7", (player, npc) -> teleportToLocation(player, 6));
		bind("8", (player, npc) -> teleportToLocation(player, 7));
		bind("9", (player, npc) -> teleportToLocation(player, 8));
		bind("10", (player, npc) -> teleportToLocation(player, 9));
	}

	private void showTeleportMenu(final Player player) {
		final String[] teleportNames = new String[HuntingTeleport.values.length];
		for (int i = 0; i < HuntingTeleport.values.length; i++) {
			teleportNames[i] = HuntingTeleport.values[i].getDisplayName();
		}

		player.getDialogueManager().start(new OptionsMenuD(player, "Where would you like to teleport to?", teleportNames) {
			@Override
			public void handleClick(int slotId) {
				teleportToLocation(player, slotId);
			}
		});
	}

	private void teleportToLocation(final Player player, final int index) {
		if (index >= 0 && index < HuntingTeleport.values.length) {
			final HuntingTeleport teleport = HuntingTeleport.values[index];
			new HuntingExpertTeleport(teleport.getLocation()).teleport(player);
		}
	}

	@Override
	public int[] getNPCs() {
		return new int[]{1504};
	}

	private static final class HuntingExpertTeleport implements Teleport {
		private final Location destination;

		public HuntingExpertTeleport(Location destination) {
			this.destination = destination;
		}

		@Override
		public TeleportType getType() {
			return TeleportType.REGULAR_TELEPORT;
		}

		@Override
		public Location getDestination() {
			return destination;
		}

		@Override
		public int getLevel() {
			return 0;
		}

		@Override
		public double getExperience() {
			return 0;
		}

		@Override
		public int getRandomizationDistance() {
			return 0;
		}

		@Override
		public Item[] getRunes() {
			return null;
		}

		@Override
		public int getWildernessLevel() {
			return WILDERNESS_LEVEL;
		}

		@Override
		public boolean isCombatRestricted() {
			return UNRESTRICTED;
		}
	}

}
