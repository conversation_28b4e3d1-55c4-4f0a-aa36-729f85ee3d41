package com.zenyte.plugins.renewednpc;

import com.zenyte.game.item.Item;
import com.zenyte.game.world.entity.npc.NpcId;
import com.zenyte.game.world.entity.npc.actions.NPCPlugin;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.zenyte.plugins.dialogue.KylieMinnowD;

/**
 * <AUTHOR> | 26/11/2018 17:33
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>
 */
public class KylieMinnow extends NPCPlugin {

    @Override
    public void handle() {
        bind("Talk-to", (player, npc) -> player.getDialogueManager().start(new KylieMinnowD(player, npc.getId(), false)));
        bind("Trade", (player, npc) -> {
            final int minnowCount = player.getInventory().getAmountOf(21356);
            if (minnowCount < 40) {
                player.getDialogueManager().start(new Dialogue(player, npc) {
                    @Override
                    public void buildDialogue() {
                        npc("I can exchange your minnows for fish! I need at least 40 minnows for a shark, 60 for a sea turtle, or 80 for manta rays.");
                    }
                });
                return;
            }

            player.getDialogueManager().start(new Dialogue(player, npc) {
                @Override
                public void buildDialogue() {
                    npc("I can exchange your minnows for fish! What would you like?");
                    options("What do you want to trade for?","Shark (40 minnows each)", "Sea Turtle (60 minnows each)", "Manta Ray (80 minnows each)", "Nothing")
                        .onOptionOne(() -> {
                            player.getDialogueManager().finish();
                            promptForFish(384, 40, "sharks");
                        })
                        .onOptionTwo(() -> {
                            player.getDialogueManager().finish();
                            promptForFish(396, 60, "sea turtles");
                        })
                        .onOptionThree(() -> {
                            player.getDialogueManager().finish();
                            promptForFish(390, 80, "manta rays");
                        })
                        .onOptionFour(() -> {
                            player.getDialogueManager().finish();
                        });
                }

                private void promptForFish(final int fishId, final int minnowCost, final String fishNamePlural) {
                    final int currentMinnows = player.getInventory().getAmountOf(21356);
                    final int maxAmount = currentMinnows / minnowCost;

                    if (maxAmount == 0) {
                        player.sendMessage("You need at least " + minnowCost + " minnows for this trade.");
                        return;
                    }

                    player.sendInputInt("How many " + fishNamePlural + " would you like? (0-" + maxAmount + ")", value -> {
                        final int amount = Math.min(value, maxAmount);
                        if (amount > 0) {
                            final int totalMinnowsCost = amount * minnowCost;
                            player.getInventory().ifDeleteItem(new Item(21356, totalMinnowsCost), () -> {
                                player.getInventory().addOrDrop(new Item(fishId, amount));
                                player.sendMessage("You bought " + amount + " " + fishNamePlural + " for " + totalMinnowsCost + " minnows.");
                            });
                        }
                    });
                }
            });
        });
    }

    @Override
    public int[] getNPCs() {
        return new int[] { NpcId.KYLIE_MINNOW, NpcId.KYLIE_MINNOW_7728, 7735 };
    }
}
