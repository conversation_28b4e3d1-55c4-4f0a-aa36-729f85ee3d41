package com.zenyte.plugins.renewednpc;

import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.npc.NpcId;
import com.zenyte.game.world.entity.npc.actions.NPCPlugin;
import com.zenyte.game.world.entity.player.cutscene.FadeScreen;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;

/**
 * <AUTHOR> (Discord: imslickk) - 7/25/2025
 */
public class <PERSON><PERSON><PERSON> extends NPCPlugin {

    @Override
    public void handle() {
        bind("Talk-to", (player, npc) -> {
            player.getDialogueManager().start(new Dialogue(player, npc) {
                @Override
                public void buildDialogue() {
                    npc("How can I help you?");
                    options(TITLE,
                        new DialogueOption("Get me out of here!", () -> {
                            player.sendMessage("<PERSON><PERSON><PERSON> helps you escape the dungeon...");
                            new FadeScreen(player, () -> player.setLocation(new Location(2467, 3246, 0))).fade(3);
                        }),
                        new DialogueOption("Nevermind")
                    );
                }
            });
        });
        bind("Escape", (player, npc) -> {
            player.sendMessage("<PERSON><PERSON><PERSON> helps you escape the dungeon...");
            new FadeScreen(player, () -> player.setLocation(new Location(2467, 3246, 0))).fade(3);
        });
    }

    @Override
    public int[] getNPCs() {
        return new int[] {NpcId.JARDRIC_8155};
    }
}
