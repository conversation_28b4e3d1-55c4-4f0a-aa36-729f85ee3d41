package com.near_reality.game.content.araxxor.cave_hunt

import com.zenyte.game.world.entity.Location
import com.zenyte.game.world.entity.player.Player
import com.zenyte.game.world.entity.player.dialogue.dialogue
import com.zenyte.game.world.`object`.ObjectAction
import com.zenyte.game.world.`object`.ObjectId.*
import com.zenyte.game.world.`object`.WorldObject

/**
 * <AUTHOR> | Glabay-Studios
 * @project Exiles-server
 * @social Discord: Glabay
 * @since 2024-11-14
 */
class WebTunnelSecret: ObjectAction {
    override fun handleObjectAction(player: Player?, tunnel: WorldObject?, name: String?, optionId: Int, option: String?) {
        player ?: return
        tunnel ?: return

        // Handle specific object movements based on object ID and location
        when (tunnel.id) {
            54271 -> {
                when {
                    tunnel.x == 3678 && tunnel.y == 9819 -> {
                        player.setLocation(Location(3684, 9820, 0))
                        return
                    }
                    tunnel.x == 3677 && tunnel.y == 9843 -> {
                        player.setLocation(Location(3679, 9849, 0))
                        return
                    }
                    tunnel.x == 3690 && tunnel.y == 9836 -> {
                        player.setLocation(Location(3696, 9837, 0))
                        return
                    }
                }
            }
            54272 -> {
                when {
                    tunnel.x == 3681 && tunnel.y == 9819 -> {
                        player.setLocation(Location(3677, 9820, 0))
                        return
                    }
                    tunnel.x == 3693 && tunnel.y == 9836 -> {
                        player.setLocation(Location(3689, 9837, 0))
                        return
                    }
                }
            }
            54273 -> {
                when {
                    tunnel.x == 3677 && tunnel.y == 9846 -> {
                        player.setLocation(Location(3679, 9842, 0))
                        return
                    }
                }
            }
        }

        // if we're not already in the cave, start it
        if (tunnel.id == WEB_TUNNEL_54271 && player.mapInstance !is AraxyteCaveHunt) {
            AraxyteCaveHunt(player).constructRegion()
            return
        }
        if (player.mapInstance == null && player.location.regionId == 15002) {
            player.setLocation(Location(3682, 9802, 0))
            return
        }
        val instance = player.mapInstance as AraxyteCaveHunt
        if (tunnel.id == instance.correctTunnel && instance.roomCompleted) {
            instance.progressCaveHunt()
            player.setLocation(instance.entryTile)
        }
        if (tunnel.id == instance.wrongTunnel) {
            AraxyteCaveHunt(player).constructRegion()
            player.dialogue { plain("You get lost and find yourself back at the beginning...") }
        }
    }

    override fun getObjects(): Array<Any> =
        arrayOf(
            WEB_TUNNEL_54155,
            WEB_TUNNEL_54159,
            WEB_TUNNEL_54271,
            54272,
            54273

        )
}