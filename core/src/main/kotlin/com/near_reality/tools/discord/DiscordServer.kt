package com.near_reality.tools.discord
 import dev.kord.common.entity.Snowflake

/**
 * Represents a discord server.
 *
 * <AUTHOR>
 */
 sealed class DiscordServer(id: Long) {

    /**
     * The id of this server, or guild in discord terminology.
     */
     val guildId = Snowflake(id)

    /**
     * The [id][Snowflake] of the general chat channel.
     */
     abstract val generalChannelId: Snowflake

    /**
     * The [id][Snowflake] of the broadcast channel.
     */
     abstract val broadcastChannelId: Snowflake

     /**
      * A [DiscordServer] in which logs are send and broadcasts for the beta world.
      */
     data object Staff : DiscordServer(962064378230882344) {

         override val generalChannelId = Snowflake("962064378230882346")
         override val broadcastChannelId = Snowflake("962064378230882346")

         val economySearchChannelId = Snowflake(1382379445792280656)
         val automatedDetectionChannelId = Snowflake(1382379498900557865)
         val modelChannelId = Snowflake(1382379546380079124)
         val developerRoleId = Snowflake(962068219907149864)
         val managerRoleId = Snowflake(1379859286863384698)
     }

     /**
      * The main community [DiscordServer].
      */
     object Main : DiscordServer(962064378230882344) {

         override val generalChannelId = Snowflake("962064378230882346")
         override val broadcastChannelId = Snowflake("962064378230882346")
     }
 }
