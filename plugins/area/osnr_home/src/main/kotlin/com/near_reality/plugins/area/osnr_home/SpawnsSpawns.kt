package com.near_reality.plugins.area.osnr_home

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.ContentConstants
import com.zenyte.game.content.skills.slayer.SlayerMaster
import com.zenyte.plugins.renewednpc.ExilesGuide
import com.zenyte.game.util.Direction.*
import com.zenyte.game.world.entity.npc.NpcId.*

class SpawnsSpawns : NPCSpawnsScript() {
	init {
		if (ContentConstants.CHRISTMAS) {
			SANTA_CLAUS(3096, 3485, 0, NORTH, 0)
		}

		/*
		 * Home Area
		 */
		EXILES_GUIDE(3087, 3492, 0, SOUTH, 1) //Exiles Guide
		TOURNAMENT_GUARD_16012(3079, 3480, 0, WEST, 0) //Tournament Guard
		SHOP_KEEPER_2821(3081, 3511, 0, SOUTH, 1) //General Store
		HATIUS_COSAINTUS(3095, 3499, 0, SOUTH, 0) //Donator Store
		TRISTAN(3078, 3513, 0, SOUTH, 0) //Melee Store
		FAE(3079, 3513, 0, SOUTH, 0) //Range Store
		BABA_YAGA(3080, 3513, 0, SOUTH, 0) //Magic Store
		JOHN_16007(3081, 3513, 0, SOUTH, 0) //Consumables Store
		HERQUIN(3082, 3513, 0, SOUTH, 0) //Tools Store
		CHALLENGE_HEADMASTER(3093, 3506, 0, SOUTH, 0) //Daily Challenges
		ELLIS(3083, 3513, 0, SOUTH, 0) //Tanner
		ZAHUR(3084, 3512, 0, WEST, 0) //Decanter
		16034(3084, 3511, 0, WEST, 0) //Herblore Store
		NULODION(3092, 3467, 0, SOUTH, 1) //Dwarf Cannon
		IRON_MAN_TUTOR(3078, 3505, 0, SOUTH, 1) //Ironman Tutor
		GROUP_IRON_TUTOR(3078, 3503, 0, SOUTH, 1) //GIM Tutor
		JOSSIK(3097, 3507, 0, NORTH, 0) //God Books
		PROBITA(3098, 3507, 0, NORTH, 0) //Pet Insurance
		FRANK(3096, 3499, 0, SOUTH, 0) //Vote/Loyalty Shop
		MAKEOVER_MAGE(3094, 3499, 0, SOUTH, 0) //Makeover Mage
		16065(3069, 3517, 0, WEST, 1) //Hans - Comp Cape
		WATSON(3088, 3469, 0, NORTH, 2) //Clue Trader
		COMBAT_DUMMY_16019(3090, 3488, 0, WEST, 0) //Combat Dummy
		UNDEAD_COMBAT_DUMMY_16020(3090, 3489, 0, WEST, 0) //Undead Combat Dummy
		MAN_3108(3094, 3481, 0, NORTH, 1) //Man Pickpocket
		MASTER_FARMER(3093, 3478, 0, NORTH, 1) //Farmer Pickpocket
		KNIGHT_OF_ARDOUGNE(3086, 3477, 0, WEST, 0) //Knight Pickpocket
		VAIRE(3085, 3468, 0, WEST, 0) //Elf Pickpocket
		CRIMSONETTE_VAN_MARR(3093, 3466, 0, WEST, 0) //Vampyre Pickpocket
		BANKER_2117(3096, 3490, 0, WEST, 0)
		BANKER_2117(3096, 3488, 0, WEST, 0)
		BANKER_2118(3096, 3492, 0, NORTH, 0)
		BANKER_2118(3098, 3492, 0, NORTH, 0)
		BANKER_2117(3091, 3495, 0, EAST, 0)
		BANKER_2118(3090, 3495, 0, WEST, 0)
		GRAND_EXCHANGE_CLERK(3091, 3496, 0, EAST, 0)
		GRAND_EXCHANGE_CLERK_2150(3091, 3494, 0, EAST, 0)
		GRAND_EXCHANGE_CLERK_2149(3090, 3496, 0, WEST, 0)
		GRAND_EXCHANGE_CLERK_2149(3090, 3494, 0, WEST, 0)

		TURAEL(3111, 3511, 0, WEST) //Starter Slayer
		MAZCHNA(3111, 3512, 0, WEST) //20 Combat Slayer
		VANNAKA(3111, 3513, 0, WEST) //40 Combat Slayer
		CHAELDAR(3111, 3514, 0, WEST) //70 Combat Slayer
		SlayerMaster.NIEVE.npcId(3111, 3515, 0, WEST) //85 Combat Slayer
		DURADEL(3111, 3516, 0, WEST) //100 Combat Slayer
		KONAR_QUO_MATEN(3107, 3514, 0, EAST) //Location Slayer
		KRYSTILIA(3110, 3517, 0, SOUTH) //Wildy Slayer
		16064(3108, 3517, 0, SOUTH) //Sumona Slayer Master

		/*
		 * Other Spawns
		 */
		HARI(3131, 3507, 0, SOUTH, 2) //Edgeville Canoe
		LESSER_FANATIC(3116, 3516, 0, SOUTH, 1) //Achievement Diary
		LUNA(3115, 3518, 0, SOUTH, 3) //Rabbit
		IMP_5007(3073, 3493, 0, SOUTH, 50)
		IMP_5007(3078, 3461, 0, SOUTH, 50)
		IMP_5007(3104, 3486, 0, SOUTH, 50)
		PEKSA(3078, 3420, 0, SOUTH, 2) //Helmet Shop
		RICHARD_2200(3108, 3531, 0, NORTH, 0) //Wildy Cape Shop
		MADAM_SIKARO(3152, 3642, 0, SOUTH, 0) //Voidwaker Assembly

		/*
		 * Potential Important Disabled Spawns
		 */
//		OZIACH(3103, 3513, 0, EAST, 0) //Pvp Shop v2
//		SKULLY(3102, 3515, 0, NORTH, 0) //Wildy loot Chest?
//		EMBLEM_TRADER(3093, 3514, 0, NORTH, 0) //Pvp Shop
//		14900(3104, 3488, 0, WEST, 0) //Perk Master
//		KING_THOROS(3100, 3500, 0, EAST, 0) //Afk Master
//		LISA(3327, 4753,0, NORTH, 0) //Armor Sets
//		DUSURI(3102, 3492, 0, SOUTH) //Shooting Star
//		MAC(3077, 3495, 0, NORTH, 0)
//		WISE_OLD_MAN(3112, 3508, 0, NORTH, 0)
//		7457(3078, 3512, 0, SOUTH, 0) //Perdu (Doesn't Work?)
//		GHOMMAL(3103, 3492, 0, SOUTH, 0)
//		996(2717, 5310, 0, SOUTH, 3) //Bone Weapons

		/*
		 * Random Spawns?
		 */
//		BROTHER_ALTHRIC(3121, 3482, 0, SOUTH, 2)
//		MONK_2579(3120, 3476, 0, SOUTH, 3)
//		MONK_2579(3120, 3470, 0, SOUTH, 3)
//		MONK_2579(3114, 3480, 0, SOUTH, 3)
//		MONK_2579(3114, 3468, 0, SOUTH, 3)
//		ABBOT_LANGLEY(3127, 3468, 0, SOUTH, 3)
//		BROTHER_JERED(3114, 3475, 1, SOUTH, 2)
//		MONK_2579(3115, 3467, 1, SOUTH, 3)
//		MONK_2579(3127, 3467, 1, SOUTH, 3)
//		KHARID_SCORPION_5230(3128, 3476, 1, SOUTH, 2)
//		DUCK(3103, 3459, 0, SOUTH, 2)
//		COW_2791(3106, 3473, 0, SOUTH, 4)
//		COW_2793(3100, 3473, 0, SOUTH, 4)
//		COW_2793(3097, 3469, 0, SOUTH, 4)
//		COW_CALF_2794(3102, 3470, 0, SOUTH, 3)
//		COW_2793(3106, 3469, 0, SOUTH, 4)
//		CHICKEN_1174(3110, 3465, 0, SOUTH, 4)
//		CHICKEN_1174(3110, 3462, 0, SOUTH, 4)
//		CHICKEN_1174(3110, 3459, 0, SOUTH, 4)
//		CHICKEN(3107, 3464, 0, SOUTH, 2)
//		CHICKEN(3109, 3460, 0, SOUTH, 2)
//		FARMER(3105, 3463, 0, SOUTH, 2)
//		SQUIRREL_1417(3041, 3469, 0, SOUTH, 9)
//		SQUIRREL_1418(3054, 3482, 0, SOUTH, 4)
//		SQUIRREL(3049, 3497, 0, SOUTH, 6)
//		RACCOON_1420(3028, 3511, 0, SOUTH, 12)
//		RACCOON_1421(3010, 3509, 0, SOUTH, 11)
//		SQUIRREL_1417(2993, 3498, 0, SOUTH, 9)
//		RACCOON(2994, 3483, 0, SOUTH, 9)
//		SQUIRREL_1418(3001, 3467, 0, SOUTH, 10)
//		SQUIRREL(3050, 3494, 0, SOUTH, 3)
//		SQUIRREL_1417(3112, 3499, 0, SOUTH, 3)
//		RACCOON_1420(3110, 3451, 0, SOUTH, 3)
//		SQUIRREL(3019, 3455, 0, SOUTH, 8)
//		RACCOON(2997, 3453, 0, SOUTH, 9)
//		RACCOON_1421(3060, 3505, 0, SOUTH, 3)
//		DUCK(3037, 3503, 0, SOUTH, 1)
//		DUCK(3042, 3492, 0, SOUTH, 2)
//		DUCK(3040, 3479, 0, SOUTH, 2)
//		DUCK(3027, 3469, 0, SOUTH, 2)
//		DUCK(3003, 3479, 0, SOUTH, 2)
//		DUCK(3006, 3499, 0, SOUTH, 2)
//		LESSER_DEMON(3062, 3474, 1, NORTH, 2)
//		GRACE(3075, 3438,0, NORTH, 2)
//		TURAEL(3084, 3470, 0, SOUTH, 1)
//		GRIZZLY_BEAR(3104, 3438, 0, SOUTH, 3)
//		GRIZZLY_BEAR(3102, 3446, 0, SOUTH, 3)
//		UNICORN(3093, 3448, 0, SOUTH, 2)
//		UNICORN(3099, 3450, 0, SOUTH, 2)
//		ZOO_KEEPER(3096, 3436, 0, SOUTH, 1)
//		COMBAT_DUMMY_16019(3085, 3517, 0, NORTH)
//		FISHING_SPOT_1517(3120, 3491, 0, NORTH)
//		ROD_FISHING_SPOT_1512(3124, 3491, 0, NORTH)
		ICEFIEND(3007, 3487, 0, NORTH)
		ICEFIEND(3009, 3492, 0, NORTH)
		ICEFIEND(3007, 3484, 0, NORTH)
		ICEFIEND(3010, 3480, 0, NORTH)
		ICEFIEND(3011, 3485, 0, NORTH)
		ICEFIEND(3011, 3491, 0, NORTH)
//		HUNDING(3079, 3421, 0, NORTH, 1)
//		ORACLE(3032, 3492, 0, NORTH, 1)
//		16012(3076, 3494, 0, EAST, 0)
//		HAGAVIK(1918, 4362, 0, NORTH, 0)

//		TURAEL(3079, 3484, 0, NORTH)
//		MAZCHNA(3077, 3484, 0, NORTH)
//		VANNAKA(3075, 3484, 0, NORTH)
//		CHAELDAR(3075, 3493, 0, SOUTH)
//		KONAR_QUO_MATEN(3077, 3493, 0, SOUTH)
//		SlayerMaster.NIEVE.npcId(3079, 3493, 0, SOUTH)
//		DURADEL(3080, 3491, 0, SOUTH)

	}
}