package com.zenyte.plugins.object;

import com.near_reality.api.service.user.UserPlayerHandler;
import com.near_reality.game.world.entity.player.PlayerAttributesKt;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.player.LogLevel;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.cutscene.FadeScreen;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;
import com.zenyte.game.world.entity.player.privilege.GameMode;
import com.zenyte.game.world.object.ObjectAction;
import com.zenyte.game.world.object.WorldObject;
import com.zenyte.plugins.renewednpc.ExilesGuide;
import kotlin.Unit;

/**
 * Represents an {@link ObjectAction} that handles the portal at the tutorial island.
 */
@SuppressWarnings("unused")
public class TutorialIslandPortal implements ObjectAction {

    @Override
    public void handleObjectAction(Player player, WorldObject object, String name, int optionId, String option) {
        if (!player.getTemporaryAttributes().contains<PERSON>ey("ironman_setup")) {
            player.getDialogueManager().start(new Dialogue(player, ExilesGuide.NPC_ID) {
                @Override
                public void buildDialogue() {
                    npc("You should talk to me before leaving.");
                }
            });
            return;
        }
    }


    @Override
    public Object[] getObjects() {
        return new Object[]{55070};
    }
}
