package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7516Spawns : NPCSpawnsScript() {
    init {
        SQUIRREL(1900, 5947, 0, SOUTH, 8)
        SQUIRREL_1417(1909, 5926, 0, SOUTH, 9)
        BUTTERFLY_238(1912, 5945, 0, SOUTH, 0)
    }
}