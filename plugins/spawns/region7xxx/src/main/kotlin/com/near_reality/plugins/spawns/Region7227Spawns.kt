package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7227Spawns : NPCSpawnsScript() {
    init {
        7926(1796, 3782, 0, SOUT<PERSON>, 2)
        BANKER_6969(1796, 3792, 0, SOUTH, 0)
        BANKER_6970(1800, 3792, 0, SOUTH, 0)
        BANKER_6969(1804, 3792, 0, SOUTH, 0)
        BANKER_6970(1808, 3792, 0, SOUTH, 0)
        TYNAN(1840, 3786, 0, SOUTH, 2)
        NICHOLAS(1842, 3803, 0, SOUTH, 0)
        BILLY(1845, 3826, 0, SOUTH, 3)
    }
}