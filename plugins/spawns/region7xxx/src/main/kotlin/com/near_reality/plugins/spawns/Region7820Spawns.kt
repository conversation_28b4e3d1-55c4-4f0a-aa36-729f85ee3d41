package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7820Spawns : NPCSpawnsScript() {
    init {
        BLUE_DRAGON_266(1925, 8968, 1, SOUT<PERSON>, 4)
        BLUE_DRAGON(1928, 8973, 1, SOUT<PERSON>, 3)
        BLUE_DRAGON_268(1930, 8986, 1, SOUTH, 4)
        BLUE_DRAGON_267(1931, 8967, 1, <PERSON>OUTH, 3)
        BLUE_DRAGON_269(1931, 8979, 1, <PERSON><PERSON><PERSON><PERSON>, 3)
        GREEN_DRAGON_262(1939, 8991, 1, <PERSON><PERSON><PERSON><PERSON>, 4)
        BLACK_DRAGON(1942, 8967, 1, <PERSON><PERSON><PERSON><PERSON>, 6)
        GREEN_DRAGON_261(1944, 8996, 1, <PERSON><PERSON><PERSON><PERSON>, 4)
        BLACK_DRAGON_256(1946, 8971, 1, <PERSON><PERSON><PERSON><PERSON>, 3)
        PONTS_THE_BRIDGEMASTER(1969, 8987, 1, <PERSON><PERSON><PERSON><PERSON>, 2)
        OGRESS_WARRIOR(1970, 9022, 1, SOUTH, 5)
        OGRESS_SHAMAN(1974, 9017, 1, SOUTH, 6)
        OGRESS_WARRIOR_7990(1976, 9012, 1, SOUTH, 6)
        OGRESS_SHAMAN(1977, 9023, 1, SOUTH, 6)
        GIANT_SPIDER_3017(1978, 8979, 1, SOUTH, 10)
        GIANT_SPIDER_3017(1979, 8991, 1, SOUTH, 10)
        GIANT_SPIDER_3017(1983, 9004, 1, SOUTH, 10)
    }
}