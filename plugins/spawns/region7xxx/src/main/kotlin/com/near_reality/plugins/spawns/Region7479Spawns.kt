package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7479Spawns : NPCSpawnsScript() {
    init {
        SANDY_ROCKS(1857, 3542, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1858, 3547, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1859, 3559, 0, SOUTH, 0)
        SANDY_ROCKS(1860, 3552, 0, SOUTH, 0)
        SANDY_ROCKS(1861, 3537, 0, SOUTH, 0)
        SANDY_ROCKS(1861, 3560, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1863, 3536, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1863, 3542, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        SANDY_ROCKS_7207(1863, 3561, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        SANDY_ROCKS(1864, 3544, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        SANDY_ROCKS_7207(1868, 3545, 0, S<PERSON><PERSON><PERSON>, 0)
        SANDY_ROCKS_7207(1868, 3555, 0, SOUTH, 0)
        SANDY_ROCKS(1868, 3561, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1869, 3557, 0, SOUTH, 0)
        SANDY_ROCKS(1870, 3553, 0, SOUTH, 0)
        SANDY_ROCKS(1870, 3566, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1871, 3565, 0, SOUTH, 0)
        SANDY_ROCKS(1874, 3546, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1874, 3558, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1876, 3554, 0, SOUTH, 0)
        SANDY_ROCKS(1876, 3556, 0, SOUTH, 0)
        SANDY_ROCKS(1879, 3551, 0, SOUTH, 0)
    }
}