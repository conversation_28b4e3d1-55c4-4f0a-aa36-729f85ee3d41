package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7821Spawns : NPCSpawnsScript() {
    init {
        OGRESS_WARRIOR(1957, 9037, 1, SOUTH, 5)
        OGRESS_SHAMAN(1962, 9044, 1, SOUTH, 6)
        OGRESS_WARRIOR_7990(1963, 9077, 1, SOUTH, 6)
        OGRESS_SHAMAN(1964, 9034, 1, SOUTH, 6)
        OGRESS_SHAMAN(1965, 9072, 1, <PERSON>OUTH, 6)
        OGRESS_WARRIOR_7990(1968, 9040, 1, SOUTH, 6)
        OGRESS_SHAMAN(1968, 9080, 1, SOUT<PERSON>, 6)
        OGRESS_WARRIOR_7990(1969, 9066, 1, <PERSON><PERSON><PERSON><PERSON>, 6)
        OGRESS_WARRIOR(1971, 9045, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        OGRESS_WARRIOR(1971, 9073, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        OGRESS_SHAMAN(1972, 9077, 1, SOUTH, 6)
        OGRESS_SHAMAN(1975, 9036, 1, SOUTH, 6)
    }
}