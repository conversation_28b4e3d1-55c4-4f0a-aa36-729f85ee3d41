package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7517Spawns : NPCSpawnsScript() {
    init {
        BIRD_10541(1900, 5965, 0, SOUTH, 5)
        SQUIRREL(1909, 5993, 0, SOUTH, 8)
        BUTTERFLY_238(1910, 6014, 0, SOUTH, 0)
        10542(1913, 5967, 0, SOUTH, 5)
        BIRD_10541(1914, 6004, 0, SOUTH, 5)
        BUTTERFLY_238(1916, 5991, 0, SOUTH, 0)
    }
}