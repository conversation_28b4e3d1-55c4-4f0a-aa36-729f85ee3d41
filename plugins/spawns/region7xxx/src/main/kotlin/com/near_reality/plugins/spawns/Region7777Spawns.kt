package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7777Spawns : NPCSpawnsScript() {
    init {
        GREEN_FIREFLIES(1928, 6214, 0, SOUTH, 5)
        RED_FIREFLIES(1928, 6223, 0, SOUTH, 5)
        GREEN_FIREFLIES(1931, 6227, 0, SOUTH, 5)
        RED_FIREFLIES(1934, 6224, 0, SOUTH, 5)
        RED_FIREFLIES(1935, 6227, 0, SOUTH, 5)
        GREEN_FIREFLIES(1937, 6225, 0, SOUTH, 5)
        RED_FIREFLIES(1938, 6223, 0, SOUTH, 5)
        GREEN_FIREFLIES(1939, 6217, 0, SO<PERSON><PERSON>, 5)
        RED_FIREFLIES(1939, 6220, 0, <PERSON>OUT<PERSON>, 5)
        SCRUBFOOT(1940, 6214, 0, <PERSON>O<PERSON><PERSON>, 5)
        GREEN_FIREFLIES(1941, 6221, 0, SOUTH, 5)
        RED_FIREFLIES(1944, 6217, 0, SOUTH, 5)
        GREEN_FIREFLIES(1945, 6220, 0, SOUTH, 5)
        GOBLIN_10566(1965, 6221, 3, SOUTH, 5)
        GOBLIN_10567(1970, 6217, 3, SOUTH, 5)
        GOBLIN_10566(1970, 6224, 3, SOUTH, 5)
    }
}