package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7242Spawns : NPCSpawnsScript() {
    init {
        GULL_284(1821, 4751, 0, SOUTH, 5)
        GULL_285(1825, 4767, 0, SOUTH, 5)
        GULL_285(1826, 4754, 0, SOUTH, 5)
        GULL_284(1830, 4768, 0, SOUTH, 5)
        GULL_284(1833, 4755, 0, SOUTH, 5)
        7820(1820, 4762, 1, <PERSON><PERSON>UTH, 0)
    }
}