package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7748Spawns : NPCSpawnsScript() {
    init {
        ROCK_5945(1944, 4356, 0, SOUTH, 0)
        ROCK_5945(1957, 4368, 0, SOUTH, 0)
        ROCK_5945(1939, 4376, 1, SOUTH, 0)
        ROCK_5945(1948, 4376, 1, SOUTH, 0)
        ROCK_5945(1929, 4387, 2, SOUTH, 0)
        ROCK_5945(1930, 4383, 2, <PERSON>OUTH, 0)
        ROCK_5945(1931, 4392, 2, SOUTH, 0)
        ROCK_5945(1939, 4391, 2, SOUTH, 0)
        ROCK_5945(1944, 4394, 2, <PERSON><PERSON><PERSON><PERSON>, 0)
        ROCK_5945(1951, 4391, 2, <PERSON><PERSON><PERSON><PERSON>, 0)
        ROCK_5945(1957, 4393, 2, <PERSON><PERSON><PERSON><PERSON>, 0)
        ROCK_5945(1968, 4397, 3, <PERSON><PERSON><PERSON><PERSON>, 0)
        R<PERSON><PERSON>_5945(1972, 4400, 3, SOUTH, 0)
        ROCK_5945(1972, 4404, 3, SOUTH, 0)
    }
}