package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7564Spawns : NPCSpawnsScript() {
    init {
        BABY_BLACK_DRAGON_7955(1888, 9003, 1, SOUTH, 2)
        BABY_RED_DRAGON_246(1889, 8995, 1, SOUTH, 3)
        BABY_RED_DRAGON_244(1891, 8998, 1, SOUTH, 4)
        BABY_BLUE_DRAGON(1897, 9001, 1, SOUTH, 4)
        BABY_BLUE_DRAGON_243(1897, 9005, 1, SOUTH, 3)
        BABY_BLUE_DRAGON_242(1900, 8998, 1, S<PERSON><PERSON><PERSON>, 4)
        BABY_GREEN_DRAGON(1900, 9010, 1, <PERSON><PERSON><PERSON><PERSON>, 3)
        RED_DRAGON_248(1910, 8995, 1, <PERSON><PERSON><PERSON><PERSON>, 4)
        RED_DRAGON_250(1911, 9002, 1, <PERSON><PERSON><PERSON><PERSON>, 7)
        RED_DRAGON_251(1918, 9005, 1, <PERSON><PERSON><PERSON><PERSON>, 6)
    }
}