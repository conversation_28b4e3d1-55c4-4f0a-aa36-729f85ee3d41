package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7507Spawns : NPCSpawnsScript() {
    init {
        MOUNTAIN_DWARF(1862, 5345, 0, SOUTH, 5)
        PIRATE_PETE_3389(1862, 5347, 0, SOUTH, 5)
        GENERAL_BENTNOZE_3392(1862, 5349, 0, SOUTH, 5)
        GENERAL_WARTFACE_3391(1862, 5351, 0, SOUTH, 5)
        CULINAROMANCER_4849(1863, 5317, 0, SOUTH, 5)
        SKRACH_UGLOGWEE(1863, 5352, 0, SOUTH, 5)
        AWOWOGEI(1865, 5343, 0, SOUTH, 5)
        SIR_AMIK_VARZE_3395(1865, 5345, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        EVIL_DAVE_3394(1865, 5347, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        LUMBRIDGE_GUIDE_3393(1865, 5349, 0, SO<PERSON>H, 5)
        6256(1866, 5327, 0, SOUTH, 5)
    }
}