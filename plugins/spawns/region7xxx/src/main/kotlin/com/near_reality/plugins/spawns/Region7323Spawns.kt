package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7323Spawns : NPCSpawnsScript() {
    init {
        8720(1796, 9949, 0, SOUTH, 5)
        8718(1796, 9953, 0, SOUTH, 5)
        8719(1797, 9951, 0, SOUTH, 5)
        UNDEAD_DRUID(1799, 9937, 0, SOUTH, 2)
        UNDEAD_DRUID(1799, 9940, 0, SOUTH, 2)
        UNDEAD_DRUID(1802, 9937, 0, SOUTH, 2)
        UNDEAD_DRUID(1802, 9940, 0, SOUTH, 2)
        EODAN(1808, 9952, 0, SOUTH, 5)
        UNDEAD_DRUID(1808, 9961, 0, <PERSON>OUT<PERSON>, 2)
        UNDEAD_DRUID(1808, 9964, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        UNDEAD_DRUID(1808, 9967, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        UNDEAD_DRUID(1808, 9970, 0, SOUTH, 2)
        BABY_RED_DRAGON_244(1811, 9937, 0, SOUTH, 4)
        RED_DRAGON(1812, 9933, 0, SOUTH, 4)
        UNDEAD_DRUID(1812, 9961, 0, SOUTH, 2)
        UNDEAD_DRUID(1812, 9964, 0, SOUTH, 2)
        UNDEAD_DRUID(1812, 9967, 0, SOUTH, 2)
        UNDEAD_DRUID(1812, 9970, 0, SOUTH, 2)
        RED_DRAGON_248(1815, 9937, 0, SOUTH, 4)
        RED_DRAGON_249(1816, 9927, 0, SOUTH, 8)
        BABY_RED_DRAGON_244(1818, 9931, 0, SOUTH, 4)
        RED_DRAGON_250(1821, 9929, 0, SOUTH, 7)
        RED_DRAGON_249(1822, 9937, 0, SOUTH, 8)
        RED_DRAGON(1823, 9933, 0, SOUTH, 4)
        BABY_RED_DRAGON_244(1827, 9930, 0, SOUTH, 4)
        BABY_RED_DRAGON_244(1827, 9937, 0, SOUTH, 4)
        BABY_RED_DRAGON_244(1828, 9928, 0, SOUTH, 4)
        TEMPLE_SPIDER(1829, 9960, 0, SOUTH, 5)
        TEMPLE_SPIDER(1830, 9958, 0, SOUTH, 5)
        TEMPLE_SPIDER(1832, 9960, 0, SOUTH, 5)
        TEMPLE_SPIDER(1833, 9957, 0, SOUTH, 5)
        TEMPLE_SPIDER(1834, 9953, 0, SOUTH, 5)
        TEMPLE_SPIDER(1835, 9960, 0, SOUTH, 5)
        TEMPLE_SPIDER(1836, 9950, 0, SOUTH, 5)
        TEMPLE_SPIDER(1837, 9952, 0, SOUTH, 5)
        TEMPLE_SPIDER(1837, 9955, 0, SOUTH, 5)
        TEMPLE_SPIDER(1838, 9958, 0, SOUTH, 5)
        TEMPLE_SPIDER(1838, 9965, 0, SOUTH, 5)
        TEMPLE_SPIDER(1839, 9960, 0, SOUTH, 5)
        8717(1840, 9926, 0, SOUTH, 5)
        TEMPLE_SPIDER(1840, 9951, 0, SOUTH, 5)
        TEMPLE_SPIDER(1840, 9956, 0, SOUTH, 5)
        TEMPLE_SPIDER(1841, 9963, 0, SOUTH, 5)
        TEMPLE_SPIDER(1842, 9953, 0, SOUTH, 5)
        TEMPLE_SPIDER(1842, 9959, 0, SOUTH, 5)
        TEMPLE_SPIDER(1843, 9957, 0, SOUTH, 5)
        TEMPLE_SPIDER(1844, 9961, 0, SOUTH, 5)
        TEMPLE_SPIDER(1845, 9954, 0, SOUTH, 5)
        TEMPLE_SPIDER(1845, 9958, 0, SOUTH, 5)
        UNDEAD_DRUID(1847, 9939, 0, SOUTH, 2)
        UNDEAD_DRUID(1848, 9937, 0, SOUTH, 2)
        UNDEAD_DRUID(1848, 9941, 0, SOUTH, 2)
        UNDEAD_DRUID(1851, 9937, 0, SOUTH, 2)
        UNDEAD_DRUID(1851, 9941, 0, SOUTH, 2)
        UNDEAD_DRUID(1852, 9939, 0, SOUTH, 2)
    }
}