package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7772Spawns : NPCSpawnsScript() {
    init {
        BUTTERFLY_238(1921, 5931, 0, SOUTH, 0)
        SQUIRREL(1935, 5914, 0, SOUTH, 8)
        BIRD_10541(1941, 5945, 0, SOUTH, 5)
        BUTTERFLY_238(1947, 5908, 0, SOUTH, 0)
        BIRD_10541(1954, 5904, 0, SOUTH, 5)
        SQUIRREL_1417(1954, 5931, 0, SOUTH, 9)
        BUTTERFLY_238(1955, 5949, 0, SOUTH, 0)
        CRIMSON_SWIFT(1960, 5897, 0, <PERSON>OUT<PERSON>, 6)
        CRIMSON_SWIFT(1961, 5901, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        CRIMSON_SWIFT(1963, 5899, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        SQUIRREL_1418(1965, 5896, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        CRIMSON_SWIFT(1969, 5890, 0, SOUTH, 6)
        CRIMSON_SWIFT(1971, 5895, 0, SOUTH, 6)
        BUTTERFLY_238(1973, 5888, 0, SOUTH, 0)
        CRIMSON_SWIFT(1973, 5891, 0, SOUTH, 6)
        BUTTERFLY_238(1975, 5918, 0, SOUTH, 0)
        SQUIRREL_1417(1975, 5934, 0, SOUTH, 9)
    }
}