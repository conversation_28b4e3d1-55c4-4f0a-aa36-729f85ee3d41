package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7070Spawns : NPCSpawnsScript() {
    init {
        RAT_2854(1738, 10123, 0, SOUTH, 14)
        RAT_2854(1741, 10166, 0, SOUTH, 14)
        RAT_2854(1747, 10136, 0, SOUTH, 14)
        RAT_2854(1748, 10148, 0, SOUTH, 14)
        RAT_2854(1756, 10120, 0, SOUTH, 14)
        RAT_2854(1760, 10141, 0, SOUTH, 14)
        BARTENDER_7911(1761, 10131, 0, SOUTH, 5)
        FISH_MONGER_7912(1761, 10145, 0, SOUTH, 5)
        THIEF_7914(1763, 10154, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        MAN_7919(1764, 10131, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        THIEF_7916(1764, 10143, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7929(1764, 10158, 0, SOUTH, 5)
        PIRATE_7918(1765, 10136, 0, SOUTH, 5)
        DEVAN_RUTTER(1766, 10148, 0, SOUTH, 5)
        THIEF_7915(1767, 10152, 0, SOUTH, 5)
        RAT_2854(1770, 10158, 0, SOUTH, 14)
        RAT_2854(1772, 10133, 0, SOUTH, 14)
        WOMAN_7921(1772, 10158, 0, SOUTH, 5)
        WOMAN_7922(1774, 10129, 0, SOUTH, 5)
        MAN_7920(1774, 10152, 0, SOUTH, 5)
        PIRATE_7917(1775, 10140, 0, SOUTH, 5)
        RAT_2854(1775, 10166, 0, SOUTH, 14)
        SHOP_KEEPER_7913(1776, 10147, 0, SOUTH, 5)
        RAT_2854(1786, 10156, 0, SOUTH, 14)
        RAT_2854(1789, 10133, 0, SOUTH, 14)
    }
}