package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7236Spawns : NPCSpawnsScript() {
    init {
        WALLASALKI_5939(1797, 4377, 1, <PERSON><PERSON><PERSON><PERSON>, 8)
        WALLASALKI_5939(1799, 4371, 1, <PERSON><PERSON><PERSON><PERSON>, 8)
        WALLASALKI_5939(1799, 4376, 1, SOUTH, 8)
        WALLASALKI_5939(1801, 4372, 1, <PERSON>OUTH, 8)
        WALLASALKI_5939(1801, 4391, 1, <PERSON>OUTH, 8)
        WALLASALKI_5939(1803, 4372, 1, <PERSON>OUTH, 8)
        WALLASALKI_5939(1803, 4393, 1, <PERSON><PERSON><PERSON><PERSON>, 8)
        WALLASALKI_5939(1804, 4392, 1, <PERSON><PERSON><PERSON><PERSON>, 8)
        WALLASALKI_5939(1808, 4393, 1, <PERSON><PERSON><PERSON><PERSON>, 8)
        DAGANNOTH_5943(1831, 4360, 1, <PERSON><PERSON><PERSON><PERSON>, 24)
        DAGANNOTH_5942(1835, 4360, 1, SOUTH, 27)
        DAGANNOTH_5943(1838, 4359, 1, SOUTH, 24)
        DAGANNOTH_5943(1844, 4360, 1, SOUTH, 24)
        DAGANNOTH_FLEDGELING(1847, 4394, 1, SOUTH, 2)
        DAGANNOTH_5942(1848, 4361, 1, SOUTH, 27)
        DAGANNOTH_FLEDGELING(1848, 4390, 1, SOUTH, 2)
        DAGANNOTH_FLEDGELING(1849, 4387, 1, SOUTH, 2)
        DAGANNOTH_FLEDGELING(1849, 4394, 1, SOUTH, 2)
        BARDUR(1850, 4392, 1, SOUTH, 2)
        DAGANNOTH_FLEDGELING(1851, 4390, 1, SOUTH, 2)
        DAGANNOTH_5943(1852, 4360, 1, SOUTH, 24)
        DAGANNOTH_5942(1854, 4359, 1, SOUTH, 27)
        WALLASALKI_5939(1796, 4383, 2, SOUTH, 8)
        BOULDER_5941(1803, 4365, 2, SOUTH, 0)
        BOULDER_5941(1805, 4370, 2, SOUTH, 0)
        BOULDER_5941(1810, 4363, 2, SOUTH, 0)
        DAGANNOTH_5942(1812, 4392, 2, SOUTH, 27)
        DAGANNOTH_5942(1814, 4404, 2, SOUTH, 27)
        DAGANNOTH_5943(1814, 4407, 2, SOUTH, 24)
        BOULDER_5941(1817, 4362, 2, SOUTH, 0)
        BOULDER_5941(1817, 4367, 2, SOUTH, 0)
        DAGANNOTH_5943(1817, 4404, 2, SOUTH, 24)
        DAGANNOTH_5943(1820, 4389, 2, SOUTH, 24)
        DAGANNOTH_5942(1820, 4392, 2, SOUTH, 27)
        DAGANNOTH_5942(1820, 4403, 2, SOUTH, 27)
        DAGANNOTH_5943(1822, 4394, 2, SOUTH, 24)
        DAGANNOTH_5942(1823, 4386, 2, SOUTH, 27)
        DAGANNOTH_5943(1825, 4388, 2, SOUTH, 24)
        DAGANNOTH_5943(1839, 4371, 2, SOUTH, 24)
        DAGANNOTH_5942(1841, 4370, 2, SOUTH, 27)
        DAGANNOTH_5943(1845, 4370, 2, SOUTH, 24)
        DAGANNOTH_5942(1846, 4373, 2, SOUTH, 27)
        DAGANNOTH_5943(1848, 4379, 2, SOUTH, 24)
        DAGANNOTH_5942(1848, 4382, 2, SOUTH, 27)
        DAGANNOTH_5943(1850, 4382, 2, SOUTH, 24)
        WALLASALKI_5939(1799, 4403, 3, SOUTH, 8)
        WALLASALKI_5939(1801, 4408, 3, SOUTH, 8)
        WALLASALKI_5939(1804, 4403, 3, SOUTH, 8)
        DAGANNOTH_5943(1826, 4376, 3, SOUTH, 24)
        BOULDER_5941(1829, 4407, 3, SOUTH, 0)
        DAGANNOTH_5943(1830, 4378, 3, SOUTH, 24)
        BOULDER_5941(1835, 4396, 3, SOUTH, 0)
        BOULDER_5941(1836, 4403, 3, SOUTH, 0)
        DAGANNOTH_5943(1838, 4379, 3, SOUTH, 24)
        BOULDER_5941(1841, 4399, 3, SOUTH, 0)
        BOULDER_5941(1842, 4405, 3, SOUTH, 0)
        BOULDER_5941(1849, 4406, 3, SOUTH, 0)
    }
}