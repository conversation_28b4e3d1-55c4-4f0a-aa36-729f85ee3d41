package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7223Spawns : NPCSpawnsScript() {
    init {
        GALLOW(1805, 3562, 0, SOUTH, 2)
        11150(1806, 3550, 0, SOUTH, 5)
        TOOL_LEPRECHAUN(1806, 3555, 0, SOUTH, 0)
        FLIES(1815, 3536, 0, SOUTH, 2)
        FLIES(1817, 3536, 0, SOUTH, 2)
        FLIES(1817, 3538, 0, SOUTH, 2)
        FLIES(1818, 3537, 0, SOUTH, 2)
        FLIES(1819, 3538, 0, SOUTH, 2)
        MUGGER_6996(1825, 3573, 0, SOUTH, 8)
        GULL(1848, 3538, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        GULL(1852, 3529, 0, <PERSON>O<PERSON><PERSON>, 7)
        GULL(1853, 3574, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        MAN_6776(1816, 3537, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
    }
}