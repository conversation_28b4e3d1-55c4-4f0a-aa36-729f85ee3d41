package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13117Spawns : NPCSpawnsScript() {
    init {
        ROGUE(3276, 3930, 0, SOUTH, 7)
        ROGUE(3276, 3933, 0, SOUTH, 7)
        ROGUE_6603(3276, 3939, 0, SOUTH, 12)
        ROGUE(3278, 3927, 0, SOUTH, 7)
        ROGUE_6603(3279, 3941, 0, SOUTH, 12)
        ROGUE(3281, 3934, 0, SOUTH, 7)
        ROGUE(3284, 3929, 0, SOUTH, 7)
        ROGUE_6603(3284, 3946, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        ROGUE(3285, 3933, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        ROGUE(3285, 3939, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        ROGUE_6603(3285, 3943, 0, <PERSON>OUT<PERSON>, 12)
        ROGUE(3287, 3930, 0, SOUTH, 7)
        EDWARD(3288, 3941, 0, SOUTH, 3)
        ROGUE_6603(3288, 3946, 0, SOUTH, 12)
        ROGUE_6603(3290, 3938, 0, SOUTH, 12)
        ROGUE(3291, 3932, 0, SOUTH, 7)
        ROGUE(3292, 3924, 0, SOUTH, 7)
        ROGUE_6603(3296, 3939, 0, SOUTH, 12)
        ROGUE_6603(3297, 3942, 0, SOUTH, 12)
        ROGUE(3288, 3931, 1, SOUTH, 7)
        ROGUE(3289, 3930, 1, SOUTH, 7)
        6117(3294, 3934, 1, SOUTH, 5)
        ROGUE(3286, 3939, 2, SOUTH, 7)
        ROGUE(3287, 3929, 2, SOUTH, 7)
        ROGUE(3289, 3932, 2, SOUTH, 7)
        ROGUE(3290, 3931, 2, SOUTH, 7)
        ROGUE(3293, 3933, 2, SOUTH, 7)
        ROGUE(3296, 3934, 2, SOUTH, 7)
        ROGUE(3281, 3934, 3, SOUTH, 7)
        ROGUE(3282, 3938, 3, SOUTH, 7)
    }
}