package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13457Spawns : NPCSpawnsScript() {
    init {
        DESERT_SNAKE(3371, 9301, 0, SOUTH, 3)
        DESERT_SNAKE(3371, 9303, 0, SOUTH, 3)
        DESERT_SNAKE(3371, 9305, 0, SOUTH, 3)
        DESERT_SNAKE(3371, 9307, 0, SOUTH, 3)
        DESERT_SNAKE(3371, 9309, 0, SOUTH, 3)
        GENIE_4738(3371, 9320, 0, SOUTH, 0)
        DESERT_SNAKE(3373, 9301, 0, SOUTH, 3)
        DESERT_SNAKE(3373, 9303, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        DESERT_SNAKE(3373, 9306, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        DESERT_SNAKE(3373, 9308, 0, SOUTH, 3)
        DESERT_SNAKE(3375, 9301, 0, SOUTH, 3)
        DESERT_SNAKE(3375, 9303, 0, SOUTH, 3)
        DESERT_SNAKE(3375, 9305, 0, SOUTH, 3)
        DESERT_SNAKE(3375, 9307, 0, SOUTH, 3)
        DESERT_SNAKE(3375, 9309, 0, SOUTH, 3)
    }
}