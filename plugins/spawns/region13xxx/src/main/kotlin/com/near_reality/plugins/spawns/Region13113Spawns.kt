package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13113Spawns : NPCSpawnsScript() {
    init {
        GHOST(3272, 3664, 0, SOUTH, 5)
        GHOST_86(3278, 3663, 0, SOUTH, 5)
        GHOST_90(3279, 3654, 0, SOUTH, 8)
        LARRAN(3280, 3657, 0, SOUTH, 5)
        GHOST_87(3282, 3662, 0, SOUTH, 2)
        GHOST_91(3288, 3650, 0, SOUTH, 6)
        GHOST_92(3290, 3653, 0, SOUTH, 6)
        BLACK_SALAMANDER(3292, 3675, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        BLACK_SALAMANDER(3293, 3667, 0, <PERSON>O<PERSON><PERSON>, 4)
        BLACK_SALAMANDER(3296, 3675, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        BLACK_SALAMANDER(3298, 3666, 0, SOUTH, 4)
        B<PERSON>CK_SALAMANDER(3298, 3671, 0, SOUTH, 4)
        BLACK_SALAMANDER(3298, 3679, 0, SOUTH, 4)
        HILL_GIANT(3300, 3649, 0, SOUTH, 3)
        HILL_GIANT_2100(3307, 3669, 0, SOUTH, 3)
    }
}