package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13104Spawns : NPCSpawnsScript() {
    init {
        UGTHANKI(3267, 3077, 0, SOUTH, 6)
        UGTHANKI(3269, 3110, 0, SOUTH, 6)
        UGTHANKI(3275, 3094, 0, SOUTH, 6)
        DESERT_WOLF_4651(3283, 3084, 0, SOUTH, 5)
        DESERT_WOLF(3283, 3108, 0, SOUTH, 3)
        UGTHANKI(3291, 3078, 0, SOUTH, 6)
        UGTHANKI(3291, 3100, 0, SOUTH, 6)
        JARR(3299, 3121, 0, <PERSON>O<PERSON><PERSON>, 3)
        SHANTAY_GUARD(3299, 3124, 0, <PERSON>O<PERSON><PERSON>, 3)
        6133(3301, 3100, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        6233(3303, 3113, 0, SOUTH, 2)
        SHANTAY_GUARD_4648(3303, 3118, 0, SOUTH, 0)
        6234(3304, 3113, 0, SOUTH, 2)
        SHANTAY(3304, 3123, 0, SOUTH, 3)
        UGTHANKI(3305, 3089, 0, SOUTH, 6)
        6231(3305, 3112, 0, SOUTH, 3)
        SHANTAY_GUARD(3305, 3129, 0, SOUTH, 3)
        SHANTAY_GUARD(3307, 3123, 0, SOUTH, 3)
        DESERT_WOLF_4650(3310, 3076, 0, SOUTH, 4)
        MONKEY(3311, 3108, 0, SOUTH, 5)
        RUG_MERCHANT(3311, 3109, 0, SOUTH, 2)
        UGTHANKI(3318, 3078, 0, SOUTH, 6)
        UGTHANKI(3321, 3104, 0, SOUTH, 6)
        DESERT_WOLF(3323, 3094, 0, SOUTH, 3)
    }
}