package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13873Spawns : NPCSpawnsScript() {
    init {
        FISHING_SPOT_4476(3471, 3199, 0, SOUTH, 0)
        FISHING_SPOT_4476(3472, 3196, 0, SOUTH, 0)
        FISHING_SPOT_4476(3474, 3193, 0, SOUTH, 0)
        FISHING_SPOT_4476(3476, 3191, 0, SOUTH, 0)
        FISHING_SPOT_4476(3479, 3189, 0, SOUTH, 0)
        FISHING_SPOT_4476(3482, 3186, 0, SOUTH, 0)
        FISHING_SPOT_4476(3484, 3185, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        FISHING_SPOT_4476(3486, 3184, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        FISHING_SPOT_4476(3489, 3184, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        FISHING_SPOT_4476(3490, 3183, 0, SOUTH, 0)
        FISHING_SPOT_4476(3492, 3181, 0, SOUTH, 0)
        FISHING_SPOT_4476(3494, 3178, 0, SOUTH, 0)
    }
}