package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13462Spawns : NPCSpawnsScript() {
    init {
        CHARMED_WARRIOR_5988(3347, 9623, 0, SOUTH, 9)
        CHARMED_WARRIOR_5987(3350, 9652, 0, SOUTH, 3)
        ENCHANTMENT_GUARDIAN(3364, 9647, 0, SOUTH, 2)
        CHARMED_WARRIOR(3382, 9631, 0, SOUTH, 6)
        CHARMED_WARRIOR_5986(3382, 9655, 0, SOUTH, 3)
        GRAVEYARD_GUARDIAN(3362, 9644, 1, <PERSON>OUT<PERSON>, 2)
        FLYING_BOOK_5976(3354, 9636, 2, <PERSON><PERSON><PERSON><PERSON>, 5)
        CHARMED_WARRIOR(3362, 9650, 2, <PERSON><PERSON><PERSON><PERSON>, 6)
        ALCHEMY_GUARDIAN(3364, 9626, 2, SOUTH, 2)
        SWEEPER(3364, 9638, 2, SOUTH, 5)
        CHARMED_WARRIOR_5987(3367, 9650, 2, SOUTH, 3)
        FLYING_BOOK(3375, 9640, 2, SOUTH, 4)
    }
}