package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13101Spawns : NPCSpawnsScript() {
    init {
        CROCODILE(3264, 2886, 0, SOUTH, 6)
        JACKAL(3265, 2931, 0, SOUTH, 11)
        JACKAL(3265, 2935, 0, SOUTH, 11)
        CROCODILE(3267, 2891, 0, SOUTH, 6)
        CROCODILE(3268, 2881, 0, SOUTH, 6)
        JACKAL(3268, 2932, 0, SOUTH, 11)
        JACKAL(3268, 2935, 0, SOUTH, 11)
        CROCODILE(3269, 2885, 0, SOUT<PERSON>, 6)
        CROCODILE(3296, 2912, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        CROCODILE(3297, 2919, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        CROCODILE(3312, 2908, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        CROCODILE(3317, 2911, 0, <PERSON>OUTH, 6)
        CROCODILE(3301, 2920, 0, SOUTH, 6)
        CROCOD<PERSON>E(3297, 2919, 0, SOUTH, 6)
        CROCODILE(3289, 2924, 0, SOUTH, 6)
        CROCODILE(3301, 2929, 0, SOUTH, 6)
        CROCODILE(3288, 2918, 0, SOUTH, 6)
        CROCODILE(3286, 2924, 0, SOUTH, 6)
        J<PERSON>KAL(3316, 2900, 0, SOUTH, 11)
        JACKAL(3319, 2897, 0, SOUTH, 11)
        JACKAL(3320, 2901, 0, SOUTH, 11)
        JACKAL(3321, 2899, 0, SOUTH, 11)
    }
}