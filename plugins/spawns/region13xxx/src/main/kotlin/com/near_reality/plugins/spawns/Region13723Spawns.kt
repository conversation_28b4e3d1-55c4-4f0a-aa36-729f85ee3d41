package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13723Spawns : NPCSpawnsScript() {
    init {
        BLOODVELD_487(3403, 9934, 3, SOUT<PERSON>, 6)
        NECHRYAEL_11(3403, 9968, 3, SOUT<PERSON>, 4)
        BLOODVELD_487(3404, 9940, 3, SOUTH, 6)
        BLOODVELD_487(3405, 9929, 3, SOUTH, 6)
        BLOODVELD_487(3406, 9949, 3, <PERSON><PERSON>UTH, 6)
        NECHRYAEL_11(3406, 9961, 3, <PERSON><PERSON>UT<PERSON>, 4)
        NECHRYAEL_11(3406, 9971, 3, <PERSON><PERSON><PERSON><PERSON>, 4)
        NECHRYAEL_11(3407, 9957, 3, <PERSON><PERSON><PERSON><PERSON>, 4)
        BLOODVELD_486(3409, 9939, 3, <PERSON><PERSON><PERSON><PERSON>, 2)
        BLOODVELD_487(3411, 9945, 3, <PERSON><PERSON><PERSON><PERSON>, 6)
        NECHRYAEL_11(3411, 9961, 3, SOUTH, 4)
        BLOODVELD_487(3412, 9935, 3, SOUTH, 6)
        NECHRYAEL_11(3412, 9969, 3, SOUTH, 4)
        NECHRYAEL_11(3413, 9965, 3, SOUTH, 4)
        NECHRY<PERSON>EL_11(3414, 9954, 3, SOUTH, 4)
        BLOODVELD_486(3416, 9942, 3, SOUTH, 2)
        BLOODVELD_486(3417, 9931, 3, SOUTH, 2)
        BLOODVELD_486(3417, 9937, 3, SOUTH, 2)
        NECHRYAEL_11(3417, 9959, 3, SOUTH, 4)
        NECHRYAEL_11(3417, 9970, 3, SOUTH, 4)
        BLOODVELD_487(3421, 9939, 3, SOUTH, 6)
        BLOODVELD_486(3423, 9935, 3, SOUTH, 2)
        RAULYN(3424, 9953, 3, SOUTH, 2)
        NECHRYAEL_11(3424, 9960, 3, SOUTH, 4)
        NECHRYAEL_11(3424, 9969, 3, SOUTH, 4)
        GARGOYLE_1543(3427, 9943, 3, SOUTH, 6)
        ABYSSAL_DEMON_416(3427, 9965, 3, SOUTH, 4)
        GARGOYLE_1543(3428, 9932, 3, SOUTH, 6)
        GARGOYLE_1543(3432, 9937, 3, SOUTH, 6)
        GARGOYLE_1543(3432, 9947, 3, SOUTH, 6)
        ABYSSAL_DEMON_416(3432, 9965, 3, SOUTH, 4)
        GARGOYLE_1543(3433, 9942, 3, SOUTH, 6)
        ABYSSAL_DEMON_416(3433, 9956, 3, SOUTH, 4)
        ABYSSAL_DEMON_416(3433, 9961, 3, SOUTH, 4)
        GARGOYLE_1543(3434, 9932, 3, SOUTH, 6)
        ABYSSAL_DEMON_416(3436, 9964, 3, SOUTH, 4)
        ABYSSAL_DEMON_416(3436, 9971, 3, SOUTH, 4)
        GARGOYLE_1543(3437, 9948, 3, SOUTH, 6)
        ABYSSAL_DEMON_416(3437, 9967, 3, SOUTH, 4)
        ABYSSAL_DEMON_416(3438, 9954, 3, SOUTH, 4)
        GARGOYLE_1543(3439, 9932, 3, SOUTH, 6)
        GARGOYLE_1543(3439, 9943, 3, SOUTH, 6)
        GARGOYLE_1543(3440, 9928, 3, SOUTH, 6)
        GARGOYLE_1543(3440, 9936, 3, SOUTH, 6)
        ABYSSAL_DEMON_416(3440, 9960, 3, SOUTH, 4)
        ABYSSAL_DEMON_416(3440, 9971, 3, SOUTH, 4)
        ABYSSAL_DEMON_416(3441, 9974, 3, SOUTH, 4)
        ABYSSAL_DEMON_416(3442, 9956, 3, SOUTH, 4)
        ABYSSAL_DEMON_416(3442, 9966, 3, SOUTH, 4)
        GARGOYLE_1543(3444, 9931, 3, SOUTH, 6)
        ABYSSAL_DEMON_416(3445, 9970, 3, SOUTH, 4)
    }
}