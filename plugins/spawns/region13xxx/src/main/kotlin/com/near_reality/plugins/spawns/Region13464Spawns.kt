package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13464Spawns : NPCSpawnsScript() {
    init {
        DOUG_DEEPING(3351, 9756, 0, SOUTH, 5)
        SKELETON_71(3368, 9748, 0, SOUTH, 7)
        SKELETON(3373, 9748, 0, SOUTH, 7)
        SKELETON_72(3376, 9751, 0, SOUTH, 8)
        SKELETON_73(3377, 9743, 0, <PERSON><PERSON>UTH, 8)
        SKELETON_73(3377, 9754, 0, <PERSON>O<PERSON><PERSON>, 8)
        SKELETON(3378, 9748, 0, <PERSON>OUT<PERSON>, 7)
        SKELETON_71(3378, 9757, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        SKELETON_72(3380, 9756, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        SKELETON(3382, 9753, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        SKELETON_71(3383, 9746, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
    }
}