package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13406Spawns : NPCSpawnsScript() {
    init {
        DIRE_WOLF(3335, 6023, 0, SOUTH, 12)
        DIRE_WOLF(3341, 6023, 0, SOUTH, 12)
        DIRE_WOLF(3344, 6018, 0, SOUTH, 12)
        DIRE_WOLF(3359, 6028, 0, SOUTH, 12)
        DEADLY_RED_SPIDER(3361, 6063, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3361, 6066, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3362, 6062, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        DIRE_WOLF(3363, 6024, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        DIRE_WOLF(3363, 6031, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        DEADLY_RED_SPIDER(3365, 6061, 0, <PERSON>OUT<PERSON>, 8)
        DEADLY_RED_SPIDER(3366, 6063, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3367, 6061, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3368, 6070, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3369, 6062, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3369, 6064, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3374, 6062, 0, SOUTH, 8)
    }
}