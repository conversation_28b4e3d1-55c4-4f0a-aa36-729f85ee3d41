package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13200Spawns : NPCSpawnsScript() {
    init {
        LOCUST_RIDER(3270, 9264, 0, SOUTH, 19)
        LOCUST_RIDER(3272, 9246, 0, SOUTH, 19)
        LOCUST_RIDER_796(3273, 9251, 0, SOUTH, 23)
        SCARAB_MAGE(3273, 9263, 0, SOUTH, 8)
        LOCUST_RIDER_796(3274, 9223, 0, SOUTH, 23)
        LOCUST_RIDER_796(3274, 9262, 0, SOUTH, 23)
        SCARAB_MAGE(3275, 9226, 0, <PERSON>OUT<PERSON>, 8)
        LOCUST_RIDER(3276, 9235, 0, <PERSON><PERSON><PERSON><PERSON>, 19)
        SCARAB_MAGE(3276, 9248, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        SCARAB_MAGE(3279, 9222, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        LOCUST_RIDER_796(3280, 9240, 0, SOUTH, 23)
        LOCUST_RIDER_796(3280, 9251, 0, SOUTH, 23)
        SCARAB_MAGE(3282, 9244, 0, SOUTH, 8)
        SCARAB_MAGE(3282, 9273, 0, SOUTH, 8)
        LOCUST_RIDER(3283, 9243, 0, SOUTH, 19)
        LOCUST_RIDER_796(3284, 9247, 0, SOUTH, 23)
        LOCUST_RIDER(3284, 9252, 0, SOUTH, 19)
        SCARAB_MAGE(3286, 9227, 0, SOUTH, 8)
        LOCUST_RIDER(3287, 9268, 0, SOUTH, 19)
        SCARAB_MAGE(3288, 9265, 0, SOUTH, 8)
        LOCUST_RIDER_796(3290, 9232, 0, SOUTH, 23)
        LOCUST_RIDER_796(3291, 9262, 0, SOUTH, 23)
        LOCUST_RIDER(3294, 9233, 0, SOUTH, 19)
        LOCUST_RIDER_796(3295, 9251, 0, SOUTH, 23)
        LOCUST_RIDER_796(3296, 9229, 0, SOUTH, 23)
        SCARAB_MAGE(3296, 9248, 0, SOUTH, 8)
        LOCUST_RIDER(3296, 9261, 0, SOUTH, 19)
        SCARAB_MAGE(3297, 9232, 0, SOUTH, 8)
        SCARAB_MAGE(3298, 9225, 0, SOUTH, 8)
        LOCUST_RIDER_796(3299, 9244, 0, SOUTH, 23)
        LOCUST_RIDER(3299, 9254, 0, SOUTH, 19)
        SCARAB_MAGE(3300, 9241, 0, SOUTH, 8)
        SCARAB_MAGE(3301, 9252, 0, SOUTH, 8)
        LOCUST_RIDER_796(3302, 9232, 0, SOUTH, 23)
        SCARAB_MAGE(3303, 9235, 0, SOUTH, 8)
        LOCUST_RIDER(3303, 9266, 0, SOUTH, 19)
        LOCUST_RIDER_796(3304, 9252, 0, SOUTH, 23)
        LOCUST_RIDER(3305, 9234, 0, SOUTH, 19)
        LOCUST_RIDER_796(3306, 9223, 0, SOUTH, 23)
        LOCUST_RIDER_796(3308, 9262, 0, SOUTH, 23)
        SCARAB_MAGE(3309, 9224, 0, SOUTH, 8)
        SCARAB_MAGE(3309, 9265, 0, SOUTH, 8)
        LOCUST_RIDER_796(3310, 9237, 0, SOUTH, 23)
        SCARAB_MAGE(3316, 9255, 0, SOUTH, 8)
        SCARAB_MAGE(3317, 9240, 0, SOUTH, 8)
        SCARAB_MAGE(3276, 9268, 2, SOUTH, 8)
        SCARAB_MAGE(3277, 9252, 2, SOUTH, 8)
        SCARAB_MAGE(3277, 9275, 2, SOUTH, 8)
        SCARAB_MAGE(3279, 9234, 2, SOUTH, 8)
        SCARAB_MAGE(3289, 9236, 2, SOUTH, 8)
        LOCUST_RIDER_796(3292, 9256, 2, SOUTH, 23)
        SCARAB_MAGE(3296, 9256, 2, SOUTH, 8)
        LOCUST_RIDER_796(3299, 9220, 2, SOUTH, 23)
        SCARAB_MAGE(3301, 9275, 2, SOUTH, 8)
        SCARAB_MAGE(3305, 9256, 2, SOUTH, 8)
        SCARAB_MAGE(3308, 9221, 2, SOUTH, 8)
        SCARAB_MAGE(3311, 9226, 2, SOUTH, 8)
    }
}