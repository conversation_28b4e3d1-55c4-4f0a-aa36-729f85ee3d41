package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13366Spawns : NPCSpawnsScript() {
    init {
        BAT(3341, 3478, 0, SOUTH, 23)
        BAT(3343, 3484, 0, SOUTH, 23)
        BAT(3345, 3484, 0, SOUTH, 23)
        BAT(3350, 3493, 0, SOUTH, 23)
        BAT(3353, 3494, 0, SOUTH, 23)
        BAT(3361, 3484, 0, SOUTH, 23)
        ODD_OLD_MAN(3361, 3505, 0, SOUTH, 3)
        BAT(3364, 3489, 0, SOUTH, 23)
        BAT(3370, 3497, 0, SOUTH, 23)
        BAT(3379, 3478, 0, <PERSON><PERSON><PERSON><PERSON>, 23)
        BAT(3379, 3484, 0, <PERSON>OUT<PERSON>, 23)
        BAT(3387, 3483, 0, <PERSON>OUTH, 23)
        BAT(3388, 3494, 0, SOUTH, 23)
        BAT(3389, 3483, 0, SOUTH, 23)
    }
}