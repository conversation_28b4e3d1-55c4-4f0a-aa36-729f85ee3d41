package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13100Spawns : NPCSpawnsScript() {
    init {
        CROCODILE(3268, 2854, 0, SOUTH, 6)
        CROCODILE(3269, 2827, 0, SOUTH, 6)
        CROCODILE(3269, 2866, 0, SOUTH, 6)
        CROCODILE(3269, 2875, 0, SOUTH, 6)
        CROCODILE(3270, 2818, 0, SOUTH, 6)
        CROCODILE(3272, 2841, 0, SOUTH, 6)
        CROCODILE(3276, 2842, 0, SOUTH, 6)
        CROCODILE(3282, 2839, 0, <PERSON>O<PERSON>H, 6)
        6166(3289, 2818, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        CROCODILE(3290, 2847, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        VULTURE(3295, 2866, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        VULTURE(3297, 2863, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        VULTURE(3301, 2864, 0, SOUT<PERSON>, 4)
        J<PERSON><PERSON>L(3306, 2817, 0, SOUTH, 11)
        J<PERSON><PERSON>L(3308, 2818, 0, SOUTH, 11)
        <PERSON><PERSON><PERSON>L(3309, 2816, 0, SOUTH, 11)
        J<PERSON><PERSON>L(3312, 2817, 0, SOUTH, 11)
        J<PERSON><PERSON>L(3314, 2861, 0, <PERSON>OUTH, 11)
        6187(3315, 2849, 0, SOUTH, 2)
        JACKAL(3319, 2873, 0, SOUTH, 11)
        JACKAL(3321, 2871, 0, SOUTH, 11)
        BILLY_GOAT_1797(3324, 2841, 0, SOUTH, 4)
        JACKAL(3324, 2870, 0, SOUTH, 11)
        GOAT_1796(3325, 2843, 0, SOUTH, 4)
        GOAT_1796(3325, 2845, 0, SOUTH, 4)
        GOAT_1795(3326, 2847, 0, SOUTH, 4)
        GOAT_1795(3327, 2844, 0, SOUTH, 4)
        JACKAL(3327, 2858, 0, SOUTH, 11)
    }
}