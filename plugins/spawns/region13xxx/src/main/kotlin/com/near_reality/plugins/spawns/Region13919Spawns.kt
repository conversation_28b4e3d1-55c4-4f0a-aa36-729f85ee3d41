package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13919Spawns : NPCSpawnsScript() {
    init {
        DARK_WARRIOR(3513, 6136, 0, SOUTH, 5)
        DARK_WARRIOR(3513, 6139, 0, SOUTH, 5)
        DARK_WARRIOR(3513, 6142, 0, SOUTH, 5)
        DARK_WARRIOR(3516, 6136, 0, SOUTH, 5)
        DARK_WARRIOR(3516, 6139, 0, SOUTH, 5)
        DARK_WARRIOR(3516, 6142, 0, SOUTH, 5)
    }
}