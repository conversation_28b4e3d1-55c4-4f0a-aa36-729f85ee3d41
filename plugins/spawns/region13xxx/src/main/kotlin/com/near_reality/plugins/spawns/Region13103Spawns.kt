package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13103Spawns : NPCSpawnsScript() {
    init {
        DESERT_WOLF_4650(3264, 3008, 0, SOUTH, 4)
        MERCENARY_4659(3265, 3031, 0, SOUTH, 3)
        DESERT_WOLF(3265, 3066, 0, SOUTH, 3)
        DESERT_WOLF_4650(3266, 3008, 0, SOUTH, 4)
        DESERT_WOLF_4651(3266, 3010, 0, SOUTH, 5)
        MERCENARY_4658(3266, 3027, 0, SOUTH, 3)
        DESERT_WOLF(3267, 3066, 0, <PERSON>OUT<PERSON>, 3)
        DESERT_WOLF_4651(3267, 3068, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        UGTHANKI(3268, 3052, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        MERCENARY_4657(3269, 3031, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        MERCENARY(3270, 3027, 0, SOUTH, 3)
        MERC<PERSON>ARY_CAPTAIN(3270, 3029, 0, SOUTH, 3)
        GUARD_4667(3276, 3026, 0, SOUTH, 2)
        GUARD_4666(3277, 3030, 0, SOUTH, 2)
        GUARD_4660(3280, 3026, 0, SOUTH, 3)
        UGTHANKI(3281, 3056, 0, SOUTH, 6)
        FEMALE_SLAVE_4673(3285, 3016, 0, SOUTH, 2)
        DESERT_WOLF_4650(3285, 3066, 0, SOUTH, 4)
        MALE_SLAVE_4671(3287, 3016, 0, SOUTH, 2)
        MINE_CART_DRIVER(3287, 3022, 0, SOUTH, 2)
        GUARD_4665(3287, 3030, 0, SOUTH, 2)
        DESERT_WOLF_4651(3287, 3065, 0, SOUTH, 5)
        DESERT_WOLF_4651(3287, 3067, 0, SOUTH, 5)
        GUARD_4661(3288, 3018, 0, SOUTH, 3)
        GUARD_4664(3291, 3035, 0, SOUTH, 5)
        FEMALE_SLAVE_4672(3292, 3016, 0, SOUTH, 0)
        MALE_SLAVE_4670(3294, 3016, 0, SOUTH, 2)
        GUARD_4663(3296, 3018, 0, SOUTH, 2)
        GUARD_4669(3296, 3026, 0, SOUTH, 2)
        FEMALE_SLAVE_4672(3300, 3016, 0, SOUTH, 0)
        GUARD_4662(3301, 3022, 0, SOUTH, 2)
        GUARD_4661(3302, 3032, 0, SOUTH, 3)
        MALE_SLAVE_4671(3303, 3016, 0, SOUTH, 2)
        6232(3303, 3025, 0, SOUTH, 2)
        GUARD_4660(3303, 3035, 0, SOUTH, 3)
        FEMALE_SLAVE_4672(3304, 3022, 0, SOUTH, 0)
        UGTHANKI(3307, 3055, 0, SOUTH, 6)
        DESERT_WOLF(3310, 3068, 0, SOUTH, 3)
        DESERT_WOLF_4651(3310, 3070, 0, SOUTH, 5)
        DESERT_WOLF_4651(3312, 3068, 0, SOUTH, 5)
        UGTHANKI(3318, 3020, 0, SOUTH, 6)
        UGTHANKI(3318, 3040, 0, SOUTH, 6)
        DESERT_WOLF_4651(3320, 3054, 0, SOUTH, 5)
        DESERT_WOLF(3322, 3011, 0, SOUTH, 3)
        DESERT_WOLF_4650(3322, 3032, 0, SOUTH, 4)
        DESERT_WOLF(3322, 3052, 0, SOUTH, 3)
        DESERT_WOLF_4650(3323, 3055, 0, SOUTH, 4)
        DESERT_WOLF_4651(3324, 3013, 0, SOUTH, 5)
        DESERT_WOLF(3324, 3030, 0, SOUTH, 3)
        DESERT_WOLF_4650(3325, 3010, 0, SOUTH, 4)
        DESERT_WOLF_4651(3325, 3033, 0, SOUTH, 5)
        GUARD_4665(3286, 3036, 1, SOUTH, 2)
        CAPTAIN_SIAD(3291, 3032, 1, SOUTH, 5)
    }
}