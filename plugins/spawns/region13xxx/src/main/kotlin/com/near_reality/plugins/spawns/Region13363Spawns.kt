package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13363Spawns : NPCSpawnsScript() {
    init {
        JARAAH(3358, 3276, 0, SOUTH, 3)
        CHARMED_WARRIOR(3359, 3304, 0, SOUTH, 6)
        AABLA(3362, 3273, 0, SOUTH, 4)
        ENTRANCE_GUARDIAN(3363, 3304, 0, SOUTH, 2)
        FLYING_BOOK_5976(3364, 3308, 0, SOUTH, 5)
        CHARMED_WARRIOR_5988(3364, 3311, 0, SOUTH, 9)
        FLYING_BOOK(3367, 3304, 0, <PERSON>O<PERSON><PERSON>, 4)
        SABREEN(3368, 3278, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        ZAHWA(3372, 3279, 0, <PERSON>O<PERSON><PERSON>, 4)
        SURGEON_GENERAL_TAFANI(3373, 3272, 0, SOUTH, 4)
        HAMID(3375, 3285, 0, SOUTH, 2)
        FADLI(3383, 3269, 0, SOUTH, 2)
        REWARDS_GUARDIAN(3362, 3318, 1, SOUTH_EAST, 0)
        CHARMED_WARRIOR_5987(3363, 3307, 1, SOUTH, 3)
        CHARMED_WARRIOR_5986(3364, 3311, 1, SOUTH, 3)
        FLYING_BOOK_5976(3365, 3318, 1, SOUTH, 5)
    }
}