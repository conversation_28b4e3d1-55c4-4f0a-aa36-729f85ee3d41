package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13152Spawns : NPCSpawnsScript() {
    init {
        CARNIVOROUS_CHINCHOMPA(3289, 6164, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(3290, 6162, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(3291, 6163, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(3291, 6165, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(3292, 6162, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(3293, 6164, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        CARNIVOROUS_CHINCHOMPA(3300, 6153, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        LLEIDR_GOLAU(3300, 6203, 0, <PERSON>OUTH, 5)
        CARNIVOROUS_CHINCHOMPA(3301, 6155, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(3303, 6152, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(3303, 6154, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(3303, 6157, 0, SOUTH, 6)
        MEDDWL_YMLAEN_LLAW(3303, 6199, 0, SOUTH, 5)
        CARNIVOROUS_CHINCHOMPA(3305, 6153, 0, SOUTH, 6)
        RHODDWR_TAN(3306, 6197, 0, SOUTH, 5)
    }
}