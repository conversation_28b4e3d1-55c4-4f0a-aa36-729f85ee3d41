package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13108Spawns : NPCSpawnsScript() {
    init {
        GIANT_RAT_2862(3264, 3383, 0, SOUTH, 6)
        GIANT_RAT_2864(3266, 3381, 0, SOUTH, 6)
        RAT_BURGISS(3267, 3333, 0, SOUTH, 2)
        HOPS_1108(3268, 3389, 0, SOUTH, 2)
        CHANCY_1106(3271, 3388, 0, WEST, 0)
        DA_VINCI_1104(3272, 3389, 0, NORTH, 0)
        STRAY_DOG(3277, 3381, 0, <PERSON>O<PERSON><PERSON>, 10)
        10627(3279, 3360, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        JULIE(3281, 3383, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        UNICORN(3283, 3355, 0, <PERSON><PERSON><PERSON><PERSON>, 15)
        ALI_THE_LEAFLET_DROPPER(3284, 3328, 0, SOUTH, 2)
        GUIDOR(3284, 3382, 0, WEST, 0)
        UNICORN(3286, 3349, 0, SOUTH, 15)
        GIANT_RAT_2862(3292, 3377, 0, SOUTH, 6)
        BLACK_BEAR(3296, 3347, 0, SOUTH, 8)
        GOBLIN_3073(3311, 3375, 0, SOUTH, 9)
    }
}