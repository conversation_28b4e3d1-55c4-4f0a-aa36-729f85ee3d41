package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13099Spawns : NPCSpawnsScript() {
    init {
        3885(3264, 2784, 0, SOUTH, 0)
        3885(3264, 2785, 0, SOUTH, 0)
        4188(3272, 2802, 0, SOUTH, 5)
        4188(3273, 2802, 0, SOUTH, 5)
        4188(3273, 2805, 0, SOUTH, 5)
        4188(3274, 2798, 0, SOUTH, 5)
        4188(3274, 2799, 0, SOUTH, 5)
        4188(3274, 2802, 0, SOUTH, 5)
        4188(3274, 2804, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        4188(3275, 2795, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        4188(3275, 2796, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        4188(3275, 2798, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        4189(3275, 2803, 0, SO<PERSON>H, 4)
        4188(3277, 2752, 0, SOUTH, 5)
        4188(3277, 2753, 0, SOUTH, 5)
        4188(3277, 2756, 0, SOUTH, 5)
        4188(3277, 2757, 0, SOUTH, 5)
        4191(3277, 2802, 0, SOUTH, 4)
        4188(3278, 2752, 0, SOUTH, 5)
        4188(3278, 2753, 0, SOUTH, 5)
        4188(3278, 2754, 0, SOUTH, 5)
        4188(3278, 2755, 0, SOUTH, 5)
        4190(3278, 2804, 0, SOUTH, 5)
        PRIEST_4207(3280, 2764, 0, SOUTH, 2)
        PRIEST_4207(3280, 2769, 0, SOUTH, 2)
        6192(3281, 2772, 0, NORTH, 0)
        PRIEST_4207(3283, 2770, 0, SOUTH, 2)
        PRIEST_4208(3283, 2810, 0, SOUTH, 2)
        6711(3284, 2806, 0, SOUTH, 8)
        PRIEST_4208(3284, 2809, 0, SOUTH, 2)
        6170(3285, 2785, 0, SOUTH, 3)
        EMBALMER(3287, 2755, 0, SOUTH, 2)
        1321(3287, 2813, 0, SOUTH, 2)
        24(3287, 2814, 0, SOUTH, 4)
        NEFERTI_THE_CAMEL(3289, 2754, 0, SOUTH, 2)
        1994(3289, 2783, 0, SOUTH, 3)
        6711(3289, 2784, 0, SOUTH, 8)
        TARIK(3289, 2787, 0, SOUTH, 2)
        6168(3290, 2760, 0, SOUTH, 3)
        4200(3290, 2763, 0, SOUTH, 2)
        4201(3292, 2762, 0, SOUTH, 2)
        4200(3292, 2764, 0, SOUTH, 2)
        6711(3295, 2758, 0, SOUTH, 8)
        4201(3296, 2796, 0, SOUTH, 2)
        6175(3296, 2805, 0, SOUTH, 0)
        4200(3297, 2798, 0, SOUTH, 2)
        4211(3298, 2757, 0, SOUTH, 2)
        7499(3299, 2786, 0, SOUTH, 2)
        SPHINX_4209(3300, 2784, 0, SOUTH, 2)
        6711(3304, 2791, 0, SOUTH, 8)
        6170(3304, 2798, 0, SOUTH, 3)
        6174(3305, 2770, 0, SOUTH, 0)
        6711(3305, 2804, 0, SOUTH, 8)
        4201(3306, 2774, 0, SOUTH, 2)
        JEX(3306, 2800, 0, SOUTH, 2)
        6169(3307, 2769, 0, SOUTH, 3)
        4200(3307, 2772, 0, SOUTH, 2)
        6167(3307, 2779, 0, SOUTH, 3)
        6168(3308, 2765, 0, SOUTH, 3)
        6711(3309, 2779, 0, SOUTH, 8)
        4186(3309, 2806, 0, SOUTH, 2)
        6176(3311, 2779, 0, SOUTH, 0)
        4204(3311, 2787, 0, SOUTH, 2)
        4186(3312, 2806, 0, SOUTH, 2)
        CARPENTER(3313, 2771, 0, SOUTH, 2)
        4186(3314, 2752, 0, SOUTH, 2)
        6173(3314, 2756, 0, SOUTH, 0)
        4186(3314, 2806, 0, SOUTH, 2)
        SIAMUN(3315, 2786, 0, SOUTH, 2)
        4186(3315, 2805, 0, SOUTH, 2)
        4186(3316, 2780, 0, SOUTH, 2)
        4186(3316, 2795, 0, SOUTH, 2)
        4186(3317, 2752, 0, SOUTH, 2)
        4186(3317, 2762, 0, SOUTH, 2)
        4186(3317, 2763, 0, SOUTH, 2)
        4186(3317, 2779, 0, SOUTH, 2)
        4186(3317, 2794, 0, SOUTH, 2)
        4186(3318, 2761, 0, SOUTH, 2)
        4186(3318, 2778, 0, SOUTH, 2)
        4186(3318, 2781, 0, SOUTH, 2)
        4186(3318, 2794, 0, SOUTH, 2)
        4186(3318, 2795, 0, SOUTH, 2)
        4186(3319, 2760, 0, SOUTH, 2)
        4186(3319, 2763, 0, SOUTH, 2)
        PRIEST_4207(3280, 2774, 1, SOUTH, 2)
        PRIEST_4207(3281, 2771, 3, SOUTH, 2)
    }
}