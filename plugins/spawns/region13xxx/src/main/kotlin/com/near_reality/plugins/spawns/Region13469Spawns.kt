package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13469Spawns : NPCSpawnsScript() {
    init {
        ICE_GIANT_7878(3333, 10052, 0, SOUTH, 2)
        ICE_GIANT_7878(3334, 10060, 0, SOUTH, 2)
        ICE_GIANT_7880(3336, 10056, 0, SOUTH, 3)
        ICE_GIANT_7879(3342, 10056, 0, SOUTH, 3)
        ICE_GIANT_7880(3345, 10052, 0, SOUTH, 3)
        ICE_GIANT_7878(3350, 10052, 0, SOUTH, 2)
        ANKOU_7864(3348, 10075, 0, <PERSON>OUT<PERSON>, 9)
        ANKOU_7864(3351, 10084, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        AN<PERSON>U_7864(3353, 10080, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        ANKOU_7864(3357, 10076, 0, <PERSON>OUTH, 9)
        ANKOU_7864(3360, 10081, 0, SOUTH, 9)
        <PERSON>KOU_7864(3364, 10074, 0, SOUTH, 9)
        ANKOU_7864(3364, 10079, 0, SOUTH, 9)
        ANKOU_7864(3369, 10068, 0, SOUTH, 9)
        ANKOU_7864(3370, 10064, 0, SOUTH, 9)
        ANKOU_7864(3372, 10066, 0, SOUTH, 9)
    }
}