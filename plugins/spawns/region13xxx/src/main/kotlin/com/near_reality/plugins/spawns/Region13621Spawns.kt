package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13621Spawns : NPCSpawnsScript() {
    init {
        GHAST(3410, 3398, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3413, 3410, 0, SOUTH, 18)
        GHAST(3413, 3423, 0, SOUTH, 4)
        GHAST(3413, 3443, 0, SOUTH, 4)
        GHAST(3414, 3418, 0, SOUTH, 4)
        GHAST(3415, 3407, 0, SOUTH, 4)
        GHAST(3415, 3432, 0, SOUTH, 4)
        WILL_O_THE_WISP_3483(3418, 3420, 0, SOUTH, 2)
        GHAST(3419, 3395, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        GHAST(3419, 3413, 0, <PERSON>O<PERSON><PERSON>, 4)
        GHAST(3420, 3425, 0, <PERSON><PERSON>UT<PERSON>, 4)
        GHAST(3420, 3435, 0, SOUTH, 4)
        GHAST(3420, 3440, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3421, 3408, 0, SOUTH, 18)
        GHAST(3422, 3395, 0, SOUTH, 4)
        OCHRE_BLAMISH_SNAIL(3422, 3406, 0, SOUTH, 14)
        MYRE_BLAMISH_SNAIL(3423, 3408, 0, SOUTH, 18)
        GHAST(3424, 3403, 0, SOUTH, 4)
        FISHING_SPOT_2654(3424, 3409, 0, SOUTH, 0)
        OCHRE_BLAMISH_SNAIL(3424, 3427, 0, SOUTH, 14)
        WILL_O_THE_WISP_3483(3424, 3452, 0, SOUTH, 2)
        GHAST(3425, 3395, 0, SOUTH, 4)
        FISHING_SPOT_2654(3425, 3407, 0, SOUTH, 0)
        FISHING_SPOT_2654(3425, 3410, 0, SOUTH, 0)
        MYRE_BLAMISH_SNAIL(3425, 3424, 0, SOUTH, 18)
        GHAST(3426, 3419, 0, SOUTH, 4)
        GHAST(3427, 3431, 0, SOUTH, 4)
        GHAST(3428, 3395, 0, SOUTH, 4)
        OCHRE_BLAMISH_SNAIL(3428, 3400, 0, SOUTH, 14)
        WILL_O_THE_WISP_3483(3428, 3409, 0, SOUTH, 2)
        GHAST(3428, 3445, 0, SOUTH, 4)
        GHAST(3429, 3436, 0, SOUTH, 4)
        FISHING_SPOT_2654(3431, 3415, 0, SOUTH, 0)
        GHAST(3432, 3419, 0, SOUTH, 4)
        GHAST(3432, 3445, 0, SOUTH, 4)
        WILL_O_THE_WISP_3483(3433, 3416, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3434, 3394, 0, SOUTH, 2)
        FISHING_SPOT_2654(3434, 3417, 0, SOUTH, 0)
        WILL_O_THE_WISP_3483(3436, 3407, 0, SOUTH, 2)
        GHAST(3437, 3401, 0, SOUTH, 4)
        GHAST(3437, 3435, 0, SOUTH, 4)
        WILL_O_THE_WISP_3483(3438, 3414, 0, SOUTH, 2)
        OCHRE_BLAMISH_SNAIL(3439, 3398, 0, SOUTH, 14)
        WILL_O_THE_WISP_3483(3439, 3449, 0, SOUTH, 2)
        FISHING_SPOT_2654(3440, 3410, 0, SOUTH, 0)
        GHAST(3441, 3415, 0, SOUTH, 4)
        GHAST(3441, 3428, 0, SOUTH, 4)
        GHAST(3442, 3395, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3442, 3405, 0, SOUTH, 18)
        GHAST(3443, 3432, 0, SOUTH, 4)
        GHAST(3444, 3406, 0, SOUTH, 4)
        GHAST(3445, 3394, 0, SOUTH, 4)
        OCHRE_BLAMISH_SNAIL(3446, 3405, 0, SOUTH, 14)
        WILL_O_THE_WISP_3483(3446, 3439, 0, SOUTH, 2)
        GHAST(3448, 3393, 0, SOUTH, 4)
        GHAST(3450, 3423, 0, SOUTH, 4)
        GHAST(3450, 3430, 0, SOUTH, 4)
        GHAST(3451, 3396, 0, SOUTH, 4)
        GHAST(3453, 3395, 0, SOUTH, 4)
        GHAST(3453, 3436, 0, SOUTH, 4)
        WILL_O_THE_WISP_3483(3454, 3410, 0, SOUTH, 2)
        GHAST(3454, 3414, 0, SOUTH, 4)
        WILL_O_THE_WISP_3483(3454, 3423, 0, SOUTH, 2)
    }
}