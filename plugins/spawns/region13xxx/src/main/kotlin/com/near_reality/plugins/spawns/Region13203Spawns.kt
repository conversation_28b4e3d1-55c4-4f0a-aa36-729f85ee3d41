package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13203Spawns : NPCSpawnsScript() {
    init {
        MALE_SLAVE_4671(3266, 9457, 0, SOUTH, 2)
        MALE_SLAVE(3266, 9459, 0, SOUTH, 5)
        FEMALE_SLAVE_4673(3267, 9428, 0, SOUTH, 2)
        MALE_SLAVE_4671(3267, 9431, 0, SOUTH, 2)
        FEMALE_SLAVE_4673(3267, 9441, 0, SOUTH, 2)
        MALE_SLAVE_4671(3267, 9443, 0, SOUTH, 2)
        GUARD_4660(3268, 9417, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        MALE_SLAVE_4671(3269, 9420, 0, SOUTH, 2)
        FEMALE_SLAVE_4673(3270, 9415, 0, SOUTH, 2)
        MALE_SLAVE_4670(3271, 9450, 0, SOUTH, 2)
        MALE_SLAVE_4670(3272, 9429, 0, SOUTH, 2)
        FEMALE_SLAVE_4672(3272, 9433, 0, SOUTH, 0)
        FEMALE_SLAVE_4672(3272, 9452, 0, SOUTH, 0)
        ROWDY_SLAVE(3273, 9420, 0, SOUTH, 5)
        FEMALE_SLAVE_4672(3273, 9422, 0, SOUTH, 0)
        MALE_SLAVE_4671(3273, 9430, 0, SOUTH, 2)
        FEMALE_SLAVE_4673(3273, 9460, 0, SOUTH, 2)
        MALE_SLAVE_4671(3274, 9465, 0, SOUTH, 2)
        FEMALE_SLAVE_4672(3275, 9459, 0, SOUTH, 0)
        FEMALE_SLAVE_4673(3276, 9434, 0, SOUTH, 2)
        MALE_SLAVE_4670(3276, 9464, 0, SOUTH, 2)
        GUARD_4660(3277, 9452, 0, SOUTH, 3)
        FEMALE_SLAVE_4672(3277, 9454, 0, SOUTH, 0)
        GUARD_4668(3279, 9414, 0, SOUTH, 2)
        GUARD_4667(3279, 9416, 0, SOUTH, 2)
        MALE_SLAVE_4670(3281, 9438, 0, SOUTH, 2)
        MALE_SLAVE_4671(3282, 9446, 0, SOUTH, 2)
        GUARD_4660(3286, 9466, 0, SOUTH, 3)
        GUARD_4665(3287, 9414, 0, SOUTH, 2)
        GUARD_4663(3287, 9416, 0, SOUTH, 2)
        ROWDY_SLAVE(3288, 9446, 0, SOUTH, 5)
        FEMALE_SLAVE_4672(3289, 9459, 0, SOUTH, 0)
        FEMALE_SLAVE_4672(3289, 9468, 0, SOUTH, 0)
        MALE_SLAVE_4671(3290, 9466, 0, SOUTH, 2)
        GUARD_4666(3291, 9423, 0, SOUTH, 2)
        MALE_SLAVE_4671(3292, 9413, 0, SOUTH, 2)
        MALE_SLAVE_4670(3292, 9460, 0, SOUTH, 2)
        GUARD_4666(3293, 9435, 0, SOUTH, 2)
        FEMALE_SLAVE_4673(3294, 9413, 0, SOUTH, 2)
        MALE_SLAVE_4671(3295, 9468, 0, SOUTH, 2)
        MALE_SLAVE_4670(3297, 9412, 0, SOUTH, 2)
        FEMALE_SLAVE_4672(3297, 9426, 0, SOUTH, 0)
        MALE_SLAVE_4670(3297, 9463, 0, SOUTH, 2)
        FEMALE_SLAVE_4672(3297, 9468, 0, SOUTH, 0)
        FEMALE_SLAVE_4672(3298, 9413, 0, SOUTH, 0)
        GUARD_4660(3298, 9432, 0, SOUTH, 3)
        MALE_SLAVE_4670(3298, 9438, 0, SOUTH, 2)
        MALE_SLAVE_4670(3299, 9425, 0, SOUTH, 2)
        GUARD_4661(3299, 9467, 0, SOUTH, 3)
        FEMALE_SLAVE_4673(3300, 9412, 0, SOUTH, 2)
        MALE_SLAVE_4670(3300, 9453, 0, SOUTH, 2)
        MALE_SLAVE_4670(3301, 9413, 0, SOUTH, 2)
        FEMALE_SLAVE_4673(3301, 9425, 0, SOUTH, 2)
        6230(3302, 9466, 0, SOUTH, 5)
        MALE_SLAVE_4671(3303, 9425, 0, SOUTH, 2)
        MALE_SLAVE_4670(3304, 9451, 0, SOUTH, 2)
        FEMALE_SLAVE_4672(3305, 9443, 0, SOUTH, 0)
        FEMALE_SLAVE_4672(3306, 9412, 0, SOUTH, 0)
        FEMALE_SLAVE_4672(3306, 9425, 0, SOUTH, 0)
        MALE_SLAVE_4670(3307, 9424, 0, SOUTH, 2)
        MALE_SLAVE_4670(3307, 9457, 0, SOUTH, 2)
        FEMALE_SLAVE_4673(3309, 9422, 0, SOUTH, 2)
        FEMALE_SLAVE_4672(3314, 9458, 0, SOUTH, 0)
        GUARD_4660(3317, 9441, 0, SOUTH, 3)
        MALE_SLAVE_4670(3320, 9465, 0, SOUTH, 2)
        FEMALE_SLAVE_4672(3322, 9426, 0, SOUTH, 0)
        MALE_SLAVE_4670(3323, 9432, 0, SOUTH, 2)
        MALE_SLAVE_4671(3323, 9439, 0, SOUTH, 2)
    }
}