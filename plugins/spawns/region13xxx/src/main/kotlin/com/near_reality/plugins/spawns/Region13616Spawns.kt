package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13616Spawns : NPCSpawnsScript() {
    init {
        GOLDEN_WARBLER(3402, 3095, 0, SOUTH, 6)
        ORANGE_SALAMANDER(3402, 3134, 0, SOUTH, 2)
        ORANGE_SALAMANDER(3403, 3090, 0, SOUTH, 2)
        ORANGE_SALAMANDER(3405, 3101, 0, SOUTH, 2)
        ORANGE_SALAMANDER(3405, 3133, 0, SOUTH, 2)
        GOLDEN_WARBLER(3406, 3097, 0, SOUTH, 6)
        GOLDEN_WARBLER(3407, 3085, 0, SOUTH, 6)
        ORANGE_SALAMANDER(3407, 3132, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        ORANGE_SALAMANDER(3408, 3095, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        GOLDEN_WARBLER(3410, 3085, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        ORANGE_SALAMANDER(3410, 3088, 0, SOUTH, 2)
        ORANGE_SALAMANDER(3412, 3081, 0, SOUTH, 2)
        ORANGE_SALAMANDER(3414, 3075, 0, SOUTH, 2)
        ORANGE_SALAMANDER(3415, 3080, 0, SOUTH, 2)
        ORANGE_SALAMANDER(3417, 3073, 0, SOUTH, 2)
    }
}