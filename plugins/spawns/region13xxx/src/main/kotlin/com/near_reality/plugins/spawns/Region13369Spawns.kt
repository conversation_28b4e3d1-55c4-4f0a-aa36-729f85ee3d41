package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13369Spawns : NPCSpawnsScript() {
    init {
        GREEN_DRAGON_264(3331, 3672, 0, SOUTH, 3)
        GREEN_DRAGON_263(3332, 3693, 0, SOUTH, 4)
        GREEN_DRAGON_261(3333, 3685, 0, SOUTH, 4)
        GREEN_DRAGON_262(3333, 3699, 0, <PERSON>OUTH, 4)
        GREEN_DRAGON_261(3337, 3701, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        GREEN_DRAGON(3338, 3676, 0, SOUTH, 2)
        GREEN_DRAGON_263(3339, 3683, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        GREEN_DRAGON(3339, 3694, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        GREEN_DRAGON(3346, 3707, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        GREEN_DRAGON_264(3347, 3694, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        GREEN_DRAGON_262(3348, 3685, 0, SOUTH, 4)
    }
}