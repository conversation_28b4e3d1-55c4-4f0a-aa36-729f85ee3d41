package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13206Spawns : NPCSpawnsScript() {
    init {
        CAVE_GOBLIN_MINER_5331(3312, 9646, 0, SOUTH, 5)
        CAVE_GOBLIN_MINER(3312, 9652, 0, SOUTH, 5)
        CAVE_GOBLIN_MINER_5333(3314, 9630, 0, SOUTH, 5)
        CAVE_GOBLIN_MINER_5331(3314, 9636, 0, SOUTH, 5)
        CAVE_GOBLIN_MINER_5332(3315, 9620, 0, SOUTH, 5)
        CAVE_GOBLIN_MINER(3316, 9632, 0, SOUTH, 5)
        CAVE_GOBLIN_GUARD(3318, 9602, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        5324(3319, 9615, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        CAVE_GOBLIN_GUARD_5335(3320, 9632, 0, SOUTH, 7)
        CAVE_GOBLIN_MINER_5339(3321, 9646, 0, SOUTH, 3)
        CAVE_GOBLIN_GUARD_5335(3322, 9617, 0, SOUTH, 7)
        CAVE_GOBLIN_GUARD(3323, 9645, 0, SOUTH, 4)
        NARDOK(3324, 9607, 0, WEST, 0)
        CAVE_GOBLIN_MINER_5332(3324, 9621, 0, SOUTH, 5)
    }
}