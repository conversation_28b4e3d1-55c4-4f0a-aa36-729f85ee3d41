package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13470Spawns : NPCSpawnsScript() {
    init {
        LESSER_DEMON_7866(3332, 10125, 0, SOUTH, 13)
        LESSER_DEMON_7866(3333, 10130, 0, SOUTH, 13)
        LESSER_DEMON_7865(3335, 10138, 0, SOUTH, 11)
        LESSER_DEMON_7867(3337, 10134, 0, SOUTH, 4)
        LESSER_DEMON_7867(3339, 10144, 0, SOUTH, 4)
        LESSER_DEMON_7865(3340, 10130, 0, SOUTH, 11)
        LESSER_DEMON_7865(3341, 10138, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        BLACK_DRAGON_7861(3356, 10157, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        BLACK_DRAGON_7861(3361, 10159, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        BLACK_DRAGON_7861(3366, 10155, 0, SOUTH, 6)
        BLACK_DEMON_7876(3354, 10120, 0, SOUTH, 4)
        BLACK_DEMON_7876(3359, 10114, 0, SOUTH, 4)
        BLACK_DEMON_7876(3361, 10125, 0, SOUTH, 4)
        BLACK_DEMON_7876(3367, 10117, 0, SOUTH, 4)
        GREATER_NECHRYAEL(3335, 10114, 0, SOUTH, 4)
        GREATER_NECHRYAEL(3332, 10101, 0, SOUTH, 4)
        GREATER_NECHRYAEL(3334, 10105, 0, SOUTH, 4)
        GREATER_NECHRYAEL(3334, 10110, 0, SOUTH, 4)
        GREATER_NECHRYAEL(3336, 10102, 0, SOUTH, 4)
        GREATER_NECHRYAEL(3337, 10107, 0, SOUTH, 4)
        GREATER_NECHRYAEL(3339, 10104, 0, SOUTH, 4)
        GREATER_NECHRYAEL(3341, 10101, 0, SOUTH, 4)
        ABYSSAL_DEMON_416(3335, 10161, 0, SOUTH, 4)
        ABYSSAL_DEMON_416(3339, 10164, 0, SOUTH, 4)
        ABYSSAL_DEMON_416(3340, 10159, 0, SOUTH, 4)
        ABYSSAL_DEMON_416(3340, 10168, 0, SOUTH, 4)
        ABYSSAL_DEMON_416(3343, 10163, 0, SOUTH, 4)
        ABYSSAL_DEMON_416(3345, 10158, 0, SOUTH, 4)
        ABYSSAL_DEMON_416(3345, 10167, 0, SOUTH, 4)
        ABYSSAL_DEMON_416(3347, 10161, 0, SOUTH, 4)
    }
}