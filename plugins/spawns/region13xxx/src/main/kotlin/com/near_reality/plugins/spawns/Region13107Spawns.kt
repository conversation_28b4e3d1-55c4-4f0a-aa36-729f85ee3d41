package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13107Spawns : NPCSpawnsScript() {
    init {
        SCORPION_3024(3292, 3297, 0, SOUT<PERSON>, 12)
        4983(3295, 3284, 0, SOUTH, 2)
        SCORPION_3024(3298, 3299, 0, SOUTH, 12)
        SCORPION_3024(3298, 3304, 0, SOUTH, 12)
        SCORPION_3024(3298, 3311, 0, SOUTH, 12)
        IMP_5007(3299, 3273, 0, SOUTH, 100)
        SCORPION_3024(3299, 3288, 0, SOUTH, 12)
        SCORPION_3024(3300, 3315, 0, SOUTH, 12)
        SCORPION_3024(3301, 3278, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        SCORPION_3024(3302, 3306, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        SCORPION_3024(3303, 3292, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        CAMEL(3310, 3269, 0, SOUTH, 5)
    }
}