package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13878Spawns : NPCSpawnsScript() {
    init {
        WILL_O_THE_WISP_3483(3459, 3457, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3460, 3462, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3462, 3459, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3463, 3490, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3464, 3511, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3465, 3466, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        LESABR(3465, 3478, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        WILL_O_THE_WISP_3483(3466, 3495, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        WILL_O_THE_WISP_3483(3466, 3497, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3467, 3485, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3467, 3497, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3467, 3509, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3469, 3470, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3470, 3469, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3471, 3477, 0, SOUTH, 2)
        JOSEPH(3474, 3474, 0, SOUTH, 5)
        WILL_O_THE_WISP_3483(3474, 3505, 0, SOUTH, 2)
        FIDELIO(3475, 3497, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3476, 3507, 0, SOUTH, 2)
        YADVIGA(3479, 3492, 0, SOUTH, 5)
        NIKITA(3479, 3498, 0, SOUTH, 5)
        WILL_O_THE_WISP_3483(3479, 3511, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3480, 3468, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3480, 3470, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3482, 3469, 0, SOUTH, 2)
        GEORGY(3483, 3478, 0, SOUTH, 5)
        SOFIYA(3483, 3495, 0, SOUTH, 5)
        WILL_O_THE_WISP_3483(3483, 3511, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3484, 3464, 0, SOUTH, 2)
        IRINA(3485, 3488, 0, SOUTH, 5)
        WILL_O_THE_WISP_3483(3485, 3509, 0, SOUTH, 2)
        BORIS(3489, 3490, 0, SOUTH, 5)
        WILL_O_THE_WISP_3483(3490, 3461, 0, SOUTH, 2)
        VERA(3490, 3473, 0, SOUTH, 4)
        IMRE(3490, 3474, 0, SOUTH, 4)
        WILL_O_THE_WISP_3483(3491, 3460, 0, SOUTH, 2)
        ROAVAR(3493, 3471, 0, SOUTH, 5)
        WILL_O_THE_WISP_3483(3494, 3461, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3494, 3512, 0, SOUTH, 2)
        NIKOLAI(3495, 3476, 0, SOUTH, 5)
        MILLA(3497, 3474, 0, SOUTH, 2)
        MALAK(3497, 3478, 0, WEST, 0)
        YURI(3497, 3497, 0, SOUTH, 5)
        WILL_O_THE_WISP_3483(3498, 3461, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3499, 3513, 0, SOUTH, 2)
        SVETLANA(3501, 3493, 0, SOUTH, 5)
        WILL_O_THE_WISP_3483(3501, 3513, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3502, 3464, 0, SOUTH, 2)
        LEV(3502, 3487, 0, SOUTH, 5)
        6269(3503, 3477, 0, WEST, 0)
        WILL_O_THE_WISP_3483(3504, 3463, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3506, 3512, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3506, 3515, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3507, 3515, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3509, 3512, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3511, 3488, 0, SOUTH, 2)
        MAZCHNA(3511, 3509, 0, SOUTH, 3)
        WILL_O_THE_WISP_3483(3513, 3467, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3513, 3487, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3513, 3489, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3513, 3503, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3514, 3490, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3515, 3495, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3515, 3499, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3516, 3469, 0, SOUTH, 2)
        LILIYA(3479, 3498, 1, SOUTH, 5)
        TAXIDERMIST(3480, 3484, 0, SOUTH, 5)
        GALINA(3490, 3472, 1, SOUTH, 2)
        SBOTT(3490, 3503, 0, SOUTH, 2)
        KSENIA(3498, 3472, 1, SOUTH, 3)
        ALEXIS(3499, 3474, 1, SOUTH, 3)
        BARKER(3499, 3506, 0, SOUTH, 2)
        ZOJA(3505, 3491, 0, SOUTH, 4)
        RUFUS_6478(3507, 3496, 0, SOUTH, 2)
        EDUARD(3511, 3482, 0, SOUTH, 3)
        BANKER_2633(3514, 3479, 0, WEST, 0)
        BANKER_2633(3514, 3481, 0, WEST, 0)
        BIGREDJAPAN(3511, 3480, 2, SOUTH, 5)
        SLEEPWALKER_9802(3495, 3501, 0, SOUTH, 5)
        SLEEPWALKER_9802(3495, 3501, 0, SOUTH, 5)
    }
}