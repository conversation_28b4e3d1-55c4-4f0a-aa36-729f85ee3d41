package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13358Spawns : NPCSpawnsScript() {
    init {
        DESERT_SNAKE(3328, 2952, 0, SOUTH, 3)
        DESERT_SNAKE(3328, 2957, 0, SOUTH, 3)
        DESERT_SNAKE(3331, 2955, 0, SOUTH, 3)
        DESERT_SNAKE(3331, 2961, 0, SOUTH, 3)
        6138(3333, 2952, 0, <PERSON>OUTH, 8)
        ALI_THE_OPERATOR(3334, 2949, 0, SOUT<PERSON>, 2)
        6136(3334, 2956, 0, NORTH, 0)
        6138(3338, 2946, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        6138(3338, 2950, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        ARISTARCHUS(3338, 2961, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        6140(3342, 2954, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        6138(3342, 2958, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        ALI_THE_CAMEL(3342, 2962, 0, SO<PERSON>H, 2)
        ALI_THE_CAMEL(3343, 2964, 0, SOUTH, 2)
        6138(3344, 2950, 0, SOUTH, 8)
        6138(3346, 2945, 0, SOUTH, 8)
        6138(3346, 2954, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        ALI_THE_HAG(3346, 2987, 0, <PERSON>OUTH, 3)
        6138(3347, 2961, 0, SOUTH, 8)
        6141(3349, 2954, 0, SOUTH, 7)
        ALI_THE_CAMEL_MAN(3350, 2967, 0, SOUTH, 2)
        MONKEY(3350, 3000, 0, SOUTH, 5)
        RUG_MERCHANT_20(3350, 3001, 0, SOUTH, 0)
        6138(3351, 2961, 0, SOUTH, 8)
        6135(3351, 2971, 0, EAST, 0)
        6138(3352, 2951, 0, SOUTH, 8)
        STREET_URCHIN(3352, 2958, 0, SOUTH, 2)
        ALI_THE_KEBAB_SELLER(3352, 2974, 0, SOUTH, 2)
        1974(3353, 3002, 0, SOUTH, 0)
        SNAKE_3545(3354, 2952, 0, SOUTH, 0)
        ALI_THE_SNAKE_CHARMER(3354, 2953, 0, SOUTH_EAST, 0)
        1975(3354, 2987, 0, SOUTH, 3)
        1976(3354, 2994, 0, SOUTH, 4)
        1975(3354, 3000, 0, SOUTH, 3)
        6139(3355, 2949, 0, SOUTH, 3)
        6140(3355, 2957, 0, SOUTH, 7)
        STREET_URCHIN(3355, 2972, 0, SOUTH, 2)
        1976(3357, 3001, 0, SOUTH, 4)
        6139(3358, 2969, 0, SOUTH, 3)
        6141(3359, 2975, 0, SOUTH, 7)
        MARKET_SELLER(3359, 2987, 0, SOUTH, 2)
        BANKNOTE_EXCHANGE_MERCHANT(3359, 2990, 0, SOUTH, 2)
        COWARDLY_BANDIT(3359, 3000, 0, SOUTH, 9)
        DRUNKEN_ALI(3360, 2957, 0, NORTH, 0)
        6134(3360, 2970, 0, SOUTH, 3)
        1976(3360, 2999, 0, SOUTH, 4)
        ALI_THE_BARMAN(3361, 2955, 0, SOUTH, 2)
        6141(3361, 2967, 0, SOUTH, 7)
        6140(3361, 2969, 0, SOUTH, 7)
        1975(3361, 3003, 0, SOUTH, 3)
        6139(3362, 2975, 0, SOUTH, 3)
        1975(3362, 2988, 0, SOUTH, 3)
        6140(3363, 2963, 0, SOUTH, 7)
        STREET_URCHIN(3364, 2980, 0, SOUTH, 2)
        ALI_THE_DYER(3364, 3001, 0, SOUTH, 3)
        6141(3365, 2991, 0, SOUTH, 7)
        6141(3367, 2983, 0, SOUTH, 7)
        1976(3367, 2997, 0, SOUTH, 4)
        1975(3371, 2984, 0, SOUTH, 3)
        6140(3367, 2991, 1, SOUTH, 7)
        6139(3356, 2984, 2, SOUTH, 3)
    }
}