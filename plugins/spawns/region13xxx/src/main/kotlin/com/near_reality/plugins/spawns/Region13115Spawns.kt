package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13115Spawns : NPCSpawnsScript() {
    init {
        POISON_SPIDER(3268, 3823, 0, <PERSON>OUT<PERSON>, 11)
        POISON_SPIDER(3269, 3824, 0, <PERSON>OUT<PERSON>, 11)
        POISON_SPIDER(3270, 3822, 0, SOUTH, 11)
        POISON_SPIDER(3271, 3795, 0, <PERSON>OUTH, 11)
        POISON_SPIDER(3271, 3797, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        POISON_SPIDER(3272, 3798, 0, <PERSON><PERSON><PERSON>H, 11)
        PO<PERSON><PERSON>_SPIDER(3273, 3795, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        PO<PERSON><PERSON>_SPIDER(3275, 3807, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        P<PERSON><PERSON><PERSON>_SPIDER(3276, 3782, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        POISON_SPIDER(3276, 3803, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        POISON_SPIDER(3276, 3815, 0, SOUTH, 11)
        PO<PERSON><PERSON>_SPIDER(3277, 3783, 0, SOUTH, 11)
        POIS<PERSON>_SPIDER(3278, 3813, 0, SOUTH, 11)
        PO<PERSON>ON_SPIDER(3278, 3816, 0, <PERSON>O<PERSON>H, 11)
        PO<PERSON><PERSON>_SPIDER(3278, 3832, 0, <PERSON>OUTH, 11)
        POIS<PERSON>_SPIDER(3280, 3807, 0, SOUTH, 11)
        POISON_SPIDER(3280, 3830, 0, SOUTH, 11)
        POISON_SPIDER(3281, 3833, 0, SOUTH, 11)
        POISON_SPIDER(3283, 3791, 0, SOUTH, 11)
        POISON_SPIDER(3285, 3788, 0, SOUTH, 11)
        POISON_SPIDER(3285, 3811, 0, SOUTH, 11)
        POISON_SPIDER(3286, 3790, 0, SOUTH, 11)
        POISON_SPIDER(3286, 3794, 0, SOUTH, 11)
        POISON_SPIDER(3286, 3823, 0, SOUTH, 11)
        POISON_SPIDER(3288, 3790, 0, SOUTH, 11)
        POISON_SPIDER(3288, 3801, 0, SOUTH, 11)
        POISON_SPIDER(3290, 3807, 0, SOUTH, 11)
        POISON_SPIDER(3290, 3823, 0, SOUTH, 11)
        POISON_SPIDER(3292, 3809, 0, SOUTH, 11)
        POISON_SPIDER(3293, 3807, 0, SOUTH, 11)
        POISON_SPIDER(3293, 3833, 0, SOUTH, 11)
        POISON_SPIDER(3294, 3800, 0, SOUTH, 11)
        POISON_SPIDER(3294, 3819, 0, SOUTH, 11)
        POISON_SPIDER(3294, 3832, 0, SOUTH, 11)
        POISON_SPIDER(3295, 3818, 0, SOUTH, 11)
        POISON_SPIDER(3295, 3833, 0, SOUTH, 11)
        POISON_SPIDER(3297, 3793, 0, SOUTH, 11)
        POISON_SPIDER(3297, 3825, 0, SOUTH, 11)
        POISON_SPIDER(3299, 3806, 0, SOUTH, 11)
        POISON_SPIDER(3300, 3830, 0, SOUTH, 11)
        POISON_SPIDER(3302, 3786, 0, SOUTH, 11)
        POISON_SPIDER(3303, 3787, 0, SOUTH, 11)
        POISON_SPIDER(3303, 3800, 0, SOUTH, 11)
        POISON_SPIDER(3304, 3785, 0, SOUTH, 11)
        POISON_SPIDER(3305, 3800, 0, SOUTH, 11)
        POISON_SPIDER(3305, 3833, 0, SOUTH, 11)
        POISON_SPIDER(3306, 3799, 0, SOUTH, 11)
        POISON_SPIDER(3306, 3819, 0, SOUTH, 11)
        POISON_SPIDER(3306, 3834, 0, SOUTH, 11)
        POISON_SPIDER(3307, 3801, 0, SOUTH, 11)
        POISON_SPIDER(3307, 3808, 0, SOUTH, 11)
        POISON_SPIDER(3307, 3814, 0, SOUTH, 11)
        POISON_SPIDER(3308, 3821, 0, SOUTH, 11)
        POISON_SPIDER(3310, 3818, 0, SOUTH, 11)
        POISON_SPIDER(3317, 3795, 0, SOUTH, 11)
        POISON_SPIDER(3318, 3799, 0, SOUTH, 11)
        POISON_SPIDER(3319, 3830, 0, SOUTH, 11)
        POISON_SPIDER(3320, 3798, 0, SOUTH, 11)
        POISON_SPIDER(3321, 3828, 0, SOUTH, 11)
        POISON_SPIDER(3321, 3832, 0, SOUTH, 11)
        POISON_SPIDER(3322, 3829, 0, SOUTH, 11)
        POISON_SPIDER(3323, 3794, 0, SOUTH, 11)
    }
}