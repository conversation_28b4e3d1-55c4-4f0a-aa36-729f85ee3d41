package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13460Spawns : NPCSpawnsScript() {
    init {
        KALPHITE_SOLDIER_958(3329, 9487, 0, SOUTH, 11)
        KALPHITE_WORKER_956(3331, 9501, 0, SOUTH, 8)
        KALPHITE_SOLDIER_958(3333, 9490, 0, SOUTH, 11)
        KALPHITE_SOLDIER_958(3336, 9494, 0, <PERSON>OUTH, 11)
    }
}