package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13465Spawns : NPCSpawnsScript() {
    init {
        DOUG_DEEPING(3351, 9820, 0, SOUTH, 5)
        SKELETON_71(3368, 9812, 0, SOUTH, 7)
        SKELETON(3373, 9812, 0, SOUTH, 7)
        SKELETON_72(3376, 9815, 0, SOUTH, 8)
        SKELETON_73(3377, 9807, 0, SOUTH, 8)
        SKELETON_73(3377, 9818, 0, <PERSON>OUT<PERSON>, 8)
        SKELETON(3378, 9812, 0, SOUTH, 7)
        SKELETON_71(3378, 9821, 0, <PERSON>O<PERSON><PERSON>, 7)
        SKELETON_72(3380, 9820, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        SKELETON(3382, 9817, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        SKELETON_71(3383, 9810, 0, <PERSON><PERSON>UTH, 7)
    }
}