package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13877Spawns : NPCSpawnsScript() {
    init {
        MYRE_BLAMISH_SNAIL(3456, 3417, 0, SOUTH, 18)
        OCHRE_BLAMISH_SNAIL(3456, 3418, 0, SOUTH, 14)
        GHAST(3457, 3411, 0, SOUTH, 4)
        GHAST(3458, 3401, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3460, 3418, 0, SOUTH, 18)
        MYRE_BLAMISH_SNAIL(3460, 3424, 0, SOUTH, 18)
        GHAST(3461, 3406, 0, <PERSON>OUTH, 4)
        GHAST(3461, 3437, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        GHAST(3462, 3399, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        MYRE_BLAMISH_SNAIL(3462, 3432, 0, <PERSON>OUT<PERSON>, 18)
        MYRE_BLAMISH_SNAIL_2649(3463, 3404, 0, SO<PERSON>H, 10)
        G<PERSON><PERSON>(3463, 3416, 0, SOUTH, 4)
        G<PERSON>ST(3463, 3425, 0, SOUTH, 4)
        G<PERSON>ST(3463, 3448, 0, SOUTH, 4)
        G<PERSON>ST(3464, 3441, 0, SOUTH, 4)
        G<PERSON>ST(3466, 3433, 0, SOUTH, 4)
        OCHRE_BLAMISH_SNAIL(3467, 3404, 0, SOUTH, 14)
        OCHRE_BLAMISH_SNAIL_2651(3467, 3409, 0, SOUTH, 15)
        OCHRE_BLAMISH_SNAIL_2651(3467, 3410, 0, SOUTH, 15)
        GHAST(3467, 3415, 0, SOUTH, 4)
        GHAST(3469, 3397, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3469, 3432, 0, SOUTH, 18)
        GHAST(3470, 3426, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL_2649(3471, 3399, 0, SOUTH, 10)
        OCHRE_BLAMISH_SNAIL(3471, 3402, 0, SOUTH, 14)
        MYRE_BLAMISH_SNAIL_2649(3472, 3415, 0, SOUTH, 10)
        MYRE_BLAMISH_SNAIL_2649(3472, 3418, 0, SOUTH, 10)
        GHAST(3472, 3420, 0, SOUTH, 4)
        GHAST(3473, 3394, 0, SOUTH, 4)
        GHAST(3473, 3412, 0, SOUTH, 4)
        GHAST(3475, 3401, 0, SOUTH, 4)
        GHAST(3475, 3440, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL_2649(3477, 3419, 0, SOUTH, 10)
        GHAST(3478, 3429, 0, SOUTH, 4)
        FISHING_SPOT_2653(3479, 3434, 0, SOUTH, 0)
        OCHRE_BLAMISH_SNAIL_2651(3480, 3424, 0, SOUTH, 15)
        GHAST(3481, 3406, 0, SOUTH, 4)
        FISHING_SPOT_2653(3481, 3437, 0, SOUTH, 0)
        MYRE_BLAMISH_SNAIL_2649(3481, 3439, 0, SOUTH, 10)
        MYRE_BLAMISH_SNAIL_2649(3481, 3441, 0, SOUTH, 10)
        MYRE_BLAMISH_SNAIL(3481, 3442, 0, SOUTH, 18)
        GHAST(3482, 3419, 0, SOUTH, 4)
        FISHING_SPOT_2653(3482, 3430, 0, SOUTH, 0)
        GHAST(3482, 3431, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3482, 3444, 0, SOUTH, 18)
        OCHRE_BLAMISH_SNAIL(3483, 3440, 0, SOUTH, 14)
        MYRE_BLAMISH_SNAIL(3483, 3445, 0, SOUTH, 18)
        FISHING_SPOT_2653(3483, 3449, 0, SOUTH, 0)
        OCHRE_BLAMISH_SNAIL_2651(3484, 3442, 0, SOUTH, 15)
        GHAST(3485, 3436, 0, SOUTH, 4)
        BRUISE_BLAMISH_SNAIL_2652(3485, 3441, 0, SOUTH, 16)
        OCHRE_BLAMISH_SNAIL(3485, 3443, 0, SOUTH, 14)
        GHAST(3485, 3451, 0, SOUTH, 4)
        GHAST(3486, 3399, 0, SOUTH, 4)
        GHAST(3486, 3407, 0, SOUTH, 4)
        BRUISE_BLAMISH_SNAIL(3486, 3440, 0, SOUTH, 22)
        BLOOD_BLAMISH_SNAIL_2650(3486, 3442, 0, SOUTH, 19)
        GHAST(3487, 3423, 0, SOUTH, 4)
        GHAST(3490, 3395, 0, SOUTH, 4)
        FISHING_SPOT_2653(3490, 3444, 0, SOUTH, 0)
        GHAST(3490, 3446, 0, SOUTH, 4)
        GHAST(3491, 3430, 0, SOUTH, 4)
        GHAST(3492, 3405, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3493, 3393, 0, SOUTH, 18)
        BARK_BLAMISH_SNAIL(3495, 3418, 0, SOUTH, 15)
        GHAST(3495, 3439, 0, SOUTH, 4)
        GHAST(3496, 3395, 0, SOUTH, 4)
        OCHRE_BLAMISH_SNAIL(3496, 3421, 0, SOUTH, 14)
        BLOOD_BLAMISH_SNAIL_2650(3496, 3444, 0, SOUTH, 19)
        GHAST(3497, 3409, 0, SOUTH, 4)
        GHAST(3497, 3417, 0, SOUTH, 4)
        BARK_BLAMISH_SNAIL(3498, 3420, 0, SOUTH, 15)
        GHAST(3498, 3452, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3499, 3394, 0, SOUTH, 18)
        GHAST(3499, 3446, 0, SOUTH, 4)
        GHAST(3500, 3422, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3500, 3425, 0, SOUTH, 18)
        GHAST(3501, 3403, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3502, 3425, 0, SOUTH, 18)
        MYRE_BLAMISH_SNAIL(3504, 3404, 0, SOUTH, 18)
        MYRE_BLAMISH_SNAIL(3504, 3422, 0, SOUTH, 18)
        OCHRE_BLAMISH_SNAIL(3504, 3424, 0, SOUTH, 14)
        OCHRE_BLAMISH_SNAIL(3505, 3418, 0, SOUTH, 14)
        BARK_BLAMISH_SNAIL(3505, 3433, 0, SOUTH, 15)
        MYRE_BLAMISH_SNAIL(3506, 3420, 0, SOUTH, 18)
        GHAST(3507, 3428, 0, SOUTH, 4)
        GHAST(3508, 3393, 0, SOUTH, 4)
        GHAST(3508, 3400, 0, SOUTH, 4)
        8295(3508, 3440, 0, SOUTH, 5)
        GHAST(3509, 3421, 0, SOUTH, 4)
        BARK_BLAMISH_SNAIL(3510, 3435, 0, SOUTH, 15)
        MYRE_BLAMISH_SNAIL(3511, 3393, 0, SOUTH, 18)
        GHAST(3511, 3397, 0, SOUTH, 4)
        GHAST(3511, 3408, 0, SOUTH, 4)
        GHAST(3514, 3418, 0, SOUTH, 4)
        GHAST(3515, 3414, 0, SOUTH, 4)
        GHAST(3516, 3404, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3516, 3409, 0, SOUTH, 18)
        GHAST(3516, 3448, 0, SOUTH, 4)
        BRUISE_BLAMISH_SNAIL(3517, 3437, 0, SOUTH, 22)
        GHAST(3518, 3399, 0, SOUTH, 4)
        GHAST(3518, 3417, 0, SOUTH, 4)
        GHAST(3518, 3428, 0, SOUTH, 4)
        BLOOD_BLAMISH_SNAIL(3519, 3439, 0, SOUTH, 30)
    }
}