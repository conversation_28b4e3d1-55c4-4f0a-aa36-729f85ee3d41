package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13977Spawns : NPCSpawnsScript() {
    init {
        GIANT_RAT_2863(3463, 9813, 0, SOUTH, 6)
        GIANT_RAT_2862(3464, 9818, 0, SOUTH, 6)
        GOBLIN_3045(3465, 9797, 0, SOUTH, 22)
        GOBLIN_3046(3466, 9796, 0, SOUTH, 5)
        GOBLIN_3028(3466, 9798, 0, SOUTH, 34)
        DEADLY_RED_SPIDER(3466, 9815, 0, SOUTH, 8)
        POISON_SPIDER(3467, 9797, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        HOBGOBLIN_3050(3469, 9796, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        HOBGOBLIN_3049(3469, 9797, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        DOG(3469, 9806, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        GIANT_RAT_2864(3469, 9816, 0, SOUTH, 6)
        DOG(3470, 9804, 0, SOUTH, 4)
        POISON_SPIDER(3471, 9796, 0, SOUTH, 11)
        DEADLY_RED_SPIDER(3471, 9806, 0, SOUTH, 8)
        RAT_2854(3476, 9842, 0, SOUTH, 14)
        RAT_2854(3479, 9835, 0, SOUTH, 14)
        RAT_2854(3479, 9840, 0, SOUTH, 14)
        RAT_2854(3479, 9841, 0, SOUTH, 14)
        GIANT_RAT_2856(3480, 9830, 0, SOUTH, 6)
        RAT_2854(3480, 9841, 0, SOUTH, 14)
        RAT_2854(3481, 9828, 0, SOUTH, 14)
        GIANT_RAT_2856(3484, 9824, 0, SOUTH, 6)
        RAT_2854(3485, 9844, 0, SOUTH, 14)
        RAT_2854(3485, 9845, 0, SOUTH, 14)
        RAT_2854(3486, 9822, 0, SOUTH, 14)
        RAT_2854(3486, 9843, 0, SOUTH, 14)
        RAT_2854(3486, 9844, 0, SOUTH, 14)
        RAT_2854(3486, 9845, 0, SOUTH, 14)
        RAT_2854(3487, 9843, 0, SOUTH, 14)
        RAT_2854(3487, 9844, 0, SOUTH, 14)
        RAT_2854(3492, 9815, 0, SOUTH, 14)
        GIANT_RAT_2856(3495, 9812, 0, SOUTH, 6)
        RAT_2854(3497, 9808, 0, SOUTH, 14)
        GIANT_RAT_2856(3502, 9806, 0, SOUTH, 6)
        RAT_2854(3502, 9808, 0, SOUTH, 14)
        9803(3504, 9833, 0, SOUTH, 5)
        9620(3506, 9838, 0, SOUTH, 5)
        9626(3509, 9831, 0, SOUTH, 5)
        8296(3510, 9836, 0, SOUTH, 5)
        9630(3513, 9843, 0, SOUTH, 5)
        9628(3514, 9838, 0, SOUTH, 5)
    }
}