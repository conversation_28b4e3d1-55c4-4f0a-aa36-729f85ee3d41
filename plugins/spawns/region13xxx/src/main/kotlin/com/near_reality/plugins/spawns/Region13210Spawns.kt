package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13210Spawns : NPCSpawnsScript() {
    init {
        RAT_2854(3269, 9869, 0, SOUTH, 14)
        SKELETON_74(3270, 9914, 0, SOUTH, 3)
        RAT_2854(3274, 9873, 0, SOUTH, 14)
        SKELETON_75(3274, 9913, 0, SOUTH, 3)
        RAT_2854(3275, 9865, 0, SOUTH, 14)
        SKELETON_74(3275, 9909, 0, SOUTH, 3)
        SKELETON_76(3275, 9911, 0, SOUTH, 3)
        RAT_2854(3276, 9871, 0, SOUTH, 14)
        RAT_2854(3277, 9863, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        RAT_2854(3277, 9872, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        GIANT_RAT_2863(3279, 9894, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        GIANT_RAT_2862(3283, 9896, 0, SOUTH, 6)
    }
}