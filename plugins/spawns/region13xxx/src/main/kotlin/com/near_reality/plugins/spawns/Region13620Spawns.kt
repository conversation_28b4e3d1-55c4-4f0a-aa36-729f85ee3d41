package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13620Spawns : NPCSpawnsScript() {
    init {
        GHAST(3401, 3361, 0, SOUTH, 4)
        GHAST(3404, 3371, 0, SOUTH, 4)
        GHAST(3406, 3354, 0, SOUTH, 4)
        GHAST(3406, 3386, 0, SOUTH, 4)
        GHAST(3407, 3379, 0, SOUTH, 4)
        WILL_O_THE_WISP_3483(3409, 3369, 0, SOUTH, 2)
        MYRE_BLAMISH_SNAIL(3409, 3391, 0, SOUTH, 18)
        MYRE_BLAMISH_SNAIL_2649(3411, 3361, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        GHAST(3411, 3380, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        OCHRE_BLAMISH_SNAIL_2651(3411, 3387, 0, <PERSON>OUTH, 15)
        GHAST(3412, 3357, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        MY<PERSON>_<PERSON><PERSON><PERSON>SH_SNAIL(3414, 3357, 0, SOUTH, 18)
        GHAST(3414, 3365, 0, SOUTH, 4)
        G<PERSON>ST(3415, 3372, 0, SOUTH, 4)
        G<PERSON>ST(3415, 3390, 0, SOUTH, 4)
        G<PERSON>ST(3416, 3345, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3416, 3362, 0, SOUTH, 18)
        MYRE_BLAMISH_SNAIL_2649(3416, 3363, 0, SOUTH, 10)
        MYRE_BLAMISH_SNAIL_2649(3416, 3371, 0, SOUTH, 10)
        GHAST(3417, 3337, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL_2649(3417, 3368, 0, SOUTH, 10)
        GHAST(3417, 3385, 0, SOUTH, 4)
        GHAST(3418, 3356, 0, SOUTH, 4)
        GHAST(3419, 3361, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3419, 3370, 0, SOUTH, 18)
        GHAST(3420, 3333, 0, SOUTH, 4)
        WILL_O_THE_WISP_3483(3420, 3349, 0, SOUTH, 2)
        OCHRE_BLAMISH_SNAIL_2651(3422, 3377, 0, SOUTH, 15)
        GHAST(3423, 3348, 0, SOUTH, 4)
        GHAST(3424, 3337, 0, SOUTH, 4)
        GHAST(3426, 3366, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3427, 3377, 0, SOUTH, 18)
        GHAST(3427, 3379, 0, SOUTH, 4)
        GHAST(3427, 3389, 0, SOUTH, 4)
        GHAST(3429, 3330, 0, SOUTH, 4)
        GHAST(3429, 3352, 0, SOUTH, 4)
        WILL_O_THE_WISP_3483(3430, 3340, 0, SOUTH, 2)
        GHAST(3430, 3344, 0, SOUTH, 4)
        OCHRE_BLAMISH_SNAIL(3430, 3375, 0, SOUTH, 14)
        MYRE_BLAMISH_SNAIL_2649(3430, 3380, 0, SOUTH, 10)
        GHAST(3431, 3366, 0, SOUTH, 4)
        WILL_O_THE_WISP_3483(3435, 3330, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3435, 3359, 0, SOUTH, 2)
        GHAST(3436, 3389, 0, SOUTH, 4)
        WILL_O_THE_WISP_3483(3437, 3374, 0, SOUTH, 2)
        BARK_BLAMISH_SNAIL(3439, 3354, 0, SOUTH, 15)
        OCHRE_BLAMISH_SNAIL_2651(3441, 3353, 0, SOUTH, 15)
        MYRE_BLAMISH_SNAIL(3441, 3355, 0, SOUTH, 18)
        GHAST(3441, 3364, 0, SOUTH, 4)
        GHAST(3443, 3383, 0, SOUTH, 4)
        WILL_O_THE_WISP_3483(3446, 3385, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3447, 3388, 0, SOUTH, 2)
        GHAST(3447, 3391, 0, SOUTH, 4)
        GHAST(3449, 3345, 0, SOUTH, 4)
        WILL_O_THE_WISP_3483(3450, 3370, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3450, 3387, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3451, 3336, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3452, 3355, 0, SOUTH, 2)
        GHAST(3453, 3357, 0, SOUTH, 4)
        GHAST(3453, 3386, 0, SOUTH, 4)
        GHAST(3454, 3346, 0, SOUTH, 4)
        GHAST(3454, 3369, 0, SOUTH, 4)
    }
}