package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13622Spawns : NPCSpawnsScript() {
    init {
        6223(3405, 3492, 0, SOUTH, 0)
        6222(3406, 3492, 0, SOUTH, 4)
        MONK_OF_ZAMORAK_3484(3410, 3484, 0, SOUTH, 5)
        MONK_OF_ZAMORAK_3484(3410, 3493, 0, SOUTH, 5)
        MONK_OF_ZAMORAK_3486(3411, 3489, 0, SOUTH, 5)
        GHOUL(3412, 3512, 0, SOUTH, 4)
        GHOUL(3412, 3515, 0, SOUTH, 4)
        GHOUL(3413, 3514, 0, <PERSON>O<PERSON><PERSON>, 4)
        GHOUL(3414, 3512, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        MON<PERSON>_OF_ZAMORAK_3486(3415, 3485, 0, <PERSON>OUT<PERSON>, 5)
        MONK_OF_ZAMORAK_3485(3415, 3489, 0, SOUTH, 5)
        GHOUL(3415, 3518, 0, SOUTH, 4)
        GHOUL(3416, 3509, 0, SOUTH, 4)
        GHOUL(3416, 3511, 0, SOUTH, 4)
        GHOUL(3417, 3518, 0, SOUTH, 4)
        GHOUL(3418, 3509, 0, SOUTH, 4)
        GHOUL(3419, 3512, 0, SOUTH, 4)
        GHOUL(3420, 3517, 0, SOUTH, 4)
        GHOUL(3420, 3518, 0, SOUTH, 4)
        GHOUL(3423, 3461, 0, SOUTH, 4)
        GHOUL(3426, 3465, 0, SOUTH, 4)
        GHOUL(3427, 3463, 0, SOUTH, 4)
        GHOUL(3428, 3458, 0, SOUTH, 4)
        GHOUL(3428, 3465, 0, SOUTH, 4)
        GHOUL(3430, 3462, 0, SOUTH, 4)
        GHOUL(3430, 3467, 0, SOUTH, 4)
        HIYLIK_MYNA(3432, 3487, 0, SOUTH, 2)
        GHOUL(3433, 3458, 0, SOUTH, 4)
        GHOUL(3433, 3468, 0, SOUTH, 4)
        2944(3433, 3486, 0, SOUTH, 2)
        GHOUL(3434, 3464, 0, SOUTH, 4)
        2942(3434, 3483, 0, SOUTH, 2)
        2945(3434, 3486, 0, SOUTH, 2)
        2943(3435, 3483, 0, SOUTH, 2)
        2941(3437, 3487, 0, SOUTH, 2)
        2940(3438, 3487, 0, SOUTH, 2)
        ULIZIUS(3444, 3459, 0, SOUTH, 5)
        1036(3444, 3485, 0, SOUTH, 0)
        TOOL_LEPRECHAUN(3454, 3468, 0, SOUTH, 0)
        MONK_OF_ZAMORAK_3485(3411, 3485, 1, SOUTH, 5)
        MONK_OF_ZAMORAK_3484(3412, 3489, 1, SOUTH, 5)
        MONK_OF_ZAMORAK_3484(3414, 3487, 1, SOUTH, 5)
        MONK_OF_ZAMORAK_3485(3415, 3489, 1, SOUTH, 5)
        MONK_OF_ZAMORAK_3486(3416, 3491, 1, SOUTH, 5)
        9804(3417, 3489, 2, SOUTH, 5)
    }
}