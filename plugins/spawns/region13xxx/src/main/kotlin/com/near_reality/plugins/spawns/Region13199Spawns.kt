package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13199Spawns : NPCSpawnsScript() {
    init {
        6190(3277, 9170, 0, SOUTH, 5)
        MUMMY_950(3277, 9186, 0, SOUTH, 5)
        MUMMY_949(3279, 9176, 0, SOUTH, 5)
        MUMMY_952(3284, 9189, 0, SOUTH, 5)
        MUMMY_952(3287, 9177, 0, SOUTH, 5)
        6189(3304, 9195, 0, SOUTH, 5)
        6188(3304, 9196, 0, SOUTH, 5)
        6193(3305, 9193, 0, <PERSON>OUT<PERSON>, 5)
        6193(3305, 9197, 0, SOUT<PERSON>, 5)
        MUMMY_953(3306, 9183, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        6191(3307, 9196, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        6193(3308, 9194, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        MUMMY_951(3310, 9171, 0, SOUTH, 5)
    }
}