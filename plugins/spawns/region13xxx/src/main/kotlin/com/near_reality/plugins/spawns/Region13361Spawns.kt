package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13361Spawns : NPCSpawnsScript() {
    init {
        HILL_GIANT_10375(3361, 3147, 0, SOUTH, 5)
        DR_FORD(3364, 3171, 0, SOUTH, 5)
        HILL_GIANT_10374(3372, 3152, 0, SOUTH, 5)
        HILL_GIANT_10376(3373, 3148, 0, <PERSON>OUTH, 5)
        HILL_GIANT_10374(3375, 3144, 0, SOUT<PERSON>, 5)
        HILL_GIANT_10374(3376, 3150, 0, SOUTH, 5)
        HILL_GIANT_10376(3379, 3144, 0, <PERSON>OUT<PERSON>, 5)
        HILL_GIANT_10374(3380, 3152, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        HILL_GIANT_10376(3381, 3147, 0, <PERSON><PERSON>UT<PERSON>, 5)
    }
}