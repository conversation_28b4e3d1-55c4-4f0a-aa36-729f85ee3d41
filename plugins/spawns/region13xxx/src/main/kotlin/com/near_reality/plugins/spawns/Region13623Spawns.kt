package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13623Spawns : NPCSpawnsScript() {
    init {
        CRAWLING_HAND_453(3410, 3570, 0, SOUTH, 10)
        BAT(3411, 3536, 0, SOUTH, 23)
        CRAWLING_HAND_453(3411, 3546, 0, SOUTH, 10)
        CRAWLING_HAND_453(3411, 3560, 0, SOUTH, 10)
        CRAWLING_HAND_448(3412, 3549, 0, SOUTH, 7)
        CRAWLING_HAND_448(3412, 3563, 0, SOUTH, 7)
        CRAWLING_HAND_448(3412, 3575, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        CRAWLING_HAND_448(3413, 3559, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        CRAWLING_HAND_448(3413, 3571, 0, SOUTH, 7)
        CRAWLING_HAND_453(3417, 3570, 0, SOUTH, 10)
        BAT(3418, 3522, 0, SOUTH, 23)
        CRAWLING_HAND_454(3419, 3574, 0, SOUTH, 7)
        CRAWLING_HAND_448(3420, 3551, 0, SOUTH, 7)
        CRAWLING_HAND_453(3421, 3544, 0, SOUTH, 10)
        CRAWLING_HAND_448(3421, 3573, 0, SOUTH, 7)
        CRAWLING_HAND_453(3423, 3555, 0, SOUTH, 10)
        CRAWLING_HAND_448(3424, 3558, 0, SOUTH, 7)
        CRAWLING_HAND_448(3424, 3567, 0, SOUTH, 7)
        BAT(3425, 3525, 0, SOUTH, 23)
        CRAWLING_HAND_454(3427, 3548, 0, SOUTH, 7)
        CRAWLING_HAND_448(3428, 3544, 0, SOUTH, 7)
        CRAWLING_HAND_448(3428, 3553, 0, SOUTH, 7)
        CRAWLING_HAND_454(3428, 3569, 0, SOUTH, 7)
        BAT(3429, 3522, 0, SOUTH, 23)
        BANSHEE(3429, 3564, 0, SOUTH, 8)
        CRAWLING_HAND_453(3429, 3574, 0, SOUTH, 10)
        CRAWLING_HAND_448(3430, 3571, 0, SOUTH, 7)
        BAT(3432, 3527, 0, SOUTH, 23)
        BANSHEE(3433, 3552, 0, SOUTH, 8)
        BAT(3434, 3521, 0, SOUTH, 23)
        BANSHEE(3436, 3559, 0, SOUTH, 8)
        BANSHEE(3439, 3539, 0, SOUTH, 8)
        BANSHEE(3439, 3544, 0, SOUTH, 8)
        CRAWLING_HAND_448(3439, 3574, 0, SOUTH, 7)
        BANSHEE(3440, 3560, 0, SOUTH, 8)
        CRAWLING_HAND_453(3441, 3571, 0, SOUTH, 10)
        BANSHEE(3443, 3546, 0, SOUTH, 8)
        BANSHEE(3444, 3537, 0, SOUTH, 8)
        BAT(3445, 3526, 0, SOUTH, 23)
        BLOODVELD_485(3409, 3571, 1, SOUTH, 3)
        ABERRANT_SPECTRE_4(3411, 3534, 1, SOUTH, 6)
        BLOODVELD_485(3411, 3567, 1, SOUTH, 3)
        BLOODVELD_485(3411, 3576, 1, SOUTH, 3)
        BLOODVELD_485(3412, 3560, 1, SOUTH, 3)
        ABERRANT_SPECTRE_5(3413, 3545, 1, SOUTH, 9)
        ABERRANT_SPECTRE(3413, 3550, 1, SOUTH, 8)
        BLOODVELD_485(3416, 3557, 1, SOUTH, 3)
        BLOODVELD(3416, 3561, 1, SOUTH, 6)
        BLOODVELD_485(3416, 3573, 1, SOUTH, 3)
        BLOODVELD(3417, 3565, 1, SOUTH, 6)
        BLOODVELD(3419, 3559, 1, SOUTH, 6)
        ABERRANT_SPECTRE_5(3420, 3537, 1, SOUTH, 9)
        BLOODVELD_485(3421, 3574, 1, SOUTH, 3)
        BLOODVELD(3422, 3567, 1, SOUTH, 6)
        ABERRANT_SPECTRE_4(3423, 3542, 1, SOUTH, 6)
        ABERRANT_SPECTRE(3424, 3551, 1, SOUTH, 8)
        BLOODVELD(3424, 3560, 1, SOUTH, 6)
        BLOODVELD(3424, 3564, 1, SOUTH, 6)
        BLOODVELD(3426, 3557, 1, SOUTH, 6)
        BLOODVELD_485(3426, 3573, 1, SOUTH, 3)
        ABERRANT_SPECTRE_5(3427, 3539, 1, SOUTH, 9)
        ABERRANT_SPECTRE(3428, 3543, 1, SOUTH, 8)
        ABERRANT_SPECTRE_3(3428, 3551, 1, SOUTH, 7)
        ABERRANT_SPECTRE_5(3431, 3548, 1, SOUTH, 9)
        INFERNAL_MAGE(3433, 3556, 1, SOUTH, 4)
        INFERNAL_MAGE(3434, 3563, 1, SOUTH, 4)
        INFERNAL_MAGE_446(3434, 3570, 1, SOUTH, 5)
        INFERNAL_MAGE_447(3434, 3575, 1, SOUTH, 4)
        ABERRANT_SPECTRE_4(3435, 3545, 1, SOUTH, 6)
        INFERNAL_MAGE_446(3435, 3559, 1, SOUTH, 5)
        ABERRANT_SPECTRE_3(3438, 3549, 1, SOUTH, 7)
        INFERNAL_MAGE_445(3438, 3555, 1, SOUTH, 5)
        INFERNAL_MAGE_446(3439, 3569, 1, SOUTH, 5)
        INFERNAL_MAGE_444(3439, 3575, 1, SOUTH, 4)
        INFERNAL_MAGE_444(3440, 3564, 1, SOUTH, 4)
        ABERRANT_SPECTRE_5(3442, 3544, 1, SOUTH, 9)
        ABERRANT_SPECTRE_4(3442, 3550, 1, SOUTH, 6)
        INFERNAL_MAGE_447(3442, 3556, 1, SOUTH, 4)
        INFERNAL_MAGE_445(3443, 3560, 1, SOUTH, 5)
        INFERNAL_MAGE_445(3443, 3572, 1, SOUTH, 5)
        INFERNAL_MAGE_444(3446, 3569, 1, SOUTH, 4)
        INFERNAL_MAGE(3446, 3575, 1, SOUTH, 4)
        6122(3448, 3550, 1, SOUTH, 2)
        INFERNAL_MAGE_444(3448, 3573, 1, SOUTH, 4)
        ABYSSAL_DEMON_415(3408, 3573, 2, SOUTH, 5)
        ABYSSAL_DEMON_415(3411, 3564, 2, SOUTH, 5)
        ABYSSAL_DEMON_415(3411, 3570, 2, SOUTH, 5)
        ABYSSAL_DEMON_415(3411, 3576, 2, SOUTH, 5)
        ABYSSAL_DEMON_415(3413, 3573, 2, SOUTH, 5)
        ABYSSAL_DEMON_415(3414, 3562, 2, SOUTH, 5)
        ABYSSAL_DEMON_415(3415, 3569, 2, SOUTH, 5)
        ABYSSAL_DEMON_415(3418, 3567, 2, SOUTH, 5)
        ABYSSAL_DEMON_415(3419, 3562, 2, SOUTH, 5)
        ABYSSAL_DEMON_415(3421, 3573, 2, SOUTH, 5)
        ABYSSAL_DEMON_415(3422, 3570, 2, SOUTH, 5)
        ABYSSAL_DEMON_415(3423, 3565, 2, SOUTH, 5)
        ABYSSAL_DEMON_415(3426, 3569, 2, SOUTH, 5)
        ABYSSAL_DEMON_415(3427, 3572, 2, SOUTH, 5)
        GARGOYLE(3432, 3540, 2, SOUTH, 3)
        NECHRYAEL(3433, 3570, 2, SOUTH, 9)
        GARGOYLE(3435, 3548, 2, SOUTH, 3)
        NECHRYAEL(3435, 3574, 2, SOUTH, 9)
        GARGOYLE(3437, 3537, 2, SOUTH, 3)
        NECHRYAEL(3437, 3566, 2, SOUTH, 9)
        GARGOYLE(3439, 3542, 2, SOUTH, 3)
        GARGOYLE(3439, 3548, 2, SOUTH, 3)
        NECHRYAEL(3439, 3570, 2, SOUTH, 9)
        NECHRYAEL(3441, 3574, 2, SOUTH, 9)
        GARGOYLE(3443, 3548, 2, SOUTH, 3)
        NECHRYAEL(3443, 3570, 2, SOUTH, 9)
        NECHRYAEL(3444, 3566, 2, SOUTH, 9)
        GARGOYLE(3445, 3541, 2, SOUTH, 3)
        NECHRYAEL(3445, 3560, 2, SOUTH, 9)
        GARGOYLE(3446, 3535, 2, SOUTH, 3)
        NECHRYAEL(3446, 3574, 2, SOUTH, 9)
        NECHRYAEL(3447, 3563, 2, SOUTH, 9)
        NECHRYAEL(3449, 3572, 2, SOUTH, 9)
    }
}