package com.near_reality.plugins.spawns


import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11374Spawns : NPCSpawnsScript() {
    init {
        16053(2849, 7069, 3, SOUTH, 5)
        16053(2849, 7069, 3, SOUTH, 5)
        16053(2849, 7069, 3, SOUTH, 5)
        16052(2849, 7069+64, 3, SOUTH, 5)
        16052(2849, 7069+64, 3, SOUTH, 5)
        16052(2849, 7069+64, 3, SOUTH, 5)
        16056(2849, 7069+64+64, 3, SOUTH, 5)
        16056(2849, 7069+64+64, 3, SOUTH, 5)
        16056(2849, 7069+64+64, 3, <PERSON>O<PERSON><PERSON>, 5)
        16054(2849, 7069+64+64+64, 3, SOUTH, 5)
        16054(2849, 7069+64+64+64, 3, SOUTH, 5)
        16054(2849, 7069+64+64+64, 3, SOUTH, 5)
        16055(2849, 7069+64+64+64+64, 3, SOUTH, 5)
        16055(2849, 7069+64+64+64+64, 3, SOUTH, 5)
        16055(2849, 7069+64+64+64+64, 3, SOUTH, 5)
        16057(2849, 7069+64+64+64+64+64, 3, SOUTH, 5)
        16057(2849, 7069+64+64+64+64+64, 3, SOUTH, 5)
        16057(2849, 7069+64+64+64+64+64, 3, SOUTH, 5)
        16058(3403, 7996, 0, SOUTH, 0)
        
    }
}