package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13362Spawns : NPCSpawnsScript() {
    init {
        DALAL(3329, 3233, 0, <PERSON>OUT<PERSON>, 8)
        IMA(3329, 3234, 0, SOUTH, 6)
        JADID(3359, 3222, 0, SOUTH, 0)
        SABEIL(3360, 3242, 0, SOUTH, 5)
        JEED(3361, 3242, 0, SOUTH, 4)
        CAPTAIN_DAERKIN(3362, 3222, 0, SOUTH, 5)
        AFRAH(3385, 3204, 0, SOUTH, 3)
    }
}