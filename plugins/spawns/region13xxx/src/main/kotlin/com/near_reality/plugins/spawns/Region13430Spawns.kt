package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13430Spawns : NPCSpawnsScript() {
    init {
        FISHING_SPOT_1536(3378, 7599, 0, SOUTH, 0)
        FISHING_SPOT_1536(3373, 7594, 0, SOUTH, 0)
        FISHING_SPOT_1536(3377, 7588, 0, SOUTH, 0)
        FISHING_SPOT_1536(3381, 7591, 0, SOUTH, 0)
        DURADEL(3393, 7583, 0, WEST, 3)
        KNIGHT_16023(3392, 7613, 0, EAST, 0)
        
        BANKER_3093(3393, 7562, 0, NORTH, 0)
        BANKER_3093(3391, 7562, 0, NORTH, 0)
        BANKER_3093(3390, 7562, 0, NORTH, 0)
        BANKER_3093(3389, 7562, 0, NORTH, 0)
        BANKER_3093(3388, 7562, 0, NORTH, 0)
        BANKER_3093(3386, 7562, 0, NORTH, 0)
        
        BASILISK_KNIGHT(3377, 7615, 0, SOUTH, 2)
        BASILISK_KNIGHT(3374, 7616, 0, SOUTH, 2)
        BASILISK_KNIGHT(3373, 7614, 0, SOUTH, 2)
        BASILISK_KNIGHT(3379, 7615, 0, SOUTH, 2)
        BASILISK_KNIGHT(3377, 7613, 0, SOUTH, 2)
        BASILISK_KNIGHT(3375, 7615, 0, SOUTH, 2)
        BASILISK_KNIGHT(3372, 7616, 0, SOUTH, 2)
        
        BLACK_SALAMANDER(3400, 7601, 0, SOUTH, 4)
        BLACK_SALAMANDER(3405, 7601, 0, SOUTH, 5)
        BLACK_SALAMANDER(3406, 7596, 0, SOUTH, 5)
        BLACK_SALAMANDER(3405, 7594, 0, SOUTH, 5)
        BLACK_SALAMANDER(3408, 7592, 0, SOUTH, 5)
        BLACK_SALAMANDER(3402, 7592, 0, SOUTH, 5)
        BLACK_SALAMANDER(3403, 7589, 0, SOUTH, 5)
        BLACK_SALAMANDER(3403, 7584, 0, SOUTH, 5)
        BLACK_SALAMANDER(3400, 7586, 0, SOUTH, 5)
        BLACK_SALAMANDER(3396, 7586, 0, SOUTH, 5)
        BLACK_SALAMANDER(3404, 7583, 0, SOUTH, 5)
    }
}