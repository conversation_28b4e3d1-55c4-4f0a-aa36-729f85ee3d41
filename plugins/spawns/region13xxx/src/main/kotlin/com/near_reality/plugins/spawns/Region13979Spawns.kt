package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13979Spawns : NPCSpawnsScript() {
    init {
        EXPERIMENT_1275(3473, 9943, 0, SOUTH, 4)
        EXPERIMENT_1274(3474, 9933, 0, SOUTH, 3)
        EXPERIMENT(3481, 9943, 0, SOUTH, 3)
        EXPERIMENT_1274(3487, 9935, 0, SOUTH, 3)
        EXPERIMENT_1275(3488, 9945, 0, SOUTH, 4)
        EXPERIMENT(3493, 9925, 0, SOUTH, 3)
        EXPERIMENT_1275(3498, 9937, 0, SOUTH, 4)
    }
}