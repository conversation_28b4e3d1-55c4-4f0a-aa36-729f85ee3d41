package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13975Spawns : NPCSpawnsScript() {
    init {
        ASYN_SHADOW(3460, 9667, 0, SOUTH, 23)
        ASYN_SHADOW(3460, 9687, 0, SOUTH, 23)
        FIYR_SHADOW(3460, 9695, 0, SOUTH, 7)
        FIYR_SHADOW(3460, 9719, 0, SOUTH, 7)
        ASYN_SHADOW(3461, 9669, 0, SOUTH, 23)
        ASYN_SHADOW(3461, 9678, 0, SOUTH, 23)
        FIYR_SHADOW(3463, 9697, 0, <PERSON>O<PERSON><PERSON>, 7)
        FIYR_SHADOW(3463, 9704, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        FIYR_SHADOW(3463, 9709, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        FIYR_SHADOW(3463, 9713, 0, <PERSON><PERSON>UT<PERSON>, 7)
        FIYR_SHADOW(3463, 9721, 0, SOUTH, 7)
        FIYR_SHADOW(3464, 9701, 0, SOUTH, 7)
        FIYR_SHADOW(3465, 9706, 0, SOUTH, 7)
        FIYR_SHADOW(3465, 9714, 0, SOUTH, 7)
        ASYN_SHADOW(3467, 9683, 0, SOUTH, 23)
        ASYN_SHADOW(3468, 9676, 0, SOUTH, 23)
        RIYL_SHADOW(3470, 9716, 0, SOUTH, 12)
        ASYN_SHADOW(3472, 9673, 0, SOUTH, 23)
        RIYL_SHADOW(3472, 9705, 0, SOUTH, 12)
        RIYL_SHADOW(3472, 9713, 0, SOUTH, 12)
        ASYN_SHADOW(3474, 9668, 0, SOUTH, 23)
        ASYN_SHADOW(3474, 9681, 0, SOUTH, 23)
        ASYN_SHADOW(3474, 9687, 0, SOUTH, 23)
        RIYL_SHADOW(3475, 9702, 0, SOUTH, 12)
        RIYL_SHADOW(3476, 9696, 0, SOUTH, 12)
        PHRIN_SHADOW(3477, 9725, 0, SOUTH, 10)
        ASYN_SHADOW(3478, 9674, 0, SOUTH, 23)
        RIYL_SHADOW(3478, 9706, 0, SOUTH, 12)
        RIYL_SHADOW(3478, 9716, 0, SOUTH, 12)
        PHRIN_SHADOW(3478, 9721, 0, SOUTH, 10)
        ASYN_SHADOW(3480, 9681, 0, SOUTH, 23)
        RIYL_SHADOW(3480, 9713, 0, SOUTH, 12)
        RIYL_SHADOW(3483, 9708, 0, SOUTH, 12)
        PHRIN_SHADOW(3483, 9725, 0, SOUTH, 10)
        ASYN_SHADOW(3484, 9667, 0, SOUTH, 23)
        PHRIN_SHADOW(3485, 9721, 0, SOUTH, 10)
        ASYN_SHADOW(3486, 9676, 0, SOUTH, 23)
        ASYN_SHADOW(3486, 9685, 0, SOUTH, 23)
        RIYL_SHADOW(3486, 9698, 0, SOUTH, 12)
        RIYL_SHADOW(3486, 9716, 0, SOUTH, 12)
        PHRIN_SHADOW(3486, 9724, 0, SOUTH, 10)
        RIYL_SHADOW(3487, 9706, 0, SOUTH, 12)
        RIYL_SHADOW(3488, 9713, 0, SOUTH, 12)
        RIYL_SHADOW(3489, 9701, 0, SOUTH, 12)
        PHRIN_SHADOW(3491, 9724, 0, SOUTH, 10)
        PHRIN_SHADOW(3494, 9722, 0, SOUTH, 10)
        RIYL_SHADOW(3495, 9708, 0, SOUTH, 12)
        RIYL_SHADOW(3497, 9697, 0, SOUTH, 12)
        RIYL_SHADOW(3498, 9716, 0, SOUTH, 12)
        URIUM_SHADOW(3499, 9672, 0, SOUTH, 3)
        URIUM_SHADOW(3499, 9677, 0, SOUTH, 3)
        URIUM_SHADOW(3499, 9682, 0, SOUTH, 3)
        RIYL_SHADOW(3499, 9702, 0, SOUTH, 12)
        PHRIN_SHADOW(3499, 9722, 0, SOUTH, 10)
        PHRIN_SHADOW(3499, 9723, 0, SOUTH, 10)
        RIYL_SHADOW(3500, 9707, 0, SOUTH, 12)
        RIYL_SHADOW(3500, 9713, 0, SOUTH, 12)
        URIUM_SHADOW(3502, 9685, 0, SOUTH, 3)
        PHRIN_SHADOW(3502, 9723, 0, SOUTH, 10)
        URIUM_SHADOW(3504, 9669, 0, SOUTH, 3)
        RIYL_SHADOW(3506, 9716, 0, SOUTH, 12)
        URIUM_SHADOW(3507, 9686, 0, SOUTH, 3)
        RIYL_SHADOW(3507, 9699, 0, SOUTH, 12)
        RIYL_SHADOW(3508, 9707, 0, SOUTH, 12)
        RIYL_SHADOW(3508, 9713, 0, SOUTH, 12)
        PHRIN_SHADOW(3508, 9725, 0, SOUTH, 10)
        PHRIN_SHADOW(3509, 9722, 0, SOUTH, 10)
        URIUM_SHADOW(3510, 9669, 0, SOUTH, 3)
        URIUM_SHADOW(3512, 9685, 0, SOUTH, 3)
        RIYL_SHADOW(3513, 9697, 0, SOUTH, 12)
        RIYL_SHADOW(3514, 9705, 0, SOUTH, 12)
        RIYL_SHADOW(3514, 9716, 0, SOUTH, 12)
        URIUM_SHADOW(3515, 9672, 0, SOUTH, 3)
        URIUM_SHADOW(3515, 9677, 0, SOUTH, 3)
        URIUM_SHADOW(3515, 9682, 0, SOUTH, 3)
        RIYL_SHADOW(3516, 9713, 0, SOUTH, 12)
    }
}