package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13875Spawns : NPCSpawnsScript() {
    init {
        AFFLICTED(3466, 3306, 0, SOUTH, 5)
        AFFLICTED_1294(3466, 3309, 0, SOUTH, 5)
        AFFLICTED_1297(3469, 3308, 0, SOUTH, 4)
        AFFLICTED(3471, 3285, 0, SOUTH, 5)
        AFFLICTED_1298(3472, 3304, 0, SOUTH, 5)
        LOAR_SHADOW(3474, 3280, 0, SOUTH, 22)
        AFFLICTED_1297(3474, 3293, 0, SOUT<PERSON>, 4)
        AFFLICTED_1298(3475, 3274, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        AFFLICTED_1294(3476, 3288, 0, <PERSON>OUTH, 5)
        AFFLICTED_1298(3477, 3308, 0, SOUTH, 5)
        LOAR_SHADOW(3478, 3307, 0, SOUTH, 22)
        LOAR_SHADOW(3479, 3283, 0, SOUTH, 22)
        AFFLICTED(3479, 3300, 0, SOUTH, 5)
        DAMPE(3479, 3320, 0, SOUTH, 5)
        LOAR_SHADOW(3480, 3271, 0, SOUTH, 22)
        AFFLICTED_1298(3481, 3277, 0, SOUTH, 5)
        AFFLICTED_1294(3481, 3305, 0, SOUTH, 5)
        LOAR_SHADOW(3482, 3302, 0, SOUTH, 22)
        LOAR_SHADOW(3483, 3283, 0, SOUTH, 22)
        LOAR_SHADOW(3483, 3295, 0, SOUTH, 22)
        AFFLICTED_1294(3485, 3271, 0, SOUTH, 5)
        LOAR_SHADOW(3487, 3272, 0, SOUTH, 22)
        AFFLICTED_1298(3487, 3277, 0, SOUTH, 5)
        LOAR_SHADOW(3488, 3282, 0, SOUTH, 22)
        AFFLICTED_1294(3488, 3284, 0, SOUTH, 5)
        LOAR_SHADOW(3488, 3288, 0, SOUTH, 22)
        LOAR_SHADOW(3489, 3267, 0, SOUTH, 22)
        AFFLICTEDRAZMIRE(3489, 3296, 0, SOUTH, 2)
        LOAR_SHADOW(3489, 3302, 0, SOUTH, 22)
        AFFLICTED_1297(3491, 3291, 0, SOUTH, 4)
        LOAR_SHADOW(3492, 3285, 0, SOUTH, 22)
        LOAR_SHADOW(3493, 3293, 0, SOUTH, 22)
        AFFLICTED_1298(3493, 3299, 0, SOUTH, 5)
        AFFLICTED_1294(3495, 3285, 0, SOUTH, 5)
        AFFLICTED_1297(3496, 3271, 0, SOUTH, 4)
        LOAR_SHADOW(3496, 3277, 0, SOUTH, 22)
        AFFLICTEDULSQUIRE(3496, 3289, 0, SOUTH, 2)
        LOAR_SHADOW(3496, 3296, 0, SOUTH, 22)
        AFFLICTED(3499, 3298, 0, SOUTH, 5)
        LOAR_SHADOW(3500, 3286, 0, SOUTH, 22)
        LOAR_SHADOW(3501, 3294, 0, SOUTH, 22)
        LOAR_SHADOW(3501, 3297, 0, SOUTH, 22)
        LOAR_SHADOW(3501, 3313, 0, SOUTH, 22)
        LOAR_SHADOW(3501, 3321, 0, SOUTH, 22)
        AFFLICTED(3502, 3277, 0, SOUTH, 5)
        AFFLICTED_1297(3503, 3288, 0, SOUTH, 4)
        AFFLICTED_1294(3503, 3302, 0, SOUTH, 5)
        AFFLICTED_1298(3504, 3281, 0, SOUTH, 5)
        LOAR_SHADOW(3504, 3299, 0, SOUTH, 22)
        AFFLICTED_1294(3506, 3304, 0, SOUTH, 5)
        LOAR_SHADOW(3507, 3301, 0, SOUTH, 22)
        AFFLICTED_1294(3509, 3284, 0, SOUTH, 5)
        AFFLICTED(3511, 3300, 0, SOUTH, 5)
        LOAR_SHADOW(3513, 3321, 0, SOUTH, 22)
        AFFLICTED_1298(3515, 3302, 0, SOUTH, 5)
        LOAR_SHADOW(3516, 3315, 0, SOUTH, 22)
        AFFLICTED(3517, 3285, 0, SOUTH, 5)
    }
}