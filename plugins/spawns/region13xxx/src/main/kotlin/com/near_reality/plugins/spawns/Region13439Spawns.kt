package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13439Spawns : NPCSpawnsScript() {
    init {
        4712(3366, 8156, 2, <PERSON><PERSON><PERSON><PERSON>, 0)
        4710(3351, 8156, 2, SOUTH, 0)
        
        1536(3351, 8284, 2, SOUTH, 0)
        1536(3366, 8284, 2, SOUTH, 0)
        
        16031(3361, 8180, 2, SOUTH, 0)
        
        TOOL_LEPRECHAUN(3420, 7787, 0, <PERSON><PERSON>UT<PERSON>, 2)
        TOOL_LEPRECHAUN(3397, 7998, 0, SOUTH, 2)
        TOOL_LEPRECHAUN(3362, 8274, 2, <PERSON>O<PERSON><PERSON>, 2)
        TOOL_LEPRECHAUN(3362, 8146, 2, <PERSON><PERSON><PERSON><PERSON>, 2)
    }
}