package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13872Spawns : NPCSpawnsScript() {
    init {
        25(3467, 3109, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        21(3467, 3110, 0, EAST, 0)
        1988(3472, 3086, 0, SOUTH, 4)
        1988(3475, 3100, 0, SOUTH, 4)
        1988(3482, 3084, 0, SOUTH, 4)
        1986(3483, 3090, 0, SOUTH, 3)
        1985(3483, 3093, 0, SOUTH, 4)
        6277(3488, 3090, 0, SOUTH, 4)
        1988(3489, 3102, 0, SOUTH, 4)
    }
}