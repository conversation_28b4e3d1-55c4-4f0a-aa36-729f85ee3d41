package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13112Spawns : NPCSpawnsScript() {
    init {
        ENT(3299, 3586, 0, SOUTH, 11)
        ENT(3301, 3597, 0, SOUTH, 11)
        ENT(3305, 3614, 0, SOUTH, 11)
        ENT(3306, 3631, 0, SOUTH, 11)
    }
}