package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13918Spawns : NPCSpawnsScript() {
    init {
        DARK_WARRIOR(3501, 6072, 0, SOUTH, 5)
        DARK_WARRIOR(3501, 6075, 0, SOUTH, 5)
        DARK_WARRIOR(3504, 6072, 0, SOUTH, 5)
        DARK_WARRIOR(3504, 6075, 0, SOUTH, 5)
        DARK_WARRIOR(3507, 6072, 0, SOUTH, 5)
        DARK_WARRIOR(3507, 6075, 0, SOUTH, 5)
    }
}