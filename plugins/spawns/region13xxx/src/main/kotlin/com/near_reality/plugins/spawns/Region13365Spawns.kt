package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13365Spawns : NPCSpawnsScript() {
    init {
        STUDENT_3634(3348, 3424, 0, SOUTH, 4)
        DIGSITE_WORKMAN_3631(3350, 3401, 0, SOUTH, 4)
        DIGSITE_WORKMAN_3630(3354, 3408, 0, SOUTH, 5)
        DIG_SITE_WORKMAN(3357, 3415, 0, SOUTH_WEST, 0)
        BARGE_WORKMAN_1937(3357, 3446, 0, SOUTH, 0)
        DIGSITE_WORKMAN_3631(3362, 3408, 0, SOUTH, 4)
        DIGSITE_WORKMAN(3362, 3416, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7767(3362, 3446, 0, WEST, 0)
        STUDENT(3363, 3397, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        BARGE_FOREMAN(3364, 3445, 0, W<PERSON>T, 0)
        DIGSITE_<PERSON>ORKMAN_3630(3365, 3429, 0, SOUTH, 5)
        DIGSITE_WORKMAN_3631(3366, 3425, 0, SOUTH, 4)
        STUDENT_3633(3370, 3416, 0, SOUTH, 5)
        DIGSITE_WORKMAN_3630(3373, 3418, 0, SOUTH, 5)
        ELISSA(3376, 3429, 0, SOUTH, 4)
        BARGE_GUARD(3362, 3451, 1, EAST, 0)
        7822(3363, 3452, 1, SOUTH, 2)
        7820(3363, 3453, 1, SOUTH, 0)
        7819(3363, 3454, 1, SOUTH, 0)
        7821(3364, 3453, 1, SOUTH, 2)
    }
}