package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13359Spawns : NPCSpawnsScript() {
    init {
        GOAT(3335, 3019, 0, SOUTH, 3)
        GOAT_1795(3337, 3020, 0, SOUTH, 4)
        GOAT_1793(3338, 3021, 0, SOUTH, 4)
        GOAT_1793(3364, 3033, 0, SOUTH, 4)
        BILLY_GOAT(3364, 3034, 0, SOUTH, 5)
        GOAT_1793(3365, 3036, 0, <PERSON>OUTH, 4)
        GOAT(3366, 3034, 0, SOUTH, 3)
        SMALL_LIZARD(3384, 3013, 0, SOUTH, 5)
        DESERT_LIZARD(3384, 3015, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        SMALL_LIZARD_463(3385, 3067, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        DESERT_LIZARD_460(3386, 3065, 0, SOUTH, 6)
        SMALL_LIZARD(3387, 3014, 0, SOUTH, 5)
        LIZARD(3387, 3017, 0, SOUTH, 8)
        LIZARD(3387, 3068, 0, SOUTH, 8)
        SMALL_LIZARD_463(3389, 3065, 0, SOUTH, 4)
    }
}