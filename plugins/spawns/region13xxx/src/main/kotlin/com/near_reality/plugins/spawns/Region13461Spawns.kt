package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13461Spawns : NPCSpawnsScript() {
    init {
        ROD_FISHING_SPOT_1529(3350, 9567, 0, SOUTH, 5)
        ROD_FISHING_SPOT_1529(3354, 9566, 0, SOUTH, 5)
        ROD_FISHING_SPOT_1529(3356, 9566, 0, SOUTH, 5)
        ROD_FISHING_SPOT_1529(3360, 9567, 0, SOUTH, 5)
        ROD_FISHING_SPOT_1529(3362, 9588, 0, SOUTH, 5)
        NIRRIE(3367, 9586, 0, SOUT<PERSON>, 5)
        HALLAK(3368, 9585, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        TIRRIE(3368, 9587, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        ROD_FISHING_SPOT_1529(3371, 9576, 0, SOUTH, 5)
        ROD_FISHING_SPOT_1529(3371, 9578, 0, SO<PERSON>H, 5)
        4765(3379, 9556, 0, SOUTH, 0)
    }
}