package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13110Spawns : NPCSpawnsScript() {
    init {
        BARTENDER_1310(3277, 3488, 0, SOUTH, 2)
        BLACK_KNIGHT(3277, 3505, 0, SOUTH, 5)
        WOMAN_3112(3278, 3502, 0, SOUTH, 2)
        WOMAN_3015(3279, 3496, 0, SOUTH, 3)
        MAN_3108(3283, 3501, 0, SOUTH, 11)
        COOK_2896(3285, 3489, 0, EAST, 0)
        THIEF_3252(3285, 3501, 0, SOUTH, 5)
        4732(3298, 3483, 0, <PERSON>OUT<PERSON>, 2)
        CRATE(3298, 3514, 0, SOUTH, 0)
        SAWMILL_OPERATOR(3302, 3492, 0, SOUTH, 0)
        CRATE(3303, 3506, 0, SOUTH, 0)
        CRATE(3305, 3500, 0, SOUTH, 0)
        CRATE(3307, 3507, 0, SOUTH, 0)
        CRATE(3310, 3499, 0, SOUTH, 0)
        CRATE(3315, 3515, 0, SOUTH, 0)
        MAN_3014(3277, 3495, 1, SOUTH, 5)
        4988(3279, 3503, 1, SOUTH, 5)
        ANNA_JONES(3285, 3468, 0, SOUTH_WEST, 0)
        BENCH(3285, 3469, 0, SOUTH, 0)
        GERTRUDES_CAT_3497(3306, 3512, 1, SOUTH, 2)
    }
}