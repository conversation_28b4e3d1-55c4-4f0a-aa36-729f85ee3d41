package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13114Spawns : NPCSpawnsScript() {
    init {
        SKELETON_80(3264, 3727, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        SKELETON_81(3267, 3729, 0, <PERSON><PERSON>UT<PERSON>, 6)
        SKELETON_77(3272, 3731, 0, SOUTH, 7)
        SKELETON_78(3274, 3726, 0, SOUTH, 8)
        SKELETON_79(3280, 3727, 0, <PERSON><PERSON>UT<PERSON>, 8)
        BLACK_KNIGHT_517(3308, 3769, 0, SOUTH, 6)
        BLACK_KNIGHT_517(3309, 3765, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        BLACK_KNIGHT_517(3310, 3769, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        BLACK_KNIGHT_517(3311, 3773, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        BLACK_KNIGHT_517(3313, 3769, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        BLACK_KNIGHT_517(3314, 3772, 0, SOUTH, 6)
        BLACK_KNIGHT_517(3317, 3772, 0, SOUTH, 6)
    }
}