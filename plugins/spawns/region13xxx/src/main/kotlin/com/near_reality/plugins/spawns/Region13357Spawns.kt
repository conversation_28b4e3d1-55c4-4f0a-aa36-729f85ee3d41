package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13357Spawns : NPCSpawnsScript() {
    init {
        JACKAL(3329, 2933, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        JACKAL(3330, 2931, 0, SOUT<PERSON>, 11)
        JACKAL(3331, 2933, 0, SOUTH, 11)
        CROCODILE(3337, 2922, 0, SOUTH, 6)
        JACKAL(3343, 2895, 0, SOUTH, 11)
        CROCODILE(3343, 2931, 0, SOUTH, 6)
        JACKAL(3345, 2896, 0, SOUTH, 11)
        JACKAL(3346, 2894, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        MONKEY(3347, 2942, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        J<PERSON><PERSON><PERSON>(3348, 2894, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        RUG_MERCHANT_19(3348, 2942, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        CROCODILE(3349, 2922, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        CROCODILE(3351, 2927, 0, SO<PERSON>H, 6)
        CR<PERSON>ODIL<PERSON>(3357, 2922, 0, SOUTH, 6)
        CR<PERSON><PERSON><PERSON>E(3359, 2927, 0, SOUTH, 6)
        <PERSON><PERSON><PERSON><PERSON><PERSON>(3364, 2937, 0, SO<PERSON><PERSON>, 6)
        J<PERSON><PERSON>L(3376, 2934, 0, <PERSON>O<PERSON><PERSON>, 11)
        J<PERSON><PERSON>L(3378, 2933, 0, <PERSON>OUT<PERSON>, 11)
        J<PERSON><PERSON>L(3378, 2935, 0, SOUTH, 11)
        WINGSTONE(3380, 2893, 0, SOUTH, 21)
        JACKAL(3381, 2907, 0, SOUTH, 11)
        JACKAL(3383, 2905, 0, SOUTH, 11)
        JACKAL(3383, 2908, 0, SOUTH, 11)
        JACKAL(3384, 2907, 0, SOUTH, 11)
    }
}