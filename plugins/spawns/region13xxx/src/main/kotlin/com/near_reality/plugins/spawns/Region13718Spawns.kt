package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13718Spawns : NPCSpawnsScript() {
    init {
        BAT(3409, 9637, 0, SOUTH, 23)
        GIANT_BAT(3410, 9632, 0, SOUTH, 11)
        BAT(3410, 9640, 0, SOUTH, 23)
        GIANT_BAT(3411, 9620, 0, SOUTH, 11)
        BAT(3411, 9644, 0, SOUTH, 23)
        BAT(3414, 9643, 0, SOUTH, 23)
        GIANT_BAT(3416, 9620, 0, SOUTH, 11)
        BAT(3418, 9642, 0, SOUTH, 23)
        BAT(3419, 9646, 0, SOUTH, 23)
        BAT(3420, 9631, 0, <PERSON>OUT<PERSON>, 23)
        ANIMATED_SPADE(3420, 9657, 0, <PERSON>OUT<PERSON>, 9)
        BAT(3421, 9629, 0, <PERSON><PERSON>UTH, 23)
        BAT(3423, 9625, 0, SOUTH, 23)
        POSSESSED_PICKAXE_6469(3423, 9658, 0, SOUTH, 0)
        GIANT_BAT(3425, 9637, 0, SOUTH, 11)
        SKELETON_6448(3427, 9644, 0, SOUTH, 2)
        SKELETON_6448(3428, 9658, 0, SOUTH, 2)
        ANIMATED_SPADE(3429, 9648, 0, SOUTH, 9)
        BAT(3433, 9638, 0, SOUTH, 23)
        BAT(3434, 9636, 0, SOUTH, 23)
        BAT(3436, 9636, 0, SOUTH, 23)
    }
}