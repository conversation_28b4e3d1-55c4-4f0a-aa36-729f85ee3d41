package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13874Spawns : NPCSpawnsScript() {
    init {
        FERAL_VAMPYRE_3237(3458, 3225, 0, SOUTH, 18)
        FERAL_VAMPYRE_3237(3459, 3229, 0, SOUTH, 18)
        FERAL_VAMPYRE_3237(3459, 3234, 0, SOUTH, 18)
        FERAL_VAMPYRE_3237(3460, 3231, 0, SOUTH, 18)
        FERAL_VAMPYRE_3237(3461, 3236, 0, SOUTH, 18)
        FERAL_VAMPYRE_3237(3462, 3240, 0, SOUTH, 18)
        2934(3475, 3235, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        2935(3476, 3235, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        NICOLETA(3479, 3223, 0, EAST, 0)
        2936(3479, 3237, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        MIHAIL(3480, 3222, 0, NORTH, 0)
        2937(3480, 3237, 0, SO<PERSON><PERSON>, 2)
        2939(3480, 3241, 0, SOUTH, 2)
        2938(3481, 3241, 0, SOUTH, 2)
        VICTOR(3482, 3208, 0, SOUTH, 2)
        EMILIA(3482, 3210, 0, SOUTH, 2)
        RAT_2854(3483, 3210, 0, SOUTH, 14)
        FLORIN(3483, 3243, 0, SOUTH, 3)
        HELENA(3484, 3207, 0, SOUTH, 2)
        VASILE(3485, 3237, 0, SOUTH, 4)
        3488(3486, 3241, 0, SOUTH, 2)
        RAT_2854(3489, 3211, 0, SOUTH, 14)
        CATALINA(3489, 3221, 0, SOUTH, 2)
        GRIGORE(3489, 3222, 0, SOUTH, 2)
        ELISABETA(3490, 3241, 0, SOUTH, 4)
        RAT_2854(3490, 3243, 0, SOUTH, 14)
        LUSCION(3491, 3242, 0, SOUTH, 2)
        RAT_2854(3492, 3228, 0, SOUTH, 14)
        RAZVAN(3492, 3235, 0, SOUTH, 5)
        6215(3493, 3211, 0, SOUTH, 2)
        6214(3496, 3212, 0, SOUTH, 2)
        RAT_2854(3496, 3230, 0, SOUTH, 14)
        RAT_2854(3496, 3242, 0, SOUTH, 14)
        RAT_2854(3498, 3212, 0, SOUTH, 14)
        CALIN(3498, 3242, 0, SOUTH, 0)
        SIMONA(3499, 3241, 0, WEST, 0)
        RAT_2854(3500, 3224, 0, SOUTH, 14)
        SERGIU(3503, 3231, 0, SOUTH, 3)
        LUMINATA(3504, 3235, 0, SOUTH, 5)
        TEODOR(3504, 3242, 0, SOUTH, 5)
        RAT_2854(3505, 3230, 0, SOUTH, 14)
        RAT_2854(3506, 3243, 0, SOUTH, 14)
        IVAN(3508, 3208, 0, SOUTH, 2)
        RAT_2854(3508, 3225, 0, SOUTH, 14)
        VALERIA(3509, 3209, 0, SOUTH, 2)
        RADU(3511, 3224, 0, SOUTH, 2)
        RAT_2854(3512, 3228, 0, SOUTH, 14)
        SORIN(3513, 3234, 0, SOUTH, 3)
        6216(3513, 3241, 0, SOUTH, 2)
        6217(3513, 3242, 0, SOUTH, 2)
        GABRIELA(3514, 3209, 0, SOUTH, 2)
        ILEANA(3514, 3228, 0, SOUTH, 2)
        6218(3514, 3241, 0, SOUTH, 2)
        4479(3515, 3242, 0, SOUTH, 2)
        RAT_2854(3516, 3222, 0, SOUTH, 14)
        AUREL(3517, 3241, 0, SOUTH, 2)
    }
}