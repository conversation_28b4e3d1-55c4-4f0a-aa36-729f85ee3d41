package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13204Spawns : NPCSpawnsScript() {
    init {
        KALPHITE_GUARDIAN_960(3268, 9482, 0, SOUTH, 4)
        KALPHITE_WORKER_956(3272, 9514, 0, SOUTH, 8)
        KALPHITE_WORKER_956(3273, 9519, 0, SOUTH, 8)
        KALPHITE_GUARDIAN_960(3275, 9478, 0, SOUTH, 4)
        KALPHITE_GUARDIAN_960(3277, 9492, 0, SOUTH, 4)
        KALPHITE_WORKER_956(3277, 9512, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        KAL<PERSON>H<PERSON>E_WORKER_956(3277, 9522, 0, <PERSON>O<PERSON><PERSON>, 8)
        KALPHITE_GUARDIAN_960(3278, 9485, 0, SOUTH, 4)
        KALPHITE_WORKER_956(3278, 9517, 0, SOUTH, 8)
        KALPHITE_WORKER_956(3281, 9521, 0, SOUTH, 8)
        KALPHITE_WORKER_956(3281, 9526, 0, SOUTH, 8)
        K<PERSON>PH<PERSON>E_WORKER_956(3282, 9516, 0, <PERSON>OUTH, 8)
        KALPHITE_GUARDIAN_960(3284, 9481, 0, SOUTH, 4)
        KALPHITE_WORKER_956(3284, 9524, 0, SOUTH, 8)
        KALPHITE_LARVA(3296, 9497, 0, SOUTH, 9)
        KALPHITE_LARVA(3296, 9500, 0, SOUTH, 9)
        KALPHITE_LARVA(3296, 9506, 0, SOUTH, 9)
        KALPHITE_LARVA(3297, 9509, 0, SOUTH, 9)
        KALPHITE_LARVA(3298, 9495, 0, SOUTH, 9)
        KALPHITE_LARVA(3299, 9504, 0, SOUTH, 9)
        KALPHITE_SOLDIER_958(3299, 9532, 0, SOUTH, 11)
        KALPHITE_LARVA(3300, 9500, 0, SOUTH, 9)
        KALPHITE_LARVA(3300, 9507, 0, SOUTH, 9)
        KALPHITE_LARVA(3301, 9497, 0, SOUTH, 9)
        KALPHITE_SOLDIER_958(3301, 9528, 0, SOUTH, 11)
        KALPHITE_SOLDIER_958(3305, 9516, 0, SOUTH, 11)
        KALPHITE_SOLDIER_958(3305, 9522, 0, SOUTH, 11)
        ENTOMOLOGIST(3307, 9506, 0, SOUTH, 0)
        KALPHITE_SOLDIER_958(3309, 9480, 0, SOUTH, 11)
        KALPHITE_SOLDIER_958(3309, 9529, 0, SOUTH, 11)
        KALPHITE_SOLDIER_958(3311, 9521, 0, SOUTH, 11)
        KALPHITE_SOLDIER_958(3315, 9481, 0, SOUTH, 11)
        KALPHITE_WORKER_956(3315, 9502, 0, SOUTH, 8)
        KALPHITE_SOLDIER_958(3315, 9528, 0, SOUTH, 11)
        KALPHITE_SOLDIER_958(3317, 9476, 0, SOUTH, 11)
        KALPHITE_SOLDIER_958(3317, 9522, 0, SOUTH, 11)
        KALPHITE_SOLDIER_958(3318, 9486, 0, SOUTH, 11)
        KALPHITE_WORKER_956(3318, 9496, 0, SOUTH, 8)
        KALPHITE_WORKER_956(3319, 9507, 0, SOUTH, 8)
        KALPHITE_SOLDIER_958(3322, 9478, 0, SOUTH, 11)
        KALPHITE_SOLDIER_958(3322, 9483, 0, SOUTH, 11)
        KALPHITE_WORKER_956(3322, 9502, 0, SOUTH, 8)
        KALPHITE_WORKER_956(3323, 9495, 0, SOUTH, 8)
        KALPHITE_WORKER_956(3324, 9510, 0, SOUTH, 8)
        KALPHITE_SOLDIER_958(3327, 9482, 0, SOUTH, 11)
        KALPHITE_WORKER_956(3327, 9500, 0, SOUTH, 8)
        KALPHITE_WORKER_956(3327, 9506, 0, SOUTH, 8)
    }
}