package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13876Spawns : NPCSpawnsScript() {
    init {
        GHAST(3456, 3343, 0, SOUTH, 4)
        GHAST(3456, 3366, 0, SOUTH, 4)
        GHAST(3456, 3390, 0, SOUTH, 4)
        GHAST(3457, 3351, 0, SOUTH, 4)
        GHAST(3457, 3356, 0, SOUTH, 4)
        GHAST(3457, 3370, 0, SOUTH, 4)
        GHAST(3457, 3378, 0, SOUTH, 4)
        GHAST(3458, 3340, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3458, 3347, 0, <PERSON><PERSON><PERSON><PERSON>, 18)
        GHAST(3460, 3375, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        GHAST(3463, 3336, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        GHAST(3463, 3341, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        MYRE_BLAMISH_SNAIL(3463, 3346, 0, SOUTH, 18)
        GHAST(3463, 3353, 0, SOUTH, 4)
        OCHRE_BLAMISH_SNAIL_2651(3463, 3357, 0, SOUTH, 15)
        GHAST(3464, 3372, 0, SOUTH, 4)
        GHAST(3464, 3380, 0, SOUTH, 4)
        GHAST(3465, 3359, 0, SOUTH, 4)
        GHAST(3466, 3332, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3466, 3364, 0, SOUTH, 18)
        MYRE_BLAMISH_SNAIL(3467, 3345, 0, SOUTH, 18)
        GHAST(3467, 3380, 0, SOUTH, 4)
        GHAST(3468, 3348, 0, SOUTH, 4)
        GHAST(3469, 3368, 0, SOUTH, 4)
        GHAST(3469, 3390, 0, SOUTH, 4)
        GHAST(3470, 3381, 0, SOUTH, 4)
        BLOOD_BLAMISH_SNAIL_2650(3471, 3346, 0, SOUTH, 19)
        GHAST(3472, 3362, 0, SOUTH, 4)
        BRUISE_BLAMISH_SNAIL_2652(3473, 3346, 0, SOUTH, 16)
        OCHRE_BLAMISH_SNAIL_2651(3473, 3348, 0, SOUTH, 15)
        GHAST(3473, 3352, 0, SOUTH, 4)
        BRUISE_BLAMISH_SNAIL(3474, 3347, 0, SOUTH, 22)
        GHAST(3474, 3380, 0, SOUTH, 4)
        GHAST(3474, 3387, 0, SOUTH, 4)
        GHAST(3475, 3373, 0, SOUTH, 4)
        GHAST(3477, 3354, 0, SOUTH, 4)
        OCHRE_BLAMISH_SNAIL_2651(3477, 3367, 0, SOUTH, 15)
        OCHRE_BLAMISH_SNAIL(3477, 3368, 0, SOUTH, 14)
        GHAST(3478, 3328, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3478, 3371, 0, SOUTH, 18)
        OCHRE_BLAMISH_SNAIL(3480, 3363, 0, SOUTH, 14)
        GHAST(3482, 3343, 0, SOUTH, 4)
        GHAST(3485, 3373, 0, SOUTH, 4)
        GHAST(3486, 3329, 0, SOUTH, 4)
        GHAST(3486, 3380, 0, SOUTH, 4)
        GHAST(3487, 3368, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3487, 3378, 0, SOUTH, 18)
        GHAST(3488, 3343, 0, SOUTH, 4)
        GHAST(3488, 3350, 0, SOUTH, 4)
        GHAST(3488, 3357, 0, SOUTH, 4)
        GHAST(3491, 3376, 0, SOUTH, 4)
        GHAST(3491, 3381, 0, SOUTH, 4)
        GHAST(3491, 3388, 0, SOUTH, 4)
        GHAST(3492, 3360, 0, SOUTH, 4)
        GHAST(3492, 3371, 0, SOUTH, 4)
        GHAST(3494, 3328, 0, SOUTH, 4)
        GHAST(3494, 3341, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3494, 3380, 0, SOUTH, 18)
        OCHRE_BLAMISH_SNAIL(3494, 3389, 0, SOUTH, 14)
        OCHRE_BLAMISH_SNAIL(3496, 3386, 0, SOUTH, 14)
        GHAST(3498, 3354, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3498, 3382, 0, SOUTH, 18)
        GHAST(3500, 3385, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3501, 3382, 0, SOUTH, 18)
        GHAST(3501, 3388, 0, SOUTH, 4)
        GHAST(3502, 3328, 0, SOUTH, 4)
        GHAST(3502, 3350, 0, SOUTH, 4)
        GHAST(3502, 3363, 0, SOUTH, 4)
        GHAST(3503, 3359, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3503, 3380, 0, SOUTH, 18)
        GHAST(3505, 3379, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3506, 3382, 0, SOUTH, 18)
        GHAST(3507, 3348, 0, SOUTH, 4)
        GHAST(3508, 3367, 0, SOUTH, 4)
        GHAST(3510, 3341, 0, SOUTH, 4)
        GHAST(3510, 3381, 0, SOUTH, 4)
        GHAST(3512, 3328, 0, SOUTH, 4)
        GHAST(3513, 3383, 0, SOUTH, 4)
        GHAST(3517, 3329, 0, SOUTH, 4)
        GHAST(3517, 3336, 0, SOUTH, 4)
    }
}