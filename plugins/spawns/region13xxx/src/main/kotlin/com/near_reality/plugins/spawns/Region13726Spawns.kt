package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13726Spawns : NPCSpawnsScript() {
    init {
        GREEN_DRAGON_7868(3396, 10119, 0, SOUTH, 4)
        GREEN_DRAGON_7870(3397, 10124, 0, SOUTH, 5)
        GREEN_DRAGON_7869(3402, 10119, 0, SOUTH, 4)
        GREATER_DEMON_7871(3421, 10153, 0, SOUTH, 7)
        GREATER_DEMON_7872(3421, 10159, 0, SOUTH, 7)
        GREATER_DEMON_7872(3424, 10148, 0, SOUT<PERSON>, 7)
        GREATER_DEMON_7873(3425, 10156, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        GREATER_DEMON_7871(3426, 10143, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        GREATER_DEMON_7871(3428, 10151, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        GREATER_DEMON_7871(3432, 10148, 0, SOUTH, 7)
        GREATER_DEMON_7871(3432, 10152, 0, SOUTH, 7)
        GREATER_DEMON_7872(3433, 10139, 0, SOUTH, 7)
        GREATER_DEMON_7873(3434, 10144, 0, SOUTH, 6)
    }
}