package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13617Spawns : NPCSpawnsScript() {
    init {
        GOLDEN_WARBLER(3395, 3143, 0, SOUT<PERSON>, 6)
        GOLDEN_WARBLER(3396, 3158, 0, SOUT<PERSON>, 6)
        GOLDEN_WARBLER(3400, 3149, 0, SOUTH, 6)
        GOLDEN_WARBLER(3404, 3144, 0, SOUTH, 6)
        GOLDEN_WARBLER(3406, 3152, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        GOLDEN_WARBLER(3408, 3147, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        DESERT_PHOENIX(3414, 3156, 0, <PERSON>O<PERSON><PERSON>, 5)
        BILLY_GOAT_1797(3437, 3154, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        GOAT_1796(3438, 3152, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        GOAT_1795(3438, 3153, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        GOAT_1796(3439, 3154, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        GOAT_1795(3440, 3152, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
    }
}