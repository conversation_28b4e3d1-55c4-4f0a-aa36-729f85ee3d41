package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region13102Spawns : NPCSpawnsScript() {
    init {
        DESERT_SNAKE(3266, 2955, 0, SOUTH, 3)
        DESERT_SNAKE(3271, 2967, 0, SOUT<PERSON>, 3)
        BANDIT_695(3272, 2977, 0, SOUTH, 9)
        DESERT_SNAKE(3279, 2957, 0, <PERSON>OUTH, 3)
        BANDIT_695(3280, 2957, 0, SOUTH, 9)
        DESERT_SNAKE(3280, 2975, 0, SOUTH, 3)
        BANDIT_695(3281, 2969, 0, SOUTH, 9)
        DESERT_SNAKE(3284, 2959, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        BANDIT_695(3293, 2959, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        DESERT_SNAKE(3294, 2964, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        DESERT_SNAKE(3295, 2978, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        BANDIT_695(3302, 2977, 0, SOUTH, 9)
        DESERT_SNAKE(3305, 2966, 0, SOUTH, 3)
        DESERT_SNAKE(3307, 2959, 0, SOUTH, 3)
        DESERT_SNAKE(3309, 2973, 0, SOUTH, 3)
    }
}