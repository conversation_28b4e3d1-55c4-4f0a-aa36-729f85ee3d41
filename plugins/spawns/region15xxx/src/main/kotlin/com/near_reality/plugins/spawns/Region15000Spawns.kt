package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region15000Spawns : NPCSpawnsScript() {
    init {
        BAT(3727, 9755, 1, <PERSON>O<PERSON><PERSON>, 23)
        9824(3727, 9759, 1, SOUTH, 5)
        SPIDER_3019(3727, 9779, 1, SOUTH, 8)
        BAT(3729, 9763, 1, SOUTH, 23)
        SPIDER_3019(3730, 9781, 1, <PERSON>OUT<PERSON>, 8)
        SPIDER_3019(3733, 9780, 1, <PERSON><PERSON><PERSON><PERSON>, 8)
        SPIDER_3019(3735, 9781, 1, SOUTH, 8)
        SISTER_SALOHCIN(3742, 9768, 1, <PERSON>O<PERSON><PERSON>, 5)
    }
}