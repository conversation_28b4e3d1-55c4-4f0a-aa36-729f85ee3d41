package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10297Spawns : NPCSpawnsScript() {
    init {
        WARRIOR_3950(2619, 3671, 0, SOUTH, 12)
        WARRIOR_3950(2622, 3654, 0, SOUTH, 12)
        3854(2620, 3693, 0, SOUTH, 0)
        6535(2621, 3683, 0, SOUTH, 0)
        7322(2621, 3690, 0, SOUTH, 5)
    }
}