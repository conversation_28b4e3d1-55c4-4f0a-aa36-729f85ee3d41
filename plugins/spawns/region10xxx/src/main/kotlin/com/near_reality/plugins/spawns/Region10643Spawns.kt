package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10643Spawns : NPCSpawnsScript() {
    init {
        STEEL_DRAGON_275(2632, 9420, 0, SOUTH, 3)
        BRONZE_DRAGON_271(2632, 9457, 0, SOUTH, 3)
        BRONZE_DRAGON_271(2636, 9450, 0, SOUTH, 3)
        STEEL_DRAGON_275(2637, 9425, 0, SOUTH, 3)
        STEEL_DRAGON_275(2638, 9432, 0, SOUTH, 3)
        STEEL_DRAGON_275(2639, 9418, 0, SOUTH, 3)
        BRONZE_DRAGON_271(2639, 9456, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        BRONZE_DRAGON_271(2642, 9446, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        STEEL_DRAGON_275(2646, 9424, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        STEEL_DRAGON_275(2646, 9431, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        BRONZE_DRAGON_271(2646, 9455, 0, SOUTH, 3)
        STEEL_DRAGON_275(2653, 9427, 0, SOUTH, 3)
        STEEL_DRAGON_275(2655, 9419, 0, SOUTH, 3)
        IRON_DRAGON_273(2661, 9453, 0, SOUTH, 3)
        STEEL_DRAGON_275(2663, 9417, 0, SOUTH, 3)
        IRON_DRAGON_273(2663, 9459, 0, SOUTH, 3)
        IRON_DRAGON_273(2666, 9452, 0, SOUTH, 3)
        IRON_DRAGON_273(2670, 9447, 0, SOUTH, 3)
        IRON_DRAGON_273(2670, 9459, 0, SOUTH, 3)
        IRON_DRAGON_273(2673, 9453, 0, SOUTH, 3)
        IRON_DRAGON_273(2674, 9442, 0, SOUTH, 3)
        IRON_DRAGON_273(2678, 9457, 0, SOUTH, 3)
        HIEVE(2683, 9434, 0, WEST, 0)
    }
}