package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10031Spawns : NPCSpawnsScript() {
    init {
        OGRE_GUARD_4371(2501, 3012, 0, SOUTH, 2)
        OGRE_GUARD_4371(2503, 3011, 0, SOUTH, 2)
        OGRE_CHIEFTAIN(2505, 3032, 0, SOUTH, 2)
        OGRE_GUARD_4370(2505, 3062, 0, SOUTH, 0)
        ENCLAVE_GUARD(2507, 3036, 0, SOUTH, 5)
        ENCLAVE_GUARD(2507, 3040, 0, SOUTH, 5)
        OGRE_GUARD_4370(2507, 3062, 0, SOUTH, 0)
        OGRE_2096(2510, 3044, 0, <PERSON>O<PERSON><PERSON>, 3)
        OGRE_CHIEFTAIN(2512, 3022, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        OGRE_2095(2513, 3026, 0, <PERSON><PERSON>UTH, 2)
        OGRE_TRADER_4403(2513, 3034, 0, SOUTH, 5)
        OGRE_TRADER(2518, 3035, 0, SOUTH, 5)
        OGRE_CHIEFTAIN(2518, 3041, 0, SOUTH, 2)
        OGRE_2096(2520, 3046, 0, SOUTH, 3)
        OGRE_2095(2522, 3055, 0, SOUTH, 2)
        OGRE_2095(2523, 3057, 0, SOUTH, 2)
        OGRE_TRADER_4404(2525, 3043, 0, SOUTH, 2)
        OGRE_GUARD_4372(2526, 3018, 0, SOUTH, 5)
        OGRE_2096(2526, 3039, 0, SOUTH, 3)
        OGRE_CHIEFTAIN(2528, 3044, 0, SOUTH, 2)
        OGRE_MERCHANT(2528, 3048, 0, SOUTH, 2)
        OGRE_GUARD_4372(2529, 3019, 0, SOUTH, 5)
        OGRE_2095(2529, 3032, 0, SOUTH, 2)
        OGRE_2095(2539, 3018, 0, SOUTH, 2)
        OGRE_2095(2541, 3016, 0, SOUTH, 2)
        CITY_GUARD(2541, 3029, 0, SOUTH, 2)
        CITY_GUARD(2541, 3034, 0, SOUTH, 2)
        1451(2543, 3031, 0, SOUTH, 2)
        OGRE_GUARD_4369(2549, 3029, 0, SOUTH, 2)
        OGRE_2095(2550, 3043, 0, SOUTH, 2)
        OGRE_2095(2550, 3046, 0, SOUTH, 2)
        OGRE_GUARD_4369(2551, 3031, 0, SOUTH, 2)
        OGRE_2095(2553, 3047, 0, SOUTH, 2)
    }
}