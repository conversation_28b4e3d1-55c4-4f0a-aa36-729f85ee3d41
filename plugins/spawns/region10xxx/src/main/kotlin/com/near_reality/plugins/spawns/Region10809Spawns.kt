package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10809Spawns : NPCSpawnsScript() {
    init {
        BLACK_UNICORN_FOAL(2707, 3670, 0, SOUTH, 15)
        BLACK_UNICORN(2708, 3672, 0, SOUTH, 15)
        BLACK_UNICORN_FOAL(2710, 3672, 0, SOUTH, 15)
        SVIDI(2714, 3672, 0, <PERSON><PERSON><PERSON><PERSON>, 19)
        BLACK_UNICORN_FOAL(2728, 3661, 0, SOUTH, 15)
        BLACK_UNICORN(2730, 3659, 0, SOUTH, 15)
        BLACK_UNICORN_FOAL(2732, 3659, 0, <PERSON>OUTH, 15)
    }
}