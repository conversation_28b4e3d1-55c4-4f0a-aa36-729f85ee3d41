package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10650Spawns : NPCSpawnsScript() {
    init {
        LESSER_DEMON_2018(2630, 9867, 0, SOUTH, 8)
        LESSER_DEMON_2007(2631, 9880, 0, SOUTH, 4)
        LESSER_DEMON_2008(2632, 9874, 0, SOUTH, 6)
        GUARDIAN_OF_ARMADYL_3446(2636, 9910, 0, SOUTH, 5)
        SKELETON_82(2637, 9891, 0, SOUTH, 7)
        GUARDIAN_OF_ARMADYL(2638, 9899, 0, SOUTH, 5)
        SKELETON_82(2641, 9889, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        SKELETON_82(2645, 9891, 0, <PERSON>O<PERSON><PERSON>, 7)
        GUARDIAN_OF_ARMADYL_3446(2646, 9906, 0, <PERSON>OUTH, 5)
        SKELETON_82(2648, 9890, 0, <PERSON><PERSON><PERSON>H, 7)
        GUAR<PERSON>AN_OF_ARMADYL(2648, 9901, 0, SOUTH, 5)
        GUARDIAN_OF_ARMADYL(2649, 9912, 0, SOUTH, 5)
        SKELETON_82(2653, 9892, 0, SOUTH, 7)
        WINELDA(2656, 9875, 0, SOUTH, 5)
        SKELETON_82(2657, 9894, 0, SOUTH, 7)
        SKELETON_82(2658, 9888, 0, SOUTH, 7)
        SKELETON_82(2660, 9885, 0, SOUTH, 7)
        SKELETON_82(2660, 9890, 0, SOUTH, 7)
        SKELETON_77(2664, 9877, 0, SOUTH, 7)
    }
}