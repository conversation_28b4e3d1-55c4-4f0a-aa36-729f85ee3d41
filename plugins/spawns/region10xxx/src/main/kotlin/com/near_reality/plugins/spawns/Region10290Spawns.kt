package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10290Spawns : NPCSpawnsScript() {
    init {
        BROTHER_KOJO(2569, 3249, 0, SOUTH, 3)
        BROTHER_OMAD(2604, 3209, 0, SOUTH, 3)
        MONK_4246(2606, 3217, 0, SOUTH, 5)
        5411(2607, 3260, 0, SOUTH, 2)
        MONK_4246(2608, 3209, 0, SOUTH, 5)
        BROTHER_CEDRIC(2614, 3259, 0, SOUTH, 3)
        TORRELL(2617, 3227, 0, SOUTH, 4)
        MONK_4246(2618, 3209, 0, SOUTH, 5)
        TOOL_LEPRECHAUN(2619, 3221, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        MONK_4246(2595, 3210, 1, SOUTH, 5)
    }
}