package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10309Spawns : NPCSpawnsScript() {
    init {
        HOBGOBLIN_3049(2576, 4460, 0, <PERSON>O<PERSON><PERSON>, 13)
        HOBGOBLIN_3049(2576, 4462, 0, <PERSON><PERSON>UT<PERSON>, 13)
        GOLRIE(2579, 4451, 0, SOUTH, 5)
        HOBGOBLIN_3049(2579, 4458, 0, <PERSON><PERSON>UT<PERSON>, 13)
        HOBGOBLIN_3049(2579, 4461, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        HOBGOBLIN_3049(2579, 4463, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        HOBGOBLIN_3049(2581, 4462, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        HOBGOBLIN_3049(2582, 4461, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        ZOMBIE_49(2603, 4446, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        GIANT_BAT(2604, 4446, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        ZOMBIE_27(2605, 4449, 0, SO<PERSON>H, 5)
        GIANT_BAT(2606, 4437, 0, SOUTH, 11)
        GIANT_BAT(2607, 4444, 0, SOUTH, 11)
        ZOMBIE_28(2612, 4447, 0, SOUTH, 4)
    }
}