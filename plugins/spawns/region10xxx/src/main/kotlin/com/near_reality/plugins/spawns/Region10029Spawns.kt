package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10029Spawns : NPCSpawnsScript() {
    init {
        CARNIVOROUS_CHINCHOMPA(2497, 2901, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(2497, 2909, 0, SOUTH, 6)
        TROPICAL_WAGTAIL(2499, 2890, 0, SOUTH, 4)
        CARNIVOROUS_CHINCHOMPA(2501, 2906, 0, SOUTH, 6)
        TROPICAL_WAGTAIL(2502, 2892, 0, SOUTH, 4)
        CARNIVOROUS_CHINCHOMPA(2503, 2881, 0, SOUTH, 6)
        TROPICAL_WAGTAIL(2504, 2888, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        CARNIVOROUS_CHINCHOMPA(2507, 2885, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        TROPICAL_WAGTAIL(2511, 2914, 0, SOUTH, 4)
        TROPICAL_WAGTAIL(2513, 2911, 0, SOUTH, 4)
        TROPICAL_WAGTAIL(2515, 2914, 0, SOUTH, 4)
        TROPICAL_WAGTAIL(2523, 2939, 0, SOUTH, 4)
        HUNTING_EXPERT_1504(2525, 2917, 0, SOUTH, 2)
        TROPICAL_WAGTAIL(2525, 2933, 0, SOUTH, 4)
        TROPICAL_WAGTAIL(2526, 2939, 0, SOUTH, 4)
        HUNTING_EXPERT_5529(2527, 2893, 0, SOUTH, 2)
        BLACK_WARLOCK(2532, 2905, 0, SOUTH, 9)
        TROPICAL_WAGTAIL(2538, 2884, 0, SOUTH, 4)
        BLACK_WARLOCK(2540, 2898, 0, SOUTH, 9)
        BLACK_WARLOCK(2540, 2914, 0, SOUTH, 9)
        TROPICAL_WAGTAIL(2543, 2887, 0, SOUTH, 4)
        SPINED_LARUPIA(2544, 2910, 0, SOUTH, 7)
        TROPICAL_WAGTAIL(2545, 2882, 0, SOUTH, 4)
        BLACK_WARLOCK(2550, 2893, 0, SOUTH, 9)
        SPINED_LARUPIA(2550, 2904, 0, SOUTH, 7)
        BLACK_WARLOCK(2551, 2915, 0, SOUTH, 9)
        CARNIVOROUS_CHINCHOMPA(2553, 2935, 0, SOUTH, 6)
        SPINED_LARUPIA(2556, 2895, 0, SOUTH, 7)
        CARNIVOROUS_CHINCHOMPA(2556, 2914, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(2557, 2932, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(2557, 2936, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(2559, 2911, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(2559, 2918, 0, SOUTH, 6)
    }
}