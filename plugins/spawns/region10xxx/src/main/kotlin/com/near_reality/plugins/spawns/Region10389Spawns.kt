package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10389Spawns : NPCSpawnsScript() {
    init {
        POISON_SPIDER(2570, 9567, 0, SOUT<PERSON>, 11)
        POISON_SPIDER(2575, 9578, 0, SOUT<PERSON>, 11)
        POISON_SPIDER(2577, 9580, 0, SOUTH, 11)
        POISON_SPIDER(2578, 9583, 0, <PERSON>OUTH, 11)
        POISON_SPIDER(2579, 9577, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        POISON_SPIDER(2579, 9583, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        POISON_SPIDER(2581, 9583, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        POISON_SPIDER(2582, 9577, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        P<PERSON><PERSON><PERSON>_SPIDER(2584, 9575, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        PO<PERSON>ON_SPIDER(2590, 9574, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        POISON_SPIDER(2594, 9569, 0, SOUTH, 11)
        POISON_SPIDER(2596, 9556, 0, SOUTH, 11)
        POISON_SPIDER(2598, 9570, 0, SOUTH, 11)
        POISON_SPIDER(2599, 9555, 0, SOUTH, 11)
        PO<PERSON>ON_SPIDER(2599, 9565, 0, SOUTH, 11)
        POISON_SPIDER(2600, 9552, 0, SOUTH, 11)
        POISON_SPIDER(2601, 9559, 0, SOUTH, 11)
        POISON_SPIDER(2603, 9558, 0, SOUTH, 11)
    }
}