package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10142Spawns : NPCSpawnsScript() {
    init {
        WALLASALKI(2496, 10148, 0, SOUTH, 6)
        DAGANNOTH_2259(2497, 10129, 0, SOUTH, 25)
        BOULDER_2262(2497, 10165, 0, SOUTH, 0)
        WALLASALKI(2498, 10146, 0, SOUTH, 6)
        DAGANNOTH_2259(2499, 10126, 0, SOUTH, 25)
        DAGANNOTH_2259(2499, 10128, 0, SOUTH, 25)
        DAGANNOTH_2259(2500, 10123, 0, SOUTH, 25)
        WALLASALKI(2500, 10148, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        BOULDER_2262(2500, 10162, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        DAGANNOTH_2259(2502, 10125, 0, <PERSON><PERSON><PERSON><PERSON>, 25)
        WALLASALKI(2502, 10143, 0, <PERSON>O<PERSON>H, 6)
        WALLASALKI(2503, 10145, 0, SOUTH, 6)
        DAGANNOTH_2259(2504, 10128, 0, SOUTH, 25)
        WALLASALKI(2504, 10143, 0, SOUTH, 6)
        WALLASALKI(2504, 10147, 0, SOUTH, 6)
        BOULDER_2262(2504, 10161, 0, SOUTH, 0)
        BOULDER_2262(2504, 10166, 0, SOUTH, 0)
        DAGANNOTH_2259(2505, 10126, 0, SOUTH, 25)
        WALLASALKI(2506, 10150, 0, SOUTH, 6)
        DAGANNOTH_2259(2508, 10128, 0, SOUTH, 25)
        WALLASALKI(2508, 10145, 0, SOUTH, 6)
        WALLASALKI(2508, 10149, 0, SOUTH, 6)
        BOULDER_2262(2508, 10159, 0, SOUTH, 0)
        BOULDER_2262(2508, 10164, 0, SOUTH, 0)
        DAGANNOTH_2259(2509, 10125, 0, SOUTH, 25)
        WALLASALKI(2509, 10142, 0, SOUTH, 6)
        DAGANNOTH_2259(2511, 10130, 0, SOUTH, 25)
        WALLASALKI(2512, 10144, 0, SOUTH, 6)
        WALLASALKI(2512, 10147, 0, SOUTH, 6)
        BOULDER_2262(2512, 10160, 0, SOUTH, 0)
        DAGANNOTH_2259(2513, 10129, 0, SOUTH, 25)
        WALLASALKI(2513, 10142, 0, SOUTH, 6)
        BOULDER_2262(2513, 10164, 0, SOUTH, 0)
        WALLASALKI(2514, 10148, 0, SOUTH, 6)
        DAGANNOTH_2259(2515, 10124, 0, SOUTH, 25)
        DAGANNOTH_2259(2515, 10127, 0, SOUTH, 25)
        BOULDER_2262(2516, 10161, 0, SOUTH, 0)
        WALLASALKI(2517, 10144, 0, SOUTH, 6)
        WALLASALKI(2517, 10150, 0, SOUTH, 6)
        DAGANNOTH_2259(2518, 10121, 0, SOUTH, 25)
        DAGANNOTH_2259(2518, 10129, 0, SOUTH, 25)
        WALLASALKI(2518, 10141, 0, SOUTH, 6)
        BOULDER_2262(2518, 10165, 0, SOUTH, 0)
        DAGANNOTH_2259(2519, 10126, 0, SOUTH, 25)
        WALLASALKI(2520, 10144, 0, SOUTH, 6)
        WALLASALKI(2520, 10148, 0, SOUTH, 6)
        DAGANNOTH_2259(2521, 10122, 0, SOUTH, 25)
        BOULDER_2262(2521, 10158, 0, SOUTH, 0)
        BOULDER_2262(2521, 10163, 0, SOUTH, 0)
        DAGANNOTH_2259(2522, 10120, 0, SOUTH, 25)
        DAGANNOTH_2259(2522, 10125, 0, SOUTH, 25)
        WALLASALKI(2522, 10142, 0, SOUTH, 6)
        WALLASALKI(2522, 10149, 0, SOUTH, 6)
        DAGANNOTH_2259(2523, 10128, 0, SOUTH, 25)
        WALLASALKI(2523, 10145, 0, SOUTH, 6)
        BOULDER_2262(2523, 10166, 0, SOUTH, 0)
        DAGANNOTH_2259(2525, 10127, 0, SOUTH, 25)
        WALLASALKI(2525, 10147, 0, SOUTH, 6)
        DAGANNOTH_2259(2526, 10124, 0, SOUTH, 25)
        WALLASALKI(2526, 10142, 0, SOUTH, 6)
        BOULDER_2262(2526, 10163, 0, SOUTH, 0)
        DAGANNOTH_2259(2528, 10122, 0, SOUTH, 25)
        BOULDER_2262(2528, 10161, 0, SOUTH, 0)
        DAGANNOTH_2259(2529, 10125, 0, SOUTH, 25)
        DAGANNOTH_2259(2529, 10129, 0, SOUTH, 25)
        WALLASALKI(2529, 10144, 0, SOUTH, 6)
        WALLASALKI(2530, 10141, 0, SOUTH, 6)
        DAGANNOTH_2259(2531, 10122, 0, SOUTH, 25)
        DAGANNOTH_2259(2531, 10125, 0, SOUTH, 25)
        DAGANNOTH_2259(2531, 10129, 0, SOUTH, 25)
        BOULDER_2262(2531, 10165, 0, SOUTH, 0)
        BOULDER_2262(2532, 10160, 0, SOUTH, 0)
        DAGANNOTH_2259(2533, 10126, 0, SOUTH, 25)
        WALLASALKI(2533, 10142, 0, SOUTH, 6)
        DAGANNOTH_2259(2536, 10123, 0, SOUTH, 25)
        DAGANNOTH_2259(2536, 10127, 0, SOUTH, 25)
        DAGANNOTH_2259(2536, 10130, 0, SOUTH, 25)
        WALLASALKI(2536, 10142, 0, SOUTH, 6)
        BOULDER_2262(2536, 10162, 0, SOUTH, 0)
        BOULDER_2262(2537, 10158, 0, SOUTH, 0)
        DAGANNOTH_2259(2538, 10127, 0, SOUTH, 25)
        BOULDER_2262(2538, 10166, 0, SOUTH, 0)
        DAGANNOTH_2259(2540, 10127, 0, SOUTH, 25)
        BOULDER_2262(2540, 10162, 0, SOUTH, 0)
        DAGANNOTH_2259(2541, 10130, 0, SOUTH, 25)
        BOULDER_2262(2542, 10166, 0, SOUTH, 0)
        DOORSUPPORT_2253(2543, 10143, 0, EAST, 0)
        DAGANNOTH_2259(2544, 10131, 0, SOUTH, 25)
        BOULDER_2262(2544, 10158, 0, SOUTH, 0)
        DAGANNOTH_2259(2545, 10129, 0, SOUTH, 25)
        DOORSUPPORT(2545, 10141, 0, NORTH, 0)
        DOORSUPPORT_2256(2545, 10145, 0, SOUTH, 0)
        EGG_5932(2546, 10142, 0, SOUTH, 0)
        EGG_5932(2546, 10144, 0, SOUTH, 0)
        BOULDER_2262(2546, 10150, 0, SOUTH, 0)
        BOULDER_2262(2546, 10155, 0, SOUTH, 0)
        BOULDER_2262(2546, 10165, 0, SOUTH, 0)
        DAGANNOTH_2259(2548, 10130, 0, SOUTH, 25)
        BOULDER_2262(2549, 10153, 0, SOUTH, 0)
        DAGANNOTH_2259(2550, 10134, 0, SOUTH, 25)
    }
}