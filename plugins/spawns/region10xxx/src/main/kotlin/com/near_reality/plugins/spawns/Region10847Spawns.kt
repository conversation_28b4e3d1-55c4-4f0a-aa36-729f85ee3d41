package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10847Spawns : NPCSpawnsScript() {
    init {
        BUTTERFLY_235(2688, 6092, 0, SOUTH, 7)
        RABBIT_3420(2688, 6108, 0, SOUTH, 4)
        BUTTERFLY(2689, 6083, 0, SOUTH, 7)
        BUTTERFLY(2689, 6088, 0, SOUTH, 7)
        RABBIT_3421(2689, 6107, 0, SOUTH, 3)
        BUTTERFLY_235(2690, 6087, 0, SOUTH, 7)
        BUTTERFLY(2690, 6094, 0, SOUTH, 7)
        BUTTERFLY_235(2690, 6095, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        RABBIT_3422(2690, 6106, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        RABBIT_3421(2690, 6108, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        BUTTERFLY(2691, 6084, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        RABBIT_3420(2691, 6105, 0, SOUTH, 4)
        BUTTERFLY(2692, 6081, 0, SOUTH, 7)
        BUTTERFLY_235(2692, 6082, 0, SOUTH, 7)
        BUTTERFLY_235(2692, 6088, 0, SOUTH, 7)
        BUTTERFLY(2692, 6090, 0, SOUTH, 7)
        BUTTERFLY_235(2692, 6123, 0, SOUTH, 7)
        BUTTERFLY_235(2693, 6081, 0, SOUTH, 7)
        BUTTERFLY(2693, 6083, 0, SOUTH, 7)
        BUTTERFLY(2693, 6085, 0, SOUTH, 7)
        BUTTERFLY(2693, 6090, 0, SOUTH, 7)
        BUTTERFLY(2694, 6083, 0, SOUTH, 7)
        BUTTERFLY(2694, 6086, 0, SOUTH, 7)
        BUTTERFLY_235(2694, 6125, 0, SOUTH, 7)
        BUTTERFLY(2695, 6082, 0, SOUTH, 7)
        BUTTERFLY(2695, 6085, 0, SOUTH, 7)
        BUTTERFLY_235(2695, 6087, 0, SOUTH, 7)
        BUTTERFLY(2695, 6088, 0, SOUTH, 7)
        BUTTERFLY(2695, 6091, 0, SOUTH, 7)
        BUTTERFLY(2695, 6121, 0, SOUTH, 7)
        BUTTERFLY_235(2697, 6086, 0, SOUTH, 7)
        BUTTERFLY_235(2697, 6087, 0, SOUTH, 7)
        BUTTERFLY_235(2698, 6081, 0, SOUTH, 7)
        BUTTERFLY(2698, 6121, 0, SOUTH, 7)
        BUTTERFLY_235(2698, 6124, 0, SOUTH, 7)
        BUTTERFLY(2699, 6082, 0, SOUTH, 7)
        BUTTERFLY(2699, 6085, 0, SOUTH, 7)
        BUTTERFLY_235(2700, 6083, 0, SOUTH, 7)
        BUTTERFLY(2700, 6084, 0, SOUTH, 7)
        BUTTERFLY(2701, 6085, 0, SOUTH, 7)
        BUTTERFLY(2702, 6081, 0, SOUTH, 7)
        BUTTERFLY(2702, 6083, 0, SOUTH, 7)
        BUTTERFLY_235(2702, 6084, 0, SOUTH, 7)
        BUTTERFLY(2703, 6119, 0, SOUTH, 7)
        4556(2704, 6111, 0, SOUTH, 5)
        4259(2704, 6112, 0, SOUTH, 3)
        BUTTERFLY(2704, 6117, 0, SOUTH, 7)
        BUTTERFLY(2705, 6121, 0, SOUTH, 7)
        RABBIT_3420(2712, 6084, 0, SOUTH, 4)
        RABBIT_3422(2713, 6085, 0, SOUTH, 3)
        RABBIT_3420(2721, 6126, 0, SOUTH, 4)
        RABBIT_3420(2722, 6108, 0, SOUTH, 4)
        BUTTERFLY_238(2723, 6105, 0, SOUTH, 0)
        BUTTERFLY_237(2725, 6127, 0, SOUTH, 7)
        BUTTERFLY_235(2726, 6085, 0, SOUTH, 7)
        RABBIT_3422(2726, 6104, 0, SOUTH, 3)
        BUTTERFLY_235(2727, 6081, 0, SOUTH, 7)
        RABBIT_3420(2727, 6102, 0, SOUTH, 4)
        BUTTERFLY_235(2729, 6083, 0, SOUTH, 7)
        WILL_O_THE_WISP(2729, 6086, 0, SOUTH, 0)
        WILL_O_THE_WISP_3441(2730, 6084, 0, SOUTH, 0)
        BUTTERFLY(2730, 6091, 0, SOUTH, 7)
        WILL_O_THE_WISP(2731, 6080, 0, SOUTH, 0)
        WILL_O_THE_WISP(2731, 6089, 0, SOUTH, 0)
        BUTTERFLY_235(2732, 6084, 0, SOUTH, 7)
        BUTTERFLY(2732, 6095, 0, SOUTH, 7)
        BUTTERFLY(2734, 6090, 0, SOUTH, 7)
        BUTTERFLY(2735, 6094, 0, SOUTH, 7)
        BUTTERFLY(2735, 6097, 0, SOUTH, 7)
        BUTTERFLY(2738, 6098, 0, SOUTH, 7)
        GRIZZLY_BEAR_CUB(2739, 6100, 0, SOUTH, 8)
        BUTTERFLY_238(2739, 6123, 0, SOUTH, 0)
        GRIZZLY_BEAR_CUB_3425(2740, 6097, 0, SOUTH, 10)
        GRIZZLY_BEAR_3423(2741, 6100, 0, SOUTH, 11)
        RABBIT_3420(2741, 6119, 0, SOUTH, 4)
        DIRE_WOLF(2742, 6129, 0, SOUTH, 12)
        RABBIT_3420(2743, 6121, 0, SOUTH, 4)
        BUTTERFLY_238(2744, 6108, 0, SOUTH, 0)
        RABBIT_3422(2744, 6120, 0, SOUTH, 3)
        DIRE_WOLF(2744, 6131, 0, SOUTH, 12)
        WILL_O_THE_WISP(2744, 6139, 0, SOUTH, 0)
        RABBIT_3421(2745, 6119, 0, SOUTH, 3)
        RABBIT_3420(2745, 6120, 0, SOUTH, 4)
    }
}