package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10649Spawns : NPCSpawnsScript() {
    init {
        SCORPION_3024(2637, 9809, 0, SOUTH, 12)
        SCORPION_3024(2639, 9814, 0, SOUTH, 12)
        SCORPION_3024(2641, 9811, 0, SOUTH, 12)
        SCORPION_3024(2641, 9819, 0, SOUTH, 12)
        SCORPION_3024(2644, 9822, 0, SOUTH, 12)
        SKELETON_82(2658, 9820, 0, SOUT<PERSON>, 7)
        GIANT_BAT(2659, 9809, 0, SOUTH, 11)
        GIANT_BAT(2663, 9804, 0, <PERSON>O<PERSON><PERSON>, 11)
        SKELETON_82(2664, 9831, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        GIANT_BAT(2665, 9812, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        SKELETON_82(2667, 9824, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        SKELETON_82(2670, 9828, 0, SO<PERSON><PERSON>, 7)
        SKELETON_82(2671, 9822, 0, SOUTH, 7)
    }
}