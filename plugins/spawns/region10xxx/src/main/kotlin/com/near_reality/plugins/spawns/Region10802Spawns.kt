package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10802Spawns : NPCSpawnsScript() {
    init {
        MOSS_GIANT_2092(2691, 3215, 0, SOUTH, 3)
        MOSS_GIANT_2091(2692, 3204, 0, SOUTH, 4)
        MOSS_GIANT_2091(2695, 3216, 0, SOUTH, 4)
        MOSS_GIANT(2698, 3206, 0, <PERSON>OUTH, 4)
        MOSS_GIANT_2093(2699, 3212, 0, <PERSON>O<PERSON><PERSON>, 4)
        POISON_SCORPION(2715, 3218, 0, SOUTH, 8)
        POISON_SCORPION(2719, 3223, 0, <PERSON>OUT<PERSON>, 8)
        POISON_SCORPION(2720, 3212, 0, <PERSON>OUTH, 8)
        POISON_SCORPION(2722, 3220, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        POISON_SCORPION(2724, 3215, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        POISON_SCORPION(2729, 3224, 0, SOUTH, 8)
        POISON_SCORPION(2733, 3225, 0, SOUTH, 8)
    }
}