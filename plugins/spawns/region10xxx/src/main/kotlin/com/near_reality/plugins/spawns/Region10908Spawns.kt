package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10908Spawns : NPCSpawnsScript() {
    init {
        KURASK_410(2694, 9995, 0, SOUTH, 7)
        KURASK_411(2694, 9999, 0, SOUT<PERSON>, 4)
        KURASK_410(2698, 9992, 0, SOUTH, 7)
        KURASK_410(2699, 10001, 0, SOUTH, 7)
        JELLY(2699, 10029, 0, SOUTH, 6)
        KURASK_410(2701, 9996, 0, SOUTH, 7)
        JELLY_438(2701, 10023, 0, SOUTH, 3)
        JELLY_441(2701, 10027, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        JELLY_442(2703, 10025, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        JELLY_438(2703, 10031, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        JELLY_442(2704, 10028, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        JELLY_439(2706, 10029, 0, SOUTH, 3)
        J<PERSON>LY_441(2708, 10024, 0, SOUTH, 6)
        JELLY_440(2708, 10027, 0, SOUTH, 6)
        JELLY_440(2711, 10029, 0, SOUTH, 6)
        TUROTH_429(2717, 10007, 0, SOUTH, 4)
        TUROTH_427(2718, 10006, 0, SOUTH, 4)
        TUROTH_429(2718, 10008, 0, SOUTH, 4)
        TUROTH_430(2719, 10011, 0, SOUTH, 4)
        TUROTH_429(2720, 10003, 0, SOUTH, 4)
        TUROTH_428(2721, 10003, 0, SOUTH, 4)
        TUROTH_429(2721, 10009, 0, SOUTH, 4)
        TUROTH_429(2721, 10014, 0, SOUTH, 4)
        TUROTH_428(2722, 10008, 0, SOUTH, 4)
        TUROTH_428(2723, 9994, 0, SOUTH, 4)
        TUROTH_429(2723, 10002, 0, SOUTH, 4)
        TUROTH_429(2723, 10010, 0, SOUTH, 4)
        TUROTH_429(2724, 9996, 0, SOUTH, 4)
        TUROTH_430(2724, 10013, 0, SOUTH, 4)
        TUROTH_429(2725, 9995, 0, SOUTH, 4)
        TUROTH_430(2725, 10012, 0, SOUTH, 4)
        TUROTH_430(2726, 10010, 0, SOUTH, 4)
        TUROTH_430(2726, 10013, 0, SOUTH, 4)
        TUROTH_430(2727, 9995, 0, SOUTH, 4)
        TUROTH_430(2727, 10011, 0, SOUTH, 4)
        TUROTH_430(2728, 9996, 0, SOUTH, 4)
        TUROTH_430(2728, 9997, 0, SOUTH, 4)
        BASILISK_417(2738, 10008, 0, SOUTH, 6)
        BASILISK_417(2740, 10003, 0, SOUTH, 6)
        BASILISK_417(2740, 10014, 0, SOUTH, 6)
        BASILISK_417(2742, 10008, 0, SOUTH, 6)
        BASILISK_417(2743, 10018, 0, SOUTH, 6)
        BASILISK_417(2745, 10002, 0, SOUTH, 6)
        BASILISK_417(2746, 10006, 0, SOUTH, 6)
        BASILISK_417(2746, 10015, 0, SOUTH, 6)
        JELLY_7518(2706, 9984, 0, WEST, 0)
    }
}