package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10639Spawns : NPCSpawnsScript() {
    init {
        MANIACAL_MONKEY_ARCHER(2637, 9198, 0, SOUTH, 0)
        MANIACAL_MONKEY_7118(2638, 9195, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2639, 9193, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2640, 9199, 0, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2642, 9193, 0, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2642, 9200, 0, <PERSON>OUT<PERSON>, 0)
        MANIACAL_MONKEY_7118(2643, 9195, 0, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANI<PERSON>AL_MONKEY_7118(2644, 9199, 0, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANIACAL_MONKEY_7118(2645, 9192, 0, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANIAC<PERSON>_MONKEY_ARCHER(2646, 9197, 0, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2657, 9153, 0, SOUTH, 0)
        MANIACAL_MONKEY_7118(2657, 9155, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2660, 9153, 0, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2660, 9156, 0, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2663, 9154, 0, SOUTH, 0)
        MANIACAL_MONKEY_7118(2664, 9157, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2665, 9199, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2665, 9203, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2666, 9153, 0, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2666, 9156, 0, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2666, 9202, 0, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2667, 9197, 0, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2667, 9205, 0, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2668, 9200, 0, SOUTH, 0)
        MANIACAL_MONKEY_7118(2669, 9198, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2670, 9206, 0, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2671, 9199, 0, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2671, 9205, 0, SOUTH, 0)
        MANIACAL_MONKEY_7118(2672, 9202, 0, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2627, 9201, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2628, 9199, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2631, 9201, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2648, 9184, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2652, 9200, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2653, 9177, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2655, 9175, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2657, 9202, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2657, 9203, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2663, 9173, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2664, 9169, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2664, 9180, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2665, 9185, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2666, 9173, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2666, 9184, 1, SOUTH, 0)
    }
}