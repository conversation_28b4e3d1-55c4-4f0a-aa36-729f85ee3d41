package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10307Spawns : NPCSpawnsScript() {
    init {
        BABY_IMPLING_1645(2563, 4291, 0, SOUTH, 5)
        IMP_DEFENDER(2563, 4292, 0, SOUTH, 21)
        BABY_IMPLING_1645(2563, 4348, 0, SOUTH, 5)
        YOUNG_IMPLING_1646(2564, 4321, 0, SOUTH, 5)
        IMP_DEFENDER(2564, 4348, 0, SOUTH, 21)
        IMP_DEFENDER(2566, 4294, 0, SOUTH, 21)
        IMP_DEFENDER(2566, 4345, 0, SOUT<PERSON>, 21)
        ECLECTIC_IMPLING_1650(2567, 4319, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GOURMET_IMPLING_1647(2568, 4296, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        EARTH_IMPLING_1648(2568, 4317, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        BABY_IMPLING_1645(2568, 4323, 0, SOUTH, 5)
        GOURMET_IMPLING_1647(2568, 4327, 0, SOUTH, 5)
        IMP_DEFENDER(2569, 4297, 0, SOUTH, 21)
        9391(2569, 4342, 0, SOUTH, 0)
        9391(2570, 4297, 0, SOUTH, 0)
        EARTH_IMPLING_1648(2570, 4330, 0, SOUTH, 5)
        9392(2570, 4332, 0, SOUTH, 0)
        IMP_DEFENDER(2570, 4342, 0, SOUTH, 21)
        BABY_IMPLING_1645(2571, 4305, 0, SOUTH, 5)
        BABY_IMPLING_1645(2571, 4337, 0, SOUTH, 5)
        IMP_DEFENDER(2572, 4300, 0, SOUTH, 21)
        9391(2572, 4321, 0, SOUTH, 0)
        IMP_DEFENDER(2572, 4339, 0, SOUTH, 21)
        EARTH_IMPLING_1648(2574, 4305, 0, SOUTH, 5)
        GOURMET_IMPLING_1647(2574, 4311, 0, SOUTH, 5)
        ESSENCE_IMPLING_1649(2574, 4317, 0, SOUTH, 5)
        YOUNG_IMPLING_1646(2574, 4321, 0, SOUTH, 5)
        9392(2574, 4322, 0, SOUTH, 0)
        YOUNG_IMPLING_1646(2574, 4331, 0, SOUTH, 5)
        IMP_DEFENDER(2575, 4303, 0, SOUTH, 21)
        9391(2575, 4306, 0, SOUTH, 0)
        IMP_DEFENDER(2575, 4336, 0, SOUTH, 21)
        9390(2576, 4336, 0, SOUTH, 0)
        ESSENCE_IMPLING_1649(2576, 4337, 0, SOUTH, 5)
        9392(2578, 4299, 0, SOUTH, 0)
        IMP_DEFENDER(2578, 4306, 0, SOUTH, 21)
        IMP_DEFENDER(2578, 4333, 0, SOUTH, 21)
        9391(2578, 4334, 0, SOUTH, 0)
        GOURMET_IMPLING_1647(2580, 4343, 0, SOUTH, 5)
        BABY_IMPLING_1645(2581, 4300, 0, SOUTH, 5)
        IMP_DEFENDER(2581, 4309, 0, SOUTH, 21)
        IMP_DEFENDER(2581, 4330, 0, SOUTH, 21)
        BABY_IMPLING_1645(2584, 4344, 0, SOUTH, 5)
        GOURMET_IMPLING_1647(2585, 4296, 0, SOUTH, 5)
        ESSENCE_IMPLING_1649(2585, 4298, 0, SOUTH, 5)
        9392(2585, 4340, 0, SOUTH, 0)
        ELNOCK_INQUISITOR(2586, 4314, 0, SOUTH, 4)
        YOUNG_IMPLING_1646(2587, 4300, 0, SOUTH, 5)
        EARTH_IMPLING_1648(2587, 4342, 0, SOUTH, 5)
        9392(2589, 4299, 0, SOUTH, 0)
        EARTH_IMPLING_1648(2590, 4298, 0, SOUTH, 5)
        9394(2590, 4319, 0, SOUTH, 0)
        9395(2590, 4320, 0, SOUTH, 0)
        YOUNG_IMPLING_1646(2590, 4348, 0, SOUTH, 5)
        ECLECTIC_IMPLING_1650(2591, 4295, 0, SOUTH, 5)
        9391(2591, 4301, 0, SOUTH, 0)
        9397(2591, 4319, 0, SOUTH, 0)
        9391(2591, 4339, 0, SOUTH, 0)
        ECLECTIC_IMPLING_1650(2591, 4340, 0, SOUTH, 5)
        YOUNG_IMPLING_1646(2592, 4291, 0, SOUTH, 5)
        IMMENIZZ(2592, 4320, 0, SOUTH, 4)
        9396(2593, 4319, 0, SOUTH, 0)
        YOUNG_IMPLING_1646(2595, 4343, 0, SOUTH, 5)
        BABY_IMPLING_1645(2596, 4296, 0, SOUTH, 5)
        GOURMET_IMPLING_1647(2597, 4293, 0, SOUTH, 5)
        9392(2598, 4302, 0, SOUTH, 0)
        EARTH_IMPLING_1648(2598, 4340, 0, SOUTH, 5)
        ESSENCE_IMPLING_1649(2601, 4343, 0, SOUTH, 5)
        IMP_DEFENDER(2602, 4310, 0, SOUTH, 21)
        IMP_DEFENDER(2602, 4330, 0, SOUTH, 21)
        GOURMET_IMPLING_1647(2602, 4346, 0, SOUTH, 5)
        9392(2603, 4340, 0, SOUTH, 0)
        IMP_DEFENDER(2604, 4306, 0, SOUTH, 21)
        9391(2605, 4306, 0, SOUTH, 0)
        IMP_DEFENDER(2605, 4333, 0, SOUTH, 21)
        9391(2605, 4334, 0, SOUTH, 0)
        IMP_DEFENDER(2608, 4303, 0, SOUTH, 21)
        IMP_DEFENDER(2608, 4336, 0, SOUTH, 21)
        GOURMET_IMPLING_1647(2609, 4317, 0, SOUTH, 5)
        9392(2609, 4322, 0, SOUTH, 0)
        BABY_IMPLING_1645(2609, 4339, 0, SOUTH, 5)
        9390(2610, 4301, 0, SOUTH, 0)
        BABY_IMPLING_1645(2610, 4304, 0, SOUTH, 5)
        9391(2610, 4320, 0, SOUTH, 0)
        IMP_DEFENDER(2611, 4300, 0, SOUTH, 21)
        EARTH_IMPLING_1648(2611, 4334, 0, SOUTH, 5)
        IMP_DEFENDER(2611, 4339, 0, SOUTH, 21)
        YOUNG_IMPLING_1646(2612, 4309, 0, SOUTH, 5)
        EARTH_IMPLING_1648(2612, 4310, 0, SOUTH, 5)
        ESSENCE_IMPLING_1649(2612, 4318, 0, SOUTH, 5)
        YOUNG_IMPLING_1646(2612, 4327, 0, SOUTH, 5)
        IMP_DEFENDER(2614, 4297, 0, SOUTH, 21)
        9392(2614, 4308, 0, SOUTH, 0)
        IMP_DEFENDER(2614, 4342, 0, SOUTH, 21)
        GOURMET_IMPLING_1647(2615, 4298, 0, SOUTH, 5)
        BABY_IMPLING_1645(2615, 4322, 0, SOUTH, 5)
        ECLECTIC_IMPLING_1650(2615, 4326, 0, SOUTH, 5)
        9392(2615, 4333, 0, SOUTH, 0)
        GOURMET_IMPLING_1647(2615, 4342, 0, SOUTH, 5)
        IMP_DEFENDER(2616, 4294, 0, SOUTH, 21)
        9391(2616, 4343, 0, SOUTH, 0)
        9391(2617, 4295, 0, SOUTH, 0)
        IMP_DEFENDER(2617, 4345, 0, SOUTH, 21)
        GOURMET_IMPLING_1647(2618, 4321, 0, SOUTH, 5)
        IMP_DEFENDER(2619, 4291, 0, SOUTH, 21)
        YOUNG_IMPLING_1646(2619, 4322, 0, SOUTH, 5)
        IMP_DEFENDER(2619, 4348, 0, SOUTH, 21)
        BABY_IMPLING_1645(2620, 4291, 0, SOUTH, 5)
        BABY_IMPLING_1645(2620, 4348, 0, SOUTH, 5)
    }
}