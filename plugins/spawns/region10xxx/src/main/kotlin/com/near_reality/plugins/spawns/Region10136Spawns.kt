package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10136Spawns : NPCSpawnsScript() {
    init {
        6205(2517, 9755, 0, SOUTH, 5)
        4006(2537, 9745, 0, SOUTH, 13)
        3435(2539, 9741, 0, SOUTH, 4)
        3431(2540, 9741, 0, SOUTH, 5)
        3983(2542, 9744, 0, SOUTH, 3)
        1151(2545, 9746, 0, <PERSON>OUTH, 2)
        3430(2546, 9745, 0, SOUTH, 2)
    }
}