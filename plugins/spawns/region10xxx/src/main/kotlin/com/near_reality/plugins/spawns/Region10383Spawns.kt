package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10383Spawns : NPCSpawnsScript() {
    init {
        MANIACAL_MONKEY_7118(2617, 9207, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2617, 9214, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2618, 9213, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2619, 9206, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2620, 9202, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2622, 9202, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2623, 9200, 1, <PERSON><PERSON><PERSON><PERSON>, 0)
    }
}