package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10390Spawns : NPCSpawnsScript() {
    init {
        GOBLIN_3046(2562, 9657, 0, SOUTH, 5)
        GOBLIN_3045(2562, 9659, 0, SOUTH, 22)
        GOBLIN_3045(2563, 9661, 0, SOUTH, 22)
        THIEF_4247(2564, 9609, 0, SOUTH, 2)
        GOBLIN_3028(2564, 9653, 0, SOUTH, 34)
        GOBLIN_3028(2565, 9655, 0, SOUTH, 34)
        THIEF_4247(2566, 9605, 0, SOUTH, 2)
        GOBLIN_3028(2566, 9626, 0, <PERSON><PERSON><PERSON><PERSON>, 34)
        HOBGOBLIN_3049(2566, 9632, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        GOBLIN_3028(2567, 9653, 0, <PERSON><PERSON><PERSON><PERSON>, 34)
        RAT_2854(2568, 9620, 0, <PERSON>OUTH, 14)
        HEAD_THIEF(2569, 9606, 0, SOUTH, 2)
        GOBLIN_3028(2569, 9633, 0, SOUTH, 34)
        GOBLIN_3046(2571, 9653, 0, SOUTH, 5)
        RAT_2854(2573, 9612, 0, SOUTH, 14)
        HOBGO<PERSON>IN_3049(2573, 9626, 0, SOUTH, 13)
        DUNGEON_RAT(2573, 9630, 0, SOUTH, 2)
        RAT_2854(2579, 9631, 0, SOUTH, 14)
        DUNGEON_RAT_3607(2579, 9655, 0, SOUTH, 5)
        RAT_2854(2580, 9600, 0, SOUTH, 14)
        RAT_2854(2580, 9614, 0, SOUTH, 14)
        RAT_2854(2580, 9620, 0, SOUTH, 14)
        RAT_2854(2580, 9626, 0, SOUTH, 14)
        DUNGEON_RAT_3608(2581, 9656, 0, SOUTH, 5)
        DUNGEON_RAT_3607(2581, 9659, 0, SOUTH, 5)
        RAT_2854(2583, 9632, 0, SOUTH, 14)
        DUNGEON_RAT_3607(2583, 9659, 0, SOUTH, 5)
        RAT_2854(2584, 9625, 0, SOUTH, 14)
        RAT_2854(2584, 9637, 0, SOUTH, 14)
        DUNGEON_RAT_3609(2584, 9656, 0, SOUTH, 5)
        OGRE_2095(2585, 9608, 0, SOUTH, 2)
        OGRE_2095(2586, 9614, 0, SOUTH, 2)
        DUNGEON_RAT_3609(2586, 9659, 0, SOUTH, 5)
        DUNGEON_RAT_3607(2587, 9656, 0, SOUTH, 5)
        OGRE_2095(2588, 9606, 0, SOUTH, 2)
        RAT_2854(2589, 9644, 0, SOUTH, 14)
        DUNGEON_RAT_3608(2589, 9654, 0, SOUTH, 5)
        DUNGEON_RAT_3607(2589, 9658, 0, SOUTH, 5)
        RAT_2854(2590, 9601, 0, SOUTH, 14)
        RAT_2854(2590, 9638, 0, SOUTH, 14)
        RAT_2854(2591, 9601, 0, SOUTH, 14)
        RAT_2854(2591, 9621, 0, SOUTH, 14)
        DUNGEON_RAT_3608(2591, 9656, 0, SOUTH, 5)
        DUNGEON_RAT_3609(2591, 9659, 0, SOUTH, 5)
        RAT_2854(2594, 9636, 0, SOUTH, 14)
        RAT_2854(2594, 9644, 0, SOUTH, 14)
        RAT_2854(2597, 9604, 0, SOUTH, 14)
        GOBLIN_3045(2599, 9626, 0, SOUTH, 22)
        GOBLIN_3045(2601, 9627, 0, SOUTH, 22)
        GIANT_SPIDER_3017(2602, 9640, 0, SOUTH, 10)
        GIANT_SPIDER_3017(2603, 9635, 0, SOUTH, 10)
        GIANT_SPIDER_3017(2603, 9638, 0, SOUTH, 10)
        GOBLIN_3045(2605, 9621, 0, SOUTH, 22)
        GIANT_SPIDER_3017(2605, 9637, 0, SOUTH, 10)
        GIANT_SPIDER_3017(2605, 9639, 0, SOUTH, 10)
        GIANT_SPIDER_3017(2606, 9635, 0, SOUTH, 10)
        GIANT_SPIDER_3017(2606, 9646, 0, SOUTH, 10)
        RAT_2854(2607, 9615, 0, SOUTH, 14)
        GOBLIN_3028(2607, 9621, 0, SOUTH, 34)
        GIANT_SPIDER_3017(2607, 9637, 0, SOUTH, 10)
        GIANT_SPIDER_3017(2607, 9641, 0, SOUTH, 10)
        GIANT_SPIDER_3017(2607, 9644, 0, SOUTH, 10)
        GIANT_SPIDER_3017(2607, 9648, 0, SOUTH, 10)
        RAT_2854(2608, 9628, 0, SOUTH, 14)
        GIANT_SPIDER_3017(2608, 9635, 0, SOUTH, 10)
        GIANT_SPIDER_3017(2608, 9642, 0, SOUTH, 10)
        GIANT_SPIDER_3017(2609, 9639, 0, SOUTH, 10)
        GIANT_SPIDER_3017(2609, 9643, 0, SOUTH, 10)
        GOBLIN_3045(2610, 9620, 0, SOUTH, 22)
        RAT_2854(2614, 9651, 0, SOUTH, 14)
        RAT_2854(2614, 9656, 0, SOUTH, 14)
        RAT_2854(2615, 9647, 0, SOUTH, 14)
        RAT_2854(2615, 9661, 0, SOUTH, 14)
        RAT_2854(2616, 9633, 0, SOUTH, 14)
        RAT_2854(2618, 9630, 0, SOUTH, 14)
    }
}