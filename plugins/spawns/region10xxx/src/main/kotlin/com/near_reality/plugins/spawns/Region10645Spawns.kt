package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10645Spawns : NPCSpawnsScript() {
    init {
        MOSS_GIANT(2636, 9544, 0, SOUTH, 4)
        MOSS_GIANT_2091(2640, 9583, 0, SOUTH, 4)
        MOSS_GIANT_2091(2647, 9554, 0, SOUTH, 4)
        MOSS_GIANT_2092(2648, 9537, 0, SOUTH, 3)
        MOSS_GIANT(2651, 9564, 0, SOUTH, 4)
        MOSS_GIANT(2653, 9574, 0, SOUTH, 4)
        MOSS_GIANT_2092(2654, 9587, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        MOSS_GIANT_2093(2659, 9548, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        MOSS_GIANT(2663, 9556, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        MOSS_GIANT(2670, 9544, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        MOSS_GIANT_2091(2673, 9592, 0, SOUTH, 4)
        MOSS_GIANT_2091(2680, 9544, 0, SOUTH, 4)
        MOSS_GIANT_2092(2680, 9594, 0, SOUTH, 3)
        MOSS_GIANT_2092(2682, 9573, 0, SOUTH, 3)
        FIRE_GIANT_2079(2628, 9543, 2, SOUTH, 2)
        FIRE_GIANT_2080(2630, 9551, 2, SOUTH, 4)
        FIRE_GIANT_2078(2632, 9585, 2, SOUTH, 6)
        FIRE_GIANT_2079(2634, 9577, 2, SOUTH, 2)
        FIRE_GIANT_2078(2635, 9557, 2, SOUTH, 6)
        FIRE_GIANT_2079(2635, 9562, 2, SOUTH, 2)
        FIRE_GIANT_2080(2640, 9566, 2, SOUTH, 4)
        FIRE_GIANT_2079(2643, 9563, 2, SOUTH, 2)
        BABY_GREEN_DRAGON(2657, 9570, 2, SOUTH, 3)
        BABY_GREEN_DRAGON(2660, 9582, 2, SOUTH, 3)
        BABY_GREEN_DRAGON_5872(2662, 9573, 2, SOUTH, 2)
        BABY_GREEN_DRAGON_5873(2663, 9578, 2, SOUTH, 4)
        BABY_GREEN_DRAGON(2664, 9581, 2, SOUTH, 3)
        BABY_GREEN_DRAGON_5873(2667, 9582, 2, SOUTH, 4)
        BABY_GREEN_DRAGON(2669, 9577, 2, SOUTH, 3)
        BABY_GREEN_DRAGON(2670, 9580, 2, SOUTH, 3)
        BABY_GREEN_DRAGON(2673, 9576, 2, SOUTH, 3)
        BABY_GREEN_DRAGON(2676, 9574, 2, SOUTH, 3)
        BABY_GREEN_DRAGON_5873(2677, 9570, 2, SOUTH, 4)
    }
}