package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10647Spawns : NPCSpawnsScript() {
    init {
        GIANT_RAT_2856(2631, 9695, 0, SOUTH, 6)
        GIANT_RAT_2857(2636, 9697, 0, SOUTH, 4)
        RAT_2854(2641, 9698, 0, SOUTH, 14)
        GIANT_RAT_2858(2642, 9712, 0, SOUTH, 2)
        GIANT_RAT_2858(2644, 9696, 0, SOUTH, 2)
        GIANT_RAT_2857(2644, 9708, 0, SOUTH, 4)
        RAT_2854(2645, 9711, 0, SOUTH, 14)
        GIANT_RAT_2856(2647, 9701, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        GIANT_RAT_2856(2647, 9716, 0, <PERSON>OUTH, 6)
        GIANT_RAT_2857(2651, 9700, 0, SOUTH, 4)
        RAT_2854(2669, 9681, 0, SOUTH, 14)
        RAT_2854(2673, 9678, 0, SOUTH, 14)
        ZOMBIE_51(2675, 9680, 0, SOUTH, 4)
        ZOMBIE_50(2676, 9684, 0, SOUTH, 3)
        ZOMBIE_49(2676, 9687, 0, SOUTH, 4)
        RAT_2854(2680, 9701, 0, SOUTH, 14)
    }
}