package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10291Spawns : NPCSpawnsScript() {
    init {
        5310(2560, 3288, 0, SOUTH, 0)
        BONES_4576(2562, 3319, 0, SOUTH, 3)
        JIMMY_DAZZLER(2562, 3320, 0, SOUTH, 2)
        CERIL_CARNILLEAN(2565, 3271, 0, SOUTH, 2)
        PHILIPE_CARNILLEAN(2565, 3273, 0, SOUTH, 2)
        BUTLER_JONES(2569, 3272, 0, SOUTH, 2)
        KNIGHT_OF_ARDOUGNE_8799(2569, 3299, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GUARD_1200(2570, 3275, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        HENRYETA_CARNILLEAN(2571, 3269, 0, <PERSON><PERSON><PERSON>H, 2)
        PALADIN_3293(2571, 3307, 0, <PERSON>O<PERSON>H, 5)
        PALADIN_3293(2572, 3303, 0, SOUTH, 5)
        LUCIEN_3444(2573, 3321, 0, SOUTH, 2)
        TWOPINTS(2574, 3325, 0, SOUTH, 2)
        PALADIN_3293(2575, 3296, 0, SOUTH, 5)
        BARTENDER_1319(2575, 3320, 0, SOUTH, 2)
        PALADIN_3293(2577, 3308, 0, SOUTH, 5)
        6705(2579, 3298, 0, SOUTH, 7)
        PALADIN_3293(2581, 3286, 0, SOUTH, 5)
        PALADIN_3293(2581, 3299, 0, SOUTH, 5)
        6705(2581, 3323, 0, SOUTH, 7)
        KNIGHT_OF_ARDOUGNE(2582, 3297, 0, SOUTH, 5)
        PALADIN_3293(2583, 3292, 0, SOUTH, 5)
        WARRIOR_WOMAN(2584, 3305, 0, SOUTH, 5)
        WARRIOR_WOMAN(2585, 3289, 0, SOUTH, 5)
        WARRIOR_WOMAN(2587, 3291, 0, SOUTH, 5)
        PENGUIN_2063(2592, 3269, 0, SOUTH, 3)
        PENGUIN_2063(2594, 3271, 0, SOUTH, 3)
        MONKEY_MINDER(2595, 3277, 0, SOUTH, 3)
        PENGUIN_2063(2596, 3269, 0, SOUTH, 3)
        PENGUIN_845(2596, 3270, 0, NORTH_WEST, 0)
        LARRY(2597, 3266, 0, WEST, 0)
        MONKEY_2848(2598, 3276, 0, SOUTH, 10)
        MONKEY_5279(2599, 3274, 0, SOUTH, 0)
        MONKEY_5279(2599, 3278, 0, SOUTH, 0)
        MONKEY_2848(2600, 3275, 0, SOUTH, 10)
        MONKEY_2848(2600, 3279, 0, SOUTH, 10)
        PIT_SCORPION(2601, 3269, 0, SOUTH, 3)
        MONKEY_5279(2601, 3277, 0, SOUTH, 0)
        MONKEY_5279(2601, 3282, 0, SOUTH, 0)
        6705(2602, 3264, 0, SOUTH, 7)
        SCORPION_3024(2602, 3267, 0, SOUTH, 12)
        MONKEY_MINDER(2602, 3274, 0, SOUTH, 3)
        MONKEY_5279(2602, 3279, 0, SOUTH, 0)
        SCORPION_3024(2603, 3270, 0, SOUTH, 12)
        MONKEY_2848(2603, 3279, 0, SOUTH, 10)
        MONKEY_2848(2603, 3281, 0, SOUTH, 10)
        ZOO_KEEPER(2603, 3284, 0, SOUTH, 0)
        PIT_SCORPION(2604, 3272, 0, SOUTH, 3)
        MONKEY_5279(2604, 3276, 0, SOUTH, 0)
        MONKEY_2848(2604, 3277, 0, SOUTH, 10)
        6705(2604, 3289, 0, SOUTH, 7)
        PIT_SCORPION(2605, 3268, 0, SOUTH, 3)
        MONKEY_5279(2605, 3279, 0, SOUTH, 0)
        MONKEY_5279(2605, 3280, 0, SOUTH, 0)
        PIT_SCORPION(2606, 3271, 0, SOUTH, 3)
        MONKEY_5279(2606, 3279, 0, SOUTH, 0)
        CHARLIE_1495(2607, 3264, 0, SOUTH, 3)
        SCORPION_3024(2607, 3268, 0, SOUTH, 12)
        MONKEY_MINDER(2608, 3279, 0, SOUTH, 3)
        6705(2608, 3297, 0, SOUTH, 7)
        SNAKE_2845(2610, 3275, 0, SOUTH, 4)
        ZOO_KEEPER(2611, 3269, 0, SOUTH, 0)
        SNAKE_2845(2611, 3275, 0, SOUTH, 4)
        SNAKE_2845(2611, 3276, 0, SOUTH, 4)
        PARROTY_PETE(2611, 3285, 0, SOUTH, 2)
        PARROTS(2612, 3287, 0, SOUTH, 0)
        WOMAN_3299(2612, 3316, 0, SOUTH, 5)
        JERICO(2612, 3324, 0, SOUTH, 3)
        PARROTS(2613, 3287, 0, SOUTH, 0)
        AEMAD(2613, 3294, 0, SOUTH, 2)
        DOCTOR_ORBON(2614, 3306, 0, SOUTH, 3)
        ROSS(2614, 3318, 0, SOUTH, 5)
        CAMEL(2615, 3266, 0, SOUTH, 5)
        KORTAN(2615, 3292, 0, SOUTH, 2)
        SNAKE_2845(2616, 3275, 0, SOUTH, 4)
        SNAKE_2845(2616, 3276, 0, SOUTH, 4)
        WOLF(2616, 3283, 0, SOUTH, 6)
        1115(2617, 3299, 0, SOUTH, 3)
        PRIEST_5417(2617, 3308, 0, SOUTH, 2)
        CAMEL(2619, 3265, 0, SOUTH, 5)
        ZOO_KEEPER(2619, 3279, 0, SOUTH, 0)
        WOLF(2620, 3283, 0, SOUTH, 6)
        CAMEL(2621, 3266, 0, SOUTH, 5)
        JOGRE(2622, 3272, 0, SOUTH, 3)
        CYCLOPS(2622, 3277, 0, SOUTH, 2)
        PROBITA(2622, 3293, 0, SOUTH, 1)
        4273(2560, 3304, 0, SOUTH, 0)
        9232(2561, 3303, 0, SOUTH, 5)
        9232(2561, 3305, 0, SOUTH, 5)
        PALADIN_3293(2572, 3292, 1, SOUTH, 5)
        AMBASSADOR_GIMBLEWAP(2572, 3299, 1, SOUTH, 3)
        CERIL_CARNILLEAN(2573, 3268, 1, SOUTH, 2)
        BUTLER_JONES(2573, 3269, 1, SOUTH, 2)
        WOMAN_3299(2573, 3322, 1, SOUTH, 5)
        PALADIN_3293(2576, 3293, 1, SOUTH, 5)
        PALADIN_3293(2578, 3285, 1, SOUTH, 5)
        9228(2578, 3293, 1, SOUTH, 5)
        PALADIN_3293(2581, 3286, 1, SOUTH, 5)
        PALADIN_3293(2581, 3307, 1, SOUTH, 5)
        PALADIN_3293(2582, 3306, 1, SOUTH, 5)
        PALADIN_3293(2584, 3288, 1, SOUTH, 5)
        PALADIN_3293(2584, 3304, 1, SOUTH, 5)
        PALADIN_3293(2585, 3290, 1, SOUTH, 5)
        PALADIN_3293(2586, 3289, 1, SOUTH, 5)
        PALADIN_3293(2586, 3303, 1, SOUTH, 5)
        PALADIN_3293(2587, 3291, 1, SOUTH, 5)
        PALADIN_3293(2588, 3301, 1, SOUTH, 5)
        MAN_3298(2618, 3294, 1, SOUTH, 5)
        JESS(2620, 3292, 1, SOUTH, 5)
        ARCHER_3301(2572, 3284, 2, SOUTH, 5)
        ARCHER_3301(2572, 3309, 2, SOUTH, 5)
        ARCHER_3301(2582, 3284, 2, SOUTH, 5)
        ARCHER_3301(2582, 3309, 2, SOUTH, 5)
        ARCHER_3301(2588, 3290, 2, SOUTH, 5)
        ARCHER_3301(2588, 3303, 2, SOUTH, 5)
    }
}