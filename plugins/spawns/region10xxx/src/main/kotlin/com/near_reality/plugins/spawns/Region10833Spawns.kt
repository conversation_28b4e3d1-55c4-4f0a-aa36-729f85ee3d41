package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10833Spawns : NPCSpawnsScript() {
    init {
        FISHING_SPOT_1500(2694, 5226, 0, SOUTH, 0)
        BIG_FROG(2696, 5227, 0, SOUTH, 3)
        CAVE_SLIME(2696, 5232, 0, SOUTH, 9)
        CAVE_BUG_483(2697, 5221, 0, SOUTH, 5)
        BIG_FROG(2697, 5223, 0, SOUTH, 3)
        CAVE_SLIME(2697, 5234, 0, SOUTH, 9)
        BIG_FROG(2698, 5219, 0, SOUTH, 3)
        CAVE_BUG_LARVA(2698, 5229, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        CAVE_SLIME(2698, 5236, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        CAVE_SLIME(2699, 5233, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        CAVE_SLIME(2700, 5235, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        CAVE_SLIME(2700, 5238, 0, SOUTH, 9)
        BIG_FROG(2701, 5217, 0, SOUTH, 3)
        CAVE_BUG_LARVA(2702, 5216, 0, SOUTH, 4)
        CAVE_CRAWLER_409(2708, 5214, 0, SOUTH, 5)
        CAVE_CRAWLER_407(2710, 5211, 0, SOUTH, 4)
        CAVE_CRAWLER_408(2711, 5203, 0, SOUTH, 4)
        CAVE_BUG_LARVA(2711, 5230, 0, SOUTH, 4)
        CAVE_BUG_483(2711, 5231, 0, SOUTH, 5)
        CAVE_BUG_LARVA(2712, 5208, 0, SOUTH, 4)
        CAVE_CRAWLER(2712, 5215, 0, SOUTH, 6)
        CAVE_CRAWLER_407(2712, 5224, 0, SOUTH, 4)
        CAVE_BUG(2712, 5233, 0, SOUTH, 12)
        CAVE_BUG_LARVA(2713, 5194, 0, SOUTH, 4)
        CAVE_BUG(2713, 5237, 0, SOUTH, 12)
        CAVE_BUG_LARVA(2713, 5239, 0, SOUTH, 4)
        CAVE_BUG_LARVA(2714, 5233, 0, SOUTH, 4)
        CAVE_BUG_483(2715, 5224, 0, SOUTH, 5)
        CAVE_BUG(2715, 5240, 0, SOUTH, 12)
        CAVE_BUG(2715, 5241, 0, SOUTH, 12)
        CAVE_BUG_LARVA(2716, 5201, 0, SOUTH, 4)
        CAVE_BUG(2716, 5227, 0, SOUTH, 12)
        CAVE_BUG(2716, 5235, 0, SOUTH, 12)
        CAVE_BUG(2717, 5222, 0, SOUTH, 12)
        CAVE_BUG_LARVA(2720, 5235, 0, SOUTH, 4)
        CAVE_BUG_LARVA(2724, 5188, 0, SOUTH, 4)
        CAVE_SLIME(2726, 5235, 0, SOUTH, 9)
        CAVE_BUG_LARVA(2727, 5198, 0, SOUTH, 4)
        BIG_FROG(2727, 5200, 0, SOUTH, 3)
        CAVE_SLIME(2728, 5236, 0, SOUTH, 9)
        BIG_FROG(2729, 5189, 0, SOUTH, 3)
        BIG_FROG(2729, 5196, 0, SOUTH, 3)
        BIG_FROG(2729, 5202, 0, SOUTH, 3)
        ROCKSLUG_422(2729, 5223, 0, SOUTH, 6)
        CAVE_BUG_LARVA(2729, 5234, 0, SOUTH, 4)
        GIANT_FROG(2731, 5192, 0, SOUTH, 3)
        GIANT_FROG(2731, 5205, 0, SOUTH, 3)
        CAVE_SLIME(2731, 5235, 0, SOUTH, 9)
        ROCKSLUG(2733, 5222, 0, SOUTH, 6)
        ROCKSLUG(2734, 5221, 0, SOUTH, 6)
        CAVE_BUG_LARVA(2735, 5234, 0, SOUTH, 4)
        CAVE_BUG_LARVA(2737, 5206, 0, SOUTH, 4)
        ROCKSLUG(2737, 5221, 0, SOUTH, 6)
        CAVE_BUG_483(2738, 5193, 0, SOUTH, 5)
        CAVE_BUG_483(2738, 5206, 0, SOUTH, 5)
        BIG_FROG(2741, 5194, 0, SOUTH, 3)
        BIG_FROG(2741, 5205, 0, SOUTH, 3)
        CAVE_BUG_LARVA(2741, 5231, 0, SOUTH, 4)
        BIG_FROG(2743, 5199, 0, SOUTH, 3)
        BIG_FROG(2744, 5203, 0, SOUTH, 3)
        CAVE_BUG_483(2744, 5221, 0, SOUTH, 5)
        BIG_FROG(2745, 5198, 0, SOUTH, 3)
        GIANT_FROG(2745, 5206, 0, SOUTH, 3)
        CAVE_BUG_LARVA(2745, 5215, 0, SOUTH, 4)
        CAVE_BUG_LARVA(2745, 5218, 0, SOUTH, 4)
        CAVE_BUG(2745, 5220, 0, SOUTH, 12)
        CAVE_BUG_LARVA(2745, 5223, 0, SOUTH, 4)
        FISHING_SPOT_1499(2746, 5230, 0, SOUTH, 0)
        CAVE_BUG(2747, 5217, 0, SOUTH, 12)
        TURGALL(2741, 5241, 2, SOUTH, 5)
        3187(2706, 5240, 3, SOUTH, 5)
        3188(2707, 5239, 3, SOUTH, 5)
    }
}