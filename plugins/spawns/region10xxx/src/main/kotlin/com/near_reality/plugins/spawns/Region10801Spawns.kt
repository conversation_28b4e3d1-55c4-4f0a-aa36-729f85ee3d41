package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10801Spawns : NPCSpawnsScript() {
    init {
        TRIBESMAN(2723, 3159, 0, SOUTH, 5)
        TRIBESMAN(2724, 3158, 0, SOUTH, 5)
        TRIBESMAN(2724, 3161, 0, SOUTH, 5)
        TRIBESMAN(2725, 3163, 0, SOUTH, 5)
        TRIBESMAN(2728, 3158, 0, SOUTH, 5)
        TRIBESMAN(2729, 3158, 0, SOUTH, 5)
        TRIBESMAN(2729, 3164, 0, SOUTH, 5)
        TRIBESMAN(2730, 3162, 0, SOUT<PERSON>, 5)
        TRIBESMAN(2731, 3161, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        MONKEY_2848(2732, 3176, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        SNAKE_2845(2733, 3165, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        SNAKE_2845(2734, 3147, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        SWARM(2735, 3188, 0, SOUTH, 6)
        SWARM(2736, 3184, 0, SOUTH, 6)
        SNAKE_2845(2739, 3169, 0, SOUTH, 4)
        MONKEY_2848(2742, 3159, 0, SOUTH, 10)
        SWARM(2743, 3175, 0, SOUTH, 6)
        SWARM(2744, 3190, 0, SOUTH, 6)
        SANIBOCH(2745, 3152, 0, SOUTH, 2)
        SWARM(2748, 3177, 0, SOUTH, 6)
        SNAKE_2845(2749, 3151, 0, SOUTH, 4)
        SWARM(2750, 3181, 0, SOUTH, 6)
        SNAKE_2845(2751, 3166, 0, SOUTH, 4)
    }
}