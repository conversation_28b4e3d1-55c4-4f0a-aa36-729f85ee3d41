package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10553Spawns : NPCSpawnsScript() {
    init {
        GULL_3905(2624, 3702, 0, SOUTH, 9)
        WARRIOR_3950(2625, 3664, 0, SOUTH, 12)
        YRSA_3933(2625, 3675, 0, SOUTH, 2)
        GULL_3905(2625, 3710, 0, SOUTH, 9)
        SASSILIK_3945(2627, 3654, 0, SOUTH, 2)
        GULL_3906(2628, 3701, 0, SOUTH, 6)
        GULL_3906(2628, 3706, 0, SOUTH, 6)
        WARRIOR_3950(2630, 3676, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        GULL_3905(2630, 3710, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        GULL_3906(2632, 3700, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        FISHING_SPOT_3913(2633, 3687, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        FISHING_SPOT_3913(2633, 3690, 0, SOUTH, 0)
        FISHING_SPOT_3913(2633, 3693, 0, SOUTH, 0)
        GULL_3906(2633, 3707, 0, SOUTH, 6)
        WARRIOR_3950(2634, 3651, 0, SOUTH, 12)
        PEER_THE_SEER(2634, 3668, 0, SOUTH, 2)
        MARKET_GUARD_3949(2635, 3676, 0, SOUTH, 12)
        GULL_3905(2635, 3697, 0, SOUTH, 9)
        GULL_3906(2635, 3703, 0, SOUTH, 6)
        GULL_3905(2636, 3710, 0, SOUTH, 9)
        FISHING_SPOT_3914(2639, 3694, 0, SOUTH, 0)
        JENNELLA(2640, 3651, 0, SOUTH, 2)
        6714(2640, 3656, 0, SOUTH, 8)
        FUR_TRADER_3948(2640, 3675, 0, SOUTH, 3)
        SIGMUND_THE_MERCHANT(2641, 3680, 0, SOUTH, 3)
        GULL_3905(2641, 3704, 0, SOUTH, 9)
        AGNAR(2642, 3677, 0, SOUTH, 12)
        FISHING_SPOT_3914(2642, 3693, 0, SOUTH, 0)
        FISHING_SPOT_3914(2642, 3696, 0, SOUTH, 0)
        FISHING_SPOT_3914(2642, 3699, 0, SOUTH, 0)
        FISHING_SPOT_3915(2642, 3708, 0, SOUTH, 0)
        WARRIOR_3950(2643, 3674, 0, SOUTH, 12)
        6714(2643, 3679, 0, SOUTH, 8)
        MARKET_GUARD_3949(2644, 3670, 0, SOUTH, 12)
        MARKET_GUARD_3949(2644, 3677, 0, SOUTH, 12)
        MARKET_GUARD_3949(2644, 3683, 0, SOUTH, 12)
        FISHING_SPOT_3915(2645, 3708, 0, SOUTH, 0)
        SWENSEN_THE_NAVIGATOR(2646, 3660, 0, SOUTH, 2)
        FISH_MONGER(2646, 3675, 0, SOUTH, 3)
        FISHING_SPOT_3915(2648, 3708, 0, SOUTH, 0)
        FISHING_SPOT_3915(2648, 3711, 0, SOUTH, 0)
        WARRIOR_3950(2650, 3652, 0, SOUTH, 12)
        MARKET_GUARD_3949(2650, 3676, 0, SOUTH, 12)
        LENSA_3943(2655, 3652, 0, SOUTH, 2)
        BJORN(2655, 3673, 0, SOUTH, 5)
        WARRIOR_3950(2655, 3691, 0, SOUTH, 12)
        GUARD_3928(2657, 3663, 0, SOUTH, 0)
        STYRMIR(2657, 3680, 0, SOUTH, 0)
        3927(2658, 3660, 0, SOUTH, 2)
        ELDGRIM(2658, 3674, 0, SOUTH, 4)
        TORBRUND(2658, 3679, 0, SOUTH, 0)
        DRON(2658, 3700, 0, SOUTH, 3)
        3926(2659, 3669, 0, SOUTH, 4)
        FRIDGEIR(2659, 3678, 0, SOUTH, 0)
        VOLF_OLAFSON(2659, 3698, 0, SOUTH, 5)
        SIGLI_THE_HUNTSMAN(2660, 3653, 0, SOUTH, 2)
        GUARD_3929(2660, 3663, 0, SOUTH, 0)
        MANNI_THE_REVELLER(2660, 3673, 0, SOUTH, 4)
        OSPAK(2660, 3680, 0, SOUTH, 0)
        THORA_THE_BARKEEP(2662, 3673, 0, SOUTH, 2)
        6714(2663, 3656, 0, SOUTH, 8)
        SKULGRIMEN(2663, 3694, 0, SOUTH, 2)
        PONTAK(2666, 3652, 0, SOUTH, 3)
        WARRIOR_3950(2666, 3678, 0, SOUTH, 12)
        THORVALD_THE_WARRIOR(2666, 3693, 0, SOUTH, 3)
        REESO(2666, 3705, 0, SOUTH, 4)
        LONGHALL_BOUNCER(2667, 3684, 0, SOUTH_WEST, 0)
        FREYGERD_3942(2667, 3702, 0, SOUTH, 2)
        WARRIOR_3950(2668, 3658, 0, SOUTH, 12)
        WARRIOR_3950(2668, 3670, 0, SOUTH, 12)
        WARRIOR_3950(2668, 3710, 0, SOUTH, 12)
        INGRID_HRADSON(2670, 3662, 0, SOUTH, 5)
        6714(2670, 3675, 0, SOUTH, 8)
        6714(2672, 3708, 0, SOUTH, 8)
        OLAF_THE_BARD(2673, 3683, 0, SOUTH, 3)
        WARRIOR_3950(2673, 3695, 0, SOUTH, 12)
        FREIDIR(2674, 3675, 0, SOUTH, 3)
        INGA(2674, 3677, 0, SOUTH, 3)
        BLANIN(2675, 3671, 0, SOUTH, 2)
        LANZIG(2676, 3665, 0, SOUTH, 3)
        1172(2678, 3670, 0, SOUTH, 0)
        6714(2678, 3695, 0, SOUTH, 8)
        CHICKEN_1174(2679, 3663, 0, SOUTH, 4)
        CHICKEN(2679, 3665, 0, SOUTH, 2)
        BORROKAR(2679, 3690, 0, SOUTH, 3)
        ROOSTER(2680, 3662, 0, SOUTH, 3)
        CHICKEN(2680, 3664, 0, SOUTH, 2)
        CHICKEN_1174(2681, 3663, 0, SOUTH, 4)
        6714(2684, 3657, 0, SOUTH, 8)
        WARRIOR_3950(2685, 3653, 0, SOUTH, 12)
        SAILOR_3936(2629, 3693, 0, SOUTH, 0)
        7504(2640, 3697, 0, SOUTH, 2)
        FISHERMAN(2641, 3699, 0, NORTH, 0)
        MORD_GUNNARS(2644, 3709, 0, EAST, 0)
        MARIA_GUNNARS_1883(2644, 3710, 0, SOUTH, 0)
        BIGREDJAPAN(2656, 3676, 3, SOUTH, 5)
    }
}