package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10140Spawns : NPCSpawnsScript() {
    init {
        DAGANNOTH_974(2511, 10020, 0, SOUTH, 11)
        DAGANNOTH_970(2513, 10018, 0, SOUTH, 13)
        DAGANNOTH_972(2514, 10012, 0, SOUTH, 14)
        DAGANNOTH_974(2514, 10027, 0, SOUTH, 11)
        DAGANNOTH_973(2515, 10016, 0, SOUTH, 13)
        DAGANNOTH_971(2515, 10030, 0, SOUTH, 14)
        DAGANNOTH_971(2518, 10013, 0, <PERSON>OUT<PERSON>, 14)
        DAGANNOTH_970(2518, 10027, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        DAGANNOTH_975(2519, 10031, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        DAGANNOTH_970(2520, 10018, 0, SOUTH, 13)
        DAGANNOTH_971(2521, 10015, 0, SOUTH, 14)
        DAGANNOTH_971(2522, 10023, 0, SOUTH, 14)
        DAGANNOTH_970(2522, 10028, 0, SOUTH, 13)
        DAGANNOTH_973(2523, 10019, 0, SOUTH, 13)
        DAGANNOTH_972(2524, 10014, 0, SOUTH, 14)
        DAGANNOTH_971(2524, 10033, 0, SOUTH, 14)
        DAGANNOTH_972(2526, 10035, 0, SOUTH, 14)
        DAGANNOTH_970(2527, 10019, 0, SOUTH, 13)
        DAGANNOTH_975(2527, 10024, 0, SOUTH, 8)
        DAGANNOTH_971(2532, 10023, 0, SOUTH, 14)
        DAGANNOTH_974(2534, 10025, 0, SOUTH, 11)
        DAGANNOTH_971(2535, 10030, 0, SOUTH, 14)
    }
}