package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10137Spawns : NPCSpawnsScript() {
    init {
        MOSS_GUARDIAN(2528, 9843, 0, SOUTH, 5)
        SKELETON_77(2529, 9841, 0, SOUTH, 7)
        SKELETON_81(2530, 9846, 0, SOUTH, 6)
        ZOMBIE_29(2533, 9842, 0, SOUTH, 5)
        SKELETON(2538, 9816, 0, SOUTH, 7)
        SKELETON_80(2540, 9813, 0, SOUTH, 8)
        BUTTERFLY(2540, 9820, 0, SOUTH, 7)
        ZOMBIE(2540, 9823, 0, SOUTH, 5)
        ZOMBIE_30(2540, 9843, 0, <PERSON>OUT<PERSON>, 5)
        MOSS_<PERSON>UAR<PERSON><PERSON>(2541, 9845, 0, SOUTH, 5)
        MOSS_GUARDIAN(2542, 9819, 0, SOUTH, 5)
        ZOMBIE_50(2542, 9840, 0, SOUTH, 3)
        BUTTERFLY(2543, 9813, 0, SOUTH, 7)
        SKELETON_78(2543, 9847, 0, SOUTH, 8)
        SKELETON_78(2545, 9822, 0, SOUTH, 8)
        BUTTERFLY(2546, 9817, 0, SOUTH, 7)
        ZOMBIE_51(2546, 9842, 0, SOUTH, 4)
    }
}