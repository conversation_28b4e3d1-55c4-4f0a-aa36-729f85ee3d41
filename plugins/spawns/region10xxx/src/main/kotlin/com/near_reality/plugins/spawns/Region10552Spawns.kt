package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10552Spawns : NPCSpawnsScript() {
    init {
        WOLF_3912(2626, 3633, 0, SOUT<PERSON>, 4)
        WOLF_3912(2630, 3631, 0, SOUTH, 4)
        WOLF_3912(2630, 3637, 0, SOUTH, 4)
        WOLF_3912(2641, 3626, 0, SOUTH, 4)
        WOLF_3912(2642, 3622, 0, SOUTH, 4)
        WOLF_3912(2644, 3627, 0, SOUTH, 4)
        WOLF(2647, 3584, 0, SOUTH, 6)
        COUNCIL_WORKMAN(2655, 3592, 0, SOUTH, 2)
        THORODIN_5526(2659, 3627, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        TOWN_GUARD(2660, 3646, 0, <PERSON>OUTH, 0)
        TOWN_GUARD_3931(2664, 3646, 0, <PERSON>OUT<PERSON>, 0)
        WOLF(2680, 3585, 0, SOUTH, 6)
        WOLF(2682, 3592, 0, SOUTH, 6)
    }
}