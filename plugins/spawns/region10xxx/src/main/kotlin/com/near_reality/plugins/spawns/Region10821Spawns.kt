package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10821Spawns : NPCSpawnsScript() {
    init {
        IRON_PICKAXE(2713, 4438, 0, SOUTH, 0)
        IRON_PICKAXE(2716, 4443, 0, SOUTH, 0)
        IRON_PICKAXE(2719, 4442, 0, SOUTH, 0)
        IRON_PICKAXE(2724, 4442, 0, SOUTH, 0)
        IRON_PICKAXE(2728, 4445, 0, SOUTH, 0)
        IRON_PICKAXE(2729, 4447, 0, SOUTH, 0)
        IRON_PICKAXE(2735, 4435, 0, SOUTH, 0)
        IRON_PICKAXE(2738, 4439, 0, SOUTH, 0)
    }
}