package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10393Spawns : NPCSpawnsScript() {
    init {
        GOBLIN_5204(2564, 9836, 0, SOUTH, 10)
        GOBLIN_5205(2565, 9835, 0, SOUTH, 8)
        GOBLIN_5207(2566, 9849, 0, SOUTH, 11)
        GOBLIN_5204(2568, 9844, 0, SOUTH, 10)
        GOBLIN_5205(2569, 9847, 0, SOUTH, 8)
        GOBLIN_5195(2570, 9845, 0, SOUTH, 9)
        GOBLIN_5206(2570, 9850, 0, SOUTH, 6)
        RAT_2854(2573, 9805, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        RAT_2854(2575, 9803, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        RAT_2854(2578, 9806, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        RAT_2854(2579, 9804, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        GOBLIN_5201(2579, 9841, 0, <PERSON><PERSON><PERSON>H, 11)
        DUN<PERSON>ON_RAT_2867(2580, 9804, 0, SOUTH, 2)
        GOBLIN_5198(2580, 9825, 0, SOUTH, 5)
        GOBLIN_5202(2580, 9847, 0, SOUTH, 8)
        GOBLIN_5203(2580, 9850, 0, SOUTH, 8)
        RAT_2854(2583, 9803, 0, SOUTH, 14)
        GOBLIN_5203(2584, 9826, 0, SOUTH, 8)
        GOBLIN_5195(2584, 9829, 0, SOUTH, 9)
        GOBLIN_5198(2584, 9835, 0, SOUTH, 5)
        DUNGEON_RAT_2866(2585, 9801, 0, SOUTH, 2)
        RAT_2854(2585, 9804, 0, SOUTH, 14)
        RAT_2854(2585, 9830, 0, SOUTH, 14)
        GOBLIN_5196(2586, 9832, 0, SOUTH, 9)
        RAT_2854(2587, 9803, 0, SOUTH, 14)
        GOBLIN_5200(2587, 9837, 0, SOUTH, 8)
        DUNGEON_RAT(2588, 9806, 0, SOUTH, 2)
        RAT_2854(2588, 9825, 0, SOUTH, 14)
        GOBLIN_5199(2589, 9834, 0, SOUTH, 2)
        GOBLIN_5208(2590, 9821, 0, SOUTH, 12)
        RAT_2854(2591, 9801, 0, SOUTH, 14)
        GOBLIN_5197(2591, 9830, 0, SOUTH, 12)
        GOBLIN_5201(2591, 9836, 0, SOUTH, 11)
        RAT_2854(2592, 9806, 0, SOUTH, 14)
        GOBLIN_5193(2592, 9819, 0, SOUTH, 2)
        RAT_2854(2594, 9801, 0, SOUTH, 14)
        DUNGEON_RAT_2867(2594, 9803, 0, SOUTH, 2)
        GOBLIN_5202(2594, 9826, 0, SOUTH, 8)
        GOBLIN_5207(2595, 9821, 0, SOUTH, 11)
        GIANT_BAT(2596, 9836, 0, SOUTH, 11)
        GOBLIN_5206(2597, 9821, 0, SOUTH, 6)
        RAT_2854(2599, 9803, 0, SOUTH, 14)
        RAT_2854(2599, 9812, 0, SOUTH, 14)
        GIANT_BAT(2599, 9837, 0, SOUTH, 11)
        DUNGEON_RAT_2866(2601, 9801, 0, SOUTH, 2)
        RAT_2854(2601, 9810, 0, SOUTH, 14)
        GIANT_BAT(2606, 9825, 0, SOUTH, 11)
        GIANT_BAT(2609, 9817, 0, SOUTH, 11)
        GIANT_BAT(2609, 9823, 0, SOUTH, 11)
        RAT_2854(2610, 9806, 0, SOUTH, 14)
        RAT_2854(2612, 9812, 0, SOUTH, 14)
        GIANT_BAT(2612, 9815, 0, SOUTH, 11)
        RAT_2854(2613, 9805, 0, SOUTH, 14)
        RAT_2854(2613, 9806, 0, SOUTH, 14)
        ROCK_PILE(2620, 9833, 0, SOUTH, 0)
        ROCK_PILE(2620, 9836, 0, SOUTH, 0)
    }
}