package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10055Spawns : NPCSpawnsScript() {
    init {
        MONKEY_2848(2509, 4550, 0, SOUTH, 10)
        MONKEY_2848(2513, 4553, 0, SOUTH, 10)
        FISHING_SPOT_4712(2515, 4591, 0, SOUTH, 0)
        MONKEY_2848(2516, 4552, 0, SOUTH, 10)
        MONKEY_2848(2518, 4545, 0, SOUTH, 10)
        MONKEY_2848(2522, 4556, 0, SOUTH, 10)
        TAMAYU_4705(2523, 4567, 0, SOUTH, 5)
        MONKEY_2848(2524, 4552, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        THE_SHAIKAHAN_4709(2524, 4567, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        MONKEY_2848(2525, 4549, 0, <PERSON><PERSON>UT<PERSON>, 10)
        TIADECHE(2528, 4589, 0, <PERSON><PERSON><PERSON>H, 5)
        FISHING_SPOT_4713(2528, 4591, 0, SOUTH, 0)
        PELICAN(2530, 4578, 0, SOUTH, 4)
        THE_SHAIKAHAN_4709(2540, 4566, 0, SOUTH, 5)
        TAMAYU_4706(2541, 4565, 0, SOUTH, 5)
        CORMORANT(2547, 4580, 0, SOUTH, 0)
        PELICAN(2550, 4577, 0, SOUTH, 4)
        GULL(2553, 4576, 0, SOUTH, 7)
    }
}