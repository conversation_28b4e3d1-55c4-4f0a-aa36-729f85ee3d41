package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10831Spawns : NPCSpawnsScript() {
    init {
        GIANT_SKELETON_681(2693, 5065, 0, SOUTH, 5)
        GIANT_SKELETON(2693, 5075, 0, SOUTH, 5)
        GIANT_SKELETON_681(2693, 5089, 0, SOUTH, 5)
        BAT(2693, 5102, 0, SOUTH, 23)
        RAT_2854(2693, 5111, 0, <PERSON>OUTH, 14)
        SHADOW_HOUND(2694, 5067, 0, SOUTH, 5)
        GIANT_SKELETON(2695, 5089, 0, <PERSON>OUT<PERSON>, 5)
        GIANT_SKELETON(2697, 5096, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        RAT_2854(2699, 5069, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        BAT(2699, 5083, 0, <PERSON><PERSON><PERSON><PERSON>, 23)
        RAT_2854(2700, 5061, 0, <PERSON><PERSON><PERSON>H, 14)
        G<PERSON>NT_SK<PERSON>ETON(2703, 5064, 0, SOUTH, 5)
        GIANT_SKELETON_681(2704, 5091, 0, SOUTH, 5)
        RAT_2854(2706, 5114, 0, SOUTH, 14)
        RAT_2854(2707, 5079, 0, SOUTH, 14)
        BAT(2707, 5106, 0, SOUTH, 23)
        RAT_2854(2708, 5070, 0, SOUTH, 14)
        GIANT_BAT(2709, 5086, 0, SOUTH, 11)
        GIANT_SKELETON_681(2710, 5095, 0, SOUTH, 5)
        GIANT_SKELETON(2710, 5105, 0, SOUTH, 5)
        GIANT_SKELETON_681(2712, 5076, 0, SOUTH, 5)
        RAT_2854(2712, 5110, 0, SOUTH, 14)
        RAT_2854(2713, 5061, 0, SOUTH, 14)
        GIANT_SKELETON_681(2714, 5108, 0, SOUTH, 5)
        GIANT_SKELETON_681(2716, 5092, 0, SOUTH, 5)
        SHADOW_HOUND(2717, 5081, 0, SOUTH, 5)
        SHADOW_HOUND(2718, 5108, 0, SOUTH, 5)
        GIANT_SKELETON_681(2719, 5071, 0, SOUTH, 5)
        GIANT_SKELETON(2719, 5078, 0, SOUTH, 5)
        GIANT_SKELETON(2719, 5112, 0, SOUTH, 5)
        GIANT_SKELETON(2720, 5096, 0, SOUTH, 5)
        BAT(2721, 5062, 0, SOUTH, 23)
        RAT_2854(2721, 5076, 0, SOUTH, 14)
        GIANT_RAT_2856(2721, 5102, 0, SOUTH, 6)
        SHADOW_HOUND(2721, 5104, 0, SOUTH, 5)
        GIANT_SKELETON_681(2722, 5061, 0, SOUTH, 5)
        RAT_2854(2724, 5113, 0, SOUTH, 14)
        GIANT_SKELETON(2726, 5086, 0, SOUTH, 5)
        SHADOW_HOUND(2726, 5091, 0, SOUTH, 5)
        GIANT_SKELETON(2729, 5096, 0, SOUTH, 5)
        SHADOW_HOUND(2731, 5060, 0, SOUTH, 5)
        SHADOW_HOUND(2732, 5073, 0, SOUTH, 5)
        GIANT_SKELETON_681(2732, 5087, 0, SOUTH, 5)
        GIANT_SKELETON(2735, 5061, 0, SOUTH, 5)
        BAT(2735, 5073, 0, SOUTH, 23)
        BAT(2735, 5105, 0, SOUTH, 23)
        GIANT_RAT_2864(2737, 5062, 0, SOUTH, 6)
        GIANT_SKELETON_681(2739, 5078, 0, SOUTH, 5)
        RAT_2854(2739, 5112, 0, SOUTH, 14)
        GIANT_SKELETON(2740, 5069, 0, SOUTH, 5)
        SHADOW_HOUND(2740, 5083, 0, SOUTH, 5)
        GIANT_SKELETON(2740, 5085, 0, SOUTH, 5)
        GIANT_SKELETON_681(2740, 5104, 0, SOUTH, 5)
        RAT_2854(2741, 5077, 0, SOUTH, 14)
        BAT(2742, 5097, 0, SOUTH, 23)
        SHADOW_HOUND(2742, 5103, 0, SOUTH, 5)
        BAT(2744, 5065, 0, SOUTH, 23)
        GIANT_SKELETON(2746, 5092, 0, SOUTH, 5)
        GIANT_SKELETON(2746, 5114, 0, SOUTH, 5)
        GIANT_SKELETON_681(2747, 5083, 0, SOUTH, 5)
        SHADOW_HOUND(2747, 5094, 0, SOUTH, 5)
    }
}