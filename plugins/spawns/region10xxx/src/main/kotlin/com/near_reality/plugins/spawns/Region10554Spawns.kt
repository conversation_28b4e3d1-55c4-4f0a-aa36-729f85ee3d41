package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10554Spawns : NPCSpawnsScript() {
    init {
        HOBGOBLIN_3050(2656, 3720, 0, SOUTH, 3)
        HOBGOBLIN_3050(2656, 3727, 0, SOUTH, 3)
        HOBGOBLIN_3050(2661, 3731, 0, SOUTH, 3)
        ROCKS_103(2663, 3716, 0, SOUTH, 0)
        ROCKS(2666, 3715, 0, <PERSON>OUTH, 0)
        ROCKS(2667, 3727, 0, SOUTH, 0)
        ROCKS_103(2669, 3718, 0, SOUTH, 0)
        HOBGOBLIN_3050(2669, 3725, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        ROCKS_103(2670, 3727, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        ROCKS(2671, 3724, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        ROCKS(2672, 3732, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        ROCKS_103(2673, 3716, 0, SO<PERSON>H, 0)
        ROCKS(2675, 3729, 0, SOUTH, 0)
        HOBGOBLIN_3050(2675, 3731, 0, SOUTH, 3)
        ROCKS_103(2676, 3713, 0, SOUTH, 0)
        ROCKS_103(2677, 3717, 0, SOUTH, 0)
        ROCKS(2677, 3721, 0, SOUTH, 0)
        ROCKS_103(2677, 3733, 0, SOUTH, 0)
        ROCKS_103(2678, 3729, 0, SOUTH, 0)
        ROCKS(2681, 3727, 0, SOUTH, 0)
        ROCKS(2681, 3733, 0, SOUTH, 0)
        ROCKS(2683, 3716, 0, SOUTH, 0)
        ROCKS_103(2684, 3720, 0, SOUTH, 0)
        ROCKS_103(2686, 3713, 0, SOUTH, 0)
        ROCKS_103(2687, 3724, 0, SOUTH, 0)
    }
}