package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10903Spawns : NPCSpawnsScript() {
    init {
        HOBGOBLIN_3289(2694, 9687, 0, SOUTH, 7)
        HOBGOBLIN_3289(2696, 9689, 0, SOUTH, 7)
        HOBGOBLIN_3289(2697, 9693, 0, SOUTH, 7)
        HOBGOBLIN_3289(2699, 9688, 0, SOUTH, 7)
        OGRE_2095(2719, 9668, 0, SOUTH, 2)
        OGRE_2095(2719, 9715, 0, SOUTH, 2)
        OGRE_2095(2720, 9702, 0, <PERSON>OUT<PERSON>, 2)
        OGRE_2095(2722, 9669, 0, <PERSON>OUT<PERSON>, 2)
        OGRE_2095(2723, 9707, 0, SOUTH, 2)
        OGRE_2095(2724, 9713, 0, SOUTH, 2)
        HELLHOUND(2734, 9683, 0, SOUTH, 7)
        HELLHOUND(2734, 9688, 0, SOUTH, 7)
        HELLHOUND(2734, 9693, 0, SOUTH, 7)
        HELLHOUND(2740, 9688, 0, SOUTH, 7)
        HELLHOUND(2740, 9698, 0, SOUTH, 7)
        HELLHOUND(2741, 9678, 0, SOUTH, 7)
        HELLHOUND(2744, 9683, 0, SOUTH, 7)
        HELLHOUND(2744, 9691, 0, SOUTH, 7)
    }
}