package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10640Spawns : NPCSpawnsScript() {
    init {
        MANIACAL_MONKEY_ARCHER(2627, 9254, 0, SOUTH, 0)
        MANIACAL_MONKEY_7118(2629, 9252, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2629, 9255, 0, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2629, 9258, 0, SOUTH, 0)
        MANIACAL_MONKEY_7118(2629, 9261, 0, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2631, 9251, 0, SOUT<PERSON>, 0)
        MANIACAL_MONKEY_ARCHER(2631, 9261, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        MANI<PERSON>AL_MONKEY_7118(2632, 9258, 0, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANIACAL_MONKEY_7118(2633, 9252, 0, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANI<PERSON><PERSON>_MONKEY_7118(2634, 9253, 0, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2635, 9255, 0, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2624, 9271, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2627, 9227, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2627, 9233, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2627, 9269, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2627, 9271, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2629, 9232, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2629, 9235, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2629, 9241, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2629, 9270, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2630, 9234, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2630, 9243, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2630, 9269, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2631, 9242, 1, SOUTH, 47)
    }
}