package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10131Spawns : NPCSpawnsScript() {
    init {
        SCARED_SKAVID(2499, 9433, 0, SOUTH, 5)
        SKAVID_4378(2500, 9451, 0, SOUTH, 5)
        RAT_2854(2501, 9430, 0, SOUTH, 14)
        RAT_2854(2502, 9437, 0, SOUTH, 14)
        SKAVID_4379(2503, 9415, 0, SOUTH, 5)
        RAT_2854(2505, 9461, 0, SOUTH, 14)
        RAT_2854(2509, 9457, 0, SOUTH, 14)
        RAT_2854(2509, 9464, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        RAT_2854(2514, 9462, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        SKAVID_4377(2515, 9452, 0, <PERSON>OUTH, 5)
        RAT_2854(2519, 9454, 0, <PERSON>OUTH, 14)
        MAD_SKAVID(2523, 9411, 0, SO<PERSON>H, 5)
        RAT_2854(2524, 9437, 0, SOUTH, 14)
        RAT_2854(2527, 9412, 0, SOUTH, 14)
        SKAVID_4376(2530, 9465, 0, SOUTH, 5)
        RAT_2854(2533, 9451, 0, SOUTH, 14)
        RAT_2854(2534, 9441, 0, SOUTH, 14)
        RAT_2854(2540, 9425, 0, SOUTH, 14)
        RAT_2854(2543, 9415, 0, SOUTH, 14)
        RAT_2854(2543, 9460, 0, SOUTH, 14)
        RAT_2854(2546, 9418, 0, SOUTH, 14)
        RAT_2854(2547, 9461, 0, SOUTH, 14)
        RAT_2854(2548, 9413, 0, SOUTH, 14)
        RAT_2854(2548, 9421, 0, SOUTH, 14)
    }
}