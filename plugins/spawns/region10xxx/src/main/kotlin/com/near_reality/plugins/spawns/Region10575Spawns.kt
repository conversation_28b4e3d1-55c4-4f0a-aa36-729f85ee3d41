package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10575Spawns : NPCSpawnsScript() {
    init {
        GIANT_SKELETON(2626, 5065, 0, SOUTH, 5)
        GIANT_SKELETON_681(2627, 5093, 0, SOUTH, 5)
        GIANT_SKELETON_681(2629, 5061, 0, SOUTH, 5)
        GIANT_SKELETON(2637, 5099, 0, <PERSON>OUTH, 5)
        GIANT_SKELETON(2638, 5058, 0, <PERSON>OUT<PERSON>, 5)
        GIANT_SKELETON_681(2638, 5090, 0, SOUTH, 5)
        GIANT_SKELETON(2644, 5090, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GIANT_SKELETON_681(2651, 5105, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GIANT_SKELETON_681(2654, 5079, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GIANT_SKELETON(2658, 5082, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GIANT_SKELETON_681(2663, 5116, 0, SOUTH, 5)
        GIANT_SKELETON(2666, 5097, 0, SOUTH, 5)
        GIANT_SKELETON(2680, 5074, 0, SOUTH, 5)
        G<PERSON>NT_SKELETON_681(2683, 5059, 0, SOUTH, 5)
        G<PERSON>NT_SK<PERSON>ET<PERSON>_681(2685, 5111, 0, SOUTH, 5)
    }
}