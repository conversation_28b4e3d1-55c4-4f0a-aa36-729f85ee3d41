package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10381Spawns : NPCSpawnsScript() {
    init {
        MANIACAL_MONKEY_ARCHER(2565, 9046, 0, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2567, 9050, 0, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2575, 9054, 0, SOUTH, 0)
        MANIACAL_MONKEY_7118(2577, 9052, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2577, 9056, 0, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2578, 9059, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        MANIACAL_MONKEY_7118(2579, 9061, 0, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANIACAL_MONKEY_7118(2582, 9053, 0, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANIACAL_MONKEY_7118(2582, 9059, 0, <PERSON><PERSON><PERSON><PERSON>, 47)
        <PERSON>NI<PERSON><PERSON>_M<PERSON><PERSON>Y_<PERSON>CHER(2582, 9060, 0, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2583, 9055, 0, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2584, 9058, 0, SOUTH, 0)
        MANIACAL_MONKEY_7118(2586, 9054, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2561, 9040, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2564, 9038, 1, SOUTH, 47)
        SNAKE_2845(2582, 9068, 1, SOUTH, 4)
        MANIACAL_MONKEY_7118(2582, 9070, 1, SOUTH, 47)
        SCORPION_5242(2583, 9068, 1, SOUTH, 5)
        SCORPION_5242(2583, 9073, 1, SOUTH, 5)
        MANIACAL_MONKEY_ARCHER(2584, 9072, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2590, 9076, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2591, 9079, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2592, 9081, 1, SOUTH, 47)
        SCORPION_5242(2594, 9052, 1, SOUTH, 5)
        MANIACAL_MONKEY_ARCHER(2595, 9047, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2596, 9048, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2597, 9050, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2599, 9045, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2602, 9079, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2603, 9078, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2606, 9080, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2607, 9044, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2608, 9040, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2610, 9043, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2610, 9049, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2610, 9079, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2611, 9081, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2612, 9041, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2612, 9077, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2613, 9053, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2613, 9058, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2613, 9079, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2614, 9076, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2615, 9037, 1, SOUTH, 0)
        SCORPION_5242(2616, 9038, 1, SOUTH, 5)
        MANIACAL_MONKEY_ARCHER(2616, 9059, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2616, 9065, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2618, 9068, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2619, 9073, 1, SOUTH, 0)
        SCORPION_5242(2620, 9074, 1, SOUTH, 5)
        MANIACAL_MONKEY_ARCHER(2622, 9074, 1, SOUTH, 0)
    }
}