package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10392Spawns : NPCSpawnsScript() {
    init {
        GIANT_BAT(2563, 9753, 0, SOUTH, 11)
        GIANT_BAT(2565, 9754, 0, SOUTH, 11)
        GIANT_BAT(2567, 9752, 0, SOUTH, 11)
        GIANT_BAT(2569, 9753, 0, SOUTH, 11)
        GIANT_BAT(2572, 9752, 0, SOUTH, 11)
        OGRE_2095(2581, 9738, 0, SOUTH, 2)
        OGRE_2095(2581, 9742, 0, SOUTH, 2)
        OGRE_2095(2584, 9734, 0, SOUTH, 2)
        OGRE_2095(2585, 9738, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        OGRE_2095(2587, 9733, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        OGRE_2095(2587, 9740, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        OGRE_2095(2590, 9737, 0, <PERSON><PERSON>UTH, 2)
    }
}