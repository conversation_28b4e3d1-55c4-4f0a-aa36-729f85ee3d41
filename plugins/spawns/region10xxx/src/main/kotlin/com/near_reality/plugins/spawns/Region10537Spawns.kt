package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10537Spawns : NPCSpawnsScript() {
    init {
        SQUIRE_1768(2648, 2665, 0, SOUTH, 2)
        SQUIRE_1766(2649, 2659, 0, SOUT<PERSON>, 2)
        6713(2652, 2655, 0, SOUTH, 9)
        VOID_KNIGHT(2654, 2663, 0, SOUTH, 4)
        SQUIRE_1760(2656, 2658, 0, SOUTH, 5)
        SQUIRE_1767(2658, 2652, 0, SOUTH, 2)
        6713(2658, 2658, 0, SOUTH, 9)
        SQUIRE(2658, 2668, 0, SOUTH, 4)
        VOID_KNIGHT_1756(2659, 2665, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        VOID_KNIGHT_1757(2662, 2649, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        VOID_KNIGHT_1758(2662, 2653, 0, <PERSON><PERSON>UT<PERSON>, 5)
        6713(2662, 2654, 0, SOUTH, 9)
        SQ<PERSON>RE_1762(2662, 2661, 0, SOUTH, 2)
        1763(2666, 2651, 0, NORTH, 0)
        ELITE_VOID_KNIGHT(2667, 2648, 0, SOUTH, 2)
        SQUIRE_1761(2667, 2654, 0, SOUTH, 0)
        DODGY_SQUIRE(2667, 2657, 0, WEST, 0)
        SQUIRE_1765(2667, 2660, 0, SOUTH, 2)
        SQUIRE_1764(2668, 2651, 0, EAST, 0)
        SQUIRE_VETERAN(2638, 2656, 0, SOUTH, 0)
        SQUIRE_INTERMEDIATE(2644, 2641, 0, NORTH, 0)
        SQUIRE_NOVICE(2657, 2637, 0, NORTH, 0)
        SQUIRE_1769(2659, 2676, 0, SOUTH, 2)
    }
}