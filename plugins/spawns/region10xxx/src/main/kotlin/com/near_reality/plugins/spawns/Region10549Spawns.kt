package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10549Spawns : NPCSpawnsScript() {
    init {
        FISHING_SPOT_4082(2627, 3415, 0, SOUTH, 0)
        JOSHUA(2629, 3415, 0, SOUTH, 2)
        FISHING_SPOT_4079(2630, 3435, 0, SOUTH, 0)
        FISHING_SPOT_4081(2632, 3425, 0, SOUTH, 0)
        BIG_DAVE(2634, 3425, 0, SOUTH, 2)
        6185(2637, 3440, 0, SOUTH, 5)
        FISHING_SPOT_4080(2637, 3444, 0, SOUTH, 0)
        BONZO(2641, 3437, 0, NORTH, 0)
        MORRIS(2643, 3440, 0, EAST, 0)
        CHICKEN_1174(2650, 3441, 0, <PERSON><PERSON>UT<PERSON>, 4)
        CHICKEN(2650, 3442, 0, SOUTH, 2)
        GRANDPA_JACK(2650, 3452, 0, SOUTH, 3)
        CHICKEN(2651, 3441, 0, SOUTH, 2)
        CHICKEN_1174(2651, 3442, 0, SOUTH, 4)
        SHIELD_MASTER(2655, 3425, 0, SOUTH, 2)
        GUARD_6056(2655, 3429, 0, SOUTH, 2)
        GUARD_6056(2656, 3428, 0, SOUTH, 2)
        RANGING_GUILD_DOORMAN(2658, 3439, 0, SOUTH, 2)
        TICKET_MERCHANT(2659, 3429, 0, SOUTH, 2)
        TICKET_MERCHANT(2659, 3431, 0, SOUTH, 2)
        GUARD_6056(2659, 3438, 0, SOUTH, 2)
        TRIBAL_WEAPON_SALESMAN(2661, 3419, 0, SOUTH, 2)
        GUARD_6056(2663, 3425, 0, SOUTH, 2)
        GUARD_6056(2666, 3415, 0, SOUTH, 2)
        ARMOUR_SALESMAN(2667, 3436, 0, SOUTH, 2)
        GUARD_6056(2669, 3415, 0, SOUTH, 2)
        COMPETITION_JUDGE(2669, 3418, 0, SOUTH, 2)
        GUARD_6056(2669, 3441, 0, SOUTH, 2)
        GUARD_6056(2670, 3438, 0, SOUTH, 2)
        BOW_AND_ARROW_SALESMAN(2673, 3434, 0, SOUTH, 2)
        GUARD_6056(2673, 3439, 0, SOUTH, 2)
        GUARD_6056(2675, 3428, 0, SOUTH, 2)
        LEATHERWORKER(2678, 3434, 0, SOUTH, 2)
        GUARD_6056(2682, 3430, 0, SOUTH, 2)
        TOWER_ADVISOR_6064(2666, 3427, 2, SOUTH, 0)
        TOWER_ADVISOR(2667, 3430, 2, SOUTH, 0)
        TOWER_ADVISOR_6063(2669, 3426, 2, SOUTH, 0)
        TOWER_ADVISOR_6062(2670, 3429, 2, SOUTH, 0)
    }
}