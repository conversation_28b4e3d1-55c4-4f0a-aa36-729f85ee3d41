package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10895Spawns : NPCSpawnsScript() {
    init {
        SPIDER_5238(2697, 9171, 0, SOUT<PERSON>, 3)
        SPIDER_5238(2699, 9202, 0, SOUT<PERSON>, 3)
        SPIDER_5238(2701, 9179, 0, SOUTH, 3)
        SPIDER_5238(2701, 9194, 0, SOUTH, 3)
        SPIDER_5238(2704, 9202, 0, SOUTH, 3)
        SPIDER_5238(2706, 9194, 0, SOUT<PERSON>, 3)
        SPIDER_5238(2707, 9170, 0, SOUTH, 3)
        SPIDER_5238(2709, 9175, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SPIDER_5238(2722, 9173, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SPIDER_5238(2723, 9198, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SPIDER_5238(2725, 9195, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SPIDER_5238(2726, 9181, 0, SOUTH, 3)
        SPIDER_5238(2728, 9203, 0, SOUTH, 3)
        SPIDER_5238(2732, 9178, 0, SOUTH, 3)
        SPIDER_5238(2732, 9199, 0, SOUTH, 3)
        <PERSON><PERSON>ER_5238(2734, 9170, 0, SOUTH, 3)
    }
}