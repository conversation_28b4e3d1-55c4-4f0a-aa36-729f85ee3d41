package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10039Spawns : NPCSpawnsScript() {
    init {
        FISHING_SPOT_1544(2498, 3547, 0, SOUTH, 0)
        FISHING_SPOT_1544(2511, 3562, 0, SOUTH, 0)
        FISHING_SPOT_1544(2516, 3575, 0, SOUTH, 0)
        PRIVATE_PIERREB(2520, 3568, 0, SOUTH, 4)
        PRIVATE_PENDRON(2525, 3564, 0, SOUTH, 3)
        PRIVATE_PALDON(2526, 3575, 0, SOUTH, 3)
        CAPTAIN_CAIN(2534, 3568, 0, SOUTH, 2)
        COMMANDER_CONNAD(2536, 3577, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        GUNNJORN(2540, 3548, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        5227(2544, 3567, 0, <PERSON>OUTH, 2)
        5227(2544, 3571, 0, <PERSON>O<PERSON><PERSON>, 2)
        BARBARIAN_3062(2544, 3577, 0, SOUTH, 4)
        BARBARIAN(2546, 3560, 0, SOUTH, 3)
        BARBARIAN_3056(2546, 3565, 0, SOUTH, 11)
        BARBARIAN_3071(2548, 3558, 0, SOUTH, 9)
        BARBARIAN_3057(2549, 3566, 0, SOUTH, 4)
        BARBARIAN_3059(2549, 3570, 0, SOUTH, 8)
        BARBARIAN_3069(2550, 3563, 0, SOUTH, 4)
        KHARID_SCORPION_5229(2552, 3570, 0, SOUTH, 2)
        CHICKEN_1174(2553, 3562, 0, SOUTH, 4)
        CHICKEN_1174(2553, 3565, 0, SOUTH, 4)
        CHICKEN(2554, 3563, 0, SOUTH, 2)
    }
}