package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10144Spawns : NPCSpawnsScript() {
    init {
        THORODIN(2504, 10280, 0, SOUTH, 0)
        HALLA(2506, 10250, 0, WEST, 0)
        FINN(2508, 10260, 0, SOUTH, 0)
        FERD(2508, 10278, 0, SOUTH, 0)
        RUNOLF(2509, 10257, 0, SOUTH, 2)
        JARI(2516, 10262, 0, SOUTH, 2)
        ALVISS(2517, 10263, 0, SOUTH, 0)
        FULLANGR(2519, 10263, 0, SOUTH, 8)
        OSVALD(2521, 10251, 0, SOUTH, 0)
        TJORVI(2523, 10257, 0, <PERSON>O<PERSON><PERSON>, 6)
        INGRID(2526, 10263, 0, <PERSON>OUT<PERSON>, 2)
        RUNA(2528, 10259, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        DONAL(2529, 10256, 0, SOUTH, 2)
        THORA(2531, 10249, 0, SOUTH, 0)
    }
}