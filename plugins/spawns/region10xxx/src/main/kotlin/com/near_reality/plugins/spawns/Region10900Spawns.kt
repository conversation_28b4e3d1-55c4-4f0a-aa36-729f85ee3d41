package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10900Spawns : NPCSpawnsScript() {
    init {
        BABY_RED_DRAGON_244(2696, 9501, 0, SOUTH, 4)
        RED_DRAGON_251(2697, 9506, 0, SOUTH, 6)
        BABY_RED_DRAGON_246(2698, 9524, 0, SOUTH, 3)
        BLACK_DEMON_2050(2700, 9489, 0, SOUTH, 3)
        RED_DRAGON(2702, 9504, 0, SOUTH, 4)
        BLACK_DEMON_2048(2703, 9483, 0, SOUTH, 7)
        RED_DRAGON_249(2703, 9522, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        RED_DRAGON_248(2703, 9532, 0, SO<PERSON>H, 4)
        RED_DRAGON_250(2706, 9516, 0, <PERSON><PERSON>UTH, 7)
        BABY_RED_DRAGON_245(2708, 9500, 0, SOUTH, 3)
        RED_DRAGON_251(2708, 9508, 0, SOUTH, 6)
        BABY_RED_DRAGON_245(2708, 9526, 0, SOUTH, 3)
        BLACK_DEMON_2049(2709, 9479, 0, SOUTH, 6)
        RED_DRAGON_248(2711, 9500, 0, SOUTH, 4)
        BABY_RED_DRAGON_244(2711, 9516, 0, SOUTH, 4)
        BABY_RED_DRAGON_246(2714, 9505, 0, SOUTH, 3)
        RED_DRAGON_248(2714, 9526, 0, SOUTH, 4)
        BLACK_DEMON_2048(2715, 9482, 0, SOUTH, 7)
        RED_DRAGON_249(2717, 9516, 0, SOUTH, 8)
        BABY_RED_DRAGON_244(2721, 9511, 0, SOUTH, 4)
        RED_DRAGON_248(2721, 9522, 0, SOUTH, 4)
        BABY_RED_DRAGON_244(2721, 9530, 0, SOUTH, 4)
        RED_DRAGON(2724, 9516, 0, SOUTH, 4)
        BABY_RED_DRAGON_245(2730, 9512, 0, SOUTH, 3)
        BRONZE_DRAGON(2731, 9482, 0, SOUTH, 2)
        BRONZE_DRAGON(2731, 9491, 0, SOUTH, 2)
        BRONZE_DRAGON(2740, 9492, 0, SOUTH, 2)
        WILD_DOG(2740, 9502, 0, SOUTH, 8)
        WILD_DOG(2740, 9508, 0, SOUTH, 8)
        WILD_DOG(2743, 9503, 0, SOUTH, 8)
    }
}