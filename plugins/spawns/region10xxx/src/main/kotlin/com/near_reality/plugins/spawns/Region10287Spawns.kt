package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10287Spawns : NPCSpawnsScript() {
    init {
        OGRE_2095(2561, 3020, 0, SOUTH, 2)
        OGRE_2095(2567, 3046, 0, SOUTH, 2)
        OGRE_2096(2571, 3027, 0, SOUTH, 3)
        UNICORN(2571, 3058, 0, SOUTH, 15)
        OGRE_2096(2572, 3031, 0, SOUTH, 3)
        OGRE_2096(2575, 3024, 0, SOUTH, 3)
        TOBAN(2576, 3027, 0, SOUTH, 2)
        GORAD(2577, 3021, 0, SOUTH, 2)
        OGRE_2096(2578, 3031, 0, SOUTH, 3)
        UNICORN(2579, 3066, 0, <PERSON>OUT<PERSON>, 15)
        DWARF(2596, 3056, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        DWARF(2600, 3062, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        DWARF(2605, 3057, 0, SOUTH, 13)
        BLACK_BEAR(2606, 3008, 0, SOUTH, 8)
        DWARF(2608, 3061, 0, SOUTH, 13)
    }
}