package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10907Spawns : NPCSpawnsScript() {
    init {
        KURASK_410(2696, 9969, 0, SOUTH, 7)
        KURASK_410(2698, 9962, 0, SOUTH, 7)
        KURASK_410(2699, 9977, 0, SOUTH, 7)
        KURASK_410(2704, 9959, 0, SOUTH, 7)
        KURASK_410(2705, 9969, 0, SOUTH, 7)
        KURASK_410(2709, 9976, 0, SOUTH, 7)
        KURASK_410(2710, 9963, 0, SOUTH, 7)
        KURASK_410(2713, 9970, 0, <PERSON>OUT<PERSON>, 7)
        KURASK_410(2716, 9964, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
    }
}