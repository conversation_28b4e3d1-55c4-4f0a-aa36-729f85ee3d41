package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10129Spawns : NPCSpawnsScript() {
    init {
        CARNIVOROUS_CHINCHOMPA(2512, 9300, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(2513, 9294, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(2513, 9297, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(2515, 9289, 0, SOUTH, 6)
        1492(2516, 9322, 0, <PERSON>OUT<PERSON>, 5)
        CARNIVOROUS_CHINCHOMPA(2518, 9302, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(2518, 9305, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        CARNIVOROUS_CHINCHOMPA(2519, 9292, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        CARNIVOROUS_CHINCHOMPA(2520, 9288, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(2520, 9298, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(2523, 9302, 0, SOUTH, 6)
        1492(2523, 9317, 0, SOUTH, 5)
        CARNIVOROUS_CHINCHOMPA(2525, 9296, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(2526, 9289, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(2528, 9292, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(2528, 9303, 0, SOUTH, 6)
        CARNIVOROUS_CHINCHOMPA(2529, 9300, 0, SOUTH, 6)
    }
}