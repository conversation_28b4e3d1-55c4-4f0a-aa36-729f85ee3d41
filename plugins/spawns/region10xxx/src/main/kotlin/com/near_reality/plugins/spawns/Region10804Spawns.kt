package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10804Spawns : NPCSpawnsScript() {
    init {
        GRIZZLY_BEAR(2691, 3341, 0, SOUTH, 11)
        GRIZZLY_BEAR(2692, 3333, 0, SOUTH, 11)
        GRIZZLY_BEAR(2694, 3344, 0, SOUTH, 11)
        GRIZZLY_BEAR(2698, 3331, 0, SOUTH, 11)
        GRIZZLY_BEAR(2702, 3344, 0, SOUTH, 11)
        GRIZZLY_BEAR(2706, 3330, 0, SOUTH, 11)
        GRIZZLY_BEAR(2708, 3336, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        GRIZZLY_BEAR(2716, 3336, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        GRIZZLY_BEAR(2718, 3330, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        10712(2724, 3368, 0, <PERSON>OUTH, 5)
        10713(2724, 3378, 0, SOUTH, 5)
        LEGENDS_GUARD(2726, 3349, 0, SOUTH, 5)
        LEGENDS_GUARD_3952(2731, 3348, 0, SOUTH, 5)
        FIONELLA(2725, 3378, 1, SOUTH, 5)
        SIEGFRIED_ERKLE(2725, 3381, 2, SOUTH, 5)
    }
}