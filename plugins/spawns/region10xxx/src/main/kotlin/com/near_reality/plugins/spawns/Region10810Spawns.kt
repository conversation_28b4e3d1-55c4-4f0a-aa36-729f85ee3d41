package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10810Spawns : NPCSpawnsScript() {
    init {
        ROCKS_103(2694, 3724, 0, SOUTH, 0)
        GULL_3904(2694, 3752, 0, SOUTH, 12)
        GULL_3904(2697, 3755, 0, SOUTH, 12)
        ROCKS(2700, 3718, 0, SOUTH, 0)
        GULL_3904(2700, 3758, 0, SOUTH, 12)
        ROCKS_103(2701, 3728, 0, SOUTH, 0)
        ROCKS(2702, 3720, 0, SOUTH, 0)
        ROCKS_103(2703, 3716, 0, SOUTH, 0)
        ROCKS(2704, 3727, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        GULL_3904(2704, 3760, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        ROCKS_103(2705, 3725, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        SABRETOOTHED_KEBBIT(2707, 3774, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        ROCKS(2708, 3719, 0, SOUTH, 0)
        GULL_3904(2708, 3760, 0, SOUTH, 12)
        ROCKS_103(2711, 3715, 0, SOUTH, 0)
        SABRETOOTHED_KEBBIT(2711, 3768, 0, SOUTH, 2)
        ROCKS(2712, 3719, 0, SOUTH, 0)
        ROCKS_103(2712, 3725, 0, SOUTH, 0)
        SABRETOOTHED_KEBBIT(2712, 3773, 0, SOUTH, 2)
        GULL_3904(2714, 3760, 0, SOUTH, 12)
        CERULEAN_TWITCH(2714, 3765, 0, SOUTH, 7)
        SABRETOOTHED_KEBBIT(2714, 3770, 0, SOUTH, 2)
        ROCKS_103(2715, 3729, 0, SOUTH, 0)
        SABRETOOTHED_KEBBIT(2715, 3764, 0, SOUTH, 2)
        SABRETOOTHED_KEBBIT(2715, 3766, 0, SOUTH, 2)
        ROCKS(2716, 3721, 0, SOUTH, 0)
        SABRETOOTHED_KEBBIT(2716, 3770, 0, SOUTH, 2)
        CERULEAN_TWITCH(2716, 3775, 0, SOUTH, 7)
        ROCKS_103(2719, 3719, 0, SOUTH, 0)
        CERULEAN_TWITCH(2719, 3769, 0, SOUTH, 7)
        SABRETOOTHED_KEBBIT(2720, 3764, 0, SOUTH, 2)
        OLAF_HRADSON(2724, 3729, 0, WEST, 0)
        CERULEAN_TWITCH(2727, 3772, 0, SOUTH, 7)
        CERULEAN_TWITCH(2731, 3767, 0, SOUTH, 7)
        CERULEAN_TWITCH(2733, 3775, 0, SOUTH, 7)
        CERULEAN_TWITCH(2736, 3765, 0, SOUTH, 7)
        CERULEAN_TWITCH(2740, 3770, 0, SOUTH, 7)
        8413(2706, 3732, 0, SOUTH, 5)
        LARRY_828(2707, 3734, 0, SOUTH, 2)
    }
}