package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10638Spawns : NPCSpawnsScript() {
    init {
        MANIACAL_MONKEY_7118(2629, 9113, 0, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2631, 9110, 0, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2631, 9115, 0, SOUTH, 0)
        MANIACAL_MONKEY_7118(2633, 9117, 0, <PERSON>OUTH, 47)
        MANIACAL_MONKEY_7118(2634, 9109, 0, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2634, 9114, 0, <PERSON>O<PERSON><PERSON>, 0)
        MANIACAL_MONKEY_ARCHER(2636, 9108, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        MANIACAL_MONKEY_7118(2636, 9117, 0, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANIACAL_MONKEY_7118(2638, 9103, 0, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANIAC<PERSON>_<PERSON><PERSON><PERSON>Y_7118(2638, 9113, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2639, 9109, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2639, 9118, 0, SOUTH, 47)
        MANI<PERSON><PERSON>_MONKEY_ARCHER(2640, 9114, 0, SOUTH, 0)
        MANIACAL_MONKEY_7118(2642, 9111, 0, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2642, 9117, 0, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2643, 9108, 0, SOUTH, 0)
        MANIACAL_MONKEY_7118(2644, 9110, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2644, 9115, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2657, 9150, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2659, 9151, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2660, 9147, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2660, 9149, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2663, 9148, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2665, 9149, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2665, 9151, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2667, 9147, 0, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2626, 9139, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2627, 9137, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2629, 9138, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2631, 9136, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2637, 9094, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2638, 9089, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2638, 9093, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2639, 9137, 1, SOUTH, 0)
        SCORPION_5242(2640, 9088, 1, SOUTH, 5)
        MANIACAL_MONKEY_7118(2640, 9135, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2645, 9088, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2645, 9135, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2645, 9137, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2646, 9135, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2652, 9119, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2652, 9127, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2652, 9129, 1, SOUTH, 47)
        SCORPION_5242(2652, 9134, 1, SOUTH, 5)
        MANIACAL_MONKEY_ARCHER(2653, 9120, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2653, 9130, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2653, 9136, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2654, 9117, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2654, 9126, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2656, 9116, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2657, 9138, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2658, 9139, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2659, 9116, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2661, 9113, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2662, 9107, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2663, 9107, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2664, 9106, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2665, 9105, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2666, 9094, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2666, 9098, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2666, 9102, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2667, 9089, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2667, 9101, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2668, 9091, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2668, 9093, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2668, 9094, 1, SOUTH, 0)
        SCORPION_5242(2668, 9100, 1, SOUTH, 5)
    }
}