package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10658Spawns : NPCSpawnsScript() {
    init {
        ICELORD(2641, 10426, 0, SOUTH, 5)
        ICELORD_853(2644, 10424, 0, SOUTH, 5)
        ICELORD_854(2647, 10425, 0, SOUTH, 5)
        KGP_AGENT(2648, 10384, 0, SOUTH, 5)
        ICELORD_855(2650, 10423, 0, SOUTH, 5)
        PENGUIN_831(2655, 10389, 0, SOUTH, 5)
        PENGUIN_831(2655, 10402, 0, SOUTH, 5)
        1983(2655, 10408, 0, SOUTH, 0)
        PENGUIN_831(2660, 10378, 0, SOUTH, 5)
        PING_839(2670, 10395, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        PONG_840(2670, 10397, 0, <PERSON><PERSON>UTH, 5)
    }
}