package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10133Spawns : NPCSpawnsScript() {
    init {
        HOBGOBLIN_3049(2513, 9569, 0, <PERSON>OUT<PERSON>, 13)
        HOBGOBLIN_3049(2515, 9568, 0, SOUTH, 13)
        HOBGOBLIN_3049(2515, 9571, 0, SOUTH, 13)
        HOBGOBLIN_3049(2515, 9573, 0, <PERSON>OUTH, 13)
        GOLRIE_4183(2515, 9581, 0, SOUTH, 0)
        HOBGOBLIN_3049(2519, 9561, 0, SOUTH, 13)
        HOBGOBLIN_3049(2519, 9571, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        HOBGOBLIN_3049(2522, 9561, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        HOBGOBLIN_3049(2529, 9555, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        HOBGOBLIN_3049(2533, 9556, 0, <PERSON><PERSON><PERSON>H, 13)
        <PERSON>OMBIE_49(2539, 9566, 0, SOUTH, 4)
        GIANT_BAT(2540, 9566, 0, SOUTH, 11)
        ZOMBIE(2541, 9569, 0, SOUTH, 5)
        GIANT_BAT(2542, 9557, 0, SOUTH, 11)
        GIANT_BAT(2543, 9564, 0, SOUTH, 11)
        ZOMBIE_28(2548, 9567, 0, SOUTH, 4)
    }
}