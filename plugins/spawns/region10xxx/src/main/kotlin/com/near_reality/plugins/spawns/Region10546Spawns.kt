package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10546Spawns : NPCSpawnsScript() {
    init {
        IMP_5007(2625, 3203, 0, SOUTH, 100)
        IMP_5007(2625, 3217, 0, SOUTH, 100)
        IMP_5007(2629, 3233, 0, SOUTH, 100)
        IMP_5007(2630, 3210, 0, SOUTH, 100)
        IMP_5007(2632, 3202, 0, SOUTH, 100)
        IMP_5007(2633, 3222, 0, SOUTH, 100)
        IMP_5007(2633, 3243, 0, SOUTH, 100)
        6157(2638, 3220, 0, SOUTH, 2)
        IMP_5007(2639, 3206, 0, SOUTH, 100)
        6154(2639, 3219, 0, <PERSON>O<PERSON><PERSON>, 2)
        IMP_5007(2639, 3230, 0, SOUTH, 100)
        6158(2640, 3220, 0, SOUTH, 2)
        6156(2640, 3221, 0, SOUTH_WEST, 0)
        THE_GUNS(2643, 3226, 0, SOUTH, 0)
        NO_FINGERS(2645, 3224, 0, NORTH, 0)
        BLACKEYE(2645, 3229, 0, SOUTH, 0)
        GUMMY(2645, 3230, 0, SOUTH, 0)
        BONAFIDO(2650, 3227, 0, WEST, 0)
        IRWIN_FEASELBAUM(2666, 3246, 0, SOUTH, 2)
        NECROMANCER(2671, 3240, 0, SOUTH, 5)
        INVRIGAR_THE_NECROMANCER(2667, 3241, 1, SOUTH, 12)
        6155(2648, 3217, 3, SOUTH, 0)
    }
}