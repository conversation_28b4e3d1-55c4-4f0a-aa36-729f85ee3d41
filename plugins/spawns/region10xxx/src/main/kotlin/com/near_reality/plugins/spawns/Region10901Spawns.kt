package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10901Spawns : NPCSpawnsScript() {
    init {
        WILD_DOG(2694, 9542, 0, SOUTH, 8)
        BABY_RED_DRAGON_244(2701, 9548, 0, SOUTH, 4)
        RED_DRAGON_250(2704, 9539, 0, SOUTH, 7)
        RED_DRAGON_249(2704, 9546, 0, <PERSON>OUTH, 8)
        BABY_RED_DRAGON_246(2709, 9545, 0, SOUTH, 3)
        RED_DRAGON_251(2711, 9537, 0, SOUTH, 6)
        RED_DRAGON_248(2711, 9550, 0, SOUTH, 4)
        RED_DRAGON(2712, 9543, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        BABY_RED_DRAGON_246(2720, 9536, 0, SOUTH, 3)
    }
}