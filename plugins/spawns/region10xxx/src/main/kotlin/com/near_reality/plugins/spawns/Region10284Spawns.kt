package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10284Spawns : NPCSpawnsScript() {
    init {
        SEAGULL_1339(2564, 2855, 0, SOUTH, 4)
        SEAGULL_1339(2566, 2861, 0, SOUTH, 4)
        7994(2569, 2866, 0, SOUTH_EAST, 0)
        RAM_1262(2576, 2868, 0, SOUTH, 5)
        SEAGULL_1339(2578, 2862, 0, SOUTH, 4)
        7960(2586, 2867, 0, NORTH, 0)
        7980(2574, 2835, 1, SOUTH, 5)
        SEAGULL_1339(2574, 2852, 0, SOUTH, 4)
        SEAGULL_1339(2577, 2857, 0, SOUTH, 4)
        SEAGULL_1339(2579, 2845, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        SEAGULL_1339(2581, 2851, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        1334(2587, 2851, 0, SOUTH, 2)
        1331(2589, 2851, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
    }
}