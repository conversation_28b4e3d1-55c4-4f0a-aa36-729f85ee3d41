package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10828Spawns : NPCSpawnsScript() {
    init {
        GHOST_87(2712, 4898, 0, SOUTH, 2)
        GHOST_89(2712, 4908, 0, SOUTH, 4)
        GHOST(2714, 4892, 0, SOUTH, 5)
        1987(2721, 4910, 0, SOUTH, 0)
        913(2723, 4897, 0, NORTH, 0)
        GHOST_91(2730, 4907, 0, SOUTH, 6)
        GHOST_86(2731, 4893, 0, SOUTH, 5)
    }
}