package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10314Spawns : NPCSpawnsScript() {
    init {
        5499(2597, 4779, 0, SO<PERSON><PERSON>, 5)
        5499(2598, 4774, 0, SOUT<PERSON>, 5)
        5498(2598, 4777, 0, SOUTH, 5)
        5497(2599, 4772, 0, SOUTH, 5)
        FREAKY_FORESTER(2599, 4775, 0, SOUTH, 5)
        5501(2600, 4777, 0, SOUTH, 5)
        5501(2603, 4769, 0, SOUTH, 5)
        5497(2603, 4774, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        5499(2605, 4777, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        5498(2606, 4778, 0, <PERSON>O<PERSON><PERSON>, 5)
        5501(2607, 4775, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        5498(2608, 4772, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        5497(2610, 4774, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
    }
}