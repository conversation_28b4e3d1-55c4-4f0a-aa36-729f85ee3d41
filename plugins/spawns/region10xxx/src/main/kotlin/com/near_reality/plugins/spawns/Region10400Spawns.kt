package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10400Spawns : NPCSpawnsScript() {
    init {
        SIGNY(2571, 10278, 0, SOUTH, 5)
        ARMOD(2572, 10277, 0, SOUTH, 5)
        REINN(2572, 10278, 0, SOUTH, 5)
        BEIGARTH(2573, 10277, 0, SOUTH, 5)
        HILD(2573, 10278, 0, SOUTH, 5)
        SEA_SNAKE_HATCHLING(2577, 10295, 0, SOUTH, 5)
        SEA_SNAKE_HATCHLING(2581, 10297, 0, SOUTH, 5)
        SEA_SNAKE_HATCHLING(2583, 10294, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SEA_<PERSON><PERSON><PERSON>_HATCHLING(2587, 10294, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SEA_SNAKE_HATCHLING(2589, 10287, 0, SOUTH, 5)
        SEA_SNAKE_HATCHLING(2589, 10291, 0, SOUTH, 5)
        SEA_SNAKE_HATCHLING(2592, 10288, 0, SOUTH, 5)
        SEA_SNAKE_YOUNG(2595, 10271, 0, SOUTH, 5)
        SEA_SNAKE_YOUNG(2595, 10280, 0, SOUTH, 5)
        SEA_SNAKE_YOUNG(2597, 10263, 0, SOUTH, 5)
        SEA_SNAKE_YOUNG(2597, 10275, 0, SOUTH, 5)
        SEA_SNAKE_YOUNG(2598, 10253, 0, SOUTH, 5)
        SEA_SNAKE_YOUNG(2599, 10257, 0, SOUTH, 5)
        SEA_SNAKE_YOUNG(2599, 10267, 0, SOUTH, 5)
        SEA_SNAKE_YOUNG(2604, 10247, 0, SOUTH, 5)
        SEA_SNAKE_YOUNG(2605, 10251, 0, SOUTH, 5)
        SEA_SNAKE_YOUNG(2611, 10251, 0, SOUTH, 5)
    }
}