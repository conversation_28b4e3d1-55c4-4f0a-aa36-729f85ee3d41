package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10544Spawns : NPCSpawnsScript() {
    init {
        JUNGLE_SPIDER(2667, 3090, 0, SOUTH, 4)
        JUNGLE_SPIDER(2669, 3091, 0, SOUTH, 4)
        JUNGLE_SPIDER(2670, 3087, 0, SOUTH, 4)
        JUNGLE_SPIDER(2670, 3089, 0, SOUTH, 4)
        SPIDER_3019(2671, 3107, 0, <PERSON>OUTH, 8)
        JUNGLE_SPIDER(2672, 3093, 0, SOUT<PERSON>, 4)
        SPIDER_3019(2672, 3109, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        JUNGLE_SPIDER(2673, 3085, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        JUN<PERSON><PERSON>_SPIDER(2673, 3088, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        JUNG<PERSON>_SPIDER(2673, 3092, 0, <PERSON>OUTH, 4)
        JUNGLE_SPIDER(2674, 3096, 0, SOUTH, 4)
        JUNGLE_SPIDER(2674, 3111, 0, SOUTH, 4)
        JUNGLE_SPIDER(2677, 3097, 0, SOUTH, 4)
        JUNGLE_SPIDER(2678, 3084, 0, SOUTH, 4)
        JUNGLE_SPIDER(2678, 3091, 0, SOUTH, 4)
        JUNGLE_SPIDER(2680, 3081, 0, SOUTH, 4)
        JUNGLE_SPIDER(2680, 3090, 0, SOUTH, 4)
        JUNGLE_SPIDER(2680, 3094, 0, SOUTH, 4)
        JUNGLE_SPIDER(2680, 3110, 0, SOUTH, 4)
        JUNGLE_SPIDER(2681, 3086, 0, SOUTH, 4)
        JUNGLE_SPIDER(2681, 3094, 0, SOUTH, 4)
        JUNGLE_SPIDER(2681, 3114, 0, SOUTH, 4)
        JUNGLE_SPIDER(2683, 3088, 0, SOUTH, 4)
        HAZELMERE(2678, 3086, 1, WEST, 0)
    }
}