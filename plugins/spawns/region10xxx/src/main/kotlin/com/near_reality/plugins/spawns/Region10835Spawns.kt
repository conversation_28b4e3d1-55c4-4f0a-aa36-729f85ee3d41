package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10835Spawns : NPCSpawnsScript() {
    init {
        CAVE_GOBLIN_2274(2693, 5332, 0, SOUTH, 0)
        CAVE_GOBLIN_2281(2694, 5333, 0, SOUTH, 2)
        CAVE_GOBLIN_2283(2695, 5331, 0, SOUTH, 0)
        BARLAK(2697, 5316, 0, SOUTH, 2)
        CAVE_GOBLIN_2269(2697, 5332, 0, NORTH, 0)
        MOTHS(2699, 5329, 0, SOUTH, 3)
        MOTHS(2699, 5335, 0, SOUT<PERSON>, 3)
        BANKER_2293(2699, 5348, 0, EAST, 2)
        BANKER_2292(2699, 5349, 0, EAST, 2)
        CAVE_GOBLIN_2299(2701, 5348, 0, <PERSON><PERSON><PERSON><PERSON>, 35)
        2303(2704, 5366, 0, <PERSON><PERSON><PERSON>H, 2)
        GUNDIK(2711, 5314, 0, <PERSON>O<PERSON>H, 0)
        CAVE_GOBL<PERSON>(2711, 5331, 0, SOUTH, 4)
        DURGOK(2712, 5321, 0, SOUTH, 5)
        CAVE_GOBLIN_2280(2712, 5342, 0, SOUTH, 3)
        GOURMET_2306(2715, 5313, 0, SOUTH, 5)
        URT<PERSON>L(2716, 5359, 0, SOUTH, 2)
        LURGON(2717, 5315, 0, SOUTH, 0)
        URZEK(2717, 5359, 0, SOUTH, 2)
        GOURMET_2305(2718, 5313, 0, SOUTH, 4)
        CAVE_GOBLIN_2283(2719, 5331, 0, SOUTH, 0)
        GUARD_2316(2719, 5335, 0, SOUTH, 8)
        GOBLIN_FISH(2720, 5356, 0, SOUTH, 0)
        RELDAK(2722, 5313, 0, SOUTH, 0)
        URVASS(2722, 5333, 0, SOUTH, 5)
        MILTOG(2723, 5321, 0, SOUTH, 5)
        CAVE_GOBLIN_2271(2723, 5336, 0, SOUTH, 6)
        GOBLIN_FISH(2724, 5354, 0, SOUTH, 0)
        CAVE_GOBLIN_2277(2725, 5349, 0, SOUTH, 2)
        CAVE_GOBLIN_2274(2729, 5329, 0, SOUTH, 0)
        GUARD_2317(2730, 5333, 0, SOUTH, 4)
        CAVE_GOBLIN_2273(2743, 5358, 0, SOUTH, 2)
        5183(2744, 5344, 0, SOUTH, 3)
        MOTHS(2747, 5342, 0, SOUTH, 3)
        GUARD_2316(2747, 5373, 0, SOUTH, 8)
        CAVE_GOBLIN_2282(2701, 5352, 1, SOUTH, 3)
        CAVE_GOBLIN_2272(2702, 5352, 1, SOUTH, 5)
        GOBLIN_SCRIBE(2712, 5369, 1, SOUTH, 3)
        CAVE_GOBLIN_2301(2726, 5372, 1, SOUTH, 56)
        URTAG(2730, 5365, 1, SOUTH, 3)
        6282(2730, 5366, 1, SOUTH, 3)
        6283(2737, 5351, 1, SOUTH, 3)
        CAVE_GOBLIN_CHILD_2337(2740, 5334, 1, SOUTH, 3)
        CAVE_GOBLIN_CHILD_2324(2741, 5334, 1, SOUTH, 4)
        CAVE_GOBLIN_CHILD_2325(2741, 5341, 1, SOUTH, 4)
        YOUNG_UN(2741, 5344, 1, SOUTH, 2)
        CAVE_GOBLIN_CHILD_2329(2742, 5338, 1, SOUTH, 4)
        MERNIK(2742, 5339, 1, SOUTH, 4)
        CAVE_GOBLIN_CHILD_2331(2743, 5341, 1, SOUTH, 3)
        NIPPER(2743, 5345, 1, SOUTH, 2)
        TYKE(2744, 5333, 1, SOUTH, 0)
        CAVE_GOBLIN_CHILD_2335(2744, 5336, 1, SOUTH, 2)
        CAVE_GOBLIN_CHILD_2333(2744, 5338, 1, SOUTH, 4)
        CAVE_GOBLIN_CHILD_2327(2744, 5342, 1, SOUTH, 3)
        3186(2744, 5353, 1, SOUTH, 7)
        NIPPER_2322(2746, 5333, 1, SOUTH, 0)
    }
}