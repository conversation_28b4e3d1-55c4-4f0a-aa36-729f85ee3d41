package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10637Spawns : NPCSpawnsScript() {
    init {
        MANIACAL_MONKEY_ARCHER(2666, 9042, 0, SOUTH, 0)
        MANIACAL_MONKEY_7118(2666, 9044, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2667, 9041, 0, SOUTH, 47)
        MANIACAL_MONKEY_7118(2668, 9039, 0, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2669, 9045, 0, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2670, 9040, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        MANIACAL_MONKEY_7118(2671, 9039, 0, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANI<PERSON>AL_MONKEY_7118(2671, 9045, 0, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANIACAL_MONKEY_ARCHER(2673, 9043, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        MA<PERSON><PERSON><PERSON>_M<PERSON><PERSON>Y_7118(2673, 9046, 0, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2674, 9045, 0, SOUTH, 0)
        MANIACAL_MONKEY_7118(2626, 9040, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2627, 9071, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2628, 9072, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2629, 9040, 1, SOUTH, 0)
        SCORPION_5242(2632, 9042, 1, SOUTH, 5)
        MANIACAL_MONKEY_7118(2633, 9045, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2633, 9053, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2635, 9054, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2636, 9058, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2636, 9069, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2637, 9067, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2638, 9058, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2639, 9056, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2639, 9064, 1, SOUTH, 0)
        SCORPION_5242(2639, 9068, 1, SOUTH, 5)
        MANIACAL_MONKEY_7118(2641, 9086, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2642, 9055, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2647, 9054, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2647, 9087, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2648, 9057, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2649, 9054, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2649, 9087, 1, SOUTH, 47)
        SCORPION_5242(2650, 9047, 1, SOUTH, 5)
        MANIACAL_MONKEY_ARCHER(2650, 9061, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2651, 9045, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2651, 9050, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2651, 9069, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2651, 9070, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2652, 9061, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2653, 9071, 1, SOUTH, 0)
        SCORPION_5242(2655, 9082, 1, SOUTH, 5)
        MANIACAL_MONKEY_ARCHER(2656, 9076, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2657, 9044, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2658, 9078, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2658, 9080, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2659, 9043, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2669, 9082, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2669, 9084, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2671, 9082, 1, SOUTH, 0)
        SCORPION_5242(2672, 9076, 1, SOUTH, 5)
        MANIACAL_MONKEY_7118(2672, 9079, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2674, 9050, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2675, 9040, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2675, 9049, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2677, 9064, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2677, 9071, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2677, 9073, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2678, 9044, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2678, 9049, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2678, 9056, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2678, 9058, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2678, 9070, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2678, 9071, 1, SOUTH, 47)
        SCORPION_5242(2679, 9042, 1, SOUTH, 5)
        MANIACAL_MONKEY_ARCHER(2679, 9046, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2679, 9063, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2679, 9070, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2680, 9056, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2680, 9063, 1, SOUTH, 47)
    }
}