package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10293Spawns : NPCSpawnsScript() {
    init {
        //Platform
        DUCK(2594, 3446, 0, SOUTH, 18)
        DUCK(2595, 3449, 0, SOUT<PERSON>, 18)
        DUCK(2596, 3453, 0, SOUTH, 18)
        DUCK(2598, 3440, 0, SOUTH, 18)
        DUCK(2598, 3444, 0, SOUTH, 18)
        DUCK(2600, 3435, 0, SOUTH, 18)
        DUCK(2605, 3433, 0, SOUTH, 18)
        DUCK(2616, 3450, 0, SOUTH, 18)
        DUCK(2620, 3449, 0, <PERSON><PERSON><PERSON><PERSON>, 18)
        DUCK_1839(2593, 3445, 0, <PERSON><PERSON><PERSON><PERSON>, 20)
        DUCK_1839(2593, 3454, 0, <PERSON><PERSON><PERSON><PERSON>, 20)
        DUCK_1839(2594, 3452, 0, <PERSON><PERSON>UT<PERSON>, 20)
        DUCK_1839(2595, 3447, 0, SOUT<PERSON>, 20)
        DUCK_1839(2595, 3454, 0, SOUTH, 20)
        DUCK_1839(2597, 3443, 0, SOUTH, 20)
        DUCK_1839(2597, 3446, 0, SOUTH, 20)
        DUCK_1839(2598, 3437, 0, SOUTH, 20)
        DUCK_1839(2598, 3454, 0, SOUTH, 20)
        DUCK_1839(2599, 3434, 0, SOUTH, 20)
        DUCK_1839(2600, 3440, 0, SOUTH, 20)
        DUCK_1839(2600, 3445, 0, SOUTH, 20)
        DUCK_1839(2601, 3436, 0, SOUTH, 20)
        DUCK_1839(2602, 3433, 0, SOUTH, 20)
        DUCK_1839(2604, 3430, 0, SOUTH, 20)
        DUCK_1839(2604, 3434, 0, SOUTH, 20)
        DUCK_1839(2605, 3432, 0, SOUTH, 20)
        DUCK_1839(2607, 3433, 0, SOUTH, 20)
        DUCK_1839(2614, 3449, 0, SOUTH, 20)
        DUCK_1839(2615, 3453, 0, SOUTH, 20)
        DUCK_1839(2617, 3448, 0, SOUTH, 20)
        DUCKLINGS(2593, 3446, 0, SOUTH, 2)
        DUCKLINGS(2601, 3437, 0, SOUTH, 2)
        DUCKLINGS(2604, 3431, 0, SOUTH, 2)
        FISHING_SPOT_7730(2609, 3443, 0, SOUTH, 0)
        FISHING_SPOT_7731(2612, 3444, 0, SOUTH, 0)
        FISHING_SPOT_7732(2617, 3444, 0, SOUTH, 0)
        FISHING_SPOT_7733(2620, 3443, 0, SOUTH, 0)
        GOBLIN_5195(2566, 3453, 0, SOUTH, 9)
        GOBLIN_5196(2567, 3428, 0, SOUTH, 9)
        GOBLIN_5197(2569, 3442, 0, SOUTH, 12)
        GOBLIN_5199(2571, 3442, 0, SOUTH, 2)
        GOBLIN_5206(2563, 3439, 0, SOUTH, 6)
        KYLIE_MINNOW_7728(2614, 3446, 0, SOUTH, 5)
        //North
        BANKER_1613(2584, 3422, 0, EAST, 0)
        BANKER_1618(2584, 3421, 0, EAST, 0)
        DUCK(2615, 3423, 0, SOUTH, 18)
        DUCK(2616, 3420, 0, SOUTH, 18)
        DUCK(2617, 3425, 0, SOUTH, 18)
        DUCK(2619, 3421, 0, SOUTH, 18)
        DUCK_1839(2613, 3422, 0, SOUTH, 20)
        DUCK_1839(2614, 3424, 0, SOUTH, 20)
        DUCK_1839(2615, 3421, 0, SOUTH, 20)
        DUCK_1839(2615, 3425, 0, SOUTH, 20)
        DUCK_1839(2616, 3423, 0, SOUTH, 20)
        DUCK_1839(2618, 3422, 0, SOUTH, 20)
        DUCK_1839(2619, 3424, 0, SOUTH, 20)
        DUCKLINGS(2613, 3423, 0, SOUTH, 2)
        DUCKLINGS(2616, 3424, 0, SOUTH, 2)
        FISHING_SPOT_4316(2599, 3419, 0, SOUTH, 0) //monkfish
        FISHING_SPOT_4316(2603, 3419, 0, SOUTH, 0) //monkfish
        FISHING_SPOT_1511(2605, 3420, 0, SOUTH, 0) //shark/other
        FISHING_SPOT_1511(2605, 3424, 0, SOUTH, 0) //shark/other
        FISHING_SPOT_1511(2605, 3425, 0, SOUTH, 0) //shark/other
        ROD_FISHING_SPOT_6825(2601, 3422, 0, SOUTH, 0) //anglerfish
        ROD_FISHING_SPOT_6825(2602, 3426, 0, SOUTH, 0) //anglerfish
        GOBLIN_5196(2568, 3403, 0, SOUTH, 9)
        GOBLIN_5203(2586, 3425, 0, SOUTH, 8)
        GOBLIN_5207(2568, 3433, 0, SOUTH, 11)
        GOBLIN_5207(2584, 3428, 0, SOUTH, 11)
        GOBLIN_5208(2572, 3420, 0, SOUTH, 12)
        MAN_3106(2603, 3421, 0, SOUTH, 5)
        7735(2599, 3425, 0, SOUTH, 5)
        //South
        BANKER_1613(2584, 3418, 0, EAST, 0)
        DUCK(2617, 3407, 0, SOUTH, 18)
        DUCK(2619, 3409, 0, SOUTH, 18)
        DUCK(2620, 3405, 0, SOUTH, 18)
        DUCK(2620, 3407, 0, SOUTH, 18)
        DUCK_1839(2617, 3405, 0, SOUTH, 20)
        DUCK_1839(2617, 3410, 0, SOUTH, 20)
        DUCK_1839(2618, 3406, 0, SOUTH, 20)
        DUCK_1839(2620, 3403, 0, SOUTH, 20)
        DUCK_1839(2621, 3405, 0, SOUTH, 20)
        DUCK_1839(2621, 3410, 0, SOUTH, 20)
        FISHING_SPOT_1518(2607, 3410, 0, SOUTH, 0) //shrimp/sardine/herring
        FISHING_SPOT_1518(2608, 3416, 0, SOUTH, 0) //shrimp/sardine/herring
        FISHING_SPOT_1510(2604, 3417, 0, SOUTH, 0) //lobster/tuna/swordies
        FISHING_SPOT_1510(2606, 3416, 0, SOUTH, 0) //lobster/tuna/swordies
        FISHING_SPOT_1510(2606, 3413, 0, SOUTH, 0) //lobster/tuna/swordies
        FISHING_SPOT_4710(2612, 3415, 0, SOUTH, 0) //karambwanji
        FISHING_SPOT_4712(2602, 3412, 0, SOUTH, 0) //karambwans
        FISHING_SPOT_4712(2602, 3415, 0, SOUTH, 0) //karambwans
        ROD_FISHING_SPOT_1526(2612, 3411, 0, SOUTH, 0) //salmon/trout
        ROD_FISHING_SPOT_1526(2612, 3414, 0, SOUTH, 0) //salmon/trout
        GOBLIN_5195(2578, 3400, 0, SOUTH, 9)
        GOBLIN_5197(2563, 3398, 0, SOUTH, 12)
        GOBLIN_5200(2563, 3416, 0, SOUTH, 8)
        GOBLIN_5201(2574, 3409, 0, SOUTH, 11)
        GOBLIN_5202(2579, 3413, 0, SOUTH, 8)
        GOBLIN_5202(2620, 3394, 0, SOUTH, 8)
        GOBLIN_5203(2621, 3393, 0, SOUTH, 8)
        GOBLIN_5204(2584, 3412, 0, SOUTH, 10)
        GOBLIN_5204(2623, 3396, 0, SOUTH, 10)
        GOBLIN_5205(2569, 3410, 0, SOUTH, 8)
        GOBLIN_5205(2623, 3397, 0, SOUTH, 8)
        GOBLIN_5206(2581, 3395, 0, SOUTH, 6)
        GOBLIN_5207(2572, 3396, 0, SOUTH, 11)
        GOBLIN_5208(2564, 3407, 0, SOUTH, 12)
        MASTER_FISHER(2611, 3393, 0, SOUTH, 3)
        RAT_2854(2592, 3416, 0, SOUTH, 14)
        ROACHEY(2596, 3400, 0, SOUTH, 2)
    }
}