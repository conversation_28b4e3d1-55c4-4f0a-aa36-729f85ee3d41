package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10294Spawns : NPCSpawnsScript() {
    init {
        GUARD_5187(2560, 3456, 0, SOUTH, 0)
        GOBLIN_5192(2564, 3456, 0, SOUTH, 2)
        CAPTAIN_LAWGOF(2567, 3460, 0, SOUTH, 2)
        GIANT_BAT(2571, 3481, 0, SOUTH, 11)
        GUARD_5186(2572, 3456, 0, SOUTH, 0)
        GIANT_BAT(2572, 3472, 0, SOUTH, 11)
        STANKERS(2575, 3486, 0, SOUTH, 2)
        GIANT_BAT(2578, 3476, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        GIANT_BAT(2586, 3473, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        GIANT_BAT(2591, 3479, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        GIANT_BAT(2592, 3484, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        GIANT_BAT(2594, 3474, 0, SOUTH, 11)
        RAT_2854(2610, 3479, 0, SOUTH, 14)
        RAT_2854(2611, 3475, 0, SOUTH, 14)
        GALAHAD(2612, 3474, 0, WEST, 0)
        RAT_2854(2614, 3478, 0, SOUTH, 14)
    }
}