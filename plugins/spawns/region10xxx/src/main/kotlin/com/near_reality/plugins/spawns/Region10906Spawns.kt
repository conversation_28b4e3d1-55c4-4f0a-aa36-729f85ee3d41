package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10906Spawns : NPCSpawnsScript() {
    init {
        ELEMENTAL_ROCK(2690, 9880, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2690, 9903, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2691, 9899, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2692, 9874, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2692, 9876, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2692, 9885, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2692, 9895, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        ELEMENTAL_ROCK(2692, 9900, 0, <PERSON>O<PERSON><PERSON>, 5)
        ELEMENTAL_ROCK(2692, 9902, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        ELEMENTAL_ROCK(2692, 9914, 0, <PERSON>O<PERSON>H, 5)
        EARTH_ELEMENTAL(2693, 9879, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2693, 9881, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2693, 9891, 0, SOUTH, 5)
        <PERSON>LEMENTAL_ROCK(2693, 9900, 0, SOUTH, 5)
        EARTH_ELEMENTAL(2693, 9904, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2693, 9906, 0, SOUTH, 5)
        EARTH_ELEMENTAL(2694, 9872, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2694, 9878, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2694, 9908, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2694, 9915, 0, SOUTH, 5)
        EARTH_ELEMENTAL(2695, 9867, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2695, 9869, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2695, 9870, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2695, 9877, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2695, 9881, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2695, 9889, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2696, 9880, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2696, 9900, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2696, 9906, 0, SOUTH, 5)
        EARTH_ELEMENTAL(2696, 9908, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2696, 9910, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2697, 9866, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2697, 9915, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2698, 9871, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2698, 9900, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2698, 9901, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2699, 9865, 0, SOUTH, 5)
        EARTH_ELEMENTAL(2699, 9868, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2699, 9872, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2699, 9876, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2699, 9882, 0, SOUTH, 5)
        EARTH_ELEMENTAL(2699, 9911, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2699, 9915, 0, SOUTH, 5)
        EARTH_ELEMENTAL(2700, 9875, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2700, 9879, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2701, 9867, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2701, 9870, 0, SOUTH, 5)
        EARTH_ELEMENTAL(2701, 9901, 0, SOUTH, 5)
        EARTH_ELEMENTAL(2701, 9905, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2702, 9868, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2703, 9894, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2703, 9911, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2704, 9906, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2705, 9897, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2706, 9904, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2706, 9910, 0, SOUTH, 5)
        ELEMENTAL_ROCK(2707, 9906, 0, SOUTH, 5)
        FIRE_ELEMENTAL(2711, 9877, 0, SOUTH, 3)
        WATER_ELEMENTAL(2713, 9904, 0, SOUTH, 5)
        FIRE_ELEMENTAL(2714, 9875, 0, SOUTH, 3)
        WATER_ELEMENTAL(2715, 9906, 0, SOUTH, 5)
        FIRE_ELEMENTAL(2716, 9878, 0, SOUTH, 3)
        FIRE_ELEMENTAL(2717, 9874, 0, SOUTH, 3)
        WATER_ELEMENTAL(2718, 9902, 0, SOUTH, 5)
        WATER_ELEMENTAL(2718, 9904, 0, SOUTH, 5)
        FIRE_ELEMENTAL(2719, 9878, 0, SOUTH, 3)
        FIRE_ELEMENTAL(2720, 9875, 0, SOUTH, 3)
        WATER_ELEMENTAL(2722, 9903, 0, SOUTH, 5)
        FIRE_ELEMENTAL(2723, 9877, 0, SOUTH, 3)
        FIRE_ELEMENTAL(2724, 9879, 0, SOUTH, 3)
        WATER_ELEMENTAL(2726, 9904, 0, SOUTH, 5)
        WATER_ELEMENTAL(2729, 9904, 0, SOUTH, 5)
        AIR_ELEMENTAL(2733, 9887, 0, SOUTH, 5)
        AIR_ELEMENTAL(2733, 9894, 0, SOUTH, 5)
        AIR_ELEMENTAL(2734, 9891, 0, SOUTH, 5)
        AIR_ELEMENTAL(2735, 9893, 0, SOUTH, 5)
        AIR_ELEMENTAL(2736, 9891, 0, SOUTH, 5)
        AIR_ELEMENTAL(2738, 9889, 0, SOUTH, 5)
        AIR_ELEMENTAL(2738, 9894, 0, SOUTH, 5)
    }
}