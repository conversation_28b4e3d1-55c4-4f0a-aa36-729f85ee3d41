package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10646Spawns : NPCSpawnsScript() {
    init {
        GAMER_1014(2647, 9635, 0, SOUTH, 5)
        RAT_4613(2649, 9638, 0, SOUTH, 5)
        RAT_4614(2649, 9641, 0, SOUTH, 5)
        RAT_4615(2650, 9635, 0, SOUTH, 5)
        RAT_4615(2650, 9642, 0, SOUTH, 5)
        RAT_4613(2651, 9641, 0, SOUTH, 5)
        RAT_4615(2652, 9637, 0, SOUTH, 5)
        GAMER_1016(2653, 9632, 0, SOUTH, 5)
        RAT_4613(2653, 9636, 0, <PERSON>OUT<PERSON>, 5)
        RAT_4614(2653, 9638, 0, <PERSON>O<PERSON>H, 5)
        RAT_4614(2654, 9636, 0, <PERSON><PERSON>UTH, 5)
        RAT_4613(2654, 9642, 0, SOUTH, 5)
        RAT_4613(2655, 9637, 0, SOUTH, 5)
        RAT_4615(2655, 9639, 0, SOUTH, 5)
        GAMER_1019(2656, 9643, 0, SOUTH, 5)
        GAMER(2660, 9622, 0, SOUTH, 5)
        RAT_4614(2661, 9625, 0, SOUTH, 5)
        RAT_4615(2662, 9623, 0, SOUTH, 5)
        TOPSY(2663, 9623, 0, SOUTH, 5)
        RAT_4615(2663, 9625, 0, SOUTH, 5)
        CLAUDE(2664, 9626, 0, SOUTH, 5)
        GAMER_1015(2664, 9628, 0, SOUTH, 5)
        RAT_4613(2665, 9625, 0, SOUTH, 5)
    }
}