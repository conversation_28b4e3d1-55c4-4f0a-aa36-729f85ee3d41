package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10387Spawns : NPCSpawnsScript() {
    init {
        SPIDER_3019(2565, 9443, 0, SOUTH, 8)
        OGRE_CHIEFTAIN(2568, 9432, 0, SOUTH, 2)
        BLUE_DRAGON_269(2568, 9437, 0, SOUTH, 3)
        OGRE_CHIEFTAIN(2573, 9447, 0, SOUTH, 2)
        SKELETON(2576, 9436, 0, SOUTH, 7)
        SPIDER_3019(2576, 9454, 0, SOUT<PERSON>, 8)
        6210(2577, 9451, 0, SOUTH, 5)
        OGRE_CHIEFTAIN(2579, 9441, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        BLUE_DRAGON_268(2579, 9445, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        SKELETON(2579, 9461, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        OGRE_CHIEFTAIN(2580, 9431, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        6209(2582, 9437, 0, <PERSON>O<PERSON>H, 5)
        SPIDER_3019(2586, 9423, 0, SOUTH, 8)
        GREATER_DEMON(2586, 9458, 0, SOUTH, 2)
        SPIDER_3019(2588, 9431, 0, SOUTH, 8)
        SKELET<PERSON>(2590, 9437, 0, SOUTH, 7)
        BLUE_DRAGON(2590, 9461, 0, SOUTH, 3)
        BLUE_DRAGON(2592, 9431, 0, SOUTH, 3)
        6208(2592, 9436, 0, SOUTH, 5)
        6211(2599, 9461, 0, SOUTH, 2)
        SPIDER_3019(2600, 9432, 0, SOUTH, 8)
        SPIDER_3019(2604, 9441, 0, SOUTH, 8)
        BLUE_DRAGON_266(2604, 9443, 0, SOUTH, 4)
        6213(2606, 9438, 0, SOUTH, 5)
        6212(2607, 9451, 0, SOUTH, 5)
        SPIDER_3019(2607, 9460, 0, SOUTH, 8)
        GIANT_BAT(2608, 9421, 0, SOUTH, 11)
        SPIDER_3019(2608, 9436, 0, SOUTH, 8)
        BLUE_DRAGON_267(2609, 9459, 0, SOUTH, 3)
        SPIDER_3019(2610, 9446, 0, SOUTH, 8)
        OGRE_CHIEFTAIN(2611, 9454, 0, SOUTH, 2)
        GIANT_BAT(2612, 9416, 0, SOUTH, 11)
        GREATER_DEMON_2028(2612, 9423, 0, SOUTH, 4)
        GREATER_DEMON_2027(2615, 9423, 0, SOUTH, 2)
        GREATER_DEMON(2615, 9426, 0, SOUTH, 2)
        OGRE_CHIEFTAIN(2616, 9447, 0, SOUTH, 2)
        SPIDER_3019(2617, 9451, 0, SOUTH, 8)
        GREATER_DEMON_2026(2618, 9424, 0, SOUTH, 4)
        GIANT_BAT(2619, 9427, 0, SOUTH, 11)
        GIANT_BAT(2620, 9419, 0, SOUTH, 11)
    }
}