package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10905Spawns : NPCSpawnsScript() {
    init {
        ICE_SPIDER(2690, 9807, 0, SOUTH, 6)
        ICE_SPIDER(2693, 9816, 0, SOUTH, 6)
        ICE_SPIDER(2695, 9826, 0, SOUTH, 6)
        ICE_SPIDER(2696, 9839, 0, SOUTH, 6)
        ICE_SPIDER(2708, 9842, 0, SOUTH, 6)
        ICE_SPIDER(2719, 9845, 0, SOUTH, 6)
        ICE_SPIDER(2731, 9845, 0, SOUTH, 6)
        ICE_SPIDER(2744, 9840, 0, SOUTH, 6)
        ICE_SPIDER(2745, 9829, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
    }
}