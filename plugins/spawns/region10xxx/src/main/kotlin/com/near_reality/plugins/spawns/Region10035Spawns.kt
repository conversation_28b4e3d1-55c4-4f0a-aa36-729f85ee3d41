package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10035Spawns : NPCSpawnsScript() {
    init {
        ZOMBIE_52(2497, 3292, 0, SOUTH, 5)
        ZOMBIE_32(2499, 3286, 0, SOUTH, 2)
        ZOMBIE_34(2500, 3283, 0, SOUTH, 2)
        4267(2500, 3320, 0, SOUTH, 5)
        GHOST(2501, 3289, 0, SOUTH, 5)
        GHOST_89(2501, 3294, 0, SOUTH, 4)
        9223(2501, 3315, 0, SOUTH, 5)
        4262(2502, 3304, 0, SOUTH, 0)
        CHILD(2504, 3318, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        WOMAN_1130(2504, 3326, 0, <PERSON>O<PERSON><PERSON>, 4)
        ZOMBIE_37(2506, 3284, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        WOMAN_1130(2509, 3314, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        MAN_1118(2509, 3325, 0, SOUTH, 2)
        WOMAN_1131(2510, 3318, 0, SOUTH, 4)
        ZOM<PERSON>E_54(2511, 3287, 0, SOUTH, 5)
        WOMAN(2511, 3322, 0, SOUTH, 3)
        5287(2513, 3294, 0, SOUTH, 28)
        WOM<PERSON>_1142(2513, 3315, 0, SOUTH, 4)
        9223(2513, 3325, 0, SOUTH, 5)
        NURSE_SARAH(2516, 3274, 0, SOUTH, 2)
        CHILD_1133(2518, 3275, 0, SOUTH, 3)
        9226(2518, 3309, 0, SOUTH, 5)
        5287(2518, 3320, 0, SOUTH, 28)
        WOMAN_1140(2519, 3277, 0, SOUTH, 2)
        CLERK_8968(2520, 3314, 0, SOUTH, 5)
        4267(2521, 3283, 0, SOUTH, 5)
        CHILD_1133(2523, 3307, 0, SOUTH, 3)
        WOMAN_1131(2524, 3271, 0, SOUTH, 4)
        9222(2524, 3292, 0, SOUTH, 5)
        4262(2525, 3308, 0, SOUTH, 0)
        9223(2526, 3279, 0, SOUTH, 5)
        9226(2526, 3303, 0, SOUTH, 5)
        4267(2527, 3326, 0, SOUTH, 5)
        9223(2528, 3297, 0, SOUTH, 5)
        CLERK(2528, 3317, 0, SOUTH, 5)
        PRIEST(2529, 3285, 0, SOUTH, 5)
        5287(2530, 3274, 0, SOUTH, 28)
        4262(2531, 3327, 0, SOUTH, 0)
        4262(2532, 3274, 0, SOUTH, 0)
        7382(2534, 3273, 0, SOUTH, 5)
        9223(2535, 3288, 0, SOUTH, 5)
        5287(2535, 3296, 0, SOUTH, 28)
        9222(2536, 3294, 0, SOUTH, 5)
        MAN_4271(2536, 3308, 0, SOUTH, 5)
        BRAVEK(2536, 3314, 0, SOUTH, 5)
        WOMAN_1141(2537, 3324, 0, SOUTH, 2)
        4267(2538, 3304, 0, SOUTH, 5)
        9222(2538, 3321, 0, SOUTH, 5)
        7382(2539, 3273, 0, SOUTH, 5)
        WOMAN(2540, 3279, 0, SOUTH, 3)
        5312(2540, 3305, 0, SOUTH, 0)
        MAN_4270(2540, 3308, 0, SOUTH, 0)
        5286(2542, 3286, 0, SOUTH, 34)
        5311(2542, 3306, 0, SOUTH, 8)
        9226(2543, 3309, 0, SOUTH, 5)
        4267(2544, 3324, 0, SOUTH, 5)
        9231(2545, 3326, 0, SOUTH, 5)
        WOMAN_1130(2546, 3277, 0, SOUTH, 4)
        CHILD(2547, 3277, 0, SOUTH, 2)
        9223(2548, 3287, 0, SOUTH, 5)
        4262(2548, 3298, 0, SOUTH, 0)
        9229(2548, 3324, 0, SOUTH, 5)
        4267(2549, 3282, 0, SOUTH, 5)
        4267(2549, 3315, 0, SOUTH, 5)
        4262(2549, 3323, 0, SOUTH, 0)
        9229(2550, 3326, 0, SOUTH, 5)
        9231(2551, 3322, 0, SOUTH, 5)
        9227(2552, 3320, 0, SOUTH, 5)
        4267(2552, 3326, 0, SOUTH, 5)
        WOMAN_1139(2553, 3275, 0, SOUTH, 4)
        9229(2553, 3325, 0, SOUTH, 5)
        9231(2555, 3324, 0, SOUTH, 5)
        9224(2556, 3266, 0, SOUTH, 5)
        9225(2559, 3266, 0, SOUTH, 5)
        9230(2551, 3327, 1, SOUTH, 5)
        9232(2559, 3304, 0, SOUTH, 5)
        7734(2540, 3306, 0, SOUTH, 5)
    }
}