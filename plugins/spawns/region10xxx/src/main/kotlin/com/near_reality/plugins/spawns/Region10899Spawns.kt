package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10899Spawns : NPCSpawnsScript() {
    init {
        STEEL_DRAGON_274(2702, 9447, 0, SOUT<PERSON>, 6)
        IRON_DRAGON(2704, 9431, 0, SOUT<PERSON>, 7)
        IRON_DRAGON(2704, 9457, 0, SOUTH, 7)
        IRON_DRAGON(2705, 9424, 0, SOUTH, 7)
        STEEL_DRAGON_274(2712, 9435, 0, SOUTH, 6)
        IRON_DRAGON(2714, 9420, 0, SOUTH, 7)
        IRON_DRAGON(2714, 9449, 0, <PERSON>O<PERSON><PERSON>, 7)
        IRON_DRAGON(2714, 9460, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        IRON_DRAGON(2722, 9424, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        STEEL_DRAGON_274(2723, 9458, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        IRON_DRAGON(2724, 9435, 0, <PERSON><PERSON><PERSON>H, 7)
        STEEL_DRAG<PERSON>_274(2726, 9445, 0, SOUTH, 6)
        IRON_DRAGON(2730, 9437, 0, <PERSON>OUTH, 7)
        <PERSON>RON_DRAGON(2732, 9459, 0, <PERSON>OUTH, 7)
        <PERSON>RON_DRAGON(2736, 9424, 0, <PERSON>OUTH, 7)
        <PERSON>RON_DRAGON(2738, 9440, 0, <PERSON>OUTH, 7)
        <PERSON>RON_DRAGON(2739, 9450, 0, SOUTH, 7)
    }
}