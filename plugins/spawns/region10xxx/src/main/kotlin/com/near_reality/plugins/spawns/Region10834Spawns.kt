package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10834Spawns : NPCSpawnsScript() {
    init {
        URMEG(2691, 5303, 0, SOUTH, 3)
        CAVE_GOBLIN_2277(2698, 5264, 0, SOUTH, 2)
        CAVE_GOBLIN_2272(2707, 5286, 0, SOUTH, 5)
        BARTAK(2708, 5275, 0, SOUTH, 3)
        MOTHS(2712, 5297, 0, SOUTH, 3)
        TURGOK(2712, 5303, 0, SOUTH, 4)
        TINDAR(2712, 5309, 0, SOUTH, 0)
        GOBLIN_FISH(2713, 5262, 0, <PERSON>OUT<PERSON>, 0)
        CAVE_GOBLIN_2284(2713, 5293, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GOBLIN_FISH(2714, 5287, 0, <PERSON>OUTH, 0)
        MOTHS(2715, 5259, 0, SOUTH, 3)
        GOURMET_2307(2716, 5310, 0, SOUTH, 5)
        CAVE_GOBL<PERSON>(2717, 5294, 0, SOUTH, 4)
        GOURMET(2720, 5309, 0, SOUTH, 5)
        CAVE_GOBLIN_2282(2721, 5294, 0, SOUTH, 3)
        GUARD_2317(2722, 5260, 0, SOUTH, 4)
        MOTHS(2722, 5297, 0, SOUTH, 3)
        MARKOG(2723, 5303, 0, SOUTH, 6)
        ZENKOG(2723, 5307, 0, SOUTH, 0)
        SPIT_GOBLIN(2729, 5269, 0, SOUTH, 0)
        MOTHS(2730, 5259, 0, SOUTH, 3)
        MOTHS(2732, 5270, 0, SOUTH, 3)
        CRATE_GOBLIN(2733, 5274, 0, SOUTH, 27)
        CAVE_GOBLIN_2281(2734, 5275, 0, SOUTH, 2)
        MOTHS(2735, 5270, 0, SOUTH, 3)
        URLUN(2747, 5263, 0, SOUTH, 0)
        URPEL(2748, 5263, 0, SOUTH, 0)
        CAVE_GOBLIN_2273(2732, 5275, 1, SOUTH, 2)
        CAVE_GOBLIN_2280(2735, 5275, 1, SOUTH, 3)
        CAVE_GOBLIN_2275(2740, 5285, 2, SOUTH, 5)
        CAVE_GOBLIN_2271(2743, 5284, 2, SOUTH, 6)
    }
}