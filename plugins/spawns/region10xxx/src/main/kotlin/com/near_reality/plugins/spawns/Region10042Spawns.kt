package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10042Spawns : NPCSpawnsScript() {
    init {
        ROCKS_103(2501, 3755, 0, SOUTH, 0)
        ROCKS(2502, 3754, 0, SOUTH, 0)
        HOBGOBLIN_3049(2503, 3730, 0, SOUTH, 13)
        ROCKS_103(2512, 3765, 0, SOUTH, 0)
        ROCKS_103(2512, 3766, 0, SOUTH, 0)
        ROCKS(2514, 3766, 0, SOUTH, 0)
        ROCKS_103(2521, 3756, 0, SOUTH, 0)
        ROCKS(2522, 3716, 0, SOUT<PERSON>, 0)
        ROCKS(2523, 3723, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        ROCKS_103(2524, 3724, 0, <PERSON>OUTH, 0)
        HOBGOBLIN_3049(2526, 3739, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        ROCKS_103(2529, 3740, 0, SOUT<PERSON>, 0)
        ROCKS_103(2530, 3762, 0, SOUTH, 0)
        ROCKS_103(2531, 3721, 0, SOUTH, 0)
        ROCKS_103(2532, 3760, 0, SOUTH, 0)
        ROCKS(2534, 3719, 0, SOUTH, 0)
        ROCKS(2537, 3765, 0, SOUTH, 0)
        2260(2539, 3739, 0, SOUTH, 2)
        ROCKS_103(2541, 3757, 0, SOUTH, 0)
        ROCKS_103(2543, 3754, 0, SOUTH, 0)
        JARVALD_10407(2544, 3761, 0, SOUTH, 5)
        ROCKS_103(2548, 3734, 0, SOUTH, 0)
        ROCKS(2553, 3746, 0, SOUTH, 0)
    }
}