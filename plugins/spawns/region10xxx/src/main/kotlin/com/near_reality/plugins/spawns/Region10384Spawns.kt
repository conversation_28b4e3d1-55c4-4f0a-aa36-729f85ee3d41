package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10384Spawns : NPCSpawnsScript() {
    init {
        MANIACAL_MONKEY_7118(2606, 9273, 1, SOUT<PERSON>, 47)
        MANIACAL_MONKEY_7118(2607, 9271, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2609, 9270, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2612, 9274, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2614, 9272, 1, SOUTH, 47)
        MANIACAL_MONKEY_7118(2615, 9274, 1, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANIACAL_MONKEY_7118(2617, 9273, 1, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANIACAL_MONKEY_7118(2618, 9220, 1, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANIACAL_MONKEY_7118(2619, 9218, 1, <PERSON><PERSON><PERSON><PERSON>, 47)
        MANIACAL_<PERSON><PERSON><PERSON>Y_ARCHER(2619, 9272, 1, SOUTH, 0)
        MANIACAL_MONKEY_ARCHER(2620, 9224, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2620, 9227, 1, SOUTH, 47)
        MANI<PERSON>AL_MONKEY_ARCHER(2620, 9229, 1, SOUTH, 0)
        MANIACAL_MONKEY_7118(2621, 9271, 1, SOUTH, 47)
        MANIACAL_MONKEY_ARCHER(2622, 9227, 1, SOUTH, 0)
    }
}