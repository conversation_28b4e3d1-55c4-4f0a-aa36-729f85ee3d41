package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10300Spawns : NPCSpawnsScript() {
    init {
        RABBIT_3664(2560, 3869, 0, SOUTH, 5)
        FISHING_SPOT_3657(2572, 3860, 0, SOUTH, 0)
        FISHING_SPOT_3657(2577, 3854, 0, SOUTH, 0)
        GULL_3907(2580, 3892, 0, SOUTH, 9)
        GULL_3907(2581, 3888, 0, SOUTH, 9)
        FISHING_SPOT_3657(2582, 3851, 0, SOUTH, 0)
        FARMER_FROMUND(2582, 3864, 0, SOUTH, 0)
        GULL_3907(2585, 3896, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        RABBIT_3664(2588, 3874, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        RHAZIEN(2591, 3865, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        RABBIT_3665(2592, 3867, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        <PERSON>OLDOF(2594, 3886, 0, SOUTH, 5)
        CARPENTER_KJALLAK(2594, 3891, 0, SOUTH, 0)
        TOOL_LEPRECHAUN(2596, 3860, 0, SOUTH, 0)
        CHICKEN_3662(2601, 3874, 0, SOUTH, 5)
        CHICKEN_3662(2601, 3878, 0, SOUTH, 5)
        GREENGROCER(2602, 3876, 0, SOUTH, 2)
        CHICKEN_3661(2602, 3877, 0, SOUTH, 4)
        GULL_3907(2602, 3901, 0, SOUTH, 9)
        ASHILD(2603, 3858, 0, SOUTH, 5)
        MATILDA(2603, 3871, 0, SOUTH, 5)
        SKRAELING_774(2603, 3877, 0, SOUTH, 4)
        HAMING(2604, 3881, 0, SOUTH, 2)
        FISHMONGER(2605, 3876, 0, SOUTH, 2)
        SKRAELING(2605, 3877, 0, SOUTH, 5)
        CHICKEN_3662(2606, 3867, 0, SOUTH, 5)
        ROOSTER_3663(2606, 3877, 0, SOUTH, 5)
        CHICKEN_3661(2612, 3897, 0, SOUTH, 4)
        CHICKEN_3661(2612, 3900, 0, SOUTH, 4)
        HELGA(2614, 3883, 0, SOUTH, 3)
        ARNOR(2615, 3899, 0, SOUTH, 3)
        YULF_SQUECKS(2616, 3859, 0, SOUTH, 4)
        CHICKEN_3661(2617, 3897, 0, SOUTH, 4)
        GUARD_1100(2619, 3865, 0, SOUTH, 0)
        BANKER(2620, 3895, 0, WEST, 0)
        FISHERMAN_FRODI(2576, 3852, 0, SOUTH, 0)
        SAILOR(2581, 3847, 0, SOUTH, 7)
        QUEEN_SIGRID(2612, 3877, 1, SOUTH, 2)
    }
}