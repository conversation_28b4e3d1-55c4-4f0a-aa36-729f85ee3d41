package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10037Spawns : NPCSpawnsScript() {
    init {
        ROD_FISHING_SPOT_1508(2508, 3421, 0, SOUTH, 0)
        HADLEY(2516, 3428, 0, SOUTH, 3)
        ROD_FISHING_SPOT_1508(2527, 3412, 0, SOUTH, 0)
        GERALD(2528, 3414, 0, SOUTH, 2)
        ROD_FISHING_SPOT_1508(2530, 3412, 0, SOUTH, 0)
        ROD_FISHING_SPOT_1508(2533, 3410, 0, SOUTH, 0)
        ROD_FISHING_SPOT_1508(2534, 3403, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        <PERSON><PERSON><PERSON><PERSON>(2535, 3432, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        ROD_FISHING_SPOT_1508(2537, 3406, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        MOSS_GIANT(2549, 3408, 0, <PERSON>OUT<PERSON>, 4)
        MOSS_GIANT_2092(2554, 3401, 0, SOUTH, 3)
        MOSS_GIANT_2091(2554, 3409, 0, SOUTH, 4)
        6104(2555, 3444, 0, SOUTH, 2)
        MOSS_GIANT_2093(2556, 3406, 0, SOUTH, 4)
        BUTTERFLY(2556, 3444, 0, SOUTH, 7)
    }
}