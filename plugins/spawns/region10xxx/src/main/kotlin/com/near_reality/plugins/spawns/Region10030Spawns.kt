package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10030Spawns : NPCSpawnsScript() {
    init {
        WOLF(2496, 2959, 0, SOUTH, 6)
        WOLF(2498, 2968, 0, SOUTH, 6)
        WOLF(2500, 2959, 0, SOUTH, 6)
        WOLF(2502, 2969, 0, SOUTH, 6)
        OGRE_2095(2504, 2965, 0, SOUTH, 2)
        OGRE_2095(2504, 2984, 0, SOUTH, 2)
        WOLF(2509, 2964, 0, SOUTH, 6)
        GIANT_SPIDER_3018(2517, 2976, 0, SO<PERSON><PERSON>, 10)
        OGRE_2095(2520, 2973, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        SWAMP_TOAD(2523, 2977, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SWAMP_TOAD(2524, 2981, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SWAMP_TOAD(2525, 2975, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        OGRE_2095(2525, 2986, 0, SOUTH, 2)
        RAT_2854(2527, 2986, 0, SOUTH, 14)
        SWAMP_TOAD(2528, 2983, 0, SOUTH, 3)
        SWAMP_TOAD(2530, 2978, 0, SOUTH, 3)
        RAT_2854(2531, 2976, 0, SOUTH, 14)
        OGRE_2095(2533, 2977, 0, SOUTH, 2)
        OGRE_2095(2536, 2975, 0, SOUTH, 2)
        OGRE_2095(2539, 2990, 0, SOUTH, 2)
        SWAMP_TOAD(2543, 2984, 0, SOUTH, 3)
        GNORMADIUM_AVLAFRIM_7517(2544, 2973, 0, SOUTH, 2)
        SWAMP_TOAD(2545, 2988, 0, SOUTH, 3)
        SWAMP_TOAD(2547, 2982, 0, SOUTH, 3)
        RAT_2854(2547, 2990, 0, SOUTH, 14)
        OGRE_2095(2549, 2979, 0, SOUTH, 2)
        GIANT_SPIDER_3018(2550, 2981, 0, SOUTH, 10)
        OGRE_2095(2551, 2955, 0, SOUTH, 2)
        SWAMP_TOAD(2551, 2985, 0, SOUTH, 3)
        OGRE_2095(2552, 2961, 0, SOUTH, 2)
    }
}