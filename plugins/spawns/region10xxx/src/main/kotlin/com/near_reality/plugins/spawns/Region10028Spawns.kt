package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10028Spawns : NPCSpawnsScript() {
    init {
        SNAKE_2845(2507, 2869, 0, SOUTH, 4)
        FISHING_SPOT_7947(2509, 2838, 0, SOUTH, 0)
        SNAKE_2845(2512, 2860, 0, SOUTH, 4)
        FISHING_SPOT_7947(2515, 2838, 0, SOUTH, 0)
        SNAKE_2845(2524, 2855, 0, SOUTH, 4)
        SNAKE_2845(2527, 2869, 0, SOUTH, 4)
        7974(2548, 2863, 0, SOUTH, 2)
        SEAGULL(2549, 2860, 0, SOUTH, 5)
        MADAME_CALDARIUM(2553, 2868, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        SEAGULL(2557, 2864, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7978(2559, 2856, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        7962(2529, 2839, 1, <PERSON><PERSON><PERSON>H, 0)
        7973(2544, 2862, 1, SOUTH, 5)
        7977(2553, 2858, 1, SOUTH, 5)
        7968(2558, 2858, 1, SOUTH, 5)
    }
}