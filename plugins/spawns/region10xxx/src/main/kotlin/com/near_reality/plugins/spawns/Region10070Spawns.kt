package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10070Spawns : NPCSpawnsScript() {
    init {
        BUILDER_5181(2511, 5532, 0, SOUTH, 5)
        BUILDER(2511, 5536, 0, SOUTH, 5)
        TEGDAK(2511, 5563, 0, SOUTH, 5)
        BUILDER_5179(2512, 5535, 0, SOUTH, 5)
        6284(2512, 5563, 0, <PERSON>OUTH, 5)
        BUILDER_5180(2513, 5533, 0, SOUTH, 5)
    }
}