package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10286Spawns : NPCSpawnsScript() {
    init {
        OGRE_2095(2568, 2991, 0, SOUTH, 2)
        OGRE_2095(2574, 2988, 0, SOUTH, 2)
        OGRE_2095(2584, 2967, 0, SOUTH, 2)
        OGRE_2095(2585, 2977, 0, SOUTH, 2)
        OGRE_2095(2592, 2965, 0, SOUTH, 2)
        SWAMP_TOAD(2595, 2964, 0, SOUTH, 3)
        SWAMP_TOAD(2595, 2968, 0, SOUTH, 3)
        SWAMP_TOAD(2596, 2962, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SWAMP_TOAD(2596, 2970, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SWAMP_TOAD(2599, 2962, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SWAMP_TOAD(2599, 2970, 0, <PERSON><PERSON>UT<PERSON>, 3)
        WOLF(2602, 2955, 0, SOUTH, 6)
        SWAMP_TOAD(2602, 2969, 0, SOUTH, 3)
        SWAMP_TOAD(2603, 2966, 0, SOUTH, 3)
        WOLF(2605, 2963, 0, SOUTH, 6)
        WOLF(2607, 2967, 0, SOUTH, 6)
        OGRE_2095(2608, 2980, 0, SOUTH, 2)
        OGRE_2095(2609, 2990, 0, SOUTH, 2)
        WOLF(2610, 2958, 0, SOUTH, 6)
        WOLF(2610, 2961, 0, SOUTH, 6)
        WOLF(2610, 2965, 0, SOUTH, 6)
        OGRE_2095(2614, 2998, 0, SOUTH, 2)
        OGRE_CHIEFTAIN(2615, 2987, 0, SOUTH, 2)
    }
}