package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region10794Spawns : NPCSpawnsScript() {
    init {
        FISHING_SPOT_1511(2694, 2706, 0, SOUTH, 0)
        BIRD_5241(2696, 2709, 0, <PERSON><PERSON>UT<PERSON>, 26)
        BIRD_5241(2713, 2697, 0, SOUTH, 26)
        SNAKE_5244(2716, 2719, 0, <PERSON>OUTH, 4)
        JUNGLE_SPIDER_5243(2716, 2722, 0, <PERSON>OUTH, 4)
        BIRD(2718, 2726, 0, <PERSON>OUT<PERSON>, 13)
        JUNGLE_SPIDER_5243(2721, 2729, 0, S<PERSON><PERSON><PERSON>, 4)
        SNAKE_5244(2726, 2712, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        SNAKE_5244(2731, 2724, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        JUNGLE_SPIDER_5243(2732, 2714, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        BIRD_5241(2737, 2695, 0, <PERSON><PERSON><PERSON><PERSON>, 26)
        JUNG<PERSON>_<PERSON><PERSON><PERSON>_5243(2738, 2722, 0, SOUTH, 4)
        SNAKE_5244(2740, 2721, 0, SOUTH, 4)
        BIRD(2741, 2712, 0, SOUTH, 13)
        JUNGLE_SPIDER_5243(2742, 2704, 0, <PERSON>OUTH, 4)
        <PERSON>NAKE_5244(2742, 2707, 0, <PERSON>OUTH, 4)
        JUNGLE_SPIDER_5243(2742, 2711, 0, SOUTH, 4)
        JUNGLE_SPIDER_5243(2749, 2710, 0, SOUTH, 4)
    }
}