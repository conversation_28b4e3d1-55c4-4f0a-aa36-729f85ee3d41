package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region4919Spawns : NPCSpawnsScript() {
    init {
        MOUNTAIN_TROLL_937(1233, 3521, 0, SOUTH, 6)
        CAPTAIN_RIMOR(1234, 3568, 0, SOUTH, 2)
        MOUNTAIN_TROLL_939(1236, 3520, 0, SOUTH, 9)
        7619(1256, 3548, 0, WEST, 0)
        FISHING_SPOT_7323(1266, 3541, 0, SOUTH, 0)
        FISHING_SPOT_7323(1272, 3546, 0, SOUTH, 0)
        7617(1276, 3559, 0, SOUTH, 2)
        8190(1276, 3561, 0, SOUTH, 2)
        8191(1277, 3562, 0, <PERSON>O<PERSON><PERSON>, 3)
        8192(1277, 3564, 0, <PERSON>OUTH, 2)
    }
}