package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region4922Spawns : NPCSpawnsScript() {
    init {
        RUBY_HARVEST(1219, 3761, 0, SOUTH, 6)
        BLACK_WARLOCK(1220, 3747, 0, SOUTH, 9)
        FISHING_SPOT_8526(1221, 3714, 0, SOUTH, 0)
        SNOWY_KNIGHT(1223, 3723, 0, SOUTH, 6)
        RABBIT_8591(1223, 3749, 0, SOUTH, 11)
        BLACK_WARLOCK(1224, 3764, 0, SOUTH, 9)
        DUCK_1839(1225, 3730, 0, SOUTH, 20)
        DUCKLING(1226, 3729, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        DUCK(1226, 3731, 0, <PERSON><PERSON><PERSON><PERSON>, 18)
        DUCK_2003(1227, 3733, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        BUTTERFLY_235(1229, 3722, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        FELFIZ_YARY<PERSON>(1229, 3724, 0, NORTH, 0)
        ARNO(1230, 3728, 0, WEST, 0)
        RABBIT_8592(1230, 3735, 0, SOUTH, 10)
        ROSIE(1232, 3733, 0, SOUTH, 3)
        BLACK_WARLOCK(1233, 3745, 0, SOUTH, 9)
        TOOL_LEPRECHAUN(1234, 3761, 0, SOUTH, 0)
        RABBIT_8593(1235, 3725, 0, SOUTH, 5)
        ALEXANDRA(1235, 3752, 0, SOUTH, 2)
        MASTER_FARMER(1236, 3727, 0, SOUTH, 5)
        TOOL_LEPRECHAUN(1236, 3732, 0, SOUTH, 0)
        RABBIT_8592(1236, 3759, 0, SOUTH, 10)
        RABBIT_8591(1237, 3733, 0, SOUTH, 11)
        SAPPHIRE_GLACIALIS(1237, 3746, 0, SOUTH, 4)
        RUBY_HARVEST(1237, 3747, 0, SOUTH, 6)
        SNOWY_KNIGHT(1238, 3740, 0, SOUTH, 6)
        BUTTERFLY(1240, 3720, 0, SOUTH, 7)
        RABBIT_8591(1240, 3753, 0, SOUTH, 11)
        NIKKIE(1241, 3756, 0, SOUTH, 0)
        BLACK_WARLOCK(1242, 3760, 0, SOUTH, 9)
        BUTTERFLY_235(1243, 3744, 0, SOUTH, 7)
        SOLDIER_8599(1245, 3717, 0, SOUTH, 4)
        RABBIT_8592(1245, 3757, 0, SOUTH, 10)
        RANGER_8601(1246, 3719, 0, SOUTH, 4)
        AMELIA_8530(1246, 3739, 0, SOUTH, 3)
        TAYLOR(1246, 3753, 0, SOUTH, 0)
        MASTER_FARMER(1247, 3750, 0, SOUTH, 5)
        8628(1248, 3727, 0, SOUTH, 1)
        BANKER_8589(1248, 3760, 0, SOUTH, 0)
        BANKER_8590(1249, 3760, 0, SOUTH, 0)
        SERGEANT_10725(1250, 3716, 0, SOUTH, 5)
        SOLDIER_8599(1251, 3719, 0, SOUTH, 4)
        ALLANNA(1251, 3734, 0, SOUTH, 3)
        BUTTERFLY(1252, 3744, 0, SOUTH, 7)
        RABBIT_8591(1252, 3748, 0, SOUTH, 11)
        RABBIT_8593(1252, 3754, 0, SOUTH, 5)
        TOOL_LEPRECHAUN(1252, 3756, 0, SOUTH, 0)
        LATLINK_FASTBELL(1254, 3752, 0, SOUTH, 2)
        BUTTERFLY_235(1256, 3737, 0, SOUTH, 7)
        CAT_8594(1256, 3758, 0, SOUTH, 0)
        BUTTERFLY_235(1257, 3721, 0, SOUTH, 7)
        BUTTERFLY_238(1257, 3739, 0, SOUTH, 0)
        SAPPHIRE_GLACIALIS(1258, 3746, 0, SOUTH, 4)
        BUTTERFLY_238(1260, 3742, 0, SOUTH, 0)
        BLACK_WARLOCK(1260, 3750, 0, SOUTH, 9)
        TOOL_LEPRECHAUN(1262, 3731, 0, SOUTH, 0)
        RABBIT_8592(1262, 3735, 0, SOUTH, 10)
        RUBY_HARVEST(1264, 3718, 0, SOUTH, 6)
        MASTER_FARMER_5731(1264, 3725, 0, SOUTH, 5)
        ALAN(1265, 3734, 0, SOUTH, 2)
        11122(1273, 3760, 0, SOUTH, 5)
        BUTTERFLY(1276, 3728, 0, SOUTH, 7)
    }
}