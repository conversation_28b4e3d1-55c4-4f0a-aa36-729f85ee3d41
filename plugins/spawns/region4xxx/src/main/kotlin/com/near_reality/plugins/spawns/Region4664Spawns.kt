package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region4664Spawns : NPCSpawnsScript() {
    init {
        KETSAL_KUK(1179, 3627, 0, SOUTH, 5)
        CRIMSON_SWIFT(1182, 3595, 0, SOUTH, 6)
        CRIMSON_SWIFT(1184, 3591, 0, SOUTH, 6)
        CRIMSON_SWIFT(1185, 3598, 0, SOUTH, 6)
        CRIMSON_SWIFT(1187, 3596, 0, <PERSON>OUTH, 6)
        CRIMSON_SWIFT(1188, 3593, 0, SOUTH, 6)
        ROCKS_103(1197, 3587, 0, SOUTH, 0)
        ROCKS_103(1199, 3584, 0, SOUTH, 0)
        ROCKS(1200, 3588, 0, SOUTH, 0)
        ROCKS_103(1204, 3590, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        ROCKS_103(1213, 3596, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
    }
}