package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region4763Spawns : NPCSpawnsScript() {
    init {
        LIZARD_7597(1167, 9930, 0, SOUTH, 5)
        GNOSI(1168, 9940, 0, SOUTH, 5)
        HISTORIAN_DUFFY(1171, 9935, 0, SOUTH, 5)
        NATURAL_HISTORIAN_8021(1174, 9939, 0, SOUTH, 5)
        LIZARD_7597(1177, 9941, 0, SOUTH, 5)
        LIZARD_7597(1201, 9973, 0, SOUTH, 5)
    }
}