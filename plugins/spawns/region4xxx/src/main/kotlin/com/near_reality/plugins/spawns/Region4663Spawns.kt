package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region4663Spawns : NPCSpawnsScript() {
    init {
        ROCKS_103(1199, 3562, 0, SOUTH, 0)
        ROCKS(1200, 3565, 0, SOUTH, 0)
        ROCKS_103(1200, 3579, 0, SOUTH, 0)
        ROCKS_103(1201, 3553, 0, SOUTH, 0)
        ROCKS(1201, 3559, 0, SOUTH, 0)
        ROCKS_103(1202, 3562, 0, SOUTH, 0)
        ROCKS_103(1202, 3577, 0, SOUTH, 0)
        ROCKS_103(1202, 3581, 0, SOUTH, 0)
        ROCKS_103(1203, 3546, 0, SOUTH, 0)
        ROCKS(1203, 3551, 0, <PERSON>OUTH, 0)
        ROCKS_103(1203, 3554, 0, <PERSON>OUTH, 0)
        ROCKS_103(1204, 3557, 0, SOUTH, 0)
        ROCKS(1204, 3563, 0, SOUTH, 0)
        ROCKS(1205, 3544, 0, SOUTH, 0)
        ROCKS(1207, 3538, 0, SOUTH, 0)
        ROCKS_103(1208, 3541, 0, SOUTH, 0)
        ROCKS(1209, 3533, 0, SOUTH, 0)
        ROCKS_103(1210, 3535, 0, SOUTH, 0)
        ROCKS(1210, 3543, 0, SOUTH, 0)
        ROCKS_103(1211, 3538, 0, SOUTH, 0)
        ROCKS_103(1212, 3532, 0, SOUTH, 0)
        ROCKS(1213, 3539, 0, SOUTH, 0)
        ROCKS_103(1214, 3535, 0, SOUTH, 0)
    }
}