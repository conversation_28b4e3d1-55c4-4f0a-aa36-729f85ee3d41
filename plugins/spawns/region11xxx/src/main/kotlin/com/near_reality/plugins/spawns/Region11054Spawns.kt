package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11054Spawns : NPCSpawnsScript() {
    init {
        JUNGLE_FORESTER_3955(2759, 2944, 0, SOUTH, 2)
        JUNGLE_FORESTER(2764, 2944, 0, SOUTH, 3)
        6237(2764, 2976, 0, SOUTH, 3)
        SCORPION_3024(2765, 2978, 0, SOUTH, 12)
        HORNED_GRAAHK(2767, 3005, 0, SOUTH, 6)
        SCORPION_3024(2768, 2980, 0, SOUTH, 12)
        HORNED_GRAAHK(2774, 3002, 0, <PERSON>OUT<PERSON>, 6)
        HORNED_GRAAHK(2781, 3001, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        SCORPION_3024(2785, 2955, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        SCORPION_3024(2786, 2947, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        MONKEY_2848(2787, 3002, 0, SOUTH, 10)
        SCOR<PERSON>ON_3024(2788, 2948, 0, SOUTH, 12)
        MONKEY_2848(2790, 2997, 0, SOUTH, 10)
        JUNGLE_FORESTER_3955(2792, 2944, 0, SOUTH, 2)
        <PERSON>ONKEY_2848(2792, 2994, 0, SOUTH, 10)
        MONKEY_2848(2794, 3000, 0, SOUTH, 10)
        MONKEY_2848(2796, 2993, 0, SOUTH, 10)
        JUNGLE_FORESTER(2798, 2944, 0, SOUTH, 3)
        MONKEY_2848(2799, 2996, 0, SOUTH, 10)
        JUNGLE_SPIDER(2810, 2981, 0, SOUTH, 4)
        JUNGLE_SPIDER(2810, 2987, 0, SOUTH, 4)
        JUNGLE_SPIDER(2812, 2999, 0, SOUTH, 4)
        JUNGLE_SPIDER(2814, 2996, 0, SOUTH, 4)
        CAPTAIN_SHANKS(2763, 2961, 1, SOUTH, 2)
    }
}