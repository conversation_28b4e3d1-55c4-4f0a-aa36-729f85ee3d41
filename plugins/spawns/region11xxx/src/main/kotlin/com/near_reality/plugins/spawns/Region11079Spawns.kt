package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11079Spawns : NPCSpawnsScript() {
    init {
        IRON_PICKAXE(2769, 4593, 0, SOUTH, 0)
        IRON_PICKAXE(2770, 4591, 0, SOUTH, 0)
        IRON_PICKAXE(2774, 4592, 0, SOUTH, 0)
        RAT_2854(2776, 4577, 0, SOUTH, 14)
        RAT_2854(2777, 4582, 0, SOUTH, 14)
        RAT_2854(2780, 4592, 0, SOUTH, 14)
        IRON_PICKAXE(2783, 4591, 0, SOUTH, 0)
        RAT_2854(2783, 4593, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        RAT_2854(2785, 4580, 0, <PERSON>OUT<PERSON>, 14)
        IRON_PICKAXE(2785, 4594, 0, SOUTH, 0)
        IRON_PICKAXE(2785, 4598, 0, SOUTH, 0)
        HAZE(2786, 4571, 0, SOUTH, 0)
        RAT_2854(2786, 4578, 0, SOUTH, 14)
        RAT_2854(2787, 4579, 0, SOUTH, 14)
        HAZE(2791, 4560, 0, SOUTH, 0)
        HAZE(2792, 4567, 0, SOUTH, 0)
        IRON_PICKAXE(2792, 4597, 0, SOUTH, 0)
        HAZE(2796, 4587, 0, SOUTH, 0)
        HAZE(2798, 4592, 0, SOUTH, 0)
        HAZE(2798, 4596, 0, SOUTH, 0)
        CORPSE(2800, 4573, 0, SOUTH, 5)
        CORPSE(2801, 4580, 0, SOUTH, 5)
        CORPSE(2802, 4580, 0, SOUTH, 5)
        HAZE(2802, 4591, 0, SOUTH, 0)
        RAT_2854(2802, 4599, 0, SOUTH, 14)
        CORPSE(2807, 4572, 0, SOUTH, 5)
        HAZE(2808, 4597, 0, SOUTH, 0)
        HAZE(2808, 4601, 0, SOUTH, 0)
        CORPSE(2810, 4573, 0, SOUTH, 5)
        CORPSE(2811, 4567, 0, SOUTH, 5)
        RAT_2854(2812, 4577, 0, SOUTH, 14)
        CORPSE(2813, 4571, 0, SOUTH, 5)
    }
}