package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11050Spawns : NPCSpawnsScript() {
    init {
        JUNGLE_SPIDER_5243(2755, 2705, 0, <PERSON>OUT<PERSON>, 4)
        SNAKE_5244(2755, 2708, 0, SOUT<PERSON>, 4)
        JUNGLE_SPIDER_5243(2756, 2716, 0, SOUTH, 4)
        BOBAWU(2758, 2747, 0, <PERSON>OUTH, 5)
        BIRD(2760, 2711, 0, <PERSON>OUTH, 13)
        SCORPION_5242(2762, 2749, 0, <PERSON>OUT<PERSON>, 5)
        BIRD_5241(2764, 2694, 0, <PERSON>OUTH, 26)
        SNAKE_5244(2766, 2715, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        JUN<PERSON><PERSON>_SPIDER_5243(2768, 2703, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        SCORPION_5242(2769, 2734, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        JUNGLE_SPIDER_5243(2770, 2709, 0, <PERSON><PERSON><PERSON>H, 4)
        B<PERSON>D_5241(2771, 2737, 0, SOUTH, 26)
        SCOR<PERSON>ON_5242(2772, 2740, 0, SOUTH, 5)
        B<PERSON>D(2773, 2704, 0, SOUTH, 13)
        S<PERSON>KE_5244(2774, 2702, 0, <PERSON>OUTH, 4)
        JUNG<PERSON>_<PERSON>IDER_5243(2779, 2707, 0, SOUTH, 4)
        B<PERSON>D_5241(2779, 2743, 0, SOUTH, 26)
        FISHING_SPOT_5233(2780, 2741, 0, SOUTH, 0)
        SCORPION_5242(2781, 2743, 0, SOUTH, 5)
        BIRD(2783, 2709, 0, SOUTH, 13)
        SNAKE_5244(2784, 2714, 0, SOUTH, 4)
        BIRD_5241(2787, 2694, 0, SOUTH, 26)
        JUNGLE_SPIDER_5243(2788, 2713, 0, SOUTH, 4)
        BIRD_5241(2794, 2705, 0, SOUTH, 26)
        BIRD_5241(2795, 2739, 0, SOUTH, 26)
        BIRD_5241(2798, 2716, 0, SOUTH, 26)
        DUGOPUL(2802, 2746, 0, SOUTH, 3)
        2022(2803, 2707, 0, SOUTH, 0)
        BIRD_5241(2807, 2714, 0, SOUTH, 26)
        BIRD_5241(2810, 2736, 0, SOUTH, 26)
    }
}