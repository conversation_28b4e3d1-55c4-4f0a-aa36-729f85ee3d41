package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11056Spawns : NPCSpawnsScript() {
    init {
        6261(2753, 3081, 0, SOUTH, 0)
        6558(2759, 3096, 0, SOUTH, 4)
        AISLES(2765, 3120, 0, SOUTH, 2)
        JIMINUA(2767, 3122, 0, SOUTH, 3)
        SNAKE_2845(2772, 3093, 0, <PERSON>OUT<PERSON>, 4)
        SNAKE_2845(2775, 3089, 0, SOUTH, 4)
        JUNGLE_SPIDER(2777, 3116, 0, SOUTH, 4)
        6578(2782, 3094, 0, <PERSON>O<PERSON><PERSON>, 5)
        749(2782, 3121, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        SNAKE_2845(2784, 3104, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        SNAKE_2845(2785, 3101, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        6556(2790, 3100, 0, SOUTH, 4)
        6549(2794, 3076, 0, SO<PERSON>H, 2)
        JUNGLE_SPIDER(2796, 3104, 0, SOUTH, 4)
        JUNGLE_SPIDER(2796, 3116, 0, SOUTH, 4)
        TOOL_LEPRECHAUN(2797, 3104, 0, SOUTH, 0)
        JUNGLE_SPIDER(2797, 3112, 0, SOUTH, 4)
        IMIAGO(2798, 3102, 0, SOUTH, 4)
        JUNGLE_SPIDER(2798, 3107, 0, SOUTH, 4)
        6551(2799, 3084, 0, SOUTH, 4)
        JUNGLE_SPIDER(2799, 3115, 0, SOUTH, 4)
        SNAKE_2845(2804, 3116, 0, SOUTH, 4)
        TRUFITUS(2809, 3086, 0, SOUTH, 2)
        SNAKE_2845(2811, 3112, 0, SOUTH, 4)
        TIMFRAKU(2780, 3087, 1, SOUTH, 5)
        6550(2786, 3074, 1, SOUTH, 5)
        6548(2794, 3076, 1, SOUTH, 2)
    }
}