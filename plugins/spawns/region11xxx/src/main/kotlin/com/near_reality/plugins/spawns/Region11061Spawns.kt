package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11061Spawns : NPCSpawnsScript() {
    init {
        GIANT_BAT(2752, 3409, 0, SOUTH, 11)
        GIANT_BAT(2753, 3396, 0, SOUTH, 11)
        GIANT_BAT(2759, 3401, 0, SOUTH, 11)
        RENEGADE_KNIGHT(2766, 3397, 0, SOUTH, 3)
        RENEGADE_KNIGHT(2767, 3399, 0, SOUTH, 3)
        RENEGADE_KNIGHT(2773, 3399, 0, SOUTH, 3)
        CANDLE_MAKER(2800, 3439, 0, SOUT<PERSON>, 2)
        MAN_3106(2803, 3431, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        6704(2807, 3440, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        BANKER_1613(2807, 3443, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        7457(2808, 3454, 0, SOUTH, 0)
        BANKER_1618(2809, 3443, 0, SOUTH, 0)
        6560(2810, 3443, 0, SOUTH, 0)
        BANKER_1618(2811, 3443, 0, EAST, 0)
        RENEGADE_KNIGHT(2766, 3406, 1, SOUTH, 3)
        RENEGADE_KNIGHT(2767, 3400, 1, SOUTH, 3)
        RENEGADE_KNIGHT(2770, 3396, 1, SOUTH, 3)
        RENEGADE_KNIGHT(2771, 3402, 1, SOUTH, 3)
        RENEGADE_KNIGHT(2774, 3405, 1, SOUTH, 3)
        1331(2796, 3415, 0, SOUTH, 2)
        1334(2797, 3415, 0, SOUTH, 2)
        ARHEIN(2803, 3430, 0, EAST, 0)
        MAN_3106(2805, 3428, 0, SOUTH, 5)
        SIR_MORDRED(2769, 3403, 2, SOUTH, 5)
    }
}