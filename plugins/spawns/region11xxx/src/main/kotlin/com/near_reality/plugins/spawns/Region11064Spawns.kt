package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11064Spawns : NPCSpawnsScript() {
    init {
        GOLDEN_SHEEP_806(2761, 3608, 0, SOUTH, 4)
        GOLDEN_SHEEP(2763, 3610, 0, SOUTH, 5)
        GOLDEN_SHEEP_807(2763, 3612, 0, SOUTH, 4)
        GOLDEN_SHEEP_805(2764, 3606, 0, SOUTH, 5)
        GOLDEN_SHEEP_806(2766, 3605, 0, SOUTH, 4)
        GOLDEN_SHEEP(2768, 3610, 0, SOUTH, 5)
        GOLDEN_SHEEP_807(2769, 3607, 0, <PERSON>OUTH, 4)
        LALLI(2770, 3622, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
    }
}