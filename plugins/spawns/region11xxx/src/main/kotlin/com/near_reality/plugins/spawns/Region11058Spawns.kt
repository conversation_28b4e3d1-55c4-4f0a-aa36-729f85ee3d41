package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11058Spawns : NPCSpawnsScript() {
    init {
        GARTH(2766, 3212, 0, SOUTH, 4)
        JUNGLE_SPIDER(2767, 3204, 0, SOUTH, 4)
        JUNGLE_SPIDER(2768, 3210, 0, SOUTH, 4)
        SNAKE_2845(2772, 3209, 0, SOUTH, 4)
        TOOL_LEPRECHAUN(2773, 3211, 0, SOUTH, 0)
        HAJEDY(2779, 3211, 0, SOUTH, 2)
        PRAISTAN_EBOLA(2799, 3202, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        1334(2759, 3239, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        1329(2760, 3239, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        748(2772, 3223, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        CAPTAIN_BARNABY_8764(2773, 3229, 0, <PERSON>OUTH, 5)
    }
}