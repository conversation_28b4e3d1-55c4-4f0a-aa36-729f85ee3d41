package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11055Spawns : NPCSpawnsScript() {
    init {
        424(2757, 3061, 0, SOUTH, 2)
        HORNED_GRAAHK(2766, 3008, 0, SOUTH, 6)
        TRIBESMAN(2771, 3014, 0, SOUTH, 5)
        TRIBESMAN(2773, 3017, 0, SOUTH, 5)
        TRIBESMAN(2777, 3068, 0, SOUTH, 5)
        JUNGLE_SPIDER(2780, 3029, 0, SOUTH, 4)
        JUNGLE_SPIDER(2780, 3031, 0, SOUTH, 4)
        CHICKEN(2780, 3064, 0, <PERSON>O<PERSON><PERSON>, 2)
        6236(2781, 3057, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        HOB<PERSON><PERSON>BLIN_3049(2787, 3013, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        CHICKEN_1174(2788, 3061, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        6238(2790, 3054, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        HOBGO<PERSON><PERSON>_3049(2791, 3013, 0, SOUTH, 13)
        FISHING_SPOT_4710(2791, 3019, 0, SOUTH, 0)
        HOBGOBLIN_3049(2794, 3013, 0, SOUTH, 13)
        6557(2794, 3065, 0, SOUTH, 5)
        6559(2799, 3047, 0, SOUTH, 2)
        6240(2800, 3057, 0, SOUTH, 2)
        FISHING_SPOT_4710(2801, 3010, 0, SOUTH, 0)
        TRIBESMAN(2801, 3067, 0, SOUTH, 5)
        CHICKEN_1174(2805, 3064, 0, SOUTH, 4)
        FISHING_SPOT_4710(2807, 3021, 0, SOUTH, 0)
        CHICKEN(2808, 3067, 0, SOUTH, 2)
        GABOOTY(2791, 3066, 0, SOUTH, 3)
    }
}