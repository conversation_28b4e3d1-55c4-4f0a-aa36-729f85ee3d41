package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11088Spawns : NPCSpawnsScript() {
    init {
        SOPHANEM_GUARD_3883(2764, 5133, 0, SOUTH, 5)
        SOPHANEM_GUARD(2767, 5133, 0, SOUTH, 5)
        SOPHANEM_GUARD_3883(2798, 5162, 0, SOUTH, 5)
        BANKER_3887(2798, 5171, 0, SOUTH, 5)
        BANKER_3888(2799, 5171, 0, SOUTH, 5)
        BANKER_3888(2800, 5171, 0, SOUTH, 5)
        SOPHANEM_GUARD(2801, 5162, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        6511(2801, 5171, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
    }
}