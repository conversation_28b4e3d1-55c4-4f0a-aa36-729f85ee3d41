package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11062Spawns : NPCSpawnsScript() {
    init {
        SIR_PALOMEDES(2754, 3509, 0, SOUTH, 5)
        SIR_LUCAN(2754, 3515, 0, SOUTH, 4)
        7458(2755, 3481, 0, SOUTH, 0)
        THE_WEDGE(2760, 3476, 0, WEST, 0)
        SIR_KAY(2761, 3497, 0, SOUTH, 4)
        SIR_GAWAIN(2763, 3506, 0, SOUTH, 3)
        KING_ARTHUR(2764, 3515, 0, SOUTH, 2)
        UNICORN(2783, 3466, 0, <PERSON><PERSON><PERSON><PERSON>, 15)
        9392(2784, 3463, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        UNICORN(2791, 3460, 0, <PERSON><PERSON><PERSON><PERSON>, 15)
        ICE_GIANT_2087(2804, 3507, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        DANTAERA(2808, 3464, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        ICE_G<PERSON>NT(2811, 3506, 0, SOUTH, 3)
        TOOL_LEPRECHAUN(2815, 3467, 0, SOUTH, 0)
        SIR_TRISTRAM(2755, 3512, 1, SOUTH, 5)
        SIR_LANCELOT(2759, 3515, 1, SOUTH, 2)
        SIR_PELLEAS(2761, 3503, 1, SOUTH, 4)
        SIR_BEDIVERE(2766, 3511, 1, SOUTH, 2)
    }
}