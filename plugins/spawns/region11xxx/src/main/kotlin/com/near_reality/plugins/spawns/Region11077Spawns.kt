package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11077Spawns : NPCSpawnsScript() {
    init {
        MINE_CART_3620(2781, 4462, 0, SOUTH, 0)
        LOADING_CRANE(2782, 4458, 0, SOUTH, 0)
        MINE_CART_3620(2783, 4446, 0, SOUTH, 0)
        MINE_CART_3620(2785, 4447, 0, SOUTH, 0)
        MINE_CART_3620(2785, 4456, 0, SOUTH, 0)
        LOADING_CRANE(2787, 4446, 0, SOUTH, 0)
        LOADING_CRANE(2787, 4451, 0, SOUTH, 0)
        INNOCENTLOOKING_KEY(2788, 4455, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        MINE_CART_3620(2791, 4447, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        MINE_CART_3620(2791, 4456, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        MINE_CART_3620(2793, 4446, 0, SOUTH, 0)
        LOADING_CRANE(2793, 4458, 0, SOUTH, 0)
    }
}