package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11151Spawns : NPCSpawnsScript() {
    init {
        SPIDER_5238(2761, 9189, 0, SOUTH, 3)
        MONKEY_ZOMBIE_5283(2761, 9193, 0, SOUTH, 2)
        MONKEY_ZOMBIE(2761, 9197, 0, SOUTH, 6)
        MONKEY_ZOMBIE_5283(2761, 9204, 0, SOUTH, 2)
        MONKEY_ZOMBIE(2763, 9201, 0, SOUTH, 6)
        MONKEY_ZOMBIE_5283(2763, 9205, 0, SOUTH, 2)
        MONKEY_ZOMBIE(2764, 9191, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        MONKEY_ZOMBIE_5283(2764, 9193, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        SPIDER_5238(2764, 9195, 0, <PERSON><PERSON>UT<PERSON>, 3)
        MONKEY_ZOMBIE_5283(2764, 9197, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2765, 9188, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2765, 9202, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2765, 9203, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2766, 9191, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2766, 9196, 0, SOUTH, 2)
        MONKEY_ZOMBIE(2766, 9199, 0, SOUTH, 6)
        SPIDER_5238(2767, 9163, 0, SOUTH, 3)
        SPIDER_5238(2768, 9187, 0, SOUTH, 3)
        MONKEY_ZOMBIE_5283(2768, 9197, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2768, 9201, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5282(2768, 9203, 0, SOUTH, 2)
        MONKEY_ZOMBIE(2769, 9190, 0, SOUTH, 6)
        SPIDER_5238(2769, 9193, 0, SOUTH, 3)
        MONKEY_ZOMBIE_5282(2769, 9202, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2770, 9188, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2770, 9192, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2770, 9199, 0, SOUTH, 2)
        SPIDER_5238(2770, 9201, 0, SOUTH, 3)
        MONKEY_ZOMBIE_5282(2770, 9202, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2771, 9195, 0, SOUTH, 2)
        SPIDER_5238(2773, 9194, 0, SOUTH, 3)
        SPIDER_5238(2776, 9163, 0, SOUTH, 3)
        SPIDER_5238(2779, 9163, 0, SOUTH, 3)
        MONKEY_ZOMBIE_5283(2782, 9193, 0, SOUTH, 2)
        SPIDER_5238(2784, 9201, 0, SOUTH, 3)
        SPIDER_5238(2785, 9164, 0, SOUTH, 3)
        SPIDER_5238(2785, 9190, 0, SOUTH, 3)
        MONKEY_ZOMBIE_5283(2785, 9200, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2786, 9204, 0, SOUTH, 2)
        SPIDER_5238(2787, 9169, 0, SOUTH, 3)
        SPIDER_5238(2788, 9196, 0, SOUTH, 3)
        SPIDER_5238(2789, 9172, 0, SOUTH, 3)
        MONKEY_ZOMBIE_5283(2790, 9194, 0, SOUTH, 2)
        SPIDER_5238(2792, 9167, 0, SOUTH, 3)
        SPIDER_5238(2796, 9170, 0, SOUTH, 3)
        SPIDER_5238(2796, 9175, 0, SOUTH, 3)
        SPIDER_5238(2797, 9198, 0, SOUTH, 3)
        MONKEY_ZOMBIE_5283(2798, 9196, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2798, 9199, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2798, 9204, 0, SOUTH, 2)
        SPIDER_5238(2799, 9205, 0, SOUTH, 3)
        SPIDER_5238(2800, 9162, 0, SOUTH, 3)
        MONKEY_ZOMBIE_5283(2800, 9196, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2800, 9198, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2800, 9203, 0, SOUTH, 2)
        SPIDER_5238(2801, 9198, 0, SOUTH, 3)
        MONKEY_ZOMBIE_5283(2806, 9201, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2807, 9200, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5283(2807, 9202, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5282(2808, 9191, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5282(2808, 9210, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5282(2809, 9193, 0, SOUTH, 2)
        MONKEY_ZOMBIE_5282(2809, 9208, 0, SOUTH, 2)
    }
}