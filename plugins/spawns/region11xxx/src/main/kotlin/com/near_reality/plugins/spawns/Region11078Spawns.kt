package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11078Spawns : NPCSpawnsScript() {
    init {
        BAT(2759, 4498, 0, SOUTH, 23)
        BAT(2761, 4495, 0, SOUTH, 23)
        BAT(2762, 4500, 0, SOUTH, 23)
        CORPSE(2762, 4509, 0, SOUTH, 5)
        CORPSE(2767, 4523, 0, SOUTH, 5)
        CORPSE(2769, 4501, 0, SOUTH, 5)
        CORPSE(2769, 4517, 0, SOUTH, 5)
        CORPSE(2773, 4515, 0, SOUTH, 5)
        CORPSE(2773, 4532, 0, SOUTH, 5)
        BAT(2782, 4491, 0, <PERSON><PERSON><PERSON><PERSON>, 23)
        BAT(2783, 4487, 0, <PERSON><PERSON><PERSON><PERSON>, 23)
        CORPSE(2783, 4494, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GIANT_BAT(2787, 4503, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        GIANT_BAT(2789, 4499, 0, SOUTH, 11)
        CORPSE(2789, 4516, 0, SOUTH, 5)
        BAT(2792, 4488, 0, SOUTH, 23)
        CORPSE(2792, 4490, 0, SOUTH, 5)
        CORPSE(2795, 4496, 0, SOUTH, 5)
        RAT_2854(2798, 4531, 0, SOUTH, 14)
        RAT_2854(2800, 4526, 0, SOUTH, 14)
        RAT_2854(2803, 4526, 0, SOUTH, 14)
        CORPSE(2804, 4525, 0, SOUTH, 5)
        CORPSE(2806, 4502, 0, SOUTH, 5)
        CORPSE(2807, 4513, 0, SOUTH, 5)
        CORPSE(2808, 4518, 0, SOUTH, 5)
    }
}