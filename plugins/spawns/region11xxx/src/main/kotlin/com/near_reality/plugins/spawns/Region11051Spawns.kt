package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11051Spawns : NPCSpawnsScript() {
    init {
        SCORPION_5242(2752, 2762, 0, SOUTH, 5)
        BIRD_5241(2753, 2754, 0, SOUT<PERSON>, 26)
        SPIDER_5238(2753, 2799, 0, SOUTH, 3)
        IFABA(2754, 2775, 0, SOUTH, 2)
        BIRD_5241(2756, 2811, 0, SOUTH, 26)
        TUTAB(2757, 2770, 0, SOUTH, 2)
        PADULAH(2757, 2788, 0, SOUTH, 2)
        7195(2758, 2789, 0, SO<PERSON><PERSON>, 2)
        DAGA(2759, 2775, 0, <PERSON>O<PERSON><PERSON>, 2)
        SLEEPING_MONKEY(2760, 2771, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        SPIDER_5238(2761, 2770, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SCORPION_5242(2761, 2803, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        ABERAB(2764, 2803, 0, SOUTH, 5)
        SCOR<PERSON>ON_5242(2766, 2761, 0, SOUTH, 5)
        7195(2767, 2776, 0, SOUTH, 2)
        SPIDER_5238(2767, 2794, 0, SOUTH, 3)
        BIRD_5241(2768, 2757, 0, SOUTH, 26)
        TREFAJI(2768, 2803, 0, SOUTH, 4)
        SOLIHIB(2769, 2789, 0, SOUTH, 2)
        SCORPION_5242(2769, 2806, 0, SOUTH, 5)
        SPIDER_5238(2770, 2766, 0, SOUTH, 3)
        OOBAPOHK(2774, 2778, 0, SOUTH, 2)
        SPIDER_5238(2775, 2777, 0, SOUTH, 3)
        7183(2775, 2796, 0, SOUTH, 2)
        7185(2775, 2798, 0, SOUTH, 2)
        7187(2775, 2801, 0, SOUTH, 2)
        SCORPION_5242(2776, 2764, 0, SOUTH, 5)
        BIRD_5241(2779, 2809, 0, SOUTH, 26)
        SPIDER_5238(2780, 2791, 0, SOUTH, 3)
        6765(2781, 2782, 0, SOUTH, 2)
        HAMAB(2781, 2793, 0, SOUTH, 2)
        7196(2781, 2799, 0, SOUTH, 2)
        BIRD_5241(2787, 2752, 0, SOUTH, 26)
        MIZARU(2787, 2795, 0, SOUTH, 0)
        MONKEY_GUARD_5275(2788, 2778, 0, SOUTH, 6)
        KIKAZARU(2788, 2795, 0, SOUTH, 0)
        SNAKE_5244(2789, 2758, 0, SOUTH, 4)
        IWAZARU(2789, 2795, 0, SOUTH, 0)
        SPIDER_5238(2791, 2780, 0, SOUTH, 3)
        SCORPION_5242(2792, 2796, 0, SOUTH, 5)
        SCORPION_5242(2793, 2778, 0, SOUTH, 5)
        MONKEY_GUARD_5275(2793, 2782, 0, SOUTH, 6)
        MONKEY_GUARD_5275(2793, 2789, 0, SOUTH, 6)
        MONKEY_GUARD_5275(2795, 2775, 0, SOUTH, 6)
        SPIDER_5238(2796, 2763, 0, SOUTH, 3)
        BIRD_5241(2797, 2810, 0, SOUTH, 26)
        ELDER_GUARD(2798, 2760, 0, NORTH_EAST, 0)
        SPIDER_5238(2798, 2792, 0, SOUTH, 3)
        ELDER_GUARD_5278(2801, 2757, 0, EAST, 0)
        UWOGO(2801, 2764, 0, SOUTH, 2)
        SPIDER_5238(2801, 2780, 0, SOUTH, 3)
        MONKEY_GUARD_5275(2801, 2782, 0, SOUTH, 6)
        MONKEY_GUARD_5275(2801, 2789, 0, SOUTH, 6)
        MONKEY_GUARD_5276(2803, 2785, 0, SOUTH, 2)
        MURUWOI(2804, 2764, 0, SOUTH, 2)
        SCORPION_5242(2804, 2797, 0, SOUTH, 5)
        7181(2805, 2762, 0, SOUTH, 2)
        MONKEY_GUARD_5276(2805, 2783, 0, SOUTH, 2)
        MONKEY_GUARD_5276(2805, 2786, 0, SOUTH, 2)
        SNAKE_5244(2809, 2754, 0, SOUTH, 4)
        BIRD_5241(2813, 2757, 0, SOUTH, 26)
        MONKEY_ARCHER_5274(2752, 2782, 0, SOUTH, 0)
        MONKEY_ARCHER_5274(2754, 2778, 0, SOUTH, 0)
        MONKEY_ARCHER_5274(2755, 2774, 0, SOUTH, 0)
        MONKEY_ARCHER_5274(2756, 2789, 0, SOUTH, 0)
        MONKEY_ARCHER_5274(2759, 2756, 0, SOUTH, 0)
        MONKEY_ARCHER_5274(2761, 2778, 0, SOUTH, 0)
        MONKEY_ARCHER_5274(2762, 2793, 0, SOUTH, 0)
        MONKEY_ARCHER_5274(2765, 2779, 0, SOUTH, 0)
        SPIDER_5238(2767, 2765, 1, SOUTH, 3)
        MONKEY_ARCHER_5274(2767, 2784, 0, SOUTH, 0)
        MONKEY_ARCHER(2768, 2794, 1, SOUTH, 0)
        MONKEY_ARCHER_5274(2769, 2754, 0, SOUTH, 0)
        SPIDER_5238(2769, 2798, 1, SOUTH, 3)
        MONKEY_ARCHER_5274(2770, 2774, 0, SOUTH, 0)
        MONKEY_ARCHER_5274(2772, 2781, 0, SOUTH, 0)
        MONKEY_ARCHER_5274(2774, 2787, 0, SOUTH, 0)
        MONKEY_ARCHER(2774, 2799, 1, SOUTH, 0)
        MONKEY_ARCHER_5274(2775, 2756, 0, SOUTH, 0)
        MONKEY_ARCHER_5274(2777, 2790, 0, SOUTH, 0)
        SPIDER_5238(2778, 2768, 1, SOUTH, 3)
        MONKEY_ARCHER_5274(2780, 2773, 0, SOUTH, 0)
        MONKEY_ARCHER_5274(2785, 2768, 0, SOUTH, 0)
        MONKEY_ARCHER_5274(2789, 2754, 0, SOUTH, 0)
        MONKEY_GUARD_5275(2790, 2790, 1, SOUTH, 6)
        MONKEY_ARCHER_5274(2791, 2763, 0, SOUTH, 0)
        SPIDER_5238(2793, 2789, 1, SOUTH, 3)
        SPIDER_5238(2794, 2780, 1, SOUTH, 3)
        MONKEY_GUARD_5275(2797, 2780, 1, SOUTH, 6)
        MONKEY_GUARD_5275(2797, 2791, 1, SOUTH, 6)
        HAFUBA(2797, 2798, 1, SOUTH, 2)
        LOFU(2805, 2782, 1, SOUTH, 4)
        DENADU(2805, 2788, 1, SOUTH, 3)
        SPIDER_5238(2806, 2787, 1, SOUTH, 3)
        SPIDER_5238(2752, 2798, 2, SOUTH, 3)
        6810(2755, 2770, 2, SOUTH, 5)
        6809(2768, 2798, 2, SOUTH, 3)
        MONKEY_ARCHER(2774, 2768, 2, SOUTH, 0)
        MONKEY_ARCHER(2774, 2777, 2, SOUTH, 0)
        MONKEY_ARCHER(2780, 2791, 2, SOUTH, 0)
        MONKEY_ARCHER(2787, 2793, 2, SOUTH, 0)
    }
}