package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11065Spawns : NPCSpawnsScript() {
    init {
        BALD_HEADED_EAGLE(2757, 3674, 0, SOUTH, 5)
        GUARD_1371(2760, 3660, 0, SOUTH, 2)
        BALD_HEADED_EAGLE(2762, 3671, 0, SOUTH, 5)
        RAGNAR(2765, 3676, 0, SOUTH, 5)
        BALD_HEADED_EAGLE(2785, 3658, 0, SOUTH, 5)
        CAMP_DWELLER(2794, 3668, 0, SOUTH, 5)
        CAMP_DWELLER_1382(2797, 3672, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GUARD_1372(2798, 3667, 0, <PERSON>O<PERSON><PERSON>, 5)
        CAMP_DWELLER_1380(2801, 3669, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        CAMP_DWELLER_1383(2802, 3674, 0, <PERSON><PERSON><PERSON>H, 5)
        CA<PERSON>_DW<PERSON>LER_1381(2803, 3668, 0, SOUTH, 5)
        MOUNTAIN_GOAT_1387(2810, 3684, 0, SOUTH, 5)
        HAMAL_THE_CHIEFTAIN(2811, 3673, 0, SOUTH, 5)
        JOKUL(2811, 3681, 0, SOUTH, 5)
        MOUNTAIN_GOAT_1386(2811, 3687, 0, SOUTH, 5)
        MOUNTAIN_GOAT_1385(2812, 3685, 0, SOUTH, 5)
        MOUNTAIN_GOAT(2813, 3687, 0, SOUTH, 5)
    }
}