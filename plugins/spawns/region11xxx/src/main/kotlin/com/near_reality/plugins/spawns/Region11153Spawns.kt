package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11153Spawns : NPCSpawnsScript() {
    init {
        UNGADULU(2792, 9327, 0, SOUTH, 5)
        DEATH_WING(2796, 9304, 0, SOUTH, 9)
        DEATH_WING(2799, 9308, 0, SOUTH, 9)
        DEATH_WING(2800, 9301, 0, SOUTH, 9)
        DEATH_WING(2803, 9296, 0, <PERSON>OUTH, 9)
        DEATH_WING(2806, 9292, 0, SOUTH, 9)
        DEATH_WING(2808, 9308, 0, SOUTH, 9)
        DEATH_WING(2811, 9296, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        DEATH_WING(2811, 9301, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
    }
}