package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11053Spawns : NPCSpawnsScript() {
    init {
        OOMLIE_BIRD(2768, 2912, 0, SOUTH, 5)
        OOMLIE_BIRD(2771, 2907, 0, SOUTH, 5)
        OOMLIE_BIRD(2773, 2928, 0, SOUTH, 5)
        JUNGLE_WOLF(2775, 2913, 0, SOUTH, 6)
        JUNGLE_SAVAGE(2776, 2905, 0, SOUTH, 22)
        OOMLIE_BIRD(2776, 2934, 0, SOUTH, 5)
        OOMLIE_BIRD(2778, 2900, 0, SOUTH, 5)
        JUNGLE_SAVAGE(2783, 2920, 0, <PERSON><PERSON><PERSON><PERSON>, 22)
        JUNG<PERSON>_SAVAGE(2784, 2908, 0, <PERSON>O<PERSON><PERSON>, 22)
        OOMLIE_BIRD(2785, 2925, 0, SOUTH, 5)
        OOMLIE_BIRD(2785, 2935, 0, SOUTH, 5)
        O<PERSON>LIE_BIRD(2789, 2915, 0, SOUTH, 5)
        JUNGLE_WOLF(2789, 2932, 0, SOUTH, 6)
        OOMLIE_BIRD(2791, 2931, 0, SOUTH, 5)
        OOMLIE_BIRD(2794, 2906, 0, SOUTH, 5)
        O<PERSON>LIE_<PERSON>IRD(2798, 2901, 0, SOUTH, 5)
        OOMLIE_BIRD(2799, 2933, 0, SOUTH, 5)
        JUNGLE_WOLF(2801, 2899, 0, SOUTH, 6)
        JUNGLE_SAVAGE(2802, 2903, 0, SOUTH, 22)
        JUNGLE_SAVAGE(2802, 2913, 0, SOUTH, 22)
        OOMLIE_BIRD(2802, 2921, 0, SOUTH, 5)
        OOMLIE_BIRD(2807, 2908, 0, SOUTH, 5)
        JUNGLE_SAVAGE(2809, 2912, 0, SOUTH, 22)
        JUNGLE_WOLF(2813, 2917, 0, SOUTH, 6)
        OOMLIE_BIRD(2815, 2908, 0, SOUTH, 5)
    }
}