package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11103Spawns : NPCSpawnsScript() {
    init {
        8817(2771, 6104, 0, <PERSON><PERSON>UT<PERSON>, 5)
        DALLDAV(2772, 6108, 0, SOUTH, 3)
        ORONWEN(2772, 6123, 0, SOUTH, 2)
        3206(2774, 6093, 0, SOUTH, 0)
        8828(2774, 6105, 0, SOUTH, 5)
        ELF_ARCHER(2777, 6114, 0, SOUTH, 6)
        ELF_WARRIOR_5294(2777, 6117, 0, SOUTH, 6)
        MAWRTH(2779, 6109, 0, SOUT<PERSON>, 4)
        IONA(2780, 6119, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        ARVEL(2781, 6122, 0, <PERSON>O<PERSON><PERSON>, 4)
        EUDAV(2782, 6126, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        4555(2784, 6116, 0, <PERSON>OUTH, 5)
        GOREU(2785, 6103, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        EOIN(2790, 6121, 0, SOUTH, 5)
        ELF_WARRIOR(2791, 6111, 0, SOUTH, 6)
        LILIWEN(2794, 6104, 0, SOUTH, 2)
        TOOL_LEPRECHAUN(2794, 6108, 0, <PERSON>OUTH, 0)
        4557(2799, 6118, 0, SOUTH, 5)
        BANKER_1479(2800, 6110, 0, SOUTH, 0)
        4008(2801, 6116, 0, SOUTH, 4)
        8824(2802, 6099, 0, SOUTH, 5)
        BANKER_1480(2802, 6110, 0, SOUTH, 0)
        ELF_ARCHER_5296(2784, 6111, 1, SOUTH, 2)
        GETHIN(2789, 6102, 1, SOUTH, 2)
    }
}