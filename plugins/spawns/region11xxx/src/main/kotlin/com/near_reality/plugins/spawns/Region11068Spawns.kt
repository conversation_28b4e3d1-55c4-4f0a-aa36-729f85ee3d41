package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11068Spawns : NPCSpawnsScript() {
    init {
        ICE_WOLF(2761, 3863, 0, SOUTH, 5)
        ICE_WOLF_646(2764, 3858, 0, SOUTH, 5)
        ICE_WOLF(2769, 3854, 0, SOUTH, 5)
        ICE_WOLF_646(2769, 3861, 0, SOUTH, 5)
        BRAMBICKLE(2786, 3862, 0, <PERSON>OUTH, 5)
        MOUNTAIN_GOAT_4146(2798, 3842, 0, SOUTH, 2)
        ICE_WOLF_646(2800, 3859, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        MOUNTAIN_GOAT_4145(2802, 3841, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        ICE_WOLF_646(2803, 3864, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        MOUNTAIN_GOAT_4147(2804, 3843, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        ICE_WOLF(2805, 3859, 0, SOUTH, 5)
        MOUNTA<PERSON>_GOAT_4145(2807, 3843, 0, SOUTH, 2)
    }
}