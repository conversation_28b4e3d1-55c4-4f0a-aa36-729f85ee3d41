package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8284Spawns : NPCSpawnsScript() {
    init {
        BIRD_10541(2050, 5902, 0, SOUTH, 5)
        SQUIRREL_1418(2052, 5922, 0, SOUTH, 10)
        BUTTERFLY_238(2056, 5930, 0, SOUTH, 0)
        BUTTERFLY_238(2064, 5900, 0, SOUTH, 0)
        IMP_5007(2066, 5903, 0, SOUTH, 100)
        IMP_5007(2075, 5914, 0, SOUTH, 100)
        SQUIRREL_1417(2076, 5917, 0, SOUTH, 9)
        IMP_5007(2077, 5925, 0, SOUTH, 100)
        BIRD_10541(2079, 5947, 0, <PERSON>O<PERSON><PERSON>, 5)
        CRAB_8733(2083, 5892, 0, <PERSON>OUTH, 5)
        IMP_5007(2083, 5905, 0, SOUTH, 100)
        BIRD_10541(2085, 5921, 0, SOUTH, 5)
        FISHING_SPOT_10514(2088, 5909, 0, SOUTH, 5)
        CRAB_1553(2091, 5898, 0, SOUTH, 4)
        BUTTERFLY_238(2092, 5946, 0, SOUTH, 0)
        SQUIRREL(2095, 5929, 0, SOUTH, 8)
        FISHING_SPOT_10514(2096, 5919, 0, SOUTH, 5)
    }
}