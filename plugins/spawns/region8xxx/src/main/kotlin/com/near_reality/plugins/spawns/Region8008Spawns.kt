package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8008Spawns : NPCSpawnsScript() {
    init {
        DARK_BEAST(1988, 4659, 0, SOUTH, 16)
        DARK_BEAST(1988, 4664, 0, SOUTH, 16)
        DARK_BEAST(1990, 4651, 0, SOUTH, 16)
        DARK_BEAST(1992, 4656, 0, SOUTH, 16)
        DARK_BEAST(1993, 4632, 0, SOUTH, 16)
        DARK_BEAST(1994, 4647, 0, SOUTH, 16)
        DARK_BEAST(1994, 4660, 0, SOUTH, 16)
        DARK_BEAST(1995, 4650, 0, SOUTH, 16)
        DARK_BEAST(1996, 4664, 0, <PERSON><PERSON><PERSON><PERSON>, 16)
        DARK_BEAST(1999, 4638, 0, <PERSON>OUT<PERSON>, 16)
        1136(2000, 4611, 0, <PERSON><PERSON><PERSON>H, 2)
        DARK_BEAST(2000, 4645, 0, <PERSON><PERSON><PERSON><PERSON>, 16)
        1143(2008, 4613, 0, SOUTH, 2)
        DARK_BEAST(2020, 4659, 0, SOUTH, 16)
        DARK_BEAST(2021, 4664, 0, SOUTH, 16)
        1117(2022, 4616, 0, SOUTH, 0)
        1148(2023, 4612, 0, SOUTH, 2)
        DARK_BEAST(2023, 4655, 0, SOUTH, 16)
        1117(2024, 4620, 0, SOUTH, 0)
        DARK_BEAST(2024, 4661, 0, SOUTH, 16)
        1136(2026, 4613, 0, SOUTH, 2)
        DARK_BEAST(2028, 4660, 0, SOUTH, 16)
        DARK_BEAST(2029, 4664, 0, SOUTH, 16)
        DARK_BEAST(2032, 4657, 0, SOUTH, 16)
        DARK_BEAST(2033, 4662, 0, SOUTH, 16)
        6287(2036, 4630, 0, SOUTH, 5)
        9237(2036, 4633, 0, SOUTH, 5)
        4267(2036, 4641, 0, SOUTH, 5)
        9236(2037, 4636, 0, SOUTH, 5)
        9236(2038, 4644, 0, SOUTH, 5)
        4267(2039, 4636, 0, SOUTH, 5)
        9236(2041, 4638, 0, SOUTH, 5)
        4262(2043, 4632, 0, SOUTH, 0)
        9235(2044, 4628, 0, SOUTH, 5)
        9236(2044, 4642, 0, SOUTH, 5)
        4262(2044, 4644, 0, SOUTH, 0)
    }
}