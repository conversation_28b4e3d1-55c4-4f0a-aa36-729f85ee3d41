package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8030Spawns : NPCSpawnsScript() {
    init {
        SQUIRREL_1418(1987, 6055, 0, SOUTH, 10)
        BIRD_10541(1989, 6056, 0, SOUTH, 5)
        BUTTERFLY_238(1990, 6051, 0, SOUTH, 0)
        BIRD_10541(1996, 6067, 0, SOUTH, 5)
        BIRD_10541(2000, 6058, 0, SOUTH, 5)
        SQUIRREL_1417(2001, 6033, 0, SOUTH, 9)
        COPPER_LONGTAIL(2011, 6035, 0, SOUTH, 7)
        SQUIRREL(2011, 6065, 0, SOUTH, 8)
        COPPER_LONGTAIL(2013, 6039, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        COPPER_LONGTAIL(2015, 6033, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        COPPER_LONGTAIL(2015, 6036, 0, <PERSON><PERSON>UTH, 7)
        UNICORN(2016, 6064, 0, SOUTH, 15)
        COPPER_LONGTAIL(2017, 6037, 0, SOUTH, 7)
        COPPER_LONGTAIL(2018, 6032, 0, SOUTH, 7)
        BUTTERFLY_238(2019, 6035, 0, SOUTH, 0)
        COPPER_LONGTAIL(2020, 6036, 0, SOUTH, 7)
        BIRD_10541(2023, 6067, 0, SOUTH, 5)
        BIRD_10541(2036, 6022, 0, SOUTH, 5)
        SQUIRREL_1418(2041, 6021, 0, SOUTH, 10)
    }
}