package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8499Spawns : NPCSpawnsScript() {
    init {
        FISHING_SPOT_3419(2165, 3266, 0, SOUTH, 0)
        FISHING_SPOT_3419(2165, 3268, 0, SOUTH, 0)
        FISHING_SPOT_3419(2165, 3286, 0, SOUTH, 0)
        SLIPPERS(2158, 3325, 0, SOUTH, 0)
        SURMA(2160, 3326, 0, SOUTH, 0)
        FLOKI(2161, 3326, 0, SOUTH, 0)
    }
}