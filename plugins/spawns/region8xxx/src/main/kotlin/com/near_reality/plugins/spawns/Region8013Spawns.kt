package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8013Spawns : NPCSpawnsScript() {
    init {
        2023(2006, 4959, 3, SOUTH, 5)
        EAGLE(2007, 4954, 3, SOUTH, 5)
        DESERT_EAGLE(2019, 4957, 3, SOUTH, 5)
        JUNGLE_EAGLE(2023, 4961, 3, SOUTH, 5)
        POLAR_EAGLE(2026, 4963, 3, SOUTH, 5)
    }
}