package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8283Spawns : NPCSpawnsScript() {
    init {
        BUTTERFLY_238(2050, 5860, 0, SOUTH, 0)
        SQUIRREL(2057, 5879, 0, SOUTH, 8)
        SANDY_ROCKS(2060, 5858, 0, SOUTH, 0)
        SANDY_ROCKS_7207(2065, 5863, 0, SOUTH, 0)
        SANDY_ROCKS(2069, 5863, 0, SOUTH, 0)
        CRAB_1553(2070, 5861, 0, SOUTH, 4)
        SANDY_ROCKS_7207(2075, 5864, 0, SOUTH, 0)
        SANDY_ROCKS(2081, 5870, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        CRAB_8733(2083, 5876, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SANDY_ROCKS(2084, 5864, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        SANDY_ROCKS(2085, 5884, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        CRAB_1553(2086, 5862, 0, SOUTH, 4)
        SANDY_ROCKS(2089, 5887, 0, SOUTH, 0)
        SANDY_ROCKS(2090, 5863, 0, SOUTH, 0)
        CRAB_1553(2090, 5869, 0, SOUTH, 4)
        SANDY_ROCKS(2091, 5877, 0, SOUTH, 0)
        CRAB_8733(2091, 5884, 0, SOUTH, 5)
        SANDY_ROCKS(2093, 5857, 0, SOUTH, 0)
        SANDY_ROCKS_7207(2094, 5864, 0, SOUTH, 0)
        SANDY_ROCKS(2095, 5868, 0, SOUTH, 0)
        CRAB_1553(2097, 5873, 0, SOUTH, 4)
        SANDY_ROCKS(2098, 5861, 0, SOUTH, 0)
        CRAB_8733(2099, 5858, 0, SOUTH, 5)
        SANDY_ROCKS_7207(2105, 5857, 0, SOUTH, 0)
    }
}