package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8492Spawns : NPCSpawnsScript() {
    init {
        BUTTERFLY_238(2113, 2859, 0, SOUTH, 0)
        SQUIRREL(2127, 2842, 0, SOUTH, 8)
        BIRD_10541(2133, 2873, 0, SOUTH, 5)
        BUTTERFLY_238(2139, 2836, 0, SOUTH, 0)
        BIRD_10541(2146, 2832, 0, SOUTH, 5)
        SQUIRREL_1417(2146, 2859, 0, SOUT<PERSON>, 9)
        BUTTERFLY_238(2147, 2877, 0, SOUTH, 0)
        CRIMSON_SWIFT(2152, 2825, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        CRIMSON_SWIFT(2153, 2829, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        CRIMSON_SWIFT(2155, 2827, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        SQUIRREL_1418(2157, 2824, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        CRIMSON_S<PERSON>FT(2161, 2818, 0, SOUTH, 6)
        CRIMSON_SWIFT(2163, 2823, 0, SOUTH, 6)
        BUTTERFLY_238(2165, 2816, 0, SOUTH, 0)
        CRIMSON_SWIFT(2165, 2819, 0, SOUTH, 6)
        BUTTERFLY_238(2167, 2846, 0, SOUTH, 0)
        SQUIRREL_1417(2167, 2862, 0, SOUTH, 9)
    }
}