package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8286Spawns : NPCSpawnsScript() {
    init {
        PYREFIEND(2055, 6036, 0, SOUTH, 4)
        PYRELORD_6795(2058, 6034, 0, SOUTH, 5)
        PYREFIEND_436(2059, 6038, 0, SOUTH, 2)
        PYREFIEND_436(2060, 6032, 0, SOUTH, 2)
        PYREFIEND_435(2061, 6030, 0, SOUTH, 5)
        PYREFIEND(2062, 6039, 0, SOUTH, 4)
        PYREFIEND_434(2063, 6033, 0, SOUTH, 4)
        PYRELORD_6795(2064, 6030, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        PYREFIEND_434(2065, 6037, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        PYREFIEND(2067, 6030, 0, <PERSON>OUT<PERSON>, 4)
        PYRELORD(2067, 6033, 0, SOUTH, 5)
        PYREFIEND_435(2068, 6036, 0, SOUTH, 5)
        PYREFIEND(2070, 6033, 0, SOUTH, 4)
        BUTTERFLY_238(2086, 6028, 0, SOUTH, 0)
        FISHING_SPOT_10515(2086, 6048, 0, SOUTH, 5)
        FISHING_SPOT_10515(2094, 6046, 0, SOUTH, 5)
        BIRD_10541(2097, 6027, 0, SOUTH, 5)
        SQUIRREL(2110, 6037, 0, SOUTH, 8)
    }
}