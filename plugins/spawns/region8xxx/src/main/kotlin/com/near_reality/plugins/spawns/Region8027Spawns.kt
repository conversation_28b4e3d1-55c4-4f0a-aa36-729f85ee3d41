package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8027Spawns : NPCSpawnsScript() {
    init {
        BUTTERFLY_238(1988, 5870, 0, SOUTH, 0)
        SQUIRREL(1992, 5854, 0, SOUTH, 8)
        RAT_2854(2001, 5861, 0, SOUTH, 14)
        RAT_2854(2002, 5869, 0, SOUTH, 14)
        RAT_2854(2012, 5863, 0, SOUTH, 14)
        BUTTERFLY_238(2017, 5884, 0, SOUTH, 0)
        RAT_2854(2024, 5859, 0, SOUTH, 14)
        BUTTERFLY_238(2025, 5852, 0, SOUTH, 0)
        SQUIRREL_1418(2035, 5877, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        MOSS_GIANT_2091(2035, 5887, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        BIRD_10541(2042, 5874, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
    }
}