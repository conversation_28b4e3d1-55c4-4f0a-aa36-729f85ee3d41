package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8236Spawns : NPCSpawnsScript() {
    init {
        SQUIRREL(2092, 2875, 0, SOUTH, 8)
        SQUIRREL_1417(2101, 2854, 0, SOUTH, 9)
        BUTTERFLY_238(2104, 2873, 0, SOUTH, 0)
    }
}