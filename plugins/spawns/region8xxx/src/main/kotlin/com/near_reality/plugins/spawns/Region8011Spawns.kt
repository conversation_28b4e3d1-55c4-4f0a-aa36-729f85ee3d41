package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8011Spawns : NPCSpawnsScript() {
    init {
        10711(2011, 4818, 0, SOUTH, 0)
        10710(2011, 4830, 0, SOUTH, 0)
        MURPHY_5609(2013, 4825, 0, SOUTH, 5)
        GULL_285(2014, 4818, 0, SOUTH, 5)
        GULL_285(2014, 4832, 0, SOUTH, 5)
        GULL_285(2018, 4831, 0, SOUTH, 5)
        GULL_284(2020, 4816, 0, SOUTH, 5)
        GULL_285(2023, 4820, 0, SOUTH, 5)
        10711(2011, 4818, 1, SOUTH, 0)
        10710(2011, 4830, 1, SOUTH, 0)
    }
}