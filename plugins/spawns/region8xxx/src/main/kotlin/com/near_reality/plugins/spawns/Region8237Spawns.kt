package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8237Spawns : NPCSpawnsScript() {
    init {
        BIRD_10541(2092, 2893, 0, SOUTH, 5)
        SQUIRREL(2101, 2921, 0, SOUTH, 8)
        BUTTERFLY_238(2102, 2942, 0, SOUTH, 0)
        10542(2105, 2895, 0, SOUTH, 5)
        BIRD_10541(2106, 2932, 0, SOUTH, 5)
        BUTTERFLY_238(2108, 2919, 0, SOUTH, 0)
    }
}