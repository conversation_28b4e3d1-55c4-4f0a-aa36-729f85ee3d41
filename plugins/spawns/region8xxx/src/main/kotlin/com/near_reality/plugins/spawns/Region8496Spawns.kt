package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8496Spawns : NPCSpawnsScript() {
    init {
        TYRAS_GUARD_1327(2154, 3121, 0, SOUTH, 2)
        TYRAS_GUARD_1327(2154, 3123, 0, SOUTH, 2)
        TYRAS_GUARD_1327(2159, 3125, 0, SOUTH, 2)
        TYRAS_GUARD_1327(2160, 3122, 0, SOUTH, 2)
        TYRAS_GUARD_1327(2141, 3122, 0, SOUTH, 2)
        1329(2144, 3122, 0, SOUTH, 3)
        1332(2145, 3122, 0, SOUTH, 2)
        TYRAS_GUARD_1327(2148, 3122, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
    }
}