package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8501Spawns : NPCSpawnsScript() {
    init {
        DIRE_WOLF_9181(2132, 3425, 0, SOUTH, 5)
        DIRE_WOLF(2143, 3417, 0, SOUTH, 12)
        DIRE_WOLF(2155, 3406, 0, SOUTH, 12)
        BLACK_DRAGON_253(2155, 3427, 0, SOUTH, 2)
        GHOST_89(2164, 3431, 0, SOUTH, 4)
        ED_9200(2166, 3417, 0, SOUTH, 5)
        GHOST_87(2167, 3429, 0, SOUTH, 2)
        GHOST_88(2167, 3432, 0, SOUTH, 5)
        GHOST(2168, 3427, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        9243(2168, 3430, 0, <PERSON><PERSON>UT<PERSON>, 5)
        GHOST_86(2170, 3428, 0, <PERSON>OUTH, 5)
        DIRE_WOLF(2172, 3409, 0, SOUTH, 12)
    }
}