package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8500Spawns : NPCSpawnsScript() {
    init {
        FISHING_SPOT_9173(2163, 3365, 0, SOUTH, 5)
        FISHING_SPOT_9171(2165, 3373, 0, SOUTH, 5)
        FISHING_SPOT_9173(2165, 3374, 0, SOUTH, 5)
        FISHING_SPOT_9171(2166, 3347, 0, <PERSON>OUTH, 5)
        RABBIT_3420(2168, 3365, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        STAG(2169, 3387, 0, SOUTH, 5)
        RABBIT_3420(2170, 3356, 0, SOUTH, 4)
        BEAR_CUB_9199(2170, 3370, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        1334(2157, 3329, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        1331(2157, 3330, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
    }
}