package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8028Spawns : NPCSpawnsScript() {
    init {
        DUCK_10547(1996, 5912, 0, SOUTH, 5)
        DUCK_10546(2002, 5907, 0, SOUTH, 5)
        DUCK_10546(2005, 5920, 0, SOUTH, 5)
        ZIMBERFIZZ_10530(2016, 5930, 0, SOUTH, 5)
        10555(2020, 5929, 0, <PERSON><PERSON>UT<PERSON>, 5)
        MOSS_GIANT_2092(2030, 5889, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        MOSS_GIANT(2031, 5893, 0, SOUTH, 4)
        MOSS_GIANT_2093(2034, 5891, 0, SOUTH, 4)
        MOSS_GIANT_2092(2038, 5893, 0, SO<PERSON>H, 3)
        MOSS_GIANT_2093(2040, 5888, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
    }
}