package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8076Spawns : NPCSpawnsScript() {
    init {
        OGRESS_WARRIOR_7990(1996, 8985, 1, SOUTH, 6)
        OGRESS_WARRIOR_7990(1997, 8967, 1, SOUTH, 6)
        OGRESS_SHAMAN(2000, 8965, 1, SOUTH, 6)
        OGRESS_SHAMAN(2002, 8989, 1, SOUTH, 6)
        OGRESS_WARRIOR(2003, 8981, 1, SOUTH, 5)
        OGRESS_SHAMAN(2009, 8977, 1, SOUTH, 6)
        OGRESS_WARRIOR_7990(2009, 8995, 1, SOUTH, 6)
        CHIEF_TESS(2010, 9007, 1, <PERSON><PERSON><PERSON><PERSON>, 2)
        OGRESS_SHAMAN(2015, 8980, 1, <PERSON><PERSON><PERSON><PERSON>, 6)
        OGRESS_WARRIOR(2018, 8993, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        OGRESS_SHAMAN(2019, 8988, 1, SOUTH, 6)
        OGRESS_SHAMAN(2022, 8973, 1, SOUTH, 6)
        OGRESS_WARRIOR(2028, 8971, 1, SOUTH, 5)
        OGRESS_WARRIOR_7990(2034, 8972, 1, SOUTH, 6)
        OGRESS_SHAMAN(2035, 8978, 1, SOUTH, 6)
        OGRESS_SHAMAN(2035, 8984, 1, SOUTH, 6)
        OGRESS_WARRIOR(2039, 8971, 1, SOUTH, 5)
    }
}