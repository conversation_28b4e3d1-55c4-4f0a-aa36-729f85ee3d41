package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8280Spawns : NPCSpawnsScript() {
    init {
        DEMONIC_GORILLA_7145(2072, 5651, 0, SOUTH, 22)
        DEMONIC_GORILLA_7148(2072, 5673, 0, SOUTH, 5)
        DEMONIC_GORILLA(2072, 5681, 0, SOUTH, 11)
        DEMONIC_GORILLA_7146(2073, 5645, 0, SOUTH, 8)
        DEMONIC_GORILLA_7147(2079, 5644, 0, SOUTH, 5)
        DEMONIC_GORILLA_7148(2080, 5651, 0, SO<PERSON><PERSON>, 5)
        DEMONIC_GORILLA_7146(2082, 5676, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        DEMONIC_GORILLA_7148(2095, 5673, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        DEMONIC_GORILLA_7147(2102, 5672, 0, <PERSON>OUT<PERSON>, 5)
        DEMONIC_GORILLA_7145(2102, 5679, 0, SOUTH, 22)
        DEMONIC_GORILLA_7149(2094, 5651, 0, SOUTH, 5)
        DEMONIC_GORILLA(2095, 5658, 0, SOUTH, 11)
        DEMONIC_GORILLA_7145(2096, 5645, 0, SOUTH, 22)
        DEMONIC_GORILLA_7147(2100, 5641, 0, SOUTH, 5)
        DEMONIC_GORILLA_7147(2102, 5651, 0, SOUTH, 5)
        DEMONIC_GORILLA_7146(2105, 5659, 0, SOUTH, 8)
        DEMONIC_GORILLA_7148(2107, 5646, 0, SOUTH, 5)
        DEMONIC_GORILLA_7149(2111, 5661, 0, SOUTH, 5)
    }
}