package com.near_reality.game.content.gauntlet.plugins;

import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.object.ObjectAction;
import com.zenyte.game.world.object.WorldObject;

public final class GauntletLobbyPortal implements ObjectAction {

    @Override
    public void handleObjectAction(Player player, WorldObject object, String name, int optionId, String option) {

    }

    @Override
    public Object[] getObjects() {
        return new Object[0];
    }

}
