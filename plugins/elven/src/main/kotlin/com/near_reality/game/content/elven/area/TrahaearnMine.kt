package com.near_reality.game.content.elven.area

import com.zenyte.game.world.entity.player.Player
import com.zenyte.game.world.region.RSPolygon

/**
 * Trahaearn mine is a mine located in [Prifddinas].
 *
 * <AUTHOR>
 */
@Suppress("UNUSED")
class TrahaearnMine : <PERSON><PERSON><PERSON><PERSON><PERSON>() {

    override fun enter(player: Player?) = Unit

    override fun leave(player: Player?, logout: <PERSON>ole<PERSON>) = Unit

    override fun name() = "Trahaearn mine"

    override fun polygons() = arrayOf(RSPolygon(3279, 12432, 3311, 12470))
}
