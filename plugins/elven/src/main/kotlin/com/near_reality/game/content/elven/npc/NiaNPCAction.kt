package com.near_reality.game.content.elven.npc

import com.near_reality.game.content.elven.npc.dialogue.NiaDialogue
import com.near_reality.scripts.npc.actions.NPCActionScript
import com.zenyte.game.world.entity.npc.NpcId

class NiaNPCAction : NPCActionScript() {
    init {
        npcs(NpcId.NIA)

        "Talk-To" {
            player.dialogueManager.start(Nia<PERSON><PERSON><PERSON>(player, npc))
        }

        "Trade" {
            // TODO: open shop
        }
    }
}
