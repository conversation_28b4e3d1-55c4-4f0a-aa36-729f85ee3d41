package com.near_reality.game.content.dt2.npc.whisperer.attacks.special.impl.leech

import com.zenyte.game.world.entity.Location
import com.zenyte.game.world.`object`.WorldObject

/**
 * <AUTHOR> | Glabay-Studios
 * @project near-reality-server
 * @social Discord: Glabay
 * @since 2024-09-27
 */
class CorruptedSeed(objectId: Int, location: Location, objectType: Int = 10, face: Int = 0) :
    WorldObject(objectId, objectType, face, location)