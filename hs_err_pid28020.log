#
# A fatal error has been detected by the Java Runtime Environment:
#
#  Internal Error (xMapper_windows.cpp:296), pid=28020, tid=8156
#  fatal error: Failed to map memory: 0x000004048cc00000 2M (1455)
#
# JRE version:  (21.0.7+6) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed class ptrs, z gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
#

---------------  S U M M A R Y ------------

Command Line: -Dio.netty.handler.ssl.openssl.useTasks=true -Dio.netty.tryReflectionSetAccessible=true -XX:+UseZGC -XX:-OmitStackTraceInFastThrow --add-opens=java.base/java.time=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=jdk.unsupported/sun.misc=ALL-UNNAMED --add-opens=java.base/jdk.internal.misc=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=jdk.naming.rmi/com.sun.jndi.rmi.registry=ALL-UNNAMED --add-opens=java.base/sun.net=ALL-UNNAMED -XX:+UseZGC -Xms24g -Xmx60g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -ea com.zenyte.Main main

Host: AMD Ryzen 5 PRO 7530U with Radeon Graphics     , 12 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
Time: Fri Jun  6 15:02:34 2025 Eastern Daylight Time elapsed time: 7.206897 seconds (0d 0h 0m 7s)

---------------  T H R E A D  ---------------

Current thread (0x0000020f178945e0):  JavaThread "Unknown thread" [_thread_in_vm, id=8156, stack(0x000000b677a00000,0x000000b677b00000) (1024K)]

Stack: [0x000000b677a00000,0x000000b677b00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d4bb9]
V  [jvm.dll+0x8afa11]
V  [jvm.dll+0x8b1f3e]
V  [jvm.dll+0x280601]
V  [jvm.dll+0x8ef808]
V  [jvm.dll+0x8fc490]
V  [jvm.dll+0x8fb41d]
V  [jvm.dll+0x8fb306]
V  [jvm.dll+0x8f7766]
V  [jvm.dll+0x8f73f5]
V  [jvm.dll+0x8e9bd7]
V  [jvm.dll+0x8e604a]
V  [jvm.dll+0x8788cd]
V  [jvm.dll+0x3c1b91]
V  [jvm.dll+0x861973]
V  [jvm.dll+0x456b7e]
V  [jvm.dll+0x4587c1]
C  [jli.dll+0x52a0]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x9c5dc]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffdda572108, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:

=>0x0000020f178945e0 (exited) JavaThread "Unknown thread"    [_thread_in_vm, id=8156, stack(0x000000b677a00000,0x000000b677b00000) (1024K)]
Total: 1

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread: None

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 NUMA Support: Disabled
 CPUs: 12 total, 12 available
 Memory: 15681M
 Large Page Support: Disabled
 GC Workers: 3 (dynamic)
 Address Space Type: Contiguous/Unrestricted/Complete
 Address Space Size: 983040M x 3 = 2949120M
 Min Capacity: 24576M
 Initial Capacity: 24576M
 Max Capacity: 61440M
 Medium Page Size: 32M
 Pre-touch: Disabled
 Uncommit: Enabled
 Uncommit Delay: 300s

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.016 Loaded shared library C:\Users\<USER>\.jdks\ms-21.0.7\bin\java.dll
Event: 0.018 Loaded shared library KernelBase

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff6e8aa0000 - 0x00007ff6e8aae000 	C:\Users\<USER>\.jdks\ms-21.0.7\bin\java.exe
0x00007ffe8dd60000 - 0x00007ffe8dfc6000 	C:\windows\SYSTEM32\ntdll.dll
0x00007ffe8cc80000 - 0x00007ffe8cd49000 	C:\windows\System32\KERNEL32.DLL
0x00007ffe8b5d0000 - 0x00007ffe8b99c000 	C:\windows\System32\KERNELBASE.dll
0x00007ffe8b120000 - 0x00007ffe8b26b000 	C:\windows\System32\ucrtbase.dll
0x00007ffe72800000 - 0x00007ffe7281d000 	C:\Users\<USER>\.jdks\ms-21.0.7\bin\VCRUNTIME140.dll
0x00007ffe76e60000 - 0x00007ffe76e78000 	C:\Users\<USER>\.jdks\ms-21.0.7\bin\jli.dll
0x00007ffe8da80000 - 0x00007ffe8dc4a000 	C:\windows\System32\USER32.dll
0x00007ffe8afa0000 - 0x00007ffe8afc7000 	C:\windows\System32\win32u.dll
0x00007ffe6da70000 - 0x00007ffe6dd0a000 	C:\windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e\COMCTL32.dll
0x00007ffe8d260000 - 0x00007ffe8d28b000 	C:\windows\System32\GDI32.dll
0x00007ffe8b270000 - 0x00007ffe8b3a2000 	C:\windows\System32\gdi32full.dll
0x00007ffe8cbd0000 - 0x00007ffe8cc79000 	C:\windows\System32\msvcrt.dll
0x00007ffe8aef0000 - 0x00007ffe8af93000 	C:\windows\System32\msvcp_win.dll
0x00007ffe8ca60000 - 0x00007ffe8ca90000 	C:\windows\System32\IMM32.DLL
0x00007ffe7a8a0000 - 0x00007ffe7a8ac000 	C:\Users\<USER>\.jdks\ms-21.0.7\bin\vcruntime140_1.dll
0x00007ffe5f920000 - 0x00007ffe5f9ad000 	C:\Users\<USER>\.jdks\ms-21.0.7\bin\msvcp140.dll
0x00007ffdd9930000 - 0x00007ffdda6c8000 	C:\Users\<USER>\.jdks\ms-21.0.7\bin\server\jvm.dll
0x00007ffe8c880000 - 0x00007ffe8c932000 	C:\windows\System32\ADVAPI32.dll
0x00007ffe8c660000 - 0x00007ffe8c706000 	C:\windows\System32\sechost.dll
0x00007ffe8c530000 - 0x00007ffe8c646000 	C:\windows\System32\RPCRT4.dll
0x00007ffe8c710000 - 0x00007ffe8c784000 	C:\windows\System32\WS2_32.dll
0x00007ffe8ad40000 - 0x00007ffe8ad9e000 	C:\windows\SYSTEM32\POWRPROF.dll
0x00007ffe84640000 - 0x00007ffe8464b000 	C:\windows\SYSTEM32\VERSION.dll
0x00007ffe84600000 - 0x00007ffe84636000 	C:\windows\SYSTEM32\WINMM.dll
0x00007ffe8ad20000 - 0x00007ffe8ad34000 	C:\windows\SYSTEM32\UMPDC.dll
0x00007ffe89b40000 - 0x00007ffe89b5a000 	C:\windows\SYSTEM32\kernel.appcore.dll
0x00007ffe727f0000 - 0x00007ffe727fa000 	C:\Users\<USER>\.jdks\ms-21.0.7\bin\jimage.dll
0x00007ffe7ebe0000 - 0x00007ffe7ee21000 	C:\windows\SYSTEM32\DBGHELP.DLL
0x00007ffe8bb20000 - 0x00007ffe8bea4000 	C:\windows\System32\combase.dll
0x00007ffe8c790000 - 0x00007ffe8c870000 	C:\windows\System32\OLEAUT32.dll
0x00007ffe73f80000 - 0x00007ffe73fb9000 	C:\windows\SYSTEM32\dbgcore.DLL
0x00007ffe8b530000 - 0x00007ffe8b5c9000 	C:\windows\System32\bcryptPrimitives.dll
0x00007ffe726f0000 - 0x00007ffe72710000 	C:\Users\<USER>\.jdks\ms-21.0.7\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\ms-21.0.7\bin;C:\windows\SYSTEM32;C:\windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e;C:\Users\<USER>\.jdks\ms-21.0.7\bin\server

VM Arguments:
jvm_args: -Dio.netty.handler.ssl.openssl.useTasks=true -Dio.netty.tryReflectionSetAccessible=true -XX:+UseZGC -XX:-OmitStackTraceInFastThrow --add-opens=java.base/java.time=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=jdk.unsupported/sun.misc=ALL-UNNAMED --add-opens=java.base/jdk.internal.misc=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=jdk.naming.rmi/com.sun.jndi.rmi.registry=ALL-UNNAMED --add-opens=java.base/sun.net=ALL-UNNAMED -XX:+UseZGC -Xms24g -Xmx60g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -ea 
java_command: com.zenyte.Main main
java_class_path (initial): C:\Users\<USER>\.gradle\.tmp\gradle-javaexec-classpath6907727046543667210.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
   size_t InitialHeapSize                          = 25769803776                               {product} {command line}
   size_t MaxHeapSize                              = 64424509440                               {product} {command line}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 25769803776                               {product} {command line}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
     bool OmitStackTraceInFastThrow                = false                                     {product} {command line}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 64424509440                            {manageable} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseZGC                                   = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\HID Global\ActivClient\;C:\Program Files (x86)\HID Global\ActivClient\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;
USERNAME=1612358490.CIV
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 80 Stepping 0, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
OS uptime: 10 days 2:52 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (12 cores per cpu, 2 threads per core) family 25 model 80 stepping 0 microcode 0xa500011, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, rdpid, fsrm, f16c, cet_ss
Processor Information for the first 12 processors :
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000

Memory: 4k page, system-wide physical 15681M (2024M free)
TotalPageFile size 62785M (AvailPageFile size 12M)
current process WorkingSet (physical memory assigned to process): 12M, peak: 12M
current process commit charge ("private bytes"): 48M, peak: 48M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-09T22:17:25Z by "MicrosoftCorporation" with unknown MS VC++:1939

END.
